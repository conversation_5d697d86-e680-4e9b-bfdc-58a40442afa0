// Import route modules
// const refundRoutes = require("./routes/refund");
// const stripeRoutes = require("./routes/stripe");
// const trainerRoutes = require("./routes/trainer");
// const athleteRoutes = require("./routes/athlete");
// const programRoutes = require("./routes/program");
// const discountRoutes = require("./routes/discount");
// const developerRoutes = require("./routes/develop");
// const postFeedRoutes = require("./routes/post_feed");
// const enrollmentRoutes = require("./routes/enrollment");
// const superAdminRoutes = require("./routes/super_admin");
// const transactionRoutes = require("./routes/transaction");
// const landingPageRoutes = require("./routes/landing_page");
// const athleteProgressRoutes = require("./routes/athlete_progress");
// const trainerDashboardRoutes = require("./routes/trainer_dashboard");
// const forgotPasswordRoutes = require("./routes/forgot_password");
// const resetPasswordRoutes = require("./routes/reset_password");
// const verifyOtp = require("./routes/verify_otp");

module.exports = function (app) {
  // console.log("🔧 Loading KSL_BE API routes...");
  // // Register landing page routes
  // console.log("📄 Loading landing page routes...");
  // try {
  //   landingPageRoutes(app);
  //   console.log("✅ Landing page routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading landing page routes:", error);
  // }
  // try {
  //   // Register program routes
  //   programRoutes(app);
  //   console.log("✅ Program routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading program routes:", error);
  // }
  // try {
  //   // Register discount routes
  //   discountRoutes(app);
  //   console.log("✅ Discount routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading discount routes:", error);
  // }
  // try {
  //   // Register post feed routes
  //   postFeedRoutes(app);
  //   console.log("✅ Post feed routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading post feed routes:", error);
  // }
  // try {
  //   // Register enrollment routes
  //   enrollmentRoutes(app);
  //   console.log("✅ Enrollment routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading enrollment routes:", error);
  // }
  // // Register trainer routes
  // console.log("👨‍🏫 Loading trainer routes...");
  // try {
  //   trainerRoutes(app);
  //   console.log("✅ Trainer routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading trainer routes:", error);
  // }
  // // Register trainerDashboard routes
  // console.log("👨‍🏫 Loading trainerDashboard routes...");
  // try {
  //   trainerDashboardRoutes(app);
  //   console.log("✅ TrainerDashboard routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading trainerDashboard routes:", error);
  // }
  // // Register transaction routes
  // console.log("👨‍🏫 Loading transaction routes...");
  // try {
  //   transactionRoutes(app);
  //   console.log("✅ Transaction routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading transaction routes:", error);
  // }
  // // Register athlete routes
  // console.log("👨‍🏫 Loading athlete routes...");
  // try {
  //   athleteRoutes(app);
  //   console.log("✅ Athlete routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading athlete routes:", error);
  // }
  // // Register refund routes
  // console.log("👨‍🏫 Loading refund routes...");
  // try {
  //   refundRoutes(app);
  //   console.log("✅ Refund routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading refund routes:", error);
  // }
  // // Register athlete progress routes
  // console.log("👨‍🏫 Loading athlete progress routes...");
  // try {
  //   athleteProgressRoutes(app);
  //   console.log("✅ AthleteProgress routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading athlete progress routes:", error);
  // }
  // // Register admin  routes
  // console.log("👨‍🏫 Loading super admin  routes...");
  // try {
  //   superAdminRoutes(app);
  //   console.log("✅ Super admin  routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading super admin  routes:", error);
  // }
  // console.log("👨‍🏫 Loading stripe routes...");
  // try {
  //   // Register stripe routes
  //   stripeRoutes(app);
  //   console.log("✅ Stripe routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading stripe routes:", error);
  // }
  // try {
  //   // Register developer strict routes
  //   developerRoutes(app);
  //   console.log("✅ Trainer private routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading trainer private routes:", error);
  // }
  // try {
  //   // Register forgot password routes
  //   forgotPasswordRoutes(app);
  //   console.log("✅ Forgot password routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading forgot password routes:", error);
  // }
  // try {
  //   // Register reset password routes
  //   resetPasswordRoutes(app);
  //   console.log("✅ Reset password routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading reset password routes:", error);
  // }
  // try {
  //   // Register verify otp routes
  //   verifyOtp(app);
  //   console.log("✅ Verify otp routes loaded successfully");
  // } catch (error) {
  //   console.error("❌ Error loading verify otp routes:", error);
  // }
};
