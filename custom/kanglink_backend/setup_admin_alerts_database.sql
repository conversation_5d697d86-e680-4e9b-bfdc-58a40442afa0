-- =====================================================
-- Admin Alerts Database Setup Script
-- Date: 2025-01-15
-- Description: Complete setup for admin alerts functionality
-- =====================================================

-- Step 1: Update activity table for admin alerts
-- =====================================================

-- Add new activity types for admin alerts
ALTER TABLE kanglink_activity 
MODIFY COLUMN activity_type ENUM(
  'workout_started',
  'workout_completed', 
  'day_completed',
  'week_completed',
  'program_completed',
  'new_enrollment',
  'payment_made',
  'program_created',
  'program_updated',
  'session_scheduled',
  'milestone_reached',
  'refund_requested',
  'subscription_cancelled',
  'profile_updated',
  -- Admin alert activity types
  'program_approval_pending',
  'new_athlete_signup',
  'new_trainer_signup',
  'new_transaction',
  'refund_approved',
  'refund_rejected',
  'low_rated_trainer',
  'system_alert'
) NOT NULL;

-- Update visibility enum to include admin_only
ALTER TABLE kanglink_activity 
MODIFY COLUMN visibility ENUM('public', 'private', 'trainer_only', 'admin_only') NOT NULL DEFAULT 'private';

-- Add indexes for admin alerts queries
CREATE INDEX IF NOT EXISTS idx_admin_alerts ON kanglink_activity (visibility, user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_type_visibility ON kanglink_activity (activity_type, visibility);
CREATE INDEX IF NOT EXISTS idx_activity_created_date ON kanglink_activity (created_at DESC, visibility, user_id);

-- Step 2: Ensure notification table exists
-- =====================================================

-- Create notification table if it doesn't exist
CREATE TABLE IF NOT EXISTS kanglink_notification (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  sender_id INT NULL,
  related_id INT NULL,
  related_type ENUM('enrollment', 'program', 'split', 'exercise', 'payment', 'user', 'general') NULL,
  notification_type ENUM(
    'exercise_completed', 
    'day_completed', 
    'week_completed', 
    'program_completed', 
    'milestone_reached',
    'new_enrollment',
    'payment_received',
    'program_updated',
    'athlete_message',
    'system_alert'
  ) NOT NULL,
  category ENUM('progress', 'enrollment', 'payment', 'communication', 'system', 'general') NOT NULL DEFAULT 'general',
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSON NULL,
  is_read BOOLEAN NOT NULL DEFAULT FALSE,
  read_at DATETIME NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id),
  INDEX idx_sender_id (sender_id),
  INDEX idx_notification_type (notification_type),
  INDEX idx_category (category),
  INDEX idx_is_read (is_read),
  INDEX idx_created_at (created_at),
  INDEX idx_related (related_type, related_id)
);

-- Step 3: Insert sample data for testing
-- =====================================================

-- Sample Activities (Admin Alerts)
INSERT INTO kanglink_activity (
  user_id, 
  actor_id, 
  activity_type, 
  related_id, 
  related_type, 
  title, 
  description, 
  metadata, 
  visibility, 
  created_at
) VALUES
-- Program approval pending alerts
(1, 5, 'program_approval_pending', 101, 'program', 'Program Approval Pending', 'Program "Advanced Fitness Training" by John Trainer is pending approval', 
 '{"program_id": 101, "program_name": "Advanced Fitness Training", "trainer_id": 5, "trainer_name": "John Trainer", "trainer_email": "<EMAIL>", "created_at": "2025-01-15T10:30:00Z"}', 
 'admin_only', '2025-01-15 10:30:00'),

(1, 6, 'program_approval_pending', 102, 'program', 'Program Approval Pending', 'Program "Yoga for Beginners" by Sarah Wilson is pending approval', 
 '{"program_id": 102, "program_name": "Yoga for Beginners", "trainer_id": 6, "trainer_name": "Sarah Wilson", "trainer_email": "<EMAIL>", "created_at": "2025-01-15T11:15:00Z"}', 
 'admin_only', '2025-01-15 11:15:00'),

-- New athlete signup alerts
(1, 7, 'new_athlete_signup', 7, 'user', 'New Athlete Signup', 'New athlete Mike Johnson (<EMAIL>) has signed up', 
 '{"athlete_id": 7, "athlete_name": "Mike Johnson", "athlete_email": "<EMAIL>", "signup_date": "2025-01-15T09:45:00Z", "level": "beginner", "fitness_goals": "weight_loss"}', 
 'admin_only', '2025-01-15 09:45:00'),

(1, 8, 'new_athlete_signup', 8, 'user', 'New Athlete Signup', 'New athlete Lisa Chen (<EMAIL>) has signed up', 
 '{"athlete_id": 8, "athlete_name": "Lisa Chen", "athlete_email": "<EMAIL>", "signup_date": "2025-01-15T14:20:00Z", "level": "intermediate", "fitness_goals": "muscle_gain"}', 
 'admin_only', '2025-01-15 14:20:00'),

-- New trainer signup alerts
(1, 9, 'new_trainer_signup', 9, 'user', 'New Trainer Signup', 'New trainer David Brown (<EMAIL>) has signed up', 
 '{"trainer_id": 9, "trainer_name": "David Brown", "trainer_email": "<EMAIL>", "signup_date": "2025-01-15T12:10:00Z", "years_of_experience": 5, "qualifications": "NASM-CPT", "specializations": "strength_training"}', 
 'admin_only', '2025-01-15 12:10:00'),

-- New transaction alerts
(1, 7, 'new_transaction', 201, 'enrollment', 'New Transaction', 'New transaction of USD 99.99 for Advanced Fitness Training', 
 '{"transaction_id": 201, "athlete_id": 7, "athlete_name": "Mike Johnson", "trainer_id": 5, "trainer_name": "John Trainer", "program_id": 101, "program_name": "Advanced Fitness Training", "amount": 99.99, "currency": "USD", "payment_type": "credit_card", "payment_status": "completed"}', 
 'admin_only', '2025-01-15 16:30:00'),

(1, 8, 'new_transaction', 202, 'enrollment', 'New Transaction', 'New transaction of USD 79.99 for Yoga for Beginners', 
 '{"transaction_id": 202, "athlete_id": 8, "athlete_name": "Lisa Chen", "trainer_id": 6, "trainer_name": "Sarah Wilson", "program_id": 102, "program_name": "Yoga for Beginners", "amount": 79.99, "currency": "USD", "payment_type": "paypal", "payment_status": "completed"}', 
 'admin_only', '2025-01-15 17:45:00'),

-- Refund request alerts
(1, 7, 'refund_requested', 301, 'refund_request', 'Refund Requested', 'Refund request of USD 99.99 for Advanced Fitness Training', 
 '{"refund_id": 301, "athlete_id": 7, "athlete_name": "Mike Johnson", "trainer_id": 5, "trainer_name": "John Trainer", "program_id": 101, "program_name": "Advanced Fitness Training", "amount": 99.99, "currency": "USD", "reason": "schedule_conflict", "status": "pending"}', 
 'admin_only', '2025-01-15 18:20:00'),

-- Refund decision alerts
(1, 7, 'refund_approved', 301, 'refund_request', 'Refund Approved', 'Refund approved for USD 99.99 for Advanced Fitness Training', 
 '{"refund_id": 301, "athlete_id": 7, "athlete_name": "Mike Johnson", "trainer_id": 5, "trainer_name": "John Trainer", "program_id": 101, "program_name": "Advanced Fitness Training", "amount": 99.99, "currency": "USD", "decision": "approve", "admin_notes": "Valid reason provided"}', 
 'admin_only', '2025-01-15 19:00:00'),

-- Low rated trainer alerts
(1, 10, 'low_rated_trainer', 10, 'user', 'Low Rated Trainer', 'Trainer Alex Smith has a low rating of 2.1/5', 
 '{"trainer_id": 10, "trainer_name": "Alex Smith", "trainer_email": "<EMAIL>", "rating": 2.1, "review_count": 8, "program_count": 3}', 
 'admin_only', '2025-01-15 20:15:00'),

-- System alerts
(1, 1, 'system_alert', NULL, 'system', 'System Maintenance', 'Scheduled maintenance will occur on Sunday at 2 AM', 
 '{"priority": "medium", "category": "maintenance", "scheduled_date": "2025-01-19T02:00:00Z"}', 
 'admin_only', '2025-01-15 21:00:00'),

(1, 1, 'system_alert', NULL, 'system', 'New Feature Available', 'The new analytics dashboard is now available for all trainers', 
 '{"priority": "high", "category": "feature_update"}', 
 'admin_only', '2025-01-15 22:30:00');

-- Sample Notifications (User Notifications)
INSERT INTO kanglink_notification (
  user_id,
  sender_id,
  related_id,
  related_type,
  notification_type,
  category,
  title,
  message,
  data,
  is_read,
  created_at
) VALUES
-- Progress notifications for athletes
(7, NULL, 101, 'program', 'exercise_completed', 'progress', 'Exercise Completed', 'Great job! You completed the "Push-ups" exercise', 
 '{"exercise_name": "Push-ups", "sets": 3, "reps": 10, "program_name": "Advanced Fitness Training"}', 
 FALSE, '2025-01-15 10:30:00'),

(7, NULL, 101, 'program', 'day_completed', 'progress', 'Day Completed', 'Congratulations! You completed Day 1 of your program', 
 '{"day_number": 1, "program_name": "Advanced Fitness Training", "exercises_completed": 5}', 
 FALSE, '2025-01-15 11:45:00'),

(8, NULL, 102, 'program', 'week_completed', 'progress', 'Week Completed', 'Amazing progress! You completed Week 2 of your program', 
 '{"week_number": 2, "program_name": "Yoga for Beginners", "days_completed": 7}', 
 FALSE, '2025-01-15 14:20:00'),

-- Enrollment notifications
(7, 5, 201, 'enrollment', 'new_enrollment', 'enrollment', 'Welcome to Advanced Fitness Training', 'You have been enrolled in "Advanced Fitness Training" by John Trainer', 
 '{"program_name": "Advanced Fitness Training", "trainer_name": "John Trainer", "start_date": "2025-01-15"}', 
 FALSE, '2025-01-15 16:30:00'),

(8, 6, 202, 'enrollment', 'new_enrollment', 'enrollment', 'Welcome to Yoga for Beginners', 'You have been enrolled in "Yoga for Beginners" by Sarah Wilson', 
 '{"program_name": "Yoga for Beginners", "trainer_name": "Sarah Wilson", "start_date": "2025-01-15"}', 
 FALSE, '2025-01-15 17:45:00'),

-- Payment notifications
(7, NULL, 201, 'payment', 'payment_received', 'payment', 'Payment Confirmed', 'Your payment of $99.99 for Advanced Fitness Training has been confirmed', 
 '{"amount": 99.99, "currency": "USD", "program_name": "Advanced Fitness Training"}', 
 TRUE, '2025-01-15 16:35:00'),

(8, NULL, 202, 'payment', 'payment_received', 'payment', 'Payment Confirmed', 'Your payment of $79.99 for Yoga for Beginners has been confirmed', 
 '{"amount": 79.99, "currency": "USD", "program_name": "Yoga for Beginners"}', 
 TRUE, '2025-01-15 17:50:00'),

-- Program update notifications
(7, 5, 101, 'program', 'program_updated', 'general', 'Program Updated', 'Your program "Advanced Fitness Training" has been updated with new exercises', 
 '{"program_name": "Advanced Fitness Training", "updates": ["Added new cardio section", "Updated warm-up routine"]}', 
 FALSE, '2025-01-15 18:00:00'),

-- Communication notifications
(7, 5, NULL, 'general', 'athlete_message', 'communication', 'Message from John Trainer', 'You have a new message from your trainer John', 
 '{"trainer_name": "John Trainer", "message_preview": "Great progress this week! Keep up the good work."}', 
 FALSE, '2025-01-15 19:15:00'),

(8, 6, NULL, 'general', 'athlete_message', 'communication', 'Message from Sarah Wilson', 'You have a new message from your trainer Sarah', 
 '{"trainer_name": "Sarah Wilson", "message_preview": "Don\'t forget to practice your breathing exercises."}', 
 FALSE, '2025-01-15 20:30:00'),

-- System notifications
(7, NULL, NULL, 'general', 'system_alert', 'system', 'System Maintenance', 'Scheduled maintenance will occur on Sunday at 2 AM. Some features may be temporarily unavailable.', 
 '{"maintenance_time": "2025-01-19T02:00:00Z", "duration": "2 hours"}', 
 FALSE, '2025-01-15 21:00:00'),

(8, NULL, NULL, 'general', 'system_alert', 'system', 'New Feature Available', 'The new progress tracking feature is now available! Track your fitness journey with detailed analytics.', 
 '{"feature_name": "Progress Tracking", "description": "Track your fitness journey with detailed analytics"}', 
 FALSE, '2025-01-15 22:30:00'),

-- Trainer notifications
(5, 7, 201, 'enrollment', 'new_enrollment', 'enrollment', 'New Athlete Enrolled', 'Mike Johnson has enrolled in your "Advanced Fitness Training" program', 
 '{"athlete_name": "Mike Johnson", "program_name": "Advanced Fitness Training", "enrollment_date": "2025-01-15"}', 
 FALSE, '2025-01-15 16:30:00'),

(6, 8, 202, 'enrollment', 'new_enrollment', 'enrollment', 'New Athlete Enrolled', 'Lisa Chen has enrolled in your "Yoga for Beginners" program', 
 '{"athlete_name": "Lisa Chen", "program_name": "Yoga for Beginners", "enrollment_date": "2025-01-15"}', 
 FALSE, '2025-01-15 17:45:00'),

-- Milestone notifications
(7, NULL, 101, 'program', 'milestone_reached', 'progress', 'Milestone Reached!', 'Congratulations! You\'ve completed 10 workouts in your program', 
 '{"milestone": "10 workouts completed", "program_name": "Advanced Fitness Training", "achievement_date": "2025-01-15"}', 
 FALSE, '2025-01-15 15:00:00'),

(8, NULL, 102, 'program', 'milestone_reached', 'progress', 'Milestone Reached!', 'Congratulations! You\'ve completed 5 weeks of yoga practice', 
 '{"milestone": "5 weeks completed", "program_name": "Yoga for Beginners", "achievement_date": "2025-01-15"}', 
 FALSE, '2025-01-15 16:45:00');

-- Step 4: Verification queries
-- =====================================================

-- Verify admin alerts data
SELECT 
  'Admin Alerts Count' as metric,
  COUNT(*) as count
FROM kanglink_activity 
WHERE visibility = 'admin_only';

-- Verify notification data
SELECT 
  'User Notifications Count' as metric,
  COUNT(*) as count
FROM kanglink_notification;

-- Show activity types distribution
SELECT 
  activity_type,
  COUNT(*) as count
FROM kanglink_activity 
WHERE visibility = 'admin_only'
GROUP BY activity_type
ORDER BY count DESC;

-- Show notification categories distribution
SELECT 
  category,
  COUNT(*) as count
FROM kanglink_notification
GROUP BY category
ORDER BY count DESC;

-- Show recent alerts (last 24 hours)
SELECT 
  title,
  activity_type,
  created_at
FROM kanglink_activity 
WHERE visibility = 'admin_only' 
  AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY created_at DESC;

-- Show unread notifications
SELECT 
  title,
  category,
  created_at
FROM kanglink_notification
WHERE is_read = FALSE
ORDER BY created_at DESC;

-- =====================================================
-- Setup Complete!
-- =====================================================
-- 
-- The database now includes:
-- 1. Updated activity table with admin alert activity types
-- 2. Notification table for user notifications
-- 3. Sample data for testing admin alerts functionality
-- 4. Proper indexes for optimal query performance
-- 
-- You can now test the admin alerts API endpoints:
-- - GET /v2/api/kanglink/custom/admin/alerts
-- - GET /v2/api/kanglink/custom/admin/alerts/stats
-- - POST /v2/api/kanglink/custom/admin/alerts/system
-- ===================================================== 