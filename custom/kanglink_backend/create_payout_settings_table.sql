-- Create payout settings table for kanglink project
CREATE TABLE
  IF NOT EXISTS kanglink_payout_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trainer_payout_time_hours INT NOT NULL DEFAULT 24 COMMENT 'Hours to wait before processing trainer payouts',
    -- Split percentages for regular enrollments (non-affiliate)
    split_company_percentage DECIMAL(5, 2) NOT NULL DEFAULT 30.00 COMMENT 'Company percentage for regular split enrollments (0-100)',
    split_trainer_percentage DECIMAL(5, 2) NOT NULL DEFAULT 70.00 COMMENT 'Trainer percentage for regular split enrollments (0-100)',
    -- Split percentages for affiliate enrollments (higher trainer commission)
    affiliate_company_percentage DECIMAL(5, 2) NOT NULL DEFAULT 20.00 COMMENT 'Company percentage for affiliate split enrollments (0-100)',
    affiliate_trainer_percentage DECIMAL(5, 2) NOT NULL DEFAULT 80.00 COMMENT 'Trainer percentage for affiliate split enrollments (0-100)',
    -- Status and metadata
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether these settings are currently active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    -- Constraints to ensure percentages add up correctly
    CONSTRAINT chk_split_percentages CHECK (
      split_company_percentage + split_trainer_percentage = 100.00
    ),
    CONSTRAINT chk_affiliate_percentages CHECK (
      affiliate_company_percentage + affiliate_trainer_percentage = 100.00
    ),
    CONSTRAINT chk_percentage_ranges CHECK (
      split_company_percentage >= 0
      AND split_company_percentage <= 100
      AND split_trainer_percentage >= 0
      AND split_trainer_percentage <= 100
      AND affiliate_company_percentage >= 0
      AND affiliate_company_percentage <= 100
      AND affiliate_trainer_percentage >= 0
      AND affiliate_trainer_percentage <= 100
    )
  );

-- Create commission tracking table
CREATE TABLE
  IF NOT EXISTS kanglink_commission (
    id INT AUTO_INCREMENT PRIMARY KEY,
    enrollment_id INT NOT NULL COMMENT 'Reference to enrollment table',
    program_id INT NOT NULL COMMENT 'Reference to program table',
    split_id INT NOT NULL COMMENT 'Reference to split table',
    trainer_id INT NOT NULL COMMENT 'Reference to trainer user',
    athlete_id INT NOT NULL COMMENT 'Reference to athlete user',
    -- Commission type and amounts
    commission_type ENUM ('regular', 'affiliate') NOT NULL COMMENT 'Type of commission calculation',
    total_amount DECIMAL(10, 2) NOT NULL COMMENT 'Total enrollment amount after discount (amount used for commission)',
    original_amount DECIMAL(10, 2) DEFAULT NULL COMMENT 'Original amount before discount',
    discount_amount DECIMAL(10, 2) DEFAULT 0.00 COMMENT 'Discount amount applied',
    -- Commission breakdown
    company_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT 'Amount for company',
    trainer_amount DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT 'Amount for trainer (higher if affiliate)',
    -- Affiliate information (if applicable)
    affiliate_code VARCHAR(255) DEFAULT NULL COMMENT 'Affiliate code used for enrollment',
    affiliate_user_id INT DEFAULT NULL COMMENT 'User ID of referring trainer (same as trainer_id for affiliate enrollments)',
    -- Payout tracking
    payout_status ENUM ('pending', 'processed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
    payout_scheduled_at TIMESTAMP NULL COMMENT 'When payout is scheduled to be processed',
    payout_processed_at TIMESTAMP NULL COMMENT 'When payout was actually processed',
    -- Metadata
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    -- Indexes for performance
    INDEX idx_enrollment_id (enrollment_id),
    INDEX idx_trainer_id (trainer_id),
    INDEX idx_affiliate_user_id (affiliate_user_id),
    INDEX idx_payout_status (payout_status),
    INDEX idx_payout_scheduled (payout_scheduled_at),
    -- Foreign key constraints (if needed)
    CONSTRAINT fk_commission_enrollment FOREIGN KEY (enrollment_id) REFERENCES kanglink_enrollment (id) ON DELETE CASCADE,
    CONSTRAINT fk_commission_program FOREIGN KEY (program_id) REFERENCES kanglink_program (id) ON DELETE CASCADE,
    CONSTRAINT fk_commission_split FOREIGN KEY (split_id) REFERENCES kanglink_split (id) ON DELETE CASCADE
  );

-- Insert default payout settings
INSERT INTO
  kanglink_payout_settings (
    trainer_payout_time_hours,
    split_company_percentage,
    split_trainer_percentage,
    affiliate_company_percentage,
    affiliate_trainer_percentage,
    is_active
  )
VALUES
  (
    24, -- 24 hours default payout time
    30.00, -- 30% company for regular splits
    70.00, -- 70% trainer for regular splits
    20.00, -- 20% company for affiliate splits
    80.00, -- 80% trainer for affiliate splits (higher commission)
    TRUE
  );