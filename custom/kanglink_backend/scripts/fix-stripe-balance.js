#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix Stripe platform balance for testing withdrawals
 *
 * This script adds test funds to the Stripe platform account so that
 * trainer withdrawals can be processed successfully in test mode.
 *
 * Usage:
 * node fix-stripe-balance.js [amount] [currency]
 *
 * Examples:
 * node fix-stripe-balance.js 1000 USD
 * node fix-stripe-balance.js 500 CAD
 */

const StripeService = require("../../../baas/services/StripeService");

async function addTestFunds(amount = 1000, currency = "USD") {
  try {
    console.log(
      `🔄 Adding ${amount} ${currency} to Stripe platform balance...`
    );

    const stripe = new StripeService();

    // Check current balance
    console.log("📊 Checking current platform balance...");
    const currentBalance = await stripe.getPlatformBalance();
    const currentAvailable = currentBalance.available.find(
      (balance) => balance.currency === currency.toLowerCase()
    );

    console.log(
      `💰 Current available balance: ${
        currentAvailable ? currentAvailable.amount / 100 : 0
      } ${currency}`
    );

    // Create test charge to add funds
    console.log("💳 Creating test charge...");
    const charge = await stripe.createTestCharge({
      amount,
      currency,
      description: `Test charge to add ${amount} ${currency} to platform balance for trainer withdrawals`,
    });

    console.log(`✅ Test charge created: ${charge.id}`);

    // Check updated balance
    console.log("📊 Checking updated platform balance...");
    const updatedBalance = await stripe.getPlatformBalance();
    const updatedAvailable = updatedBalance.available.find(
      (balance) => balance.currency === currency.toLowerCase()
    );

    console.log(
      `💰 Updated available balance: ${
        updatedAvailable ? updatedAvailable.amount / 100 : 0
      } ${currency}`
    );

    console.log("\n🎉 Success! Platform balance has been updated.");
    console.log("🔄 You can now try the trainer withdrawal again.");

    return {
      success: true,
      charge_id: charge.id,
      amount_added: amount,
      currency: currency,
      previous_balance: currentAvailable ? currentAvailable.amount / 100 : 0,
      new_balance: updatedAvailable ? updatedAvailable.amount / 100 : 0,
    };
  } catch (error) {
    console.error("❌ Error adding test funds:", error.message);

    if (error.message.includes("insufficient_funds")) {
      console.log(
        "\n💡 Tip: In Stripe test mode, you need to use specific test cards to add funds."
      );
      console.log(
        "   The script uses 'tok_bypassPending' which should work for most cases."
      );
    }

    if (error.message.includes("Invalid source")) {
      console.log(
        "\n💡 Alternative: Use the Stripe Dashboard to create test charges manually:"
      );
      console.log("   1. Go to Stripe Dashboard > Payments > Create payment");
      console.log("   2. Use test card: ****************");
      console.log(
        "   3. Create charges to add funds to your available balance"
      );
    }

    throw error;
  }
}

async function checkBalance() {
  try {
    console.log("📊 Checking current platform balance...");

    const stripe = new StripeService();
    const balance = await stripe.getPlatformBalance();

    console.log("\n💰 Platform Balance:");
    console.log("Available:");
    if (balance.available.length === 0) {
      console.log("  No available funds");
    } else {
      balance.available.forEach((bal) => {
        console.log(`  ${bal.amount / 100} ${bal.currency.toUpperCase()}`);
      });
    }

    console.log("Pending:");
    if (balance.pending.length === 0) {
      console.log("  No pending funds");
    } else {
      balance.pending.forEach((bal) => {
        console.log(`  ${bal.amount / 100} ${bal.currency.toUpperCase()}`);
      });
    }

    // Also check recent charges to see if payments went through
    console.log("\n💳 Recent Charges (last 10):");
    try {
      const charges = await stripe.stripe.charges.list({ limit: 10 });
      if (charges.data.length === 0) {
        console.log("  No recent charges found");
      } else {
        charges.data.forEach((charge) => {
          const status = charge.paid ? "✅ Paid" : "❌ Failed";
          const amount = charge.amount / 100;
          const currency = charge.currency.toUpperCase();
          const date = new Date(charge.created * 1000).toISOString();
          console.log(
            `  ${status} ${amount} ${currency} - ${date} - ${charge.id}`
          );
        });
      }
    } catch (chargeError) {
      console.log("  Error fetching charges:", chargeError.message);
    }

    return balance;
  } catch (error) {
    console.error("❌ Error checking balance:", error.message);
    throw error;
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (command === "check" || command === "balance") {
    await checkBalance();
    return;
  }

  const amount = args[0] ? parseFloat(args[0]) : 1000;
  const currency = args[1] ? args[1].toUpperCase() : "USD";

  if (isNaN(amount) || amount <= 0) {
    console.error("❌ Invalid amount. Please provide a positive number.");
    process.exit(1);
  }

  console.log("🚀 Stripe Platform Balance Fixer");
  console.log("================================");
  console.log(`Amount: ${amount} ${currency}`);
  console.log("Purpose: Enable trainer withdrawals in test mode\n");

  await addTestFunds(amount, currency);
}

// Run the script if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error("\n💥 Script failed:", error.message);
    process.exit(1);
  });
}

module.exports = { addTestFunds, checkBalance };
