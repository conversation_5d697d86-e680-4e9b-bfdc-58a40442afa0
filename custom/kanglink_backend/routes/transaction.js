const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const StripeService = require("../../../baas/services/StripeService");
const CommissionProcessingService = require("../services/CommissionProcessingService");

module.exports = function (app) {
  // Get trainer transaction stats (total earnings, pending payouts, available to withdraw, withdrawn)
  app.get(
    "/v2/api/kanglink/custom/trainer/transactions/stats",
    TokenMiddleware({ role: "trainer" }),
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const trainerId = req.user_id;

        // Get active payout settings
        const payoutSettingsQuery = `
          SELECT trainer_payout_time_hours
          FROM kanglink_payout_settings
          WHERE is_active = true
          ORDER BY created_at DESC
          LIMIT 1
        `;
        const payoutSettings = await sdk.rawQuery(payoutSettingsQuery);
        const payoutTimeHours =
          payoutSettings[0]?.trainer_payout_time_hours || 24;

        // Calculate stats from commission table
        const statsQuery = `
          SELECT
            -- Total earnings (all processed commissions)
            COALESCE(SUM(CASE WHEN payout_status = 'processed' THEN trainer_amount ELSE 0 END), 0) as total_earnings,

            -- Pending payouts (commissions that haven't reached payout time yet)
            COALESCE(SUM(CASE
              WHEN payout_status = 'pending'
              AND payout_scheduled_at > NOW()
              THEN trainer_amount
              ELSE 0
            END), 0) as pending_payouts,

            -- Available to withdraw (commissions ready for payout but not yet processed)
            COALESCE(SUM(CASE
              WHEN payout_status = 'pending'
              AND payout_scheduled_at <= NOW()
              THEN trainer_amount
              ELSE 0
            END), 0) as available_to_withdraw,

            -- Withdrawn (already processed payouts)
            COALESCE(SUM(CASE WHEN payout_status = 'processed' THEN trainer_amount ELSE 0 END), 0) as withdrawn,

            -- Currency (assuming all commissions use same currency)
            COALESCE(MAX(currency), 'USD') as currency
          FROM kanglink_commission
          WHERE trainer_id = ?
        `;

        const statsResult = await sdk.rawQuery(statsQuery, [trainerId]);
        const stats = statsResult[0] || {
          total_earnings: 0,
          pending_payouts: 0,
          available_to_withdraw: 0,
          withdrawn: 0,
          currency: "USD",
        };

        // Format response
        const response = {
          success: true,
          data: {
            total_earnings: parseFloat(stats.total_earnings) || 0,
            pending_payouts: parseFloat(stats.pending_payouts) || 0,
            available_to_withdraw: parseFloat(stats.available_to_withdraw) || 0,
            withdrawn: parseFloat(stats.withdrawn) || 0,
            currency: stats.currency,
            payout_time_hours: payoutTimeHours,
          },
        };

        res.json(response);
      } catch (error) {
        console.error("Error fetching trainer transaction stats:", error);
        res.status(500).json({
          error: true,
          message: "Failed to fetch transaction stats",
          details: error.message,
        });
      }
    }
  );

  // Get trainer earnings graph data (monthly earnings for past 12 months)
  app.get(
    "/v2/api/kanglink/custom/trainer/transactions/earnings-graph",
    TokenMiddleware({ role: "trainer" }),
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const trainerId = req.user_id;

        // Get monthly earnings for the past 12 months
        const earningsQuery = `
          SELECT
            DATE_FORMAT(created_at, '%Y-%m') as month,
            MONTHNAME(created_at) as month_name,
            YEAR(created_at) as year,
            MONTH(created_at) as month_number,
            COALESCE(SUM(trainer_amount), 0) as earnings
          FROM kanglink_commission
          WHERE trainer_id = ?
            AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            AND payout_status IN ('pending', 'processed')
          GROUP BY DATE_FORMAT(created_at, '%Y-%m'), MONTHNAME(created_at), YEAR(created_at), MONTH(created_at)
          ORDER BY year ASC, month_number ASC
        `;

        const earningsResult = await sdk.rawQuery(earningsQuery, [trainerId]);

        // Create array for past 12 months with zero values for months with no data
        const monthsData = [];
        const currentDate = new Date();

        for (let i = 11; i >= 0; i--) {
          const date = new Date(
            currentDate.getFullYear(),
            currentDate.getMonth() - i,
            1
          );
          const monthKey = `${date.getFullYear()}-${String(
            date.getMonth() + 1
          ).padStart(2, "0")}`;
          const monthName = date.toLocaleString("default", { month: "short" });

          const existingData = earningsResult.find(
            (row) => row.month === monthKey
          );

          monthsData.push({
            month: monthKey,
            month_name: monthName,
            year: date.getFullYear(),
            month_number: date.getMonth() + 1,
            earnings: existingData ? parseFloat(existingData.earnings) : 0,
          });
        }

        const response = {
          success: true,
          data: {
            earnings_by_month: monthsData,
            total_months: 12,
            currency: "USD",
          },
        };

        res.json(response);
      } catch (error) {
        console.error("Error fetching trainer earnings graph:", error);
        res.status(500).json({
          error: true,
          message: "Failed to fetch earnings graph data",
          details: error.message,
        });
      }
    }
  );

  // Request withdrawal of available funds
  app.post(
    "/v2/api/kanglink/custom/trainer/transactions/withdraw",
    TokenMiddleware({ role: "trainer" }),
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const trainerId = req.user_id;
        const { amount, currency = "USD" } = req.body;

        // Validate amount
        if (!amount || amount <= 0) {
          return res.status(400).json({
            error: true,
            message: "Valid withdrawal amount is required",
          });
        }

        // Get trainer's available funds
        const availableFundsQuery = `
          SELECT
            COALESCE(SUM(CASE
              WHEN payout_status = 'pending'
              AND payout_scheduled_at <= NOW()
              THEN trainer_amount
              ELSE 0
            END), 0) as available_amount
          FROM kanglink_commission
          WHERE trainer_id = ?
        `;

        const availableFundsResult = await sdk.rawQuery(availableFundsQuery, [
          trainerId,
        ]);
        const availableAmount =
          parseFloat(availableFundsResult[0]?.available_amount) || 0;

        // Check if requested amount is available
        if (amount > availableAmount) {
          return res.status(400).json({
            error: true,
            message: `Insufficient funds. Available: ${availableAmount} ${currency}`,
          });
        }

        // Get trainer details for Stripe Connect
        const trainerQuery = `
          SELECT u.*
          FROM kanglink_user u
          WHERE u.id = ?
        `;
        const trainerResult = await sdk.rawQuery(trainerQuery, [trainerId]);
        const trainer = trainerResult[0];

        if (!trainer) {
          return res.status(404).json({
            error: true,
            message: "Trainer not found",
          });
        }

        // Check if trainer has Stripe Connect account
        let stripeConnectAccountId = null;
        try {
          const userData = trainer.data ? JSON.parse(trainer.data) : {};
          stripeConnectAccountId = userData.stripe_connect_account_id;
        } catch (e) {
          console.log("Error parsing user data:", e);
        }

        if (!stripeConnectAccountId) {
          return res.status(400).json({
            error: true,
            message:
              "Stripe Connect account not set up. Please complete your payment settings first.",
            action_required: "setup_stripe_connect",
          });
        }

        // Initialize Stripe service
        const stripe = new StripeService();

        // Check platform balance before attempting transfer
        try {
          const platformBalance = await stripe.getPlatformBalance();
          const availableBalance = platformBalance.available.find(
            (balance) => balance.currency === currency.toLowerCase()
          );
          const pendingBalance = platformBalance.pending.find(
            (balance) => balance.currency === currency.toLowerCase()
          );

          const availableAmount = availableBalance
            ? availableBalance.amount / 100
            : 0;
          const pendingAmount = pendingBalance
            ? pendingBalance.amount / 100
            : 0;

          if (!availableBalance || availableBalance.amount < amount * 100) {
            let errorMessage = `Platform has ${availableAmount} ${currency} available, but ${amount} ${currency} is needed.`;
            let actionRequired = "add_platform_funds";

            if (pendingAmount > 0) {
              errorMessage += ` However, you have ${pendingAmount} ${currency} in pending funds. In test mode, pending funds need to be manually moved to available through the Stripe Dashboard.`;
              actionRequired = "move_pending_to_available";
            } else {
              errorMessage += ` In test mode, create charges using test card **************** to add funds to platform balance.`;
            }

            return res.status(400).json({
              error: true,
              message: "Insufficient platform funds for withdrawal",
              details: errorMessage,
              platform_balance: {
                available: availableAmount,
                pending: pendingAmount,
                currency: currency,
                required: amount,
              },
              action_required: actionRequired,
              instructions:
                pendingAmount > 0
                  ? "Go to Stripe Dashboard → Balance → Move pending funds to available"
                  : "Create test charges or use admin endpoint to add funds",
            });
          }
        } catch (balanceError) {
          console.error("Error checking platform balance:", balanceError);
          // Continue with transfer attempt - let Stripe handle the error
        }

        // Create Stripe transfer to trainer's Connect account
        try {
          const transfer = await stripe.stripe.transfers.create({
            amount: Math.round(amount * 100), // Convert to cents
            currency: currency.toLowerCase(),
            destination: stripeConnectAccountId,
            description: `Trainer earnings withdrawal - ${trainer.email}`,
            metadata: {
              trainer_id: trainerId.toString(),
              withdrawal_amount: amount.toString(),
              project_id: "kanglink",
            },
          });

          // Update commission records to mark as processed
          const updateCommissionsQuery = `
            UPDATE kanglink_commission
            SET
              payout_status = 'processed',
              payout_processed_at = NOW(),
              updated_at = NOW()
            WHERE trainer_id = ?
              AND payout_status = 'pending'
              AND payout_scheduled_at <= NOW()
              AND trainer_amount > 0
            ORDER BY created_at ASC
            LIMIT ?
          `;

          // Calculate how many commission records to update based on amount
          const commissionsToUpdateQuery = `
            SELECT id, trainer_amount
            FROM kanglink_commission
            WHERE trainer_id = ?
              AND payout_status = 'pending'
              AND payout_scheduled_at <= NOW()
              AND trainer_amount > 0
            ORDER BY created_at ASC
          `;

          const commissionsToUpdate = await sdk.rawQuery(
            commissionsToUpdateQuery,
            [trainerId]
          );
          let remainingAmount = amount;
          let commissionsUpdated = 0;

          for (const commission of commissionsToUpdate) {
            if (remainingAmount <= 0) break;

            if (commission.trainer_amount <= remainingAmount) {
              remainingAmount -= commission.trainer_amount;
              commissionsUpdated++;
            } else {
              // Partial withdrawal - need to split commission record
              break;
            }
          }

          await sdk.rawQuery(updateCommissionsQuery, [
            trainerId,
            commissionsUpdated,
          ]);

          const response = {
            success: true,
            data: {
              withdrawal_id: transfer.id,
              amount: amount,
              currency: currency,
              status: "processed",
              transfer_details: {
                stripe_transfer_id: transfer.id,
                destination_account: stripeConnectAccountId,
                created: transfer.created,
              },
              message: "Withdrawal processed successfully",
            },
          };

          res.json(response);
        } catch (stripeError) {
          console.error("Stripe transfer error:", stripeError);
          res.status(500).json({
            error: true,
            message: "Failed to process withdrawal through Stripe",
            details: stripeError.message,
          });
        }
      } catch (error) {
        console.error("Error processing withdrawal:", error);
        res.status(500).json({
          error: true,
          message: "Failed to process withdrawal",
          details: error.message,
        });
      }
    }
  );

  // Get trainer transaction history with pagination and filtering
  app.get(
    "/v2/api/kanglink/custom/trainer/transactions/history",
    TokenMiddleware({ role: "trainer" }),
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const trainerId = req.user_id;
        const {
          page = 1,
          limit = 20,
          type = "all", // 'all', 'earnings', 'withdrawals', 'refunds'
          start_date,
          end_date,
          status,
        } = req.query;

        const offset = (page - 1) * limit;

        // Build WHERE conditions
        let whereConditions = ["c.trainer_id = ?"];
        let queryParams = [trainerId];

        if (type !== "all") {
          switch (type) {
            case "earnings":
              whereConditions.push(
                "c.payout_status IN ('pending', 'processed')"
              );
              break;
            case "withdrawals":
              whereConditions.push("c.payout_status = 'processed'");
              break;
            case "refunds":
              whereConditions.push("e.status = 'refund'");
              break;
          }
        }

        if (status) {
          whereConditions.push("c.payout_status = ?");
          queryParams.push(status);
        }

        if (start_date) {
          whereConditions.push("c.created_at >= ?");
          queryParams.push(start_date);
        }

        if (end_date) {
          whereConditions.push("c.created_at <= ?");
          queryParams.push(end_date);
        }

        const whereClause = whereConditions.join(" AND ");

        // Get transaction history
        const historyQuery = `
          SELECT
            c.id as commission_id,
            c.enrollment_id,
            c.program_id,
            c.split_id,
            c.commission_type,
            c.total_amount,
            c.original_amount,
            c.discount_amount,
            c.trainer_amount,
            c.company_amount,
            c.payout_status,
            c.payout_scheduled_at,
            c.payout_processed_at,
            c.currency,
            c.created_at,
            c.updated_at,

            -- Enrollment details
            e.payment_type,
            e.status as enrollment_status,
            e.payment_status,
            e.enrollment_date,
            e.stripe_subscription_id,
            e.stripe_payment_intent_id,

            -- Program details
            p.title as program_title,
            p.description as program_description,

            -- Split details
            s.title as split_title,
            s.full_price as split_full_price,
            s.subscription_price as split_subscription_price,

            -- Athlete details
            athlete.email as athlete_email,
            athlete.data as athlete_data

          FROM kanglink_commission c
          LEFT JOIN kanglink_enrollment e ON c.enrollment_id = e.id
          LEFT JOIN kanglink_program p ON c.program_id = p.id
          LEFT JOIN kanglink_split s ON c.split_id = s.id
          LEFT JOIN kanglink_user athlete ON e.athlete_id = athlete.id
          WHERE ${whereClause}
          ORDER BY c.created_at DESC
          LIMIT ? OFFSET ?
        `;

        queryParams.push(parseInt(limit), parseInt(offset));

        const historyResult = await sdk.rawQuery(historyQuery, queryParams);

        // Get total count for pagination
        const countQuery = `
          SELECT COUNT(*) as total
          FROM kanglink_commission c
          LEFT JOIN kanglink_enrollment e ON c.enrollment_id = e.id
          WHERE ${whereClause}
        `;

        const countParams = queryParams.slice(0, -2); // Remove limit and offset
        const countResult = await sdk.rawQuery(countQuery, countParams);
        const totalRecords = countResult[0]?.total || 0;

        // Format the response
        const transactions = historyResult.map((row) => {
          let athleteData = {};
          try {
            athleteData = row.athlete_data ? JSON.parse(row.athlete_data) : {};
          } catch (e) {
            console.log("Error parsing athlete data:", e);
          }

          return {
            commission_id: row.commission_id,
            enrollment_id: row.enrollment_id,
            type: row.payout_status === "processed" ? "withdrawal" : "earning",
            commission_type: row.commission_type,
            amount: {
              total: parseFloat(row.total_amount) || 0,
              original: parseFloat(row.original_amount) || 0,
              discount: parseFloat(row.discount_amount) || 0,
              trainer_amount: parseFloat(row.trainer_amount) || 0,
              company_amount: parseFloat(row.company_amount) || 0,
              currency: row.currency,
            },
            status: row.payout_status,
            dates: {
              created: row.created_at,
              scheduled_payout: row.payout_scheduled_at,
              processed_payout: row.payout_processed_at,
              enrollment_date: row.enrollment_date,
            },
            program: {
              id: row.program_id,
              title: row.program_title,
              description: row.program_description,
            },
            split: {
              id: row.split_id,
              title: row.split_title,
              full_price: parseFloat(row.split_full_price) || 0,
              subscription_price: parseFloat(row.split_subscription_price) || 0,
            },
            athlete: {
              email: row.athlete_email,
              name:
                athleteData.full_name ||
                `${athleteData.first_name || ""} ${
                  athleteData.last_name || ""
                }`.trim() ||
                "Unknown",
            },
            enrollment: {
              payment_type: row.payment_type,
              status: row.enrollment_status,
              payment_status: row.payment_status,
              stripe_subscription_id: row.stripe_subscription_id,
              stripe_payment_intent_id: row.stripe_payment_intent_id,
            },
          };
        });

        const response = {
          success: true,
          data: {
            transactions,
            pagination: {
              current_page: parseInt(page),
              per_page: parseInt(limit),
              total_records: parseInt(totalRecords),
              total_pages: Math.ceil(totalRecords / limit),
              has_next: page * limit < totalRecords,
              has_prev: page > 1,
            },
            filters: {
              type,
              status,
              start_date,
              end_date,
            },
          },
        };

        res.json(response);
      } catch (error) {
        console.error("Error fetching transaction history:", error);
        res.status(500).json({
          error: true,
          message: "Failed to fetch transaction history",
          details: error.message,
        });
      }
    }
  );

  // Setup Stripe Connect account for trainer
  app.post(
    "/v2/api/kanglink/custom/trainer/transactions/setup-stripe-connect",
    TokenMiddleware({ role: "trainer" }),
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const trainerId = req.user_id;
        const {
          country = "CA", // Default to Canada to match platform region
          business_type = "individual",
          return_url,
          refresh_url,
        } = req.body;

        // Get trainer details
        const trainerQuery = `
          SELECT u.*
          FROM kanglink_user u
          WHERE u.id = ?
        `;
        const trainerResult = await sdk.rawQuery(trainerQuery, [trainerId]);
        const trainer = trainerResult[0];

        if (!trainer) {
          return res.status(404).json({
            error: true,
            message: "Trainer not found",
          });
        }

        // Check if trainer already has a Stripe Connect account
        let userData = {};
        try {
          userData = trainer.data ? JSON.parse(trainer.data) : {};
        } catch (e) {
          console.log("Error parsing user data:", e);
        }

        let stripeConnectAccountId = userData.stripe_connect_account_id;

        const stripe = new StripeService();

        // Create new Stripe Connect account if doesn't exist
        if (!stripeConnectAccountId) {
          const connectAccount = await stripe.createConnectAccount({
            type: "express",
            country,
            email: trainer.email,
            business_type,
            metadata: {
              trainer_id: trainerId.toString(),
              project_id: "kanglink",
            },
          });

          stripeConnectAccountId = connectAccount.id;

          // Save the account ID to trainer user data
          userData.stripe_connect_account_id = stripeConnectAccountId;

          // Update user data
          await sdk.rawQuery(
            "UPDATE kanglink_user SET data = ?, updated_at = NOW() WHERE id = ?",
            [JSON.stringify(userData), trainerId]
          );
        }

        // Create account link for onboarding
        const defaultReturnUrl = `${req.protocol}://${req.get(
          "host"
        )}/trainer/transactions?setup=complete`;
        const defaultRefreshUrl = `${req.protocol}://${req.get(
          "host"
        )}/trainer/transactions?setup=refresh`;

        const accountLink = await stripe.createConnectAccountLink({
          account_id: stripeConnectAccountId,
          return_url: return_url || defaultReturnUrl,
          refresh_url: refresh_url || defaultRefreshUrl,
          type: "account_onboarding",
        });

        // Check account status
        const accountStatus = await stripe.isAccountOnboarded(
          stripeConnectAccountId
        );

        const response = {
          success: true,
          data: {
            stripe_connect_account_id: stripeConnectAccountId,
            onboarding_url: accountLink.url,
            account_status: accountStatus,
            expires_at: accountLink.expires_at,
            message: accountStatus.onboarded
              ? "Stripe Connect account is already set up"
              : "Please complete the Stripe Connect onboarding process",
          },
        };

        res.json(response);
      } catch (error) {
        console.error("Error setting up Stripe Connect:", error);
        res.status(500).json({
          error: true,
          message: "Failed to setup Stripe Connect account",
          details: error.message,
        });
      }
    }
  );

  // Delete Stripe Connect account for trainer
  app.delete(
    "/v2/api/kanglink/custom/trainer/transactions/stripe-connect",
    TokenMiddleware({ role: "trainer" }),
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const trainerId = req.user_id;

        // Get trainer's user data (which contains Stripe Connect account ID)
        const trainerQuery = `
          SELECT u.data as user_data
          FROM kanglink_user u
          WHERE u.id = ?
        `;
        const trainerResult = await sdk.rawQuery(trainerQuery, [trainerId]);
        const trainer = trainerResult[0];

        if (!trainer || !trainer.user_data) {
          return res.json({
            success: true,
            message: "No Stripe Connect account found to delete",
          });
        }

        let userData = {};
        try {
          userData = JSON.parse(trainer.user_data);
        } catch (e) {
          console.log("Error parsing user data:", e);
        }

        const stripeConnectAccountId = userData.stripe_connect_account_id;

        if (!stripeConnectAccountId) {
          return res.json({
            success: true,
            message: "No Stripe Connect account found to delete",
          });
        }

        // Delete the Stripe Connect account
        const stripe = new StripeService();
        try {
          await stripe.deleteConnectAccount(stripeConnectAccountId);
        } catch (stripeError) {
          console.log(
            "Stripe account deletion error (may already be deleted):",
            stripeError.message
          );
        }

        // Remove the account ID from user data
        delete userData.stripe_connect_account_id;

        // Update user data
        await sdk.rawQuery(
          "UPDATE kanglink_user SET data = ?, updated_at = NOW() WHERE id = ?",
          [JSON.stringify(userData), trainerId]
        );

        res.json({
          success: true,
          message: "Stripe Connect account deleted successfully",
          data: {
            deleted_account_id: stripeConnectAccountId,
          },
        });
      } catch (error) {
        console.error("Error deleting Stripe Connect account:", error);
        res.status(500).json({
          error: true,
          message: "Failed to delete Stripe Connect account",
          details: error.message,
        });
      }
    }
  );

  // Get Stripe Connect account status
  app.get(
    "/v2/api/kanglink/custom/trainer/transactions/stripe-connect-status",
    TokenMiddleware({ role: "trainer" }),
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const trainerId = req.user_id;

        // Get trainer's user data (which contains Stripe Connect account ID)
        const trainerQuery = `
          SELECT u.data as user_data
          FROM kanglink_user u
          WHERE u.id = ?
        `;
        const trainerResult = await sdk.rawQuery(trainerQuery, [trainerId]);
        const trainer = trainerResult[0];

        if (!trainer || !trainer.user_data) {
          return res.json({
            success: true,
            data: {
              has_stripe_connect: false,
              onboarded: false,
              message: "Stripe Connect account not set up",
            },
          });
        }

        let userData = {};
        try {
          userData = JSON.parse(trainer.user_data);
        } catch (e) {
          console.log("Error parsing user data:", e);
        }

        const stripeConnectAccountId = userData.stripe_connect_account_id;

        if (!stripeConnectAccountId) {
          return res.json({
            success: true,
            data: {
              has_stripe_connect: false,
              onboarded: false,
              message: "Stripe Connect account not set up",
            },
          });
        }

        // Check account status with Stripe
        const stripe = new StripeService();
        const accountStatus = await stripe.isAccountOnboarded(
          stripeConnectAccountId
        );

        const response = {
          success: true,
          data: {
            has_stripe_connect: true,
            stripe_connect_account_id: stripeConnectAccountId,
            ...accountStatus,
            message: accountStatus.onboarded
              ? "Stripe Connect account is fully set up and ready for payouts"
              : "Stripe Connect account setup is incomplete",
          },
        };

        res.json(response);
      } catch (error) {
        console.error("Error checking Stripe Connect status:", error);
        res.status(500).json({
          error: true,
          message: "Failed to check Stripe Connect status",
          details: error.message,
        });
      }
    }
  );

  // Admin endpoint to manually process pending commissions
  app.post(
    "/v2/api/kanglink/custom/admin/transactions/process-commissions",
    TokenMiddleware({ role: "super_admin" }),
    async function (_req, res) {
      try {
        const sdk = app.get("sdk");
        const commissionService = new CommissionProcessingService(sdk);

        const result = await commissionService.processPendingCommissions();

        res.json({
          success: true,
          data: result,
          message: `Processed ${result.processed} commissions successfully`,
        });
      } catch (error) {
        console.error("Error processing commissions:", error);
        res.status(500).json({
          error: true,
          message: "Failed to process commissions",
          details: error.message,
        });
      }
    }
  );

  // Webhook endpoint for handling enrollment events
  app.post(
    "/v2/api/kanglink/custom/webhook/enrollment-created",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { enrollment_id } = req.body;

        if (!enrollment_id) {
          return res.status(400).json({
            error: true,
            message: "enrollment_id is required",
          });
        }

        const commissionService = new CommissionProcessingService(sdk);
        const result = await commissionService.createCommissionForEnrollment(
          enrollment_id
        );

        res.json({
          success: true,
          data: result,
          message: result.created
            ? "Commission created successfully"
            : "Commission already exists",
        });
      } catch (error) {
        console.error("Error creating commission:", error);
        res.status(500).json({
          error: true,
          message: "Failed to create commission",
          details: error.message,
        });
      }
    }
  );

  // Webhook endpoint for handling refund events
  app.post(
    "/v2/api/kanglink/custom/webhook/enrollment-refunded",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { enrollment_id } = req.body;

        if (!enrollment_id) {
          return res.status(400).json({
            error: true,
            message: "enrollment_id is required",
          });
        }

        const commissionService = new CommissionProcessingService(sdk);
        const result = await commissionService.handleRefund(enrollment_id);

        res.json({
          success: true,
          data: result,
          message: `Cancelled ${result.cancelled} commissions for refunded enrollment`,
        });
      } catch (error) {
        console.error("Error handling refund:", error);
        res.status(500).json({
          error: true,
          message: "Failed to handle refund",
          details: error.message,
        });
      }
    }
  );

  // Test endpoint to add funds to platform balance (for testing only)
  app.post(
    "/v2/api/kanglink/custom/admin/transactions/add-test-funds",
    TokenMiddleware({ role: "super_admin" }),
    async function (req, res) {
      try {
        const { amount = 1000, currency = "USD" } = req.body;

        const stripe = new StripeService();

        // Create test charge to add funds to platform balance
        const charge = await stripe.createTestCharge({
          amount,
          currency,
          description: `Test charge to add ${amount} ${currency} to platform balance`,
        });

        // Get updated platform balance
        const platformBalance = await stripe.getPlatformBalance();
        const availableBalance = platformBalance.available.find(
          (balance) => balance.currency === currency.toLowerCase()
        );

        res.json({
          success: true,
          data: {
            charge_id: charge.id,
            amount_added: amount,
            currency: currency,
            platform_balance: {
              available: availableBalance ? availableBalance.amount / 100 : 0,
              currency: currency,
            },
          },
          message: `Successfully added ${amount} ${currency} to platform balance`,
        });
      } catch (error) {
        console.error("Error adding test funds:", error);
        res.status(500).json({
          error: true,
          message: "Failed to add test funds to platform balance",
          details: error.message,
        });
      }
    }
  );

  // Get platform balance (for admin/testing)
  app.get(
    "/v2/api/kanglink/custom/admin/transactions/platform-balance",
    TokenMiddleware({ role: "super_admin" }),
    async function (req, res) {
      try {
        const stripe = new StripeService();
        const platformBalance = await stripe.getPlatformBalance();

        const formattedBalance = {
          available: platformBalance.available.map((balance) => ({
            amount: balance.amount / 100,
            currency: balance.currency.toUpperCase(),
          })),
          pending: platformBalance.pending.map((balance) => ({
            amount: balance.amount / 100,
            currency: balance.currency.toUpperCase(),
          })),
        };

        res.json({
          success: true,
          data: formattedBalance,
          message: "Platform balance retrieved successfully",
        });
      } catch (error) {
        console.error("Error retrieving platform balance:", error);
        res.status(500).json({
          error: true,
          message: "Failed to retrieve platform balance",
          details: error.message,
        });
      }
    }
  );
};
