const MailService = require("../../../baas/services/MailService");
const JwtService = require("../../../baas/services/JwtService");
const { sqlDateTimeFormat, sqlDateFormat } = require("../utils/date");

// Helper function to generate 6-digit OTP
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Helper function to validate email
const validateEmail = (email) => {
  if (!email) {
    return {
      status: 400,
      response: {
        error: true,
        message: "Email is required",
        validation: [{ field: "email", message: "Email is required" }],
      },
    };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return {
      status: 400,
      response: {
        error: true,
        message: "Invalid email format",
        validation: [{ field: "email", message: "Invalid email format" }],
      },
    };
  }

  return null;
};

// Helper function to validate role
const validateRole = (Role) => {
  if (!Role.canForgot()) {
    return {
      status: 403,
      response: {
        error: true,
        message: "Forgot password not allowed for this role",
      },
    };
  }
  return null;
};

const handleForgotPassword = async (req, res, sdk, mailService, config) => {
  try {
    // Validate email
    const emailError = validateEmail(req.body.email);
    if (emailError) {
      return res.status(emailError.status).json(emailError.response);
    }

    // Validate role permissions
    const Role = require(`../roles/${req.query.role}`);
    const roleError = validateRole(Role);
    if (roleError) {
      return res.status(roleError.status).json(roleError.response);
    }

    sdk.setProjectId("kanglink");
    sdk.setTable("user");

    // Find user with role_id and email
    const result = await sdk.findOne("user", {
      email: req.body.email,
      role_id: Role.slug,
    });

    if (!result) {
      return res.status(404).json({
        error: true,
        message: `No ${Role.slug === "trainer" ? "Trainer" : "Athlete"} found with this email address`,
      });
    }

    // Check if user has regular login (not social login)
    if (result.login_type != 0) {
      return res.status(403).json({
        error: true,
        message: "Cannot reset password for social login accounts",
      });
    }

    // Generate OTP and temporary token
    const otp = generateOTP();
    const tempToken = JwtService.createAccessToken(
      {
        user_id: result.id,
        email: req.body.email,
        role: Role.slug,
        type: "forgot_password",
      },
      300 * 1000, // 5 minutes expiry for temp token
      config.jwt_key
    );

    let expireDate = new Date();
    expireDate.setSeconds(expireDate.getSeconds() + 300 * 1000);
    // Save OTP token to database
    const tokenPayload = {
      code: otp,
      token: tempToken,
      type: 2, // Reset type
      data: JSON.stringify({
        email: req.body.email,
        role: Role.slug,
        otp: otp,
      }),
      user_id: result.id,
      status: 1,
      expired_at: expireDate,
      created_at: sqlDateFormat(new Date()),
      updated_at: sqlDateTimeFormat(new Date()),
    };

    await sdk.create("tokens", tokenPayload);

    // Send OTP email
    await mailService.sendPasswordResetEmail(req.body.email, otp, tempToken);

    return res.status(200).json({
      error: false,
      message: "OTP sent to your email address",
      data: {
        temp_token: tempToken,
        expires_in: 300, // 5 minutes
      },
    });
  } catch (err) {
    console.error("Forgot password error:", err);
    return res.status(500).json({
      error: true,
      message: "Internal server error",
    });
  }
};

module.exports = function (app) {
  const config = app.get("configuration");
  const mailService = new MailService(config);

  // forgot password endpoint
  app.post("/v2/api/kanglink/custom/forgot-password", async (req, res) => {
    await handleForgotPassword(req, res, app.get("sdk"), mailService, config);
  });

  return [
    {
      method: "POST",
      name: "Forgot Password API",
      url: "/v2/api/kanglink/custom/forgot-password",
      successBody: '{ "email": "<EMAIL>",  }',
      successPayload:
        '{"error": false, "message": "OTP sent to your email address", "data": {"temp_token": "jwt_token", "expires_in": 300}}',
      errors: [
        {
          name: "400",
          body: '{ "email": "", "role": "trainer" }',
          response:
            '{"error": true, "message": "Email is required", "validation": [{"field": "email", "message": "Email is required"}]}',
        },
        {
          name: "404",
          body: '{ "email": "<EMAIL>", "role": "trainer" }',
          response:
            '{"error": true, "message": "User not found with this email address"}',
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "role": "trainer" }',
          response:
            '{"error": true, "message": "Cannot reset password for social login accounts"}',
        },
      ],
      needToken: false,
    },
  ];
};
