// Public trainer endpoints - no authentication required
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");

module.exports = function (app) {
  // Get specific trainer details with ratings
  app.get(
    "/v2/api/kanglink/custom/public/trainer/:trainer_id",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const trainerId = req.params.trainer_id;

        // Validate trainer ID
        if (!trainerId || isNaN(trainerId)) {
          return res.status(400).json({
            error: true,
            message: "Invalid trainer ID provided",
          });
        }

        // Get trainer details with ratings
        const trainerQuery = `
          SELECT
            u.id,
            u.email,
            u.data,
            u.created_at,
            u.updated_at,
            COALESCE(trainer_ratings.average_rating, 0) as average_rating,
            COALESCE(trainer_ratings.review_count, 0) as review_count,
            COALESCE(trainer_ratings.program_count, 0) as program_count
          FROM kanglink_user u
          LEFT JOIN (
            SELECT
              p.user_id as trainer_id,
              AVG(pf.rating) as average_rating,
              COUNT(pf.rating) as review_count,
              COUNT(DISTINCT p.id) as program_count
            FROM kanglink_program p
            LEFT JOIN kanglink_post_feed pf ON p.id = pf.program_id AND pf.post_type = 'review' AND pf.rating IS NOT NULL
            WHERE p.status = 'published'
            GROUP BY p.user_id
          ) trainer_ratings ON u.id = trainer_ratings.trainer_id
          WHERE u.id = ? AND u.role_id LIKE '%trainer%'
        `;

        const trainerResult = await sdk.rawQuery(trainerQuery, [trainerId]);

        if (!trainerResult || trainerResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Trainer not found",
          });
        }

        const trainer = trainerResult[0];

        // Parse trainer data from JSON
        let trainerData = {};
        if (trainer.data) {
          try {
            trainerData = JSON.parse(trainer.data);
          } catch (e) {
            console.warn("Failed to parse trainer data:", e);
            trainerData = {};
          }
        }

        // Format response
        const response = {
          id: trainer.id,
          email: trainer.email,
          first_name: trainerData.first_name || "",
          last_name: trainerData.last_name || "",
          full_name:
            trainerData.full_name ||
            `${trainerData.first_name || ""} ${
              trainerData.last_name || ""
            }`.trim(),
          photo: trainerData.photo || null,
          bio: trainerData.bio || "",
          specialization: trainerData.specializations || [],
          experience_years: trainerData.years_of_experience || 0,
          certifications: trainerData.qualifications || [],
          rating: parseFloat(trainer.average_rating) || 0,
          review_count: parseInt(trainer.review_count) || 0,
          program_count: parseInt(trainer.program_count) || 0,
          created_at: trainer.created_at,
          updated_at: trainer.updated_at,
        };

        return res.status(200).json({
          error: false,
          message: "Trainer details retrieved successfully",
          data: response,
        });
      } catch (error) {
        console.error("Error fetching trainer details:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error while fetching trainer details",
        });
      }
    }
  );

  // Get trainer's published programs with pagination
  app.get(
    "/v2/api/kanglink/custom/public/trainer/:trainer_id/programs",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const trainerId = req.params.trainer_id;
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 20, 50);
        const sortBy = req.query.sort_by || "created_at"; // created_at, rating, name
        const sortOrder = req.query.sort_order || "desc"; // asc, desc

        // Validate trainer ID
        if (!trainerId || isNaN(trainerId)) {
          return res.status(400).json({
            error: true,
            message: "Invalid trainer ID provided",
          });
        }

        // Validate sort parameters
        const validSortFields = ["created_at", "rating", "program_name"];
        const validSortOrders = ["asc", "desc"];

        if (!validSortFields.includes(sortBy)) {
          return res.status(400).json({
            error: true,
            message:
              "Invalid sort_by parameter. Valid options: created_at, rating, program_name",
          });
        }

        if (!validSortOrders.includes(sortOrder.toLowerCase())) {
          return res.status(400).json({
            error: true,
            message: "Invalid sort_order parameter. Valid options: asc, desc",
          });
        }

        // First verify trainer exists
        const trainerCheckQuery = `
          SELECT id FROM kanglink_user
          WHERE id = ? AND role_id LIKE '%trainer%'
          LIMIT 1
        `;

        const trainerCheck = await sdk.rawQuery(trainerCheckQuery, [trainerId]);

        if (!trainerCheck || trainerCheck.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Trainer not found",
          });
        }

        // Build ORDER BY clause
        let orderByClause = "";
        if (sortBy === "rating") {
          orderByClause = `ORDER BY program_ratings.average_rating ${sortOrder.toUpperCase()}, p.created_at DESC`;
        } else if (sortBy === "program_name") {
          orderByClause = `ORDER BY p.program_name ${sortOrder.toUpperCase()}`;
        } else {
          orderByClause = `ORDER BY p.${sortBy} ${sortOrder.toUpperCase()}`;
        }

        // Get total count
        const countQuery = `
          SELECT COUNT(*) as total
          FROM kanglink_program p
          WHERE p.user_id = ? AND p.status = 'published'
        `;

        const countResult = await sdk.rawQuery(countQuery, [trainerId]);
        const total = countResult[0]?.total || 0;

        // Calculate pagination
        const offset = (page - 1) * limit;
        const totalPages = Math.ceil(total / limit);

        // Get programs with ratings (without splits first)
        const programsQuery = `
          SELECT
            p.id,
            p.user_id,
            p.program_name,
            p.type_of_program,
            p.program_description,
            p.target_levels,
            p.currency,
            p.days_for_preview,
            p.image,
            p.created_at,
            p.updated_at,
            COALESCE(program_ratings.average_rating, 0) as rating,
            COALESCE(program_ratings.review_count, 0) as review_count
          FROM kanglink_program p
          LEFT JOIN (
            SELECT
              pf.program_id,
              AVG(pf.rating) as average_rating,
              COUNT(pf.rating) as review_count
            FROM kanglink_post_feed pf
            WHERE pf.post_type = 'review' AND pf.rating IS NOT NULL
            GROUP BY pf.program_id
          ) program_ratings ON p.id = program_ratings.program_id
          WHERE p.user_id = ? AND p.status = 'published'
          ${orderByClause}
          LIMIT ? OFFSET ?
        `;

        const programsResult = await sdk.rawQuery(programsQuery, [
          trainerId,
          limit,
          offset,
        ]);

        // Get all splits for the programs in a separate query
        let allSplits = [];
        if (programsResult.length > 0) {
          const programIds = programsResult.map((p) => p.id);
          const splitsQuery = `
            SELECT
              id,
              program_id,
              title,
              full_price,
              subscription
            FROM kanglink_split
            WHERE program_id IN (${programIds.map(() => "?").join(",")})
          `;

          allSplits = await sdk.rawQuery(splitsQuery, programIds);
        }

        // Format programs response
        const programs = programsResult.map((program) => {
          // Parse target levels
          let targetLevels = [];
          if (program.target_levels) {
            try {
              targetLevels = JSON.parse(program.target_levels);
            } catch (e) {
              targetLevels = [];
            }
          }

          // Get splits for this program
          const splits = allSplits.filter(
            (split) => split.program_id === program.id
          );

          // Calculate minimum price
          let price = null;
          if (splits.length > 0) {
            const prices = [];
            splits.forEach((split) => {
              if (split.full_price && split.full_price > 0) {
                prices.push(split.full_price);
              }
              if (split.subscription && split.subscription > 0) {
                prices.push(split.subscription);
              }
            });
            if (prices.length > 0) {
              price = Math.min(...prices);
            }
          }
          console.log("Prices:", price, splits);

          return {
            id: program.id,
            user_id: program.user_id,
            program_name: program.program_name,
            type_of_program: program.type_of_program,
            program_description: program.program_description,
            target_levels: targetLevels,
            currency: program.currency,
            days_for_preview: program.days_for_preview,
            image: program.image,
            rating: parseFloat(program.rating) || 0,
            review_count: parseInt(program.review_count) || 0,
            price: price,
            splits: splits,
            created_at: program.created_at,
            updated_at: program.updated_at,
          };
        });

        // Build pagination response
        const pagination = {
          page: page,
          limit: limit,
          total: total,
          num_pages: totalPages,
          has_next: page < totalPages,
          has_prev: page > 1,
        };

        return res.status(200).json({
          error: false,
          message: "Trainer programs retrieved successfully",
          data: programs,
          pagination: pagination,
        });
      } catch (error) {
        console.error("Error fetching trainer programs:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error while fetching trainer programs",
        });
      }
    }
  );

  // Get specific program details with splits and pricing
  app.get(
    "/v2/api/kanglink/custom/public/program/:program_id",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const programId = req.params.program_id;

        // Validate program ID
        if (!programId || isNaN(programId)) {
          return res.status(400).json({
            error: true,
            message: "Invalid program ID provided",
          });
        }

        // Get program details with trainer information and discount data
        const programQuery = `
          SELECT
            p.id,
            p.user_id,
            p.program_name,
            p.type_of_program,
            p.program_description,
            p.target_levels,
            p.payment_plan,
            p.currency,
            p.days_for_preview,
            p.image,
            p.track_progress,
            p.allow_comments,
            p.allow_private_messages,
            p.created_at,
            p.updated_at,
            u.data as trainer_data,
            u.email as trainer_email,
            COALESCE(program_ratings.average_rating, 0) as rating,
            COALESCE(program_ratings.review_count, 0) as review_count,
            pd.id as program_discount_id,
            pd.sale_discount_type,
            pd.sale_discount_value,
            pd.created_at as program_discount_created_at,
            pd.updated_at as program_discount_updated_at
          FROM kanglink_program p
          LEFT JOIN kanglink_user u ON p.user_id = u.id
          LEFT JOIN kanglink_program_discount pd ON p.id = pd.program_id
          LEFT JOIN (
            SELECT
              pf.program_id,
              AVG(pf.rating) as average_rating,
              COUNT(pf.rating) as review_count
            FROM kanglink_post_feed pf
            WHERE pf.post_type = 'review' AND pf.rating IS NOT NULL
            GROUP BY pf.program_id
          ) program_ratings ON p.id = program_ratings.program_id
          WHERE p.id = ? AND p.status = 'published'
        `;

        const programResult = await sdk.rawQuery(programQuery, [programId]);

        if (!programResult || programResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Program not found or not published",
          });
        }

        const program = programResult[0];

        // Get general discounts for this program
        const discountsQuery = `
          SELECT
            d.id,
            d.program_id,
            d.split_id,
            d.discount_type,
            d.discount_value,
            d.applies_to,
            d.is_active,
            d.created_at,
            d.updated_at
          FROM kanglink_discount d
          WHERE d.program_id = ? AND d.is_active = true
          ORDER BY d.split_id ASC, d.id ASC
        `;

        const discountsResult = await sdk.rawQuery(discountsQuery, [programId]);

        // Get active coupons for this program
        const couponsQuery = `
          SELECT
            c.id,
            c.program_id,
            c.discount_type,
            c.discount_value,
            c.applies_to,
            c.is_active,
            c.expiry_date,
            c.usage_limit,
            c.used_count,
            c.created_at,
            c.updated_at
          FROM kanglink_coupon c
          WHERE c.program_id = ? AND c.is_active = true
          AND (c.expiry_date IS NULL OR c.expiry_date > NOW())
          AND (c.usage_limit IS NULL OR c.used_count < c.usage_limit)
          ORDER BY c.id ASC
        `;

        const couponsResult = await sdk.rawQuery(couponsQuery, [programId]);

        // Get coupon usage statistics for this program (optional - for analytics)
        const couponUsageQuery = `
          SELECT
            cu.coupon_id,
            c.code as coupon_code,
            COUNT(cu.id) as usage_count,
            SUM(cu.discount_amount) as total_discount_amount
          FROM kanglink_coupon_usage cu
          LEFT JOIN kanglink_coupon c ON cu.coupon_id = c.id
          WHERE cu.program_id = ?
          GROUP BY cu.coupon_id, c.code
          ORDER BY usage_count DESC
        `;

        const couponUsageResult = await sdk.rawQuery(couponUsageQuery, [
          programId,
        ]);

        // Get splits for this program
        const splitsQuery = `
          SELECT
            id,
            program_id,
            title,
            full_price,
            subscription,
            created_at,
            updated_at,
            equipment_required
          FROM kanglink_split
          WHERE program_id = ?
          ORDER BY id ASC
        `;

        const splitsResult = await sdk.rawQuery(splitsQuery, [programId]);

        // Get week counts for each split
        let weekCounts = [];
        if (splitsResult.length > 0) {
          try {
            const splitIds = splitsResult.map((s) => s.id).join(",");
            const weekCountsQuery = `
              SELECT
                split_id,
                COUNT(*) as week_count
              FROM kanglink_week
              WHERE split_id IN (${splitIds})
              GROUP BY split_id
            `;
            weekCounts = await sdk.rawQuery(weekCountsQuery);
          } catch (weekError) {
            console.error("Error fetching week counts:", weekError);
            weekCounts = [];
          }
        }

        // Create a map of split_id to week_count
        const weekCountMap = {};
        weekCounts.forEach((wc) => {
          weekCountMap[wc.split_id] = parseInt(wc.week_count);
        });

        // Parse target levels
        let targetLevels = [];
        if (program.target_levels) {
          try {
            targetLevels = JSON.parse(program.target_levels);
          } catch (e) {
            targetLevels = [];
          }
        }

        // Parse target levels
        let paymentPlan = [];
        if (program.payment_plan) {
          try {
            paymentPlan = JSON.parse(program.payment_plan);
          } catch (e) {
            paymentPlan = [];
          }
        }

        // Parse trainer data
        let trainerData = {};
        if (program.trainer_data) {
          try {
            trainerData = JSON.parse(program.trainer_data);
          } catch (e) {
            trainerData = {};
          }
        }

        // Calculate minimum price from splits
        let price = null;
        if (splitsResult.length > 0) {
          const prices = [];
          splitsResult.forEach((split) => {
            if (split.full_price && split.full_price > 0) {
              prices.push(split.full_price);
            }
            if (split.subscription && split.subscription > 0) {
              prices.push(split.subscription);
            }
          });
          if (prices.length > 0) {
            price = Math.min(...prices);
          }
        }

        // Calculate duration from splits
        let formattedDuration = null;
        if (splitsResult.length > 0) {
          // Get the maximum number of weeks across all splits
          const splitWeekCounts = splitsResult.map(
            (split) => weekCountMap[split.id] || 0
          );
          const maxWeeks = Math.max(...splitWeekCounts);

          if (maxWeeks > 0) {
            if (maxWeeks === 1) {
              formattedDuration = "1 Week";
            } else {
              formattedDuration = `${maxWeeks} Weeks`;
            }
          }
        }

        // Format program discount data
        let programDiscount = null;
        if (program.program_discount_id) {
          programDiscount = {
            id: program.program_discount_id,
            affiliate_link: program.affiliate_link,
            sale_discount_type: program.sale_discount_type,
            sale_discount_value: program.sale_discount_value,
            created_at: program.program_discount_created_at,
            updated_at: program.program_discount_updated_at,
          };
        }

        // Format response
        const response = {
          id: program.id,
          user_id: program.user_id,
          program_name: program.program_name,
          type_of_program: program.type_of_program,
          program_description: program.program_description,
          target_levels: targetLevels,
          payment_plan: paymentPlan,
          currency: program.currency,
          days_for_preview: program.days_for_preview,
          image: program.image,
          track_progress: program.track_progress,
          allow_comments: program.allow_comments,
          allow_private_messages: program.allow_private_messages,
          duration: formattedDuration,
          rating: parseFloat(program.rating) || 0,
          review_count: parseInt(program.review_count) || 0,
          price: price,
          splits: splitsResult,
          program_discount: programDiscount,
          discount: discountsResult[0] || null,
          coupon: couponsResult[0] || null,
          coupon_usage_stats: couponUsageResult || [],
          trainer: {
            id: program.user_id,
            email: program.trainer_email,
            first_name: trainerData.first_name || "",
            last_name: trainerData.last_name || "",
            full_name:
              trainerData.full_name ||
              `${trainerData.first_name || ""} ${
                trainerData.last_name || ""
              }`.trim(),
            photo: trainerData.photo || null,
          },
          created_at: program.created_at,
          updated_at: program.updated_at,
        };

        return res.status(200).json({
          error: false,
          message: "Program details retrieved successfully",
          data: response,
        });
      } catch (error) {
        console.error("Error fetching program details:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error while fetching program details",
        });
      }
    }
  );

  // Get public reviews for a specific program
  app.get(
    "/v2/api/kanglink/custom/public/program/:program_id/reviews",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const programId = req.params.program_id;
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 20, 50);
        const sortBy = req.query.sort_by || "created_at"; // created_at, rating
        const sortOrder = req.query.sort_order || "desc"; // asc, desc

        // Validate program ID
        if (!programId || isNaN(programId)) {
          return res.status(400).json({
            error: true,
            message: "Invalid program ID provided",
          });
        }

        // Validate sort parameters
        const validSortFields = ["created_at", "rating"];
        const validSortOrders = ["asc", "desc"];

        if (!validSortFields.includes(sortBy)) {
          return res.status(400).json({
            error: true,
            message:
              "Invalid sort_by parameter. Valid options: created_at, rating",
          });
        }

        if (!validSortOrders.includes(sortOrder.toLowerCase())) {
          return res.status(400).json({
            error: true,
            message: "Invalid sort_order parameter. Valid options: asc, desc",
          });
        }

        // First verify program exists and is published
        const programCheckQuery = `
          SELECT id FROM kanglink_program
          WHERE id = ? AND status = 'published'
          LIMIT 1
        `;

        const programCheck = await sdk.rawQuery(programCheckQuery, [programId]);

        if (!programCheck || programCheck.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Program not found or not published",
          });
        }

        // Get total count of reviews
        const countQuery = `
          SELECT COUNT(*) as total
          FROM kanglink_post_feed pf
          WHERE pf.program_id = ?
            AND pf.post_type = 'review'
            AND pf.visibility_scope = 'public'
            AND pf.rating IS NOT NULL
        `;

        const countResult = await sdk.rawQuery(countQuery, [programId]);
        const total = countResult[0]?.total || 0;

        // Calculate pagination
        const offset = (page - 1) * limit;
        const totalPages = Math.ceil(total / limit);

        // Build ORDER BY clause
        const orderByClause = `ORDER BY pf.${sortBy} ${sortOrder.toUpperCase()}`;

        // Get reviews with user information
        const reviewsQuery = `
          SELECT
            pf.id,
            pf.user_id,
            pf.program_id,
            pf.content,
            pf.rating,
            pf.attachments,
            pf.created_at,
            pf.updated_at,
            pf.is_edited,
            u.data as user_data,
            u.email as user_email,
            COALESCE(
              (SELECT JSON_ARRAYAGG(
                JSON_OBJECT(
                  'id', c.id,
                  'user_id', c.user_id,
                  'post_id', c.post_id,
                  'content', c.content,
                  'created_at', c.created_at,
                  'updated_at', c.updated_at,
                  'is_edited', c.is_edited,
                  'comment_user_data', cu.data,
                  'comment_user_email', cu.email
                )
              ) FROM kanglink_comment c
              LEFT JOIN kanglink_user cu ON c.user_id = cu.id
              WHERE c.post_id = pf.id), '[]'
            ) as replies
          FROM kanglink_post_feed pf
          LEFT JOIN kanglink_user u ON pf.user_id = u.id
          WHERE pf.program_id = ?
            AND pf.post_type = 'review'
            AND pf.visibility_scope = 'public'
            AND pf.rating IS NOT NULL
          ${orderByClause}
          LIMIT ? OFFSET ?
        `;

        const reviewsResult = await sdk.rawQuery(reviewsQuery, [
          programId,
          limit,
          offset,
        ]);

        // Format reviews response
        const reviews = reviewsResult.map((review) => {
          // Parse user data
          let userData = {};
          if (review.user_data) {
            try {
              userData = JSON.parse(review.user_data);
            } catch (e) {
              userData = {};
            }
          }

          // Parse attachments
          let attachments = [];
          if (review.attachments) {
            try {
              attachments = JSON.parse(review.attachments);
            } catch (e) {
              attachments = [];
            }
          }
          let replies = [];
          if (review.replies) {
            try {
              replies = JSON.parse(review.replies);
            } catch (e) {
              replies = [];
            }
          }

          return {
            id: review.id,
            user_id: review.user_id,
            program_id: review.program_id,
            content: review.content,
            rating: review.rating,
            attachments: attachments,
            is_edited: review.is_edited,
            replies: replies,
            user: {
              id: review.user_id,
              email: review.user_email,
              first_name: userData.first_name || "",
              last_name: userData.last_name || "",
              full_name:
                userData.full_name ||
                `${userData.first_name || ""} ${
                  userData.last_name || ""
                }`.trim(),
              photo: userData.photo || null,
            },
            created_at: review.created_at,
            updated_at: review.updated_at,
          };
        });

        // Build pagination response
        const pagination = {
          page: page,
          limit: limit,
          total: total,
          num_pages: totalPages,
          has_next: page < totalPages,
          has_prev: page > 1,
        };

        return res.status(200).json({
          error: false,
          message: "Program reviews retrieved successfully",
          data: reviews,
          pagination: pagination,
        });
      } catch (error) {
        console.error("Error fetching program reviews:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error while fetching program reviews",
        });
      }
    }
  );

  // Trainer commission endpoints - authentication required

  // Get trainer's own commission summary
  app.get(
    "/v2/api/kanglink/custom/trainer/commissions/summary",
    [TokenMiddleware({ role: "trainer" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const CommissionService = require("../services/CommissionService");
        const commissionService = new CommissionService(sdk);
        const trainerId = req.user_id;
        const { status } = req.query;

        sdk.setProjectId("kanglink");

        const commissionSummary =
          await commissionService.getTrainerCommissionSummary(
            trainerId,
            status
          );

        return res.status(200).json({
          error: false,
          data: commissionSummary,
        });
      } catch (err) {
        console.error("Get trainer commission summary error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to retrieve commission summary",
        });
      }
    }
  );

  // Get trainer's affiliate commission summary (commissions from referrals)
  app.get(
    "/v2/api/kanglink/custom/trainer/commissions/affiliate",
    [TokenMiddleware({ role: "trainer" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const CommissionService = require("../services/CommissionService");
        const commissionService = new CommissionService(sdk);
        const trainerId = req.user_id;
        const { status } = req.query;

        sdk.setProjectId("kanglink");

        const affiliateCommissionSummary =
          await commissionService.getAffiliateCommissionSummary(
            trainerId,
            status
          );

        return res.status(200).json({
          error: false,
          data: affiliateCommissionSummary,
        });
      } catch (err) {
        console.error("Get affiliate commission summary error:", err);
        return res.status(500).json({
          error: true,
          message:
            err.message || "Failed to retrieve affiliate commission summary",
        });
      }
    }
  );

  // Get trainer's detailed commission history
  app.get(
    "/v2/api/kanglink/custom/trainer/commissions/history",
    [TokenMiddleware({ role: "trainer" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const trainerId = req.user_id;
        const { status, page = 1, limit = 20 } = req.query;

        sdk.setProjectId("kanglink");
        sdk.setTable("commission");

        let whereClause = `trainer_id = ${trainerId}`;
        if (status) {
          whereClause += ` AND payout_status = '${status}'`;
        }

        const offset = (page - 1) * limit;
        const commissions = await sdk.rawQuery(`
          SELECT
            c.*,
            p.program_name,
            s.split_name,
            u.email as athlete_email
          FROM kanglink_commission c
          JOIN kanglink_program p ON c.program_id = p.id
          JOIN kanglink_split s ON c.split_id = s.id
          JOIN kanglink_user u ON c.athlete_id = u.id
          WHERE ${whereClause}
          ORDER BY c.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `);

        // Get total count
        const countResult = await sdk.rawQuery(`
          SELECT COUNT(*) as total
          FROM kanglink_commission c
          WHERE ${whereClause}
        `);

        const total = countResult[0]?.total || 0;

        return res.status(200).json({
          error: false,
          data: commissions,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: total,
            pages: Math.ceil(total / limit),
          },
        });
      } catch (err) {
        console.error("Get commission history error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to retrieve commission history",
        });
      }
    }
  );

  // Get trainer's enrolled athletes with pagination and filters
  app.get(
    "/v2/api/kanglink/custom/trainer/athletes",
    [TokenMiddleware({ role: "trainer" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const trainerId = req.user_id;

        // Extract query parameters
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 20, 50);
        const sortBy = req.query.sort_by || "enrollment_date"; // enrollment_date, athlete_name, program_name, progress
        const sortOrder = req.query.sort_order || "desc"; // asc, desc

        // Filter parameters
        const athleteName = req.query.athlete_name || "";
        const paymentType = req.query.payment_type || ""; // subscription, one_time, all
        const programName = req.query.program_name || "";
        const progressFilter = req.query.progress || ""; // below_10, below_50, above_50, completed

        sdk.setProjectId("kanglink");

        // Validate sort parameters
        const validSortFields = [
          "enrollment_date",
          "athlete_name",
          "program_name",
          "progress",
        ];
        const validSortOrders = ["asc", "desc"];
        const validPaymentTypes = ["subscription", "one_time", "all", ""];
        const validProgressFilters = [
          "below_10",
          "below_50",
          "above_50",
          "above_90",
          "completed",
          "",
        ];

        if (!validSortFields.includes(sortBy)) {
          return res.status(400).json({
            error: true,
            message:
              "Invalid sort_by parameter. Valid options: enrollment_date, athlete_name, program_name, progress",
          });
        }

        if (!validSortOrders.includes(sortOrder.toLowerCase())) {
          return res.status(400).json({
            error: true,
            message: "Invalid sort_order parameter. Valid options: asc, desc",
          });
        }

        if (!validPaymentTypes.includes(paymentType)) {
          return res.status(400).json({
            error: true,
            message:
              "Invalid payment_type parameter. Valid options: subscription, one_time, all",
          });
        }

        if (!validProgressFilters.includes(progressFilter)) {
          return res.status(400).json({
            error: true,
            message:
              "Invalid progress parameter. Valid options: below_10, below_50, above_50, above_90, completed",
          });
        }

        // Build WHERE clause for filters
        let whereConditions = [`e.trainer_id = ${trainerId}`];
        let queryParams = [];

        if (athleteName) {
          whereConditions.push(`(
            JSON_UNQUOTE(JSON_EXTRACT(athlete.data, '$.full_name')) LIKE ? OR
            JSON_UNQUOTE(JSON_EXTRACT(athlete.data, '$.first_name')) LIKE ? OR
            JSON_UNQUOTE(JSON_EXTRACT(athlete.data, '$.last_name')) LIKE ? OR
            COALESCE(
              JSON_UNQUOTE(JSON_EXTRACT(athlete.data, '$.full_name')),
              CONCAT(
                JSON_UNQUOTE(JSON_EXTRACT(athlete.data, '$.first_name')),
                ' ',
                JSON_UNQUOTE(JSON_EXTRACT(athlete.data, '$.last_name'))
              )
            ) LIKE ?
          )`);
          const searchTerm = `%${athleteName}%`;
          queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
        }

        if (paymentType && paymentType !== "all") {
          whereConditions.push(`e.payment_type = ?`);
          queryParams.push(paymentType);
        }

        if (programName) {
          whereConditions.push(`p.program_name LIKE ?`);
          queryParams.push(`%${programName}%`);
        }

        // Progress filter conditions
        if (progressFilter) {
          switch (progressFilter) {
            case "below_10":
              whereConditions.push(`COALESCE(ap.progress_percentage, 0) < 10`);
              break;
            case "below_50":
              whereConditions.push(`COALESCE(ap.progress_percentage, 0) < 50`);
              break;
            case "above_50":
              whereConditions.push(
                `COALESCE(ap.progress_percentage, 0) >= 50 AND COALESCE(ap.progress_percentage, 0) < 90`
              );
              break;
            case "above_90":
              whereConditions.push(
                `COALESCE(ap.progress_percentage, 0) >= 90 AND COALESCE(ap.progress_percentage, 0) < 100`
              );
              break;
            case "completed":
              whereConditions.push(
                `COALESCE(ap.progress_percentage, 0) >= 100`
              );
              break;
          }
        }

        const whereClause = whereConditions.join(" AND ");

        // Build ORDER BY clause
        let orderByClause = "";
        switch (sortBy) {
          case "athlete_name":
            orderByClause = `ORDER BY athlete_full_name ${sortOrder.toUpperCase()}`;
            break;
          case "program_name":
            orderByClause = `ORDER BY p.program_name ${sortOrder.toUpperCase()}`;
            break;
          case "progress":
            orderByClause = `ORDER BY COALESCE(ap.progress_percentage, 0) ${sortOrder.toUpperCase()}`;
            break;
          default: // enrollment_date
            orderByClause = `ORDER BY e.enrollment_date ${sortOrder.toUpperCase()}`;
        }

        // Get total count
        const countQuery = `
          SELECT COUNT(DISTINCT e.id) as total
          FROM kanglink_enrollment e
          LEFT JOIN kanglink_user athlete ON e.athlete_id = athlete.id
          LEFT JOIN kanglink_program p ON e.program_id = p.id
          LEFT JOIN kanglink_split s ON e.split_id = s.id
          LEFT JOIN kanglink_athlete_progress ap ON e.id = ap.enrollment_id
          WHERE ${whereClause}
        `;

        const countResult = await sdk.rawQuery(countQuery, queryParams);
        const total = countResult[0]?.total || 0;

        // Calculate pagination
        const offset = (page - 1) * limit;
        const totalPages = Math.ceil(total / limit);

        // Main query to get athletes with all required data
        const athletesQuery = `
          SELECT
            e.id as enrollment_id,
            e.athlete_id,
            e.program_id,
            e.split_id,
            e.payment_type,
            e.amount,
            e.currency,
            e.enrollment_date,
            e.status as enrollment_status,
            e.payment_status,
            athlete.email as athlete_email,
            athlete.data as athlete_data,
            p.program_name,
            s.title as split_title,
            COALESCE(ap.progress_percentage, 0) as progress_percentage,
            COALESCE(ap.total_days_completed, 0) as total_days_completed,
            COALESCE(ap.total_exercises_completed, 0) as total_exercises_completed,
            ap.last_activity_date,
            COALESCE(
              JSON_UNQUOTE(JSON_EXTRACT(athlete.data, '$.full_name')),
              CONCAT(
                COALESCE(JSON_UNQUOTE(JSON_EXTRACT(athlete.data, '$.first_name')), ''),
                ' ',
                COALESCE(JSON_UNQUOTE(JSON_EXTRACT(athlete.data, '$.last_name')), '')
              )
            ) as athlete_full_name
          FROM kanglink_enrollment e
          LEFT JOIN kanglink_user athlete ON e.athlete_id = athlete.id
          LEFT JOIN kanglink_program p ON e.program_id = p.id
          LEFT JOIN kanglink_split s ON e.split_id = s.id
          LEFT JOIN kanglink_athlete_progress ap ON e.id = ap.enrollment_id
          WHERE ${whereClause}
          ${orderByClause}
          LIMIT ? OFFSET ?
        `;

        const athletesResult = await sdk.rawQuery(athletesQuery, [
          ...queryParams,
          limit,
          offset,
        ]);

        // Format the response
        const athletes = athletesResult.map((athlete) => {
          // Parse athlete data
          let athleteData = {};
          if (athlete.athlete_data) {
            try {
              athleteData = JSON.parse(athlete.athlete_data);
            } catch (e) {
              athleteData = {};
            }
          }

          // Format progress status
          let progressStatus = "Not Started";
          const progress = parseFloat(athlete.progress_percentage) || 0;
          if (progress >= 100) {
            progressStatus = "Completed";
          } else if (progress >= 50) {
            progressStatus = "On Track";
          } else if (progress > 0) {
            progressStatus = "Behind";
          }

          // Format athlete name with fallback logic
          const athleteName =
            athleteData.full_name ||
            `${athleteData.first_name || ""} ${
              athleteData.last_name || ""
            }`.trim() ||
            athlete.athlete_full_name.trim() ||
            "Unknown";

          return {
            enrollment_id: athlete.enrollment_id,
            athlete_id: athlete.athlete_id,
            athlete_name: athleteName,
            athlete_email: athlete.athlete_email,
            athlete_photo: athleteData.photo || null,
            program_id: athlete.program_id,
            program_name: athlete.program_name,
            split_id: athlete.split_id,
            split_title: athlete.split_title,
            payment_type: athlete.payment_type,
            payment_type_display:
              athlete.payment_type === "subscription"
                ? "Subscription"
                : "Full Payment",
            amount: parseFloat(athlete.amount) || 0,
            currency: athlete.currency || "USD",
            enrollment_date: athlete.enrollment_date,
            enrollment_status: athlete.enrollment_status,
            payment_status: athlete.payment_status,
            progress_percentage: progress,
            progress_status: progressStatus,
            total_days_completed: parseInt(athlete.total_days_completed) || 0,
            total_exercises_completed:
              parseInt(athlete.total_exercises_completed) || 0,
            last_activity_date: athlete.last_activity_date,
          };
        });

        // Build pagination response
        const pagination = {
          page: page,
          limit: limit,
          total: total,
          num_pages: totalPages,
          has_next: page < totalPages,
          has_prev: page > 1,
        };

        return res.status(200).json({
          error: false,
          message: "Trainer athletes retrieved successfully",
          data: athletes,
          pagination: pagination,
          filters: {
            athlete_name: athleteName,
            payment_type: paymentType,
            program_name: programName,
            progress: progressFilter,
          },
          sorting: {
            sort_by: sortBy,
            sort_order: sortOrder,
          },
        });
      } catch (error) {
        console.error("Error fetching trainer athletes:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error while fetching trainer athletes",
        });
      }
    }
  );
};
