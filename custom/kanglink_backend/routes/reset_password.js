const PasswordService = require("../../../baas/services/PasswordService");
const JwtService = require("../../../baas/services/JwtService");
const { sqlDateTimeFormat, sqlDateFormat } = require("../utils/date");

// Helper function to validate password reset input
const validatePasswordResetInput = (body) => {
  const validationErrors = [];

  if (!body.password) {
    validationErrors.push({
      field: "password",
      message: "Password is required",
    });
  }

  if (!body.confirm_password) {
    validationErrors.push({
      field: "confirm_password",
      message: "Confirm password is required",
    });
  }

  if (!body.reset_token) {
    validationErrors.push({
      field: "reset_token",
      message: "Reset token is required",
    });
  }

  if (
    body.password &&
    body.confirm_password &&
    body.password !== body.confirm_password
  ) {
    validationErrors.push({
      field: "confirm_password",
      message: "Passwords do not match",
    });
  }

  // Password strength validation
  if (body.password) {
    if (body.password.length < 8) {
      validationErrors.push({
        field: "password",
        message: "Password must be at least 8 characters long",
      });
    }

    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!passwordRegex.test(body.password)) {
      validationErrors.push({
        field: "password",
        message:
          "Password must contain uppercase, lowercase, number, and special character",
      });
    }
  }

  if (validationErrors.length > 0) {
    return {
      status: 400,
      response: {
        error: true,
        message: validationErrors[0].message,
        validation: validationErrors,
      },
    };
  }

  return null;
};

const handlePasswordReset = async (req, res, sdk, config) => {
  try {
    // Validate input
    const validationError = validatePasswordResetInput(req.body);
    if (validationError) {
      return res.status(validationError.status).json(validationError.response);
    }

    // Verify reset token
    const resetTokenData = JwtService.verifyAccessToken(
      req.body.reset_token,
      config.jwt_key
    );
    if (!resetTokenData || resetTokenData.type !== "password_reset") {
      return res.status(403).json({
        error: true,
        message: "Invalid or expired reset token",
      });
    }

    // Validate role permissions
    const Role = require(`../roles/trainer`);
    if (!Role.canReset()) {
      return res.status(403).json({
        error: true,
        message: "Password reset not allowed for this role",
      });
    }

    sdk.setProjectId("kanglink");
    sdk.setTable("user");

    // Verify user exists and is a trainer with regular login
    const user = await sdk.findOne("user", {
      id: resetTokenData.user_id,
      email: resetTokenData.email,
      role_id: resetTokenData.role,
      login_type: 0, // Regular login only
    });

    if (!user) {
      return res.status(403).json({
        error: true,
        message: "Invalid user account",
      });
    }

    // Hash new password
    const hashedPassword = await PasswordService.hash(req.body.password);

    // Update user password
    const updateResult = await sdk.updateById("user", user.id, {
      password: hashedPassword,
      updated_at: sqlDateTimeFormat(new Date()),
    });

    if (!updateResult) {
      throw new Error("Failed to update password");
    }

    // Invalidate all reset tokens for this user
    sdk.setTable("tokens");
    await sdk.update(
      "tokens",
      {
        user_id: user.id,
        type: 2, // Reset type
        status: 1, // Active
      },
      {
        status: 0, // Mark as inactive
        updated_at: sqlDateTimeFormat(new Date()),
      }
    );

    return res.status(200).json({
      error: false,
      message: "Password reset successfully",
    });
  } catch (err) {
    console.error("Password reset error:", err);
    return res.status(500).json({
      error: true,
      message: "Internal server error",
    });
  }
};

module.exports = function (app) {
  const config = app.get("configuration");

  // Trainer password reset endpoint
  app.post(
    "/v2/api/kanglink/custom/trainer/reset-password",
    async (req, res) => {
      await handlePasswordReset(req, res, app.get("sdk"), config);
    }
  );

  return [
    {
      method: "POST",
      name: "Trainer Reset Password API",
      url: "/v2/api/kanglink/custom/trainer/reset-password",
      successBody:
        '{ "password": "NewPassword123!", "confirm_password": "NewPassword123!", "reset_token": "jwt_reset_token" }',
      successPayload:
        '{"error": false, "message": "Password reset successfully"}',
      errors: [
        {
          name: "400",
          body: '{ "password": "", "confirm_password": "", "reset_token": "jwt_reset_token" }',
          response:
            '{"error": true, "message": "Password is required", "validation": [{"field": "password", "message": "Password is required"}]}',
        },
        {
          name: "400",
          body: '{ "password": "NewPassword123!", "confirm_password": "DifferentPassword!", "reset_token": "jwt_reset_token" }',
          response:
            '{"error": true, "message": "Passwords do not match", "validation": [{"field": "confirm_password", "message": "Passwords do not match"}]}',
        },
        {
          name: "403",
          body: '{ "password": "NewPassword123!", "confirm_password": "NewPassword123!", "reset_token": "invalid_token" }',
          response:
            '{"error": true, "message": "Invalid or expired reset token"}',
        },
      ],
      needToken: false,
    },
  ];
};
