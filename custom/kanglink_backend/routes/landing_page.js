const AuthService = require("../../../baas/services/AuthService");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const UtilService = require("../../../baas/services/UtilService");
const StripeService = require("../../../baas/services/StripeService");
const PaginationService = require("../../../baas/services/PaginationService");

// Helper functions for performance optimization
const LandingPageHelpers = {
  // Cache keys
  CACHE_KEYS: {
    TOP_RATED_PROGRAMS: "landing:top_rated_programs",
    TOP_RATED_TRAINERS: "landing:top_rated_trainers",
    PROGRAM_RATINGS: "landing:program_ratings",
    TRAINER_RATINGS: "landing:trainer_ratings",
  },

  // Cache TTL (Time To Live) in seconds
  CACHE_TTL: {
    RATINGS: 300, // 5 minutes
    PROGRAMS: 600, // 10 minutes
    TRAINERS: 600, // 10 minutes
  },

  // Get Redis client (fallback implementation)
  async getRedisClient() {
    // Simple in-memory cache fallback when Redis is not available
    if (!global.simpleCache) {
      global.simpleCache = new Map();
    }
    return {
      get: async (key) => {
        const item = global.simpleCache.get(key);
        if (item && item.expiry > Date.now()) {
          return item.value;
        }
        global.simpleCache.delete(key);
        return null;
      },
      set: async (key, value, mode, ttl) => {
        const expiry =
          mode === "EX" ? Date.now() + ttl * 1000 : Date.now() + 600000; // 10 min default
        global.simpleCache.set(key, { value, expiry });
        return "OK";
      },
    };
  },

  // Calculate program ratings efficiently
  async calculateProgramRatings(sdk, programIds = null) {
    const projectId = "kanglink";
    let whereClause = `${projectId}_post_feed.post_type = 'review' AND ${projectId}_post_feed.rating IS NOT NULL`;

    if (programIds && programIds.length > 0) {
      whereClause += ` AND ${projectId}_post_feed.program_id IN (${programIds.join(
        ","
      )})`;
    }

    const ratingsQuery = `
      SELECT
        ${projectId}_post_feed.program_id,
        AVG(${projectId}_post_feed.rating) as average_rating,
        COUNT(${projectId}_post_feed.rating) as review_count
      FROM ${projectId}_post_feed
      WHERE ${whereClause}
      GROUP BY ${projectId}_post_feed.program_id
      HAVING review_count > 0
      ORDER BY average_rating DESC, review_count DESC
    `;

    return await sdk.rawQuery(ratingsQuery);
  },

  // Calculate trainer ratings efficiently
  async calculateTrainerRatings(sdk, trainerIds = null) {
    const projectId = "kanglink";
    let whereClause = `${projectId}_post_feed.post_type = 'review' AND ${projectId}_post_feed.rating IS NOT NULL`;

    if (trainerIds && trainerIds.length > 0) {
      const trainerIdsStr = trainerIds.join(",");
      whereClause += ` AND ${projectId}_program.user_id IN (${trainerIdsStr})`;
    }

    const ratingsQuery = `
      SELECT
        ${projectId}_program.user_id as trainer_id,
        AVG(${projectId}_post_feed.rating) as average_rating,
        COUNT(${projectId}_post_feed.rating) as review_count,
        COUNT(DISTINCT ${projectId}_post_feed.program_id) as program_count
      FROM ${projectId}_post_feed
      INNER JOIN ${projectId}_program ON ${projectId}_post_feed.program_id = ${projectId}_program.id
      WHERE ${whereClause}
      GROUP BY ${projectId}_program.user_id
      HAVING review_count > 0
      ORDER BY average_rating DESC, review_count DESC
    `;

    return await sdk.rawQuery(ratingsQuery);
  },

  // Helper function to check if status column exists in the program table
  async checkStatusColumnExists(sdk, projectId) {
    try {
      const query = `
        SELECT COUNT(*) as column_count
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = '${projectId}_program'
        AND COLUMN_NAME = 'status'
      `;
      const result = await sdk.rawQuery(query);
      return result[0]?.column_count > 0;
    } catch (error) {
      console.log("Error checking status column existence:", error.message);
      return false;
    }
  },

  // Get user preferences from user data
  getUserPreferences(userData) {
    if (!userData) return null;

    try {
      const data =
        typeof userData === "string" ? JSON.parse(userData) : userData;
      return {
        fitness_goals: data.fitness_goals || [],
        level: data.level || "beginner",
        qualifications: data.qualifications || [],
        specializations: data.specializations || [],
      };
    } catch (error) {
      console.error("Error parsing user data:", error);
      return null;
    }
  },

  // Match user preferences with programs
  calculateProgramMatch(userPreferences, program) {
    if (!userPreferences) return 0;

    let score = 0;
    let maxScore = 0;

    // Fitness level matching (weight: 3)
    maxScore += 3;
    if (program.target_levels) {
      try {
        const targetLevels =
          typeof program.target_levels === "string"
            ? JSON.parse(program.target_levels)
            : program.target_levels;

        if (
          Array.isArray(targetLevels) &&
          targetLevels.includes(userPreferences.level)
        ) {
          score += 3;
        }
      } catch (e) {
        // If parsing fails, give partial score
        score += 1;
      }
    }

    // Program type matching (weight: 2)
    maxScore += 2;
    if (
      program.type_of_program &&
      userPreferences.fitness_goals.includes(program.type_of_program)
    ) {
      score += 2;
    }

    // Return percentage match
    return maxScore > 0 ? (score / maxScore) * 100 : 0;
  },

  // Match user preferences with trainers
  calculateTrainerMatch(userPreferences, trainerData) {
    if (!userPreferences || !trainerData) return 0;

    let score = 0;
    let maxScore = 0;

    try {
      const data =
        typeof trainerData === "string" ? JSON.parse(trainerData) : trainerData;

      // Specialization matching (weight: 3)
      maxScore += 3;
      if (data.specializations && Array.isArray(data.specializations)) {
        const matchingSpecs = data.specializations.filter((spec) =>
          userPreferences.fitness_goals.includes(spec)
        );
        if (matchingSpecs.length > 0) {
          score += Math.min(3, matchingSpecs.length);
        }
      }

      // Experience level matching (weight: 2)
      maxScore += 2;
      if (data.years_of_experience) {
        const userLevel = userPreferences.level;
        const trainerExp = parseInt(data.years_of_experience) || 0;

        if (
          (userLevel === "beginner" && trainerExp >= 1) ||
          (userLevel === "intermediate" && trainerExp >= 3) ||
          (userLevel === "expert" && trainerExp >= 5)
        ) {
          score += 2;
        }
      }

      return maxScore > 0 ? (score / maxScore) * 100 : 0;
    } catch (error) {
      console.error("Error calculating trainer match:", error);
      return 0;
    }
  },

  // Build pagination response
  buildPaginationResponse(data, page, limit, total) {
    const paginationService = new PaginationService(page, limit);
    paginationService.setCount(total);

    return {
      data,
      pagination: {
        page: paginationService.getPage(),
        limit: paginationService.getLimit(),
        total: paginationService.getCount(),
        num_pages: paginationService.getNumPages(),
        has_next: paginationService.getPage() < paginationService.getNumPages(),
        has_prev: paginationService.getPage() > 1,
      },
    };
  },
};

module.exports = function (app) {
  console.log("🚀 Registering landing page routes...");

  /* all endpoints are public,
   but when a logged in user visits,
   we should fetch the user details and match the users goals with
   the specializations of the trainer and qualifications for
   `trainers you may like, top rated trainers, programs you may like top rated programs`
   above is the schema for the user data and program

   the user.data JSON holds information for user
   while type of program and target levels in program
   will help in filtering the `programs you may like, top rated programs`
*/

  // Migration endpoint to add status column to kanglink_program table
  app.post(
    "/v2/api/kanglink/custom/landing/migrate-status-column",
    async (req, res) => {
      try {
        const sdk = req.app.get("sdk");
        if (!sdk) {
          return res.status(500).json({
            error: true,
            message: "Database connection not available",
          });
        }

        const projectId = "kanglink";

        // Check if status column already exists
        const columnExists = await LandingPageHelpers.checkStatusColumnExists(
          sdk,
          projectId
        );

        if (columnExists) {
          return res.json({
            error: false,
            message: "Status column already exists",
            data: { columnExists: true },
          });
        }

        // Add the status column
        const alterQuery = `
        ALTER TABLE ${projectId}_program
        ADD COLUMN status VARCHAR(50) DEFAULT 'published' AFTER image
      `;

        await sdk.rawQuery(alterQuery);

        // Update any existing records to have 'published' status
        const updateQuery = `
        UPDATE ${projectId}_program
        SET status = 'published'
        WHERE status IS NULL OR status = ''
      `;

        await sdk.rawQuery(updateQuery);

        return res.json({
          error: false,
          message: "Status column added successfully",
          data: { columnExists: true },
        });
      } catch (error) {
        console.error("Migration error:", error);
        return res.status(500).json({
          error: true,
          message: "Migration failed: " + error.message,
        });
      }
    }
  );

  // Top rated programs endpoint with optional authentication
  console.log(
    "📝 Registering: GET /v2/api/kanglink/custom/landing/top-rated-programs"
  );
  app.get(
    "/v2/api/kanglink/custom/landing/top-rated-programs",
    async (req, res) => {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        // Parse query parameters
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 20, 50); // Max 50 items per page
        const category = req.query.category; // Optional category filter
        const minRating = parseFloat(req.query.min_rating) || 0;

        // Check for optional authentication
        let userId = null;
        let userPreferences = null;

        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith("Bearer ")) {
          try {
            const config = app.get("configuration");
            const token = authHeader.split(" ")[1];
            const decoded =
              require("../../../baas/services/JwtService").verifyAccessToken(
                token,
                config.jwt_key
              );

            if (decoded && decoded.user_id) {
              userId = decoded.user_id;

              // Get user preferences for personalized sorting
              const user = await sdk.findOne("user", { id: userId });
              if (user && user.data) {
                userPreferences = LandingPageHelpers.getUserPreferences(
                  user.data
                );
              }
            }
          } catch (error) {
            // Continue without authentication if token is invalid
            console.log("Invalid token provided, continuing without auth");
          }
        }

        // Try to get from cache first
        const cacheKey = `${
          LandingPageHelpers.CACHE_KEYS.TOP_RATED_PROGRAMS
        }:${page}:${limit}:${category || "all"}:${minRating}`;
        let cachedResult = null;

        try {
          const redis = await LandingPageHelpers.getRedisClient();
          const cached = await redis.get(cacheKey);
          if (cached) {
            cachedResult = JSON.parse(cached);
          }
        } catch (error) {
          console.log("Redis cache unavailable, proceeding without cache");
        }

        if (cachedResult && !userId) {
          // Return cached result for non-authenticated users
          return res.status(200).json({
            error: false,
            message: "Top rated programs retrieved successfully",
            ...cachedResult,
          });
        }

        // Build the main query with ratings
        const projectId = "kanglink";
        let whereConditions = [
          `p.status = 'published'`, // Use table alias 'p' instead of full table name
          `ratings.review_count >= 1`, // Minimum 1 review to be considered (changed from 3)
          `ratings.average_rating >= ${minRating}`,
        ];

        if (category) {
          whereConditions.push(
            `p.type_of_program = '${category}'` // Use table alias 'p'
          );
        }

        const whereClause = whereConditions.join(" AND ");
        const offset = (page - 1) * limit;

        // Get programs with ratings, trainer info, and pricing from splits
        const programsQuery = `
        SELECT
          p.id,
          p.user_id,
          p.program_name,
          p.type_of_program,
          p.program_description,
          p.target_levels,
          p.currency,
          p.days_for_preview,
          p.image,
          p.created_at,
          p.updated_at,
          ratings.average_rating,
          ratings.review_count,
          u.email as trainer_email,
          pref.first_name as trainer_first_name,
          pref.last_name as trainer_last_name,
          pref.photo as trainer_photo,
          u.data as trainer_data,
          MIN(s.full_price) as min_full_price,
          MAX(s.full_price) as max_full_price,
          MIN(s.subscription) as min_subscription_price,
          MAX(s.subscription) as max_subscription_price,
          MAX(week_counts.max_weeks) as max_weeks,
          GROUP_CONCAT(DISTINCT f.user_id) as favorite_user_ids
        FROM ${projectId}_program p
        INNER JOIN (
          SELECT
            program_id,
            AVG(rating) as average_rating,
            COUNT(rating) as review_count
          FROM ${projectId}_post_feed
          WHERE post_type = 'review' AND rating IS NOT NULL
          GROUP BY program_id
          HAVING review_count >= 1
        ) ratings ON p.id = ratings.program_id
        LEFT JOIN ${projectId}_user u ON p.user_id = u.id
        LEFT JOIN ${projectId}_preference pref ON u.id = pref.user_id
        LEFT JOIN ${projectId}_split s ON p.id = s.program_id
        LEFT JOIN (
          SELECT
            s.program_id,
            MAX(week_count) as max_weeks
          FROM ${projectId}_split s
          LEFT JOIN (
            SELECT
              split_id,
              COUNT(*) as week_count
            FROM ${projectId}_week
            GROUP BY split_id
          ) w ON s.id = w.split_id
          GROUP BY s.program_id
        ) week_counts ON p.id = week_counts.program_id
        LEFT JOIN ${projectId}_favorite f ON p.id = f.favorite_id AND f.favorite_type = 'program'
        WHERE ${whereClause}
        GROUP BY p.id, p.user_id, p.program_name, p.type_of_program, p.program_description,
                 p.target_levels, p.currency, p.days_for_preview, p.image, p.created_at,
                 p.updated_at, ratings.average_rating, ratings.review_count, u.email,
                 pref.first_name, pref.last_name, pref.photo, u.data, week_counts.max_weeks
        ORDER BY ratings.average_rating DESC, ratings.review_count DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

        // Get total count for pagination
        const countQuery = `
        SELECT COUNT(DISTINCT p.id) as total
        FROM ${projectId}_program p
        INNER JOIN (
          SELECT
            program_id,
            AVG(rating) as average_rating,
            COUNT(rating) as review_count
          FROM ${projectId}_post_feed
          WHERE post_type = 'review' AND rating IS NOT NULL
          GROUP BY program_id
          HAVING review_count >= 1
        ) ratings ON p.id = ratings.program_id
        LEFT JOIN ${projectId}_split s ON p.id = s.program_id
        WHERE ${whereClause}
      `;

        const [programs, countResult] = await Promise.all([
          sdk.rawQuery(programsQuery),
          sdk.rawQuery(countQuery),
        ]);

        const total = countResult[0]?.total || 0;

        // Process programs and add match scores for authenticated users
        const processedPrograms = programs.map((program) => {
          // Format duration from weeks
          let formattedDuration = null;
          if (program.max_weeks && parseInt(program.max_weeks) > 0) {
            const weeks = parseInt(program.max_weeks);
            if (weeks === 1) {
              formattedDuration = "1 Week";
            } else {
              formattedDuration = `${weeks} Weeks`;
            }
          }

          const processedProgram = {
            id: program.id,
            user_id: program.user_id,
            program_name: program.program_name,
            type_of_program: program.type_of_program,
            program_description: program.program_description,
            target_levels: program.target_levels,
            currency: program.currency,
            days_for_preview: program.days_for_preview,
            image: program.image,
            duration: formattedDuration,
            created_at: program.created_at,
            updated_at: program.updated_at,
            rating: parseFloat(program.average_rating).toFixed(1),
            review_count: program.review_count,
            favorite: program.favorite_user_ids ? program.favorite_user_ids.split(',').map(id => parseInt(id)) : [],
            pricing: {
              full_price: {
                min: program.min_full_price,
                max: program.max_full_price,
              },
              subscription_price: {
                min: program.min_subscription_price,
                max: program.max_subscription_price,
              },
              currency: program.currency,
            },
            price: (() => {
              const prices = [];
              if (
                program.min_full_price &&
                parseFloat(program.min_full_price) > 0
              )
                prices.push(parseFloat(program.min_full_price));
              if (
                program.min_subscription_price &&
                parseFloat(program.min_subscription_price) > 0
              )
                prices.push(parseFloat(program.min_subscription_price));
              return prices.length > 0 ? Math.min(...prices) : 0;
            })(),
            trainer: {
              id: program.user_id,
              email: program.trainer_email,
              first_name: program.trainer_first_name,
              last_name: program.trainer_last_name,
              photo: program.trainer_photo,
              full_name: `${program.trainer_first_name || ""} ${
                program.trainer_last_name || ""
              }`.trim(),
            },
          };

          // Add match score for authenticated users
          if (userPreferences) {
            processedProgram.match_score =
              LandingPageHelpers.calculateProgramMatch(
                userPreferences,
                program
              );
          }

          return processedProgram;
        });

        // Sort by match score if user is authenticated
        if (userPreferences) {
          processedPrograms.sort((a, b) => {
            // Primary sort by match score, secondary by rating
            if (b.match_score !== a.match_score) {
              return b.match_score - a.match_score;
            }
            return parseFloat(b.rating) - parseFloat(a.rating);
          });
        }

        const result = LandingPageHelpers.buildPaginationResponse(
          processedPrograms,
          page,
          limit,
          total
        );

        // Cache result for non-authenticated requests
        if (!userId) {
          try {
            const redis = await LandingPageHelpers.getRedisClient();
            await redis.set(
              cacheKey,
              JSON.stringify(result),
              "EX",
              LandingPageHelpers.CACHE_TTL.PROGRAMS
            );
          } catch (error) {
            console.log("Failed to cache result:", error);
          }
        }

        return res.status(200).json({
          error: false,
          message: "Top rated programs retrieved successfully",
          ...result,
        });
      } catch (error) {
        console.error("Error fetching top rated programs:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error while fetching programs",
        });
      }
    }
  );

  // Programs you may like endpoint - requires authentication
  app.get(
    "/v2/api/kanglink/custom/landing/programs-you-may-like",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async (req, res) => {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const userId = req.user_id;
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 20, 50);
        const minRating = parseFloat(req.query.min_rating) || 3.0; // Default minimum rating

        // Get user preferences
        const user = await sdk.findOne("user", { id: userId });
        if (!user || !user.data) {
          return res.status(400).json({
            error: true,
            message:
              "User preferences not found. Please complete your profile to get personalized recommendations.",
          });
        }

        const userPreferences = LandingPageHelpers.getUserPreferences(
          user.data
        );
        if (!userPreferences || !userPreferences.fitness_goals.length) {
          return res.status(400).json({
            error: true,
            message:
              "Please set your fitness goals in your profile to get personalized recommendations.",
          });
        }

        // Get programs with ratings, excluding user's own programs
        const projectId = "kanglink";
        const offset = (page - 1) * limit;

        // Build fitness goals filter
        const fitnessGoalsFilter = userPreferences.fitness_goals
          .map((goal) => `'${goal}'`)
          .join(",");

        const programsQuery = `
          SELECT
            p.id,
            p.user_id,
            p.program_name,
            p.type_of_program,
            p.program_description,
            p.target_levels,
            p.currency,
            p.days_for_preview,
            p.image,
            p.created_at,
            p.updated_at,
            COALESCE(ratings.average_rating, 0) as average_rating,
            COALESCE(ratings.review_count, 0) as review_count,
            u.email as trainer_email,
            pref.first_name as trainer_first_name,
            pref.last_name as trainer_last_name,
            pref.photo as trainer_photo,
            u.data as trainer_data,
            MIN(s.full_price) as min_full_price,
            MAX(s.full_price) as max_full_price,
            MIN(s.subscription) as min_subscription_price,
            MAX(s.subscription) as max_subscription_price,
            MAX(week_counts.max_weeks) as max_weeks,
            GROUP_CONCAT(DISTINCT f.user_id) as favorite_user_ids
          FROM ${projectId}_program p
          LEFT JOIN (
            SELECT
              program_id,
              AVG(rating) as average_rating,
              COUNT(rating) as review_count
            FROM ${projectId}_post_feed
            WHERE post_type = 'review' AND rating IS NOT NULL
            GROUP BY program_id
          ) ratings ON p.id = ratings.program_id
          LEFT JOIN ${projectId}_user u ON p.user_id = u.id
          LEFT JOIN ${projectId}_preference pref ON u.id = pref.user_id
          LEFT JOIN ${projectId}_split s ON p.id = s.program_id
          LEFT JOIN (
            SELECT
              s.program_id,
              MAX(week_count) as max_weeks
            FROM ${projectId}_split s
            LEFT JOIN (
              SELECT
                split_id,
              COUNT(*) as week_count
              FROM ${projectId}_week
              GROUP BY split_id
            ) w ON s.id = w.split_id
            GROUP BY s.program_id
          ) week_counts ON p.id = week_counts.program_id
          LEFT JOIN ${projectId}_favorite f ON p.id = f.favorite_id AND f.favorite_type = 'program'
          WHERE p.status = 'published'
            AND p.user_id != ${userId}
            AND (
              p.type_of_program IN (${fitnessGoalsFilter})
              OR JSON_EXTRACT(p.target_levels, '$[*]') LIKE '%${
                userPreferences.level
              }%'
              OR COALESCE(ratings.average_rating, 0) >= ${minRating}
            )
          GROUP BY p.id, p.user_id, p.program_name, p.type_of_program, p.program_description,
                   p.target_levels, p.currency, p.days_for_preview, p.image, p.created_at,
                   p.updated_at, ratings.average_rating, ratings.review_count, u.email,
                   pref.first_name, pref.last_name, pref.photo, u.data, week_counts.max_weeks
          ORDER BY COALESCE(ratings.average_rating, 0) DESC
          LIMIT ${limit * 3} OFFSET ${offset}
        `;

        // Get total count for pagination
        const countQuery = `
          SELECT COUNT(DISTINCT p.id) as total
          FROM ${projectId}_program p
          LEFT JOIN (
            SELECT
              program_id,
              AVG(rating) as average_rating,
              COUNT(rating) as review_count
            FROM ${projectId}_post_feed
            WHERE post_type = 'review' AND rating IS NOT NULL
            GROUP BY program_id
          ) ratings ON p.id = ratings.program_id
          LEFT JOIN ${projectId}_split s ON p.id = s.program_id
          WHERE p.status = 'published'
            AND p.user_id != ${userId}
            AND (
              p.type_of_program IN (${fitnessGoalsFilter})
              OR JSON_EXTRACT(p.target_levels, '$[*]') LIKE '%${userPreferences.level}%'
              OR COALESCE(ratings.average_rating, 0) >= ${minRating}
            )
        `;

        const [programs, countResult] = await Promise.all([
          sdk.rawQuery(programsQuery),
          sdk.rawQuery(countQuery),
        ]);

        const total = countResult[0]?.total || 0;

        // Calculate match scores and sort by relevance
        const programsWithScores = programs.map((program) => {
          const matchScore = LandingPageHelpers.calculateProgramMatch(
            userPreferences,
            program
          );

          // Format duration from weeks
          let formattedDuration = null;
          if (program.max_weeks && parseInt(program.max_weeks) > 0) {
            const weeks = parseInt(program.max_weeks);
            if (weeks === 1) {
              formattedDuration = "1 Week";
            } else {
              formattedDuration = `${weeks} Weeks`;
            }
          }

          return {
            id: program.id,
            user_id: program.user_id,
            program_name: program.program_name,
            type_of_program: program.type_of_program,
            program_description: program.program_description,
            target_levels: program.target_levels,
            currency: program.currency,
            days_for_preview: program.days_for_preview,
            image: program.image,
            duration: formattedDuration,
            created_at: program.created_at,
            updated_at: program.updated_at,
            rating: program.average_rating
              ? parseFloat(program.average_rating).toFixed(1)
              : "0.0",
            review_count: program.review_count,
            favorite: program.favorite_user_ids ? program.favorite_user_ids.split(',').map(id => parseInt(id)) : [],
            match_score: matchScore,
            pricing: {
              full_price: {
                min: program.min_full_price,
                max: program.max_full_price,
              },
              subscription_price: {
                min: program.min_subscription_price,
                max: program.max_subscription_price,
              },
              currency: program.currency,
            },
            price: (() => {
              const prices = [];
              if (
                program.min_full_price &&
                parseFloat(program.min_full_price) > 0
              )
                prices.push(parseFloat(program.min_full_price));
              if (
                program.min_subscription_price &&
                parseFloat(program.min_subscription_price) > 0
              )
                prices.push(parseFloat(program.min_subscription_price));
              return prices.length > 0 ? Math.min(...prices) : 0;
            })(),
            trainer: {
              id: program.user_id,
              email: program.trainer_email,
              first_name: program.trainer_first_name,
              last_name: program.trainer_last_name,
              photo: program.trainer_photo,
              full_name: `${program.trainer_first_name || ""} ${
                program.trainer_last_name || ""
              }`.trim(),
            },
          };
        });

        // Sort by match score (descending), then by rating (descending)
        programsWithScores.sort((a, b) => {
          if (b.match_score !== a.match_score) {
            return b.match_score - a.match_score;
          }
          return parseFloat(b.rating) - parseFloat(a.rating);
        });

        // Take only the requested page size after sorting
        const paginatedPrograms = programsWithScores.slice(0, limit);

        const result = LandingPageHelpers.buildPaginationResponse(
          paginatedPrograms,
          page,
          limit,
          total
        );

        return res.status(200).json({
          error: false,
          message:
            "Personalized program recommendations retrieved successfully",
          user_preferences: {
            fitness_goals: userPreferences.fitness_goals,
            level: userPreferences.level,
          },
          ...result,
        });
      } catch (error) {
        console.error("Error fetching personalized programs:", error);
        return res.status(500).json({
          error: true,
          message:
            "Internal server error while fetching personalized recommendations",
        });
      }
    }
  );

  // All programs endpoint with comprehensive filtering and search
  app.get("/v2/api/kanglink/custom/landing/all-programs", async (req, res) => {
    try {
      const sdk = app.get("sdk");
      sdk.setProjectId("kanglink");

      // Parse query parameters
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);
      const search = req.query.search?.trim();
      const category = req.query.category;
      const level = req.query.level; // beginner, intermediate, expert
      const minRating = parseFloat(req.query.min_rating) || 0;
      const maxRating = parseFloat(req.query.max_rating) || 5;
      const sortBy = req.query.sort_by || "created_at"; // created_at, rating, name, popularity, price
      const sortOrder = req.query.sort_order || "desc"; // asc, desc
      const hasPreview = req.query.has_preview === "true";
      const gender = req.query.gender?.split(",").filter(Boolean) || [];
      const experience = req.query.experience?.split(",").filter(Boolean) || [];

      // Check for optional authentication for personalized sorting
      let userId = null;
      let userPreferences = null;

      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith("Bearer ")) {
        try {
          const config = app.get("configuration");
          const token = authHeader.split(" ")[1];
          const decoded =
            require("../../../baas/services/JwtService").verifyAccessToken(
              token,
              config.jwt_key
            );

          if (decoded && decoded.user_id) {
            userId = decoded.user_id;
            const user = await sdk.findOne("user", { id: userId });
            if (user && user.data) {
              userPreferences = LandingPageHelpers.getUserPreferences(
                user.data
              );
            }
          }
        } catch (error) {
          console.log("Invalid token provided, continuing without auth");
        }
      }

      const projectId = "kanglink";
      const offset = (page - 1) * limit;

      // Build WHERE conditions
      let whereConditions = [];

      // Check if status column exists before using it
      const statusColumnExists =
        await LandingPageHelpers.checkStatusColumnExists(sdk, projectId);
      if (statusColumnExists) {
        whereConditions.push(`p.status = 'published'`);
      }

      if (search) {
        const searchTerm = search.replace(/'/g, "''"); // Escape single quotes
        whereConditions.push(`(
          p.program_name LIKE '%${searchTerm}%'
          OR p.program_description LIKE '%${searchTerm}%'
          OR p.type_of_program LIKE '%${searchTerm}%'
          OR CONCAT(pref.first_name, ' ', pref.last_name) LIKE '%${searchTerm}%'
        )`);
      }

      if (category) {
        whereConditions.push(`p.type_of_program = '${category}'`);
      }

      if (level) {
        whereConditions.push(
          `JSON_EXTRACT(p.target_levels, '$[*]') LIKE '%${level}%'`
        );
      }

      if (hasPreview) {
        whereConditions.push(`p.days_for_preview > 0`);
      }

      // Gender filter - filter by trainer gender
      if (gender.length > 0) {
        const genderConditions = gender.map(g => {
          // Map filter values to actual data format
          const genderMap = {
            man: 'Man',
            woman: 'Woman',
            non_binary: 'Non-Binary',
            transgender_woman: 'Transgender Woman',
            transgender_man: 'Transgender Man',
            other: 'Other'
          };
          const actualGender = genderMap[g] || g;
          return `JSON_EXTRACT(u.data, '$.gender') = '${actualGender}'`;
        }).join(" OR ");
        whereConditions.push(`(${genderConditions})`);
      }

      // Experience filter - filter by trainer experience
      if (experience.length > 0) {
        const experienceConditions = experience.map(exp => {
          switch (exp) {  
            case "less_than_1_year":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%less than 1 year%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%0-1 year%'`;
            case "1_2_years":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%1-2 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%1 year%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%2 years%'`;
            case "3_5_years":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%3-5 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%3 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%4 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%5 years%'`;
            case "5_7_years":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%5-7 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%6 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%7 years%'`;
            case "6_10_years":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%6-10 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%8 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%9 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%10 years%'`;
            case "10_plus_years":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%10+ years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%11 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%12 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%15 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%20 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%25 years%'`;
            default:
              return "";
          }
        }).filter(Boolean).join(" OR ");
        if (experienceConditions) {
          whereConditions.push(`(${experienceConditions})`);
        }
      }

      // Rating filter will be applied in WHERE clause since we're using LEFT JOIN
      if (minRating > 0) {
        whereConditions.push(
          `(ratings.average_rating IS NULL OR ratings.average_rating >= ${minRating})`
        );
      }
      if (maxRating < 5) {
        whereConditions.push(
          `(ratings.average_rating IS NULL OR ratings.average_rating <= ${maxRating})`
        );
      }

      // Build ORDER BY clause
      let orderByClause = "";
      switch (sortBy) {
        case "rating":
          orderByClause = `COALESCE(ratings.average_rating, 0) ${sortOrder.toUpperCase()}, ratings.review_count DESC`;
          break;
        case "name":
          orderByClause = `p.program_name ${sortOrder.toUpperCase()}`;
          break;
        case "popularity":
          orderByClause = `ratings.review_count ${sortOrder.toUpperCase()}, COALESCE(ratings.average_rating, 0) DESC`;
          break;
        case "price":
          orderByClause = `MIN(s.full_price) ${sortOrder.toUpperCase()}, COALESCE(ratings.average_rating, 0) DESC`;
          break;
        case "created_at":
        default:
          orderByClause = `p.created_at ${sortOrder.toUpperCase()}`;
          break;
      }

      const whereClause = whereConditions.join(" AND ");

      // Main query to get programs with ratings, trainer info, and pricing
      const programsQuery = `
        SELECT
          p.id,
          p.user_id,
          p.program_name,
          p.type_of_program,
          p.program_description,
          p.target_levels,
          p.currency,
          p.days_for_preview,
          p.image,
          p.created_at,
          p.updated_at,
          COALESCE(ratings.average_rating, 0) as average_rating,
          COALESCE(ratings.review_count, 0) as review_count,
          u.email as trainer_email,
          pref.first_name as trainer_first_name,
          pref.last_name as trainer_last_name,
          pref.photo as trainer_photo,
          u.data as trainer_data,
          MIN(s.full_price) as min_full_price,
          MAX(s.full_price) as max_full_price,
          MIN(s.subscription) as min_subscription_price,
          MAX(s.subscription) as max_subscription_price,
          MAX(week_counts.max_weeks) as max_weeks,
          GROUP_CONCAT(DISTINCT f.user_id) as favorite_user_ids
        FROM ${projectId}_program p
        LEFT JOIN (
          SELECT
            program_id,
            AVG(rating) as average_rating,
            COUNT(rating) as review_count
          FROM ${projectId}_post_feed
          WHERE post_type = 'review' AND rating IS NOT NULL
          GROUP BY program_id
        ) ratings ON p.id = ratings.program_id
        LEFT JOIN ${projectId}_user u ON p.user_id = u.id
        LEFT JOIN ${projectId}_preference pref ON u.id = pref.user_id
        LEFT JOIN ${projectId}_split s ON p.id = s.program_id
        LEFT JOIN (
          SELECT
            s.program_id,
            MAX(week_count) as max_weeks
          FROM ${projectId}_split s
          LEFT JOIN (
            SELECT
              split_id,
              COUNT(*) as week_count
            FROM ${projectId}_week
            GROUP BY split_id
          ) w ON s.id = w.split_id
          GROUP BY s.program_id
        ) week_counts ON p.id = week_counts.program_id
        LEFT JOIN ${projectId}_favorite f ON p.id = f.favorite_id AND f.favorite_type = 'program'
        WHERE ${whereClause}
        GROUP BY p.id, p.user_id, p.program_name, p.type_of_program, p.program_description,
                 p.target_levels, p.currency, p.days_for_preview, p.image, p.created_at,
                 p.updated_at, ratings.average_rating, ratings.review_count, u.email,
                 pref.first_name, pref.last_name, pref.photo, u.data, week_counts.max_weeks
        ORDER BY ${orderByClause}
        LIMIT ${limit} OFFSET ${offset}
      `;

      // Count query for pagination
      const countQuery = `
        SELECT COUNT(DISTINCT p.id) as total
        FROM ${projectId}_program p
        LEFT JOIN (
          SELECT
            program_id,
            AVG(rating) as average_rating,
            COUNT(rating) as review_count
          FROM ${projectId}_post_feed
          WHERE post_type = 'review' AND rating IS NOT NULL
          GROUP BY program_id
        ) ratings ON p.id = ratings.program_id
        LEFT JOIN ${projectId}_user u ON p.user_id = u.id
        LEFT JOIN ${projectId}_preference pref ON u.id = pref.user_id
        LEFT JOIN ${projectId}_split s ON p.id = s.program_id
        WHERE ${whereClause}
      `;

      const [programs, countResult] = await Promise.all([
        sdk.rawQuery(programsQuery),
        sdk.rawQuery(countQuery),
      ]);

      const total = countResult[0]?.total || 0;

      // Process programs and add match scores for authenticated users
      const processedPrograms = programs.map((program) => {
        // Format duration from weeks
        let formattedDuration = null;
        if (program.max_weeks && parseInt(program.max_weeks) > 0) {
          const weeks = parseInt(program.max_weeks);
          if (weeks === 1) {
            formattedDuration = "1 Week";
          } else {
            formattedDuration = `${weeks} Weeks`;
          }
        }

        const processedProgram = {
          id: program.id,
          user_id: program.user_id,
          program_name: program.program_name,
          type_of_program: program.type_of_program,
          program_description: program.program_description,
          target_levels: program.target_levels,
          currency: program.currency,
          days_for_preview: program.days_for_preview,
          image: program.image,
          duration: formattedDuration,
          created_at: program.created_at,
          updated_at: program.updated_at,
          rating: program.average_rating
            ? parseFloat(program.average_rating).toFixed(1)
            : "0.0",
          review_count: program.review_count,
          favorite: program.favorite_user_ids ? program.favorite_user_ids.split(',').map(id => parseInt(id)) : [],
          pricing: {
            full_price: {
              min: program.min_full_price,
              max: program.max_full_price,
            },
            subscription_price: {
              min: program.min_subscription_price,
              max: program.max_subscription_price,
            },
            currency: program.currency,
          },
          price: (() => {
            const prices = [];
            if (
              program.min_full_price &&
              parseFloat(program.min_full_price) > 0
            )
              prices.push(parseFloat(program.min_full_price));
            if (
              program.min_subscription_price &&
              parseFloat(program.min_subscription_price) > 0
            )
              prices.push(parseFloat(program.min_subscription_price));
            return prices.length > 0 ? Math.min(...prices) : 0;
          })(),
          trainer: {
            id: program.user_id,
            email: program.trainer_email,
            first_name: program.trainer_first_name,
            last_name: program.trainer_last_name,
            photo: program.trainer_photo,
            full_name: `${program.trainer_first_name || ""} ${
              program.trainer_last_name || ""
            }`.trim(),
          },
        };

        // Add match score for authenticated users
        if (userPreferences) {
          processedProgram.match_score =
            LandingPageHelpers.calculateProgramMatch(userPreferences, program);
        }

        return processedProgram;
      });

      const result = LandingPageHelpers.buildPaginationResponse(
        processedPrograms,
        page,
        limit,
        total
      );

      // Add filter summary to response
      const filterSummary = {
        search,
        category,
        level,
        gender,
        experience,
        min_rating: minRating,
        max_rating: maxRating,
        sort_by: sortBy,
        sort_order: sortOrder,
        has_preview: hasPreview,
      };

      return res.status(200).json({
        error: false,
        message: "Programs retrieved successfully",
        filters: filterSummary,
        ...result,
      });
    } catch (error) {
      console.error("Error fetching all programs:", error);
      return res.status(500).json({
        error: true,
        message: "Internal server error while fetching programs",
      });
    }
  });

  // Top rated trainers endpoint with optional authentication
  app.get(
    "/v2/api/kanglink/custom/landing/top-rated-trainers",
    async (req, res) => {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        // Parse query parameters
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 20, 50);
        const specialization = req.query.specialization; // Optional specialization filter
        const minRating = parseFloat(req.query.min_rating) || 0;
        const minExperience = parseInt(req.query.min_experience) || 0;

        // Check for optional authentication
        let userId = null;
        let userPreferences = null;

        const authHeader = req.headers.authorization;
        if (authHeader && authHeader.startsWith("Bearer ")) {
          try {
            const config = app.get("configuration");
            const token = authHeader.split(" ")[1];
            const decoded =
              require("../../../baas/services/JwtService").verifyAccessToken(
                token,
                config.jwt_key
              );

            if (decoded && decoded.user_id) {
              userId = decoded.user_id;

              // Get user preferences for personalized sorting
              const user = await sdk.findOne("user", { id: userId });
              if (user && user.data) {
                userPreferences = LandingPageHelpers.getUserPreferences(
                  user.data
                );
              }
            }
          } catch (error) {
            console.log("Invalid token provided, continuing without auth");
          }
        }

        // Try to get from cache first
        const cacheKey = `${
          LandingPageHelpers.CACHE_KEYS.TOP_RATED_TRAINERS
        }:${page}:${limit}:${
          specialization || "all"
        }:${minRating}:${minExperience}`;
        let cachedResult = null;

        try {
          const redis = await LandingPageHelpers.getRedisClient();
          const cached = await redis.get(cacheKey);
          if (cached) {
            cachedResult = JSON.parse(cached);
          }
        } catch (error) {
          console.log("Redis cache unavailable, proceeding without cache");
        }

        if (cachedResult && !userId) {
          return res.status(200).json({
            error: false,
            message: "Top rated trainers retrieved successfully",
            ...cachedResult,
          });
        }

        const projectId = "kanglink";
        const offset = (page - 1) * limit;

        // Build WHERE conditions
        let whereConditions = [
          `u.role_id = 'trainer'`,
          `u.status = 1`, // Active users only
          `trainer_ratings.review_count >= 1`, // Minimum 1 review (changed from 3)
          `trainer_ratings.average_rating >= ${minRating}`,
        ];

        // Add specialization filter if provided
        let specializationFilter = "";
        if (specialization) {
          specializationFilter = `AND JSON_EXTRACT(u.data, '$.specializations') LIKE '%${specialization}%'`;
        }

        // Add experience filter if provided
        let experienceFilter = "";
        if (minExperience > 0) {
          experienceFilter = `AND CAST(JSON_EXTRACT(u.data, '$.years_of_experience') AS UNSIGNED) >= ${minExperience}`;
        }

        const whereClause = whereConditions.join(" AND ");

        // Get trainers with ratings, program counts, and pricing information
        const trainersQuery = `
        SELECT
          u.id,
          u.email,
          u.data,
          u.created_at,
          u.updated_at,
          trainer_ratings.average_rating,
          trainer_ratings.review_count,
          trainer_ratings.program_count,
          GROUP_CONCAT(DISTINCT f.user_id) as favorite_user_ids,
          MIN(trainer_pricing.min_full_price) as min_full_price,
          MAX(trainer_pricing.max_full_price) as max_full_price,
          MIN(trainer_pricing.min_subscription_price) as min_subscription_price,
          MAX(trainer_pricing.max_subscription_price) as max_subscription_price,
          trainer_pricing.currency
        FROM ${projectId}_user u
        INNER JOIN (
          SELECT
            p.user_id as trainer_id,
            AVG(pf.rating) as average_rating,
            COUNT(pf.rating) as review_count,
            COUNT(DISTINCT p.id) as program_count
          FROM ${projectId}_program p
          INNER JOIN ${projectId}_post_feed pf ON p.id = pf.program_id
          WHERE pf.post_type = 'review' AND pf.rating IS NOT NULL AND p.status = 'published'
          GROUP BY p.user_id
          HAVING review_count >= 1
        ) trainer_ratings ON u.id = trainer_ratings.trainer_id
        LEFT JOIN (
          SELECT
            p.user_id as trainer_id,
            MIN(s.full_price) as min_full_price,
            MAX(s.full_price) as max_full_price,
            MIN(s.subscription) as min_subscription_price,
            MAX(s.subscription) as max_subscription_price,
            p.currency
          FROM ${projectId}_program p
          LEFT JOIN ${projectId}_split s ON p.id = s.program_id
          WHERE p.status = 'published'
          GROUP BY p.user_id, p.currency
        ) trainer_pricing ON u.id = trainer_pricing.trainer_id
        LEFT JOIN ${projectId}_favorite f ON u.id = f.favorite_id AND f.favorite_type = 'trainer'
        WHERE ${whereClause}
        ${specializationFilter}
        ${experienceFilter}
        GROUP BY u.id, u.email, u.data, u.created_at, u.updated_at, trainer_ratings.average_rating, trainer_ratings.review_count, trainer_ratings.program_count, trainer_pricing.currency
        ORDER BY trainer_ratings.average_rating DESC, trainer_ratings.review_count DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

        // Get total count for pagination
        const countQuery = `
        SELECT COUNT(*) as total
        FROM ${projectId}_user u
        INNER JOIN (
          SELECT
            p.user_id as trainer_id,
            AVG(pf.rating) as average_rating,
            COUNT(pf.rating) as review_count,
            COUNT(DISTINCT p.id) as program_count
          FROM ${projectId}_program p
          INNER JOIN ${projectId}_post_feed pf ON p.id = pf.program_id
          WHERE pf.post_type = 'review' AND pf.rating IS NOT NULL AND p.status = 'published'
          GROUP BY p.user_id
          HAVING review_count >= 1
        ) trainer_ratings ON u.id = trainer_ratings.trainer_id
        WHERE ${whereClause}
        ${specializationFilter}
        ${experienceFilter}
      `;

        const [trainers, countResult] = await Promise.all([
          sdk.rawQuery(trainersQuery),
          sdk.rawQuery(countQuery),
        ]);

        const total = countResult[0]?.total || 0;

        // Process trainers and add match scores for authenticated users
        const processedTrainers = trainers.map((trainer) => {
          let trainerData = {};
          try {
            trainerData = trainer.data ? JSON.parse(trainer.data) : {};
          } catch (error) {
            console.error("Error parsing trainer data:", error);
          }

          const processedTrainer = {
            id: trainer.id,
            email: trainer.email,
            first_name: trainerData.first_name || "",
            last_name: trainerData.last_name || "",
            full_name:
              trainerData.full_name ||
              `${trainerData.first_name || ""} ${
                trainerData.last_name || ""
              }`.trim(),
            photo: trainerData.photo || "",
            bio: trainerData.bio || "",
            specializations: trainerData.specializations || [],
            qualifications: trainerData.qualifications || [],
            years_of_experience: trainerData.years_of_experience || "0",
            rating: parseFloat(trainer.average_rating).toFixed(1),
            review_count: trainer.review_count,
            program_count: trainer.program_count,
            favorite: trainer.favorite_user_ids ? trainer.favorite_user_ids.split(',').map(id => parseInt(id)) : [],
            pricing: {
              full_price: {
                min: trainer.min_full_price,
                max: trainer.max_full_price,
              },
              subscription_price: {
                min: trainer.min_subscription_price,
                max: trainer.max_subscription_price,
              },
              currency: trainer.currency,
            },
            price: (() => {
              const prices = [];
              if (trainer.min_full_price && parseFloat(trainer.min_full_price) > 0)
                prices.push(parseFloat(trainer.min_full_price));
              if (trainer.min_subscription_price && parseFloat(trainer.min_subscription_price) > 0)
                prices.push(parseFloat(trainer.min_subscription_price));
              return prices.length > 0 ? Math.min(...prices) : 0;
            })(),
            created_at: trainer.created_at,
            updated_at: trainer.updated_at,
          };

          // Add match score for authenticated users
          if (userPreferences) {
            processedTrainer.match_score =
              LandingPageHelpers.calculateTrainerMatch(
                userPreferences,
                trainer.data
              );
          }

          return processedTrainer;
        });

        // Sort by match score if user is authenticated
        if (userPreferences) {
          processedTrainers.sort((a, b) => {
            if (b.match_score !== a.match_score) {
              return b.match_score - a.match_score;
            }
            return parseFloat(b.rating) - parseFloat(a.rating);
          });
        }

        const result = LandingPageHelpers.buildPaginationResponse(
          processedTrainers,
          page,
          limit,
          total
        );

        // Cache result for non-authenticated requests
        if (!userId) {
          try {
            const redis = await LandingPageHelpers.getRedisClient();
            await redis.set(
              cacheKey,
              JSON.stringify(result),
              "EX",
              LandingPageHelpers.CACHE_TTL.TRAINERS
            );
          } catch (error) {
            console.log("Failed to cache result:", error);
          }
        }

        return res.status(200).json({
          error: false,
          message: "Top rated trainers retrieved successfully",
          ...result,
        });
      } catch (error) {
        console.error("Error fetching top rated trainers:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error while fetching trainers",
        });
      }
    }
  );

  // Trainers you may like endpoint - requires authentication
  app.get(
    "/v2/api/kanglink/custom/landing/trainers-you-may-like",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async (req, res) => {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const userId = req.user_id;
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 20, 50);
        const minRating = parseFloat(req.query.min_rating) || 3.0;

        // Get user preferences
        const user = await sdk.findOne("user", { id: userId });
        if (!user || !user.data) {
          return res.status(400).json({
            error: true,
            message:
              "User preferences not found. Please complete your profile to get personalized recommendations.",
          });
        }

        const userPreferences = LandingPageHelpers.getUserPreferences(
          user.data
        );
        if (!userPreferences || !userPreferences.fitness_goals.length) {
          return res.status(400).json({
            error: true,
            message:
              "Please set your fitness goals in your profile to get personalized recommendations.",
          });
        }

        const projectId = "kanglink";
        const offset = (page - 1) * limit;

        // Get trainers with ratings, pricing, excluding the current user if they're a trainer
        const trainersQuery = `
          SELECT
            u.id,
            u.email,
            u.data,
            u.created_at,
            u.updated_at,
            COALESCE(trainer_ratings.average_rating, 0) as average_rating,
            COALESCE(trainer_ratings.review_count, 0) as review_count,
            COALESCE(trainer_ratings.program_count, 0) as program_count,
            GROUP_CONCAT(DISTINCT f.user_id) as favorite_user_ids,
            MIN(trainer_pricing.min_full_price) as min_full_price,
            MAX(trainer_pricing.max_full_price) as max_full_price,
            MIN(trainer_pricing.min_subscription_price) as min_subscription_price,
            MAX(trainer_pricing.max_subscription_price) as max_subscription_price,
            trainer_pricing.currency
          FROM ${projectId}_user u
          LEFT JOIN (
            SELECT
              p.user_id as trainer_id,
              AVG(pf.rating) as average_rating,
              COUNT(pf.rating) as review_count,
              COUNT(DISTINCT p.id) as program_count
            FROM ${projectId}_program p
            INNER JOIN ${projectId}_post_feed pf ON p.id = pf.program_id
            WHERE pf.post_type = 'review' AND pf.rating IS NOT NULL AND p.status = 'published'
            GROUP BY p.user_id
          ) trainer_ratings ON u.id = trainer_ratings.trainer_id
          LEFT JOIN (
            SELECT
              p.user_id as trainer_id,
              MIN(s.full_price) as min_full_price,
              MAX(s.full_price) as max_full_price,
              MIN(s.subscription) as min_subscription_price,
              MAX(s.subscription) as max_subscription_price,
              p.currency
            FROM ${projectId}_program p
            LEFT JOIN ${projectId}_split s ON p.id = s.program_id
            WHERE p.status = 'published'
            GROUP BY p.user_id, p.currency
          ) trainer_pricing ON u.id = trainer_pricing.trainer_id
          LEFT JOIN ${projectId}_favorite f ON u.id = f.favorite_id AND f.favorite_type = 'trainer'
          WHERE u.role_id = 'trainer'
            AND u.status = 1
            AND u.id != ${userId}
            AND (
              JSON_EXTRACT(u.data, '$.specializations') REGEXP '${userPreferences.fitness_goals.join(
                "|"
              )}'
              OR COALESCE(trainer_ratings.average_rating, 0) >= ${minRating}
            )
          GROUP BY u.id, u.email, u.data, u.created_at, u.updated_at, trainer_ratings.average_rating, trainer_ratings.review_count, trainer_ratings.program_count, trainer_pricing.currency
          ORDER BY COALESCE(trainer_ratings.average_rating, 0) DESC
          LIMIT ${limit * 2} OFFSET ${offset}
        `;

        // Get total count for pagination
        const countQuery = `
          SELECT COUNT(*) as total
          FROM ${projectId}_user u
          LEFT JOIN (
            SELECT
              p.user_id as trainer_id,
              AVG(pf.rating) as average_rating,
              COUNT(pf.rating) as review_count,
              COUNT(DISTINCT p.id) as program_count
            FROM ${projectId}_program p
            INNER JOIN ${projectId}_post_feed pf ON p.id = pf.program_id
            WHERE pf.post_type = 'review' AND pf.rating IS NOT NULL AND p.status = 'published'
            GROUP BY p.user_id
          ) trainer_ratings ON u.id = trainer_ratings.trainer_id
          WHERE u.role_id = 'trainer'
            AND u.status = 1
            AND u.id != ${userId}
            AND (
              JSON_EXTRACT(u.data, '$.specializations') REGEXP '${userPreferences.fitness_goals.join(
                "|"
              )}'
              OR COALESCE(trainer_ratings.average_rating, 0) >= ${minRating}
            )
        `;

        const [trainers, countResult] = await Promise.all([
          sdk.rawQuery(trainersQuery),
          sdk.rawQuery(countQuery),
        ]);

        const total = countResult[0]?.total || 0;

        // Calculate match scores and sort by relevance
        const trainersWithScores = trainers.map((trainer) => {
          let trainerData = {};
          try {
            trainerData = trainer.data ? JSON.parse(trainer.data) : {};
          } catch (error) {
            console.error("Error parsing trainer data:", error);
          }

          const matchScore = LandingPageHelpers.calculateTrainerMatch(
            userPreferences,
            trainer.data
          );

          return {
            id: trainer.id,
            email: trainer.email,
            first_name: trainerData.first_name || "",
            last_name: trainerData.last_name || "",
            full_name:
              trainerData.full_name ||
              `${trainerData.first_name || ""} ${
                trainerData.last_name || ""
              }`.trim(),
            photo: trainerData.photo || "",
            bio: trainerData.bio || "",
            specializations: trainerData.specializations || [],
            qualifications: trainerData.qualifications || [],
            years_of_experience: trainerData.years_of_experience || "0",
            rating: trainer.average_rating
              ? parseFloat(trainer.average_rating).toFixed(1)
              : "0.0",
            review_count: trainer.review_count,
            program_count: trainer.program_count,
            favorite: trainer.favorite_user_ids ? trainer.favorite_user_ids.split(',').map(id => parseInt(id)) : [],
            pricing: {
              full_price: {
                min: trainer.min_full_price,
                max: trainer.max_full_price,
              },
              subscription_price: {
                min: trainer.min_subscription_price,
                max: trainer.max_subscription_price,
              },
              currency: trainer.currency,
            },
            price: (() => {
              const prices = [];
              if (trainer.min_full_price && parseFloat(trainer.min_full_price) > 0)
                prices.push(parseFloat(trainer.min_full_price));
              if (trainer.min_subscription_price && parseFloat(trainer.min_subscription_price) > 0)
                prices.push(parseFloat(trainer.min_subscription_price));
              return prices.length > 0 ? Math.min(...prices) : 0;
            })(),
            match_score: matchScore,
            created_at: trainer.created_at,
            updated_at: trainer.updated_at,
          };
        });

        // Sort by match score (descending), then by rating (descending)
        trainersWithScores.sort((a, b) => {
          if (b.match_score !== a.match_score) {
            return b.match_score - a.match_score;
          }
          return parseFloat(b.rating) - parseFloat(a.rating);
        });

        // Take only the requested page size after sorting
        const paginatedTrainers = trainersWithScores.slice(0, limit);

        const result = LandingPageHelpers.buildPaginationResponse(
          paginatedTrainers,
          page,
          limit,
          total
        );

        return res.status(200).json({
          error: false,
          message:
            "Personalized trainer recommendations retrieved successfully",
          user_preferences: {
            fitness_goals: userPreferences.fitness_goals,
            level: userPreferences.level,
          },
          ...result,
        });
      } catch (error) {
        console.error("Error fetching personalized trainers:", error);
        return res.status(500).json({
          error: true,
          message:
            "Internal server error while fetching personalized trainer recommendations",
        });
      }
    }
  );

  // All trainers endpoint with comprehensive filtering and search
  app.get("/v2/api/kanglink/custom/landing/all-trainers", async (req, res) => {
    try {
      const sdk = app.get("sdk");
      sdk.setProjectId("kanglink");

      // Parse query parameters
      const page = parseInt(req.query.page) || 1;
      const limit = Math.min(parseInt(req.query.limit) || 20, 50);
      const search = req.query.search?.trim();
      const specialization = req.query.specialization;
      const minRating = parseFloat(req.query.min_rating) || 0;
      const maxRating = parseFloat(req.query.max_rating) || 5;
      const minExperience = parseInt(req.query.min_experience) || 0;
      const maxExperience = parseInt(req.query.max_experience) || 50;
      const sortBy = req.query.sort_by || "created_at"; // created_at, rating, name, experience, price
      const sortOrder = req.query.sort_order || "desc"; // asc, desc
      const hasPrograms = req.query.has_programs === "true";
      const gender = req.query.gender?.split(",").filter(Boolean) || [];
      const experience = req.query.experience?.split(",").filter(Boolean) || [];

      // Check for optional authentication for personalized sorting
      let userId = null;
      let userPreferences = null;

      const authHeader = req.headers.authorization;
      if (authHeader && authHeader.startsWith("Bearer ")) {
        try {
          const config = app.get("configuration");
          const token = authHeader.split(" ")[1];
          const decoded =
            require("../../../baas/services/JwtService").verifyAccessToken(
              token,
              config.jwt_key
            );

          if (decoded && decoded.user_id) {
            userId = decoded.user_id;
            const user = await sdk.findOne("user", { id: userId });
            if (user && user.data) {
              userPreferences = LandingPageHelpers.getUserPreferences(
                user.data
              );
            }
          }
        } catch (error) {
          console.log("Invalid token provided, continuing without auth");
        }
      }

      const projectId = "kanglink";
      const offset = (page - 1) * limit;

      // Build WHERE conditions
      let whereConditions = [
        `u.role_id = 'trainer'`,
        `u.status = 1`, // Active users only
      ];

      if (search) {
        const searchTerm = search.replace(/'/g, "''"); // Escape single quotes
        // the first, last and full name has to be case insensitive, and it doesn't have to match the full text, as long as it contains the search term
        // JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.first_name')), ' ', JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.last_name')), ' ', 
        whereConditions.push(`(
          LOWER(CONCAT(JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.full_name')))) LIKE '%${searchTerm.toLowerCase()}%'
          OR LOWER(CONCAT(JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.first_name')))) LIKE '%${searchTerm.toLowerCase()}%'
          OR LOWER(CONCAT(JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.last_name')))) LIKE '%${searchTerm.toLowerCase()}%'
          OR u.email LIKE '%${searchTerm}%'
          OR JSON_EXTRACT(u.data, '$.bio') LIKE '%${searchTerm}%'
          OR JSON_EXTRACT(u.data, '$.specializations') LIKE '%${searchTerm}%'
        )`);
      }

      if (specialization) {
        whereConditions.push(
          `JSON_EXTRACT(u.data, '$.specializations') LIKE '%${specialization}%'`
        );
      }

      // Gender filter
      if (gender.length > 0) {
        const genderConditions = gender.map(g => {
          // Map filter values to actual data format
          const genderMap = {
            man: 'Man',
            woman: 'Woman',
            non_binary: 'Non-Binary',
            transgender_woman: 'Transgender Woman',
            transgender_man: 'Transgender Man',
            other: 'Other'
          };
          const actualGender = genderMap[g] || g;
          return `JSON_EXTRACT(u.data, '$.gender') = '${actualGender}'`;
        }).join(" OR ");
        whereConditions.push(`(${genderConditions})`);
      }

      // Experience filter
      if (experience.length > 0) {
        const experienceConditions = experience.map(exp => {
          switch (exp) {
            case "less_than_1_year":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%less than 1 year%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%0-1 year%'`;
            case "1_2_years":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%1-2 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%1 year%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%2 years%'`;
            case "3_5_years":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%3-5 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%3 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%4 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%5 years%'`;
            case "5_7_years":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%5-7 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%6 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%7 years%'`;
            case "6_10_years":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%6-10 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%8 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%9 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%10 years%'`;
            case "10_plus_years":
              return `JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%10+ years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%11 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%12 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%15 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%20 years%' OR JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%25 years%'`;
            default:
              return "";
          }
        }).filter(Boolean).join(" OR ");
        if (experienceConditions) {
          whereConditions.push(`(${experienceConditions})`);
        }
      }

      if (hasPrograms) {
        whereConditions.push(`trainer_ratings.program_count > 0`);
      }

      // Experience filter
      let experienceFilter = "";
      if (minExperience > 0 || maxExperience < 50) {
        experienceFilter = `AND CAST(COALESCE(JSON_EXTRACT(u.data, '$.years_of_experience'), '0') AS UNSIGNED) BETWEEN ${minExperience} AND ${maxExperience}`;
      }

      // Rating filter will be applied after JOIN
      let havingConditions = [];
      if (minRating > 0) {
        havingConditions.push(
          `COALESCE(trainer_ratings.average_rating, 0) >= ${minRating}`
        );
      }
      if (maxRating < 5) {
        havingConditions.push(
          `COALESCE(trainer_ratings.average_rating, 0) <= ${maxRating}`
        );
      }

      // Build ORDER BY clause
      let orderByClause = "";
      switch (sortBy) {
        case "rating":
          orderByClause = `COALESCE(trainer_ratings.average_rating, 0) ${sortOrder.toUpperCase()}, trainer_ratings.review_count DESC`;
          break;
        case "name":
          // JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.first_name')), ' ', JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.last_name')), ' ', 
          orderByClause = `LOWER(CONCAT(JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.full_name')))) ${sortOrder.toUpperCase()}`;
          break;
        case "experience":
          orderByClause = `CAST(COALESCE(JSON_EXTRACT(u.data, '$.years_of_experience'), '0') AS UNSIGNED) ${sortOrder.toUpperCase()}`;
          break;
        case "price":
          orderByClause = `MIN(trainer_pricing.min_full_price) ${sortOrder.toUpperCase()}, COALESCE(trainer_ratings.average_rating, 0) DESC`;
          break;
        case "created_at":
        default:
          orderByClause = `u.created_at ${sortOrder.toUpperCase()}`;
          break;
      }

      const whereClause = whereConditions.join(" AND ");
      const havingClause =
        havingConditions.length > 0
          ? `HAVING ${havingConditions.join(" AND ")}`
          : "";

      // Main query to get trainers with ratings, program counts, and pricing information
      const trainersQuery = `
        SELECT
          u.id,
          u.email,
          u.data,
          u.created_at,
          u.updated_at,
          COALESCE(trainer_ratings.average_rating, 0) as average_rating,
          COALESCE(trainer_ratings.review_count, 0) as review_count,
          COALESCE(trainer_ratings.program_count, 0) as program_count,
          GROUP_CONCAT(DISTINCT f.user_id) as favorite_user_ids,
          MIN(trainer_pricing.min_full_price) as min_full_price,
          MAX(trainer_pricing.max_full_price) as max_full_price,
          MIN(trainer_pricing.min_subscription_price) as min_subscription_price,
          MAX(trainer_pricing.max_subscription_price) as max_subscription_price,
          trainer_pricing.currency
        FROM ${projectId}_user u
        LEFT JOIN (
          SELECT
            p.user_id as trainer_id,
            AVG(pf.rating) as average_rating,
            COUNT(pf.rating) as review_count,
            COUNT(DISTINCT p.id) as program_count
          FROM ${projectId}_program p
          LEFT JOIN ${projectId}_post_feed pf ON p.id = pf.program_id AND pf.post_type = 'review' AND pf.rating IS NOT NULL
          WHERE p.status = 'published'
          GROUP BY p.user_id
        ) trainer_ratings ON u.id = trainer_ratings.trainer_id
        LEFT JOIN (
          SELECT
            p.user_id as trainer_id,
            MIN(s.full_price) as min_full_price,
            MAX(s.full_price) as max_full_price,
            MIN(s.subscription) as min_subscription_price,
            MAX(s.subscription) as max_subscription_price,
            p.currency
          FROM ${projectId}_program p
          LEFT JOIN ${projectId}_split s ON p.id = s.program_id
          WHERE p.status = 'published'
          GROUP BY p.user_id, p.currency
        ) trainer_pricing ON u.id = trainer_pricing.trainer_id
        LEFT JOIN ${projectId}_favorite f ON u.id = f.favorite_id AND f.favorite_type = 'trainer'
        WHERE ${whereClause}
        ${experienceFilter}
        ${havingClause}
        GROUP BY u.id, u.email, u.data, u.created_at, u.updated_at, trainer_ratings.average_rating, trainer_ratings.review_count, trainer_ratings.program_count, trainer_pricing.currency
        ORDER BY ${orderByClause}
        LIMIT ${limit} OFFSET ${offset}
      `;

      // Count query for pagination
      const countQuery = `
        SELECT COUNT(*) as total
        FROM ${projectId}_user u
        LEFT JOIN (
          SELECT
            p.user_id as trainer_id,
            AVG(pf.rating) as average_rating,
            COUNT(pf.rating) as review_count,
            COUNT(DISTINCT p.id) as program_count
          FROM ${projectId}_program p
          LEFT JOIN ${projectId}_post_feed pf ON p.id = pf.program_id AND pf.post_type = 'review' AND pf.rating IS NOT NULL
          WHERE p.status = 'published'
          GROUP BY p.user_id
        ) trainer_ratings ON u.id = trainer_ratings.trainer_id
        WHERE ${whereClause}
        ${experienceFilter}
        ${havingClause}
      `;

      const [trainers, countResult] = await Promise.all([
        sdk.rawQuery(trainersQuery),
        sdk.rawQuery(countQuery),
      ]);

      const total = countResult[0]?.total || 0;

      // Process trainers and add match scores for authenticated users
      const processedTrainers = trainers.map((trainer) => {
        let trainerData = {};
        try {
          trainerData = trainer.data ? JSON.parse(trainer.data) : {};
        } catch (error) {
          console.error("Error parsing trainer data:", error);
        }

        const processedTrainer = {
          id: trainer.id,
          email: trainer.email,
          first_name: trainerData.first_name || "",
          last_name: trainerData.last_name || "",
          full_name:
            trainerData.full_name ||
            `${trainerData.first_name || ""} ${
              trainerData.last_name || ""
            }`.trim(),
          photo: trainerData.photo || "",
          bio: trainerData.bio || "",
          specializations: trainerData.specializations || [],
          qualifications: trainerData.qualifications || [],
          years_of_experience: trainerData.years_of_experience || "0",
          rating: trainer.average_rating
            ? parseFloat(trainer.average_rating).toFixed(1)
            : "0.0",
          review_count: trainer.review_count,
          program_count: trainer.program_count,
          favorite: trainer.favorite_user_ids ? trainer.favorite_user_ids.split(',').map(id => parseInt(id)) : [],
          pricing: {
            full_price: {
              min: trainer.min_full_price,
              max: trainer.max_full_price,
            },
            subscription_price: {
              min: trainer.min_subscription_price,
              max: trainer.max_subscription_price,
            },
            currency: trainer.currency,
          },
          price: (() => {
            const prices = [];
            if (trainer.min_full_price && parseFloat(trainer.min_full_price) > 0)
              prices.push(parseFloat(trainer.min_full_price));
            if (trainer.min_subscription_price && parseFloat(trainer.min_subscription_price) > 0)
              prices.push(parseFloat(trainer.min_subscription_price));
            return prices.length > 0 ? Math.min(...prices) : 0;
          })(),
          created_at: trainer.created_at,
          updated_at: trainer.updated_at,
        };

        // Add match score for authenticated users
        if (userPreferences) {
          processedTrainer.match_score =
            LandingPageHelpers.calculateTrainerMatch(
              userPreferences,
              trainer.data
            );
        }

        return processedTrainer;
      });

      const result = LandingPageHelpers.buildPaginationResponse(
        processedTrainers,
        page,
        limit,
        total
      );

      // Add filter summary to response
      const filterSummary = {
        search,
        specialization,
        gender,
        experience,
        min_rating: minRating,
        max_rating: maxRating,
        min_experience: minExperience,
        max_experience: maxExperience,
        sort_by: sortBy,
        sort_order: sortOrder,
        has_programs: hasPrograms,
      };

      return res.status(200).json({
        error: false,
        message: "Trainers retrieved successfully",
        filters: filterSummary,
        ...result,
      });
    } catch (error) {
      console.error("Error fetching all trainers:", error);
      return res.status(500).json({
        error: true,
        message: "Internal server error while fetching trainers",
      });
    }
  });
};
