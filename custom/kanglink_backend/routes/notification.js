const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const NotificationService = require("../services/NotificationService");

module.exports = function (app) {
  // Common notification endpoints for all roles
  // Get user notifications
  app.get(
    "/v2/api/kanglink/custom/notifications",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const userId = req.user_id;
        const { page = 1, limit = 20, unread_only = false } = req.query;

        sdk.setProjectId("kanglink");

        const notificationService = new NotificationService(sdk);
        const result = await notificationService.getUserNotifications(
          userId,
          parseInt(page),
          parseInt(limit),
          unread_only === "true"
        );

        if (!result.success) {
          return res.status(500).json({
            error: true,
            message: result.error || "Failed to fetch notifications",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Notifications retrieved successfully",
          data: result,
        });
      } catch (error) {
        console.error("Error fetching notifications:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch notifications",
        });
      }
    }
  );

  // Mark notification as read
  app.put(
    "/v2/api/kanglink/custom/notifications/:id/read",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const userId = req.user_id;
        const notificationId = req.params.id;

        sdk.setProjectId("kanglink");

        // Verify notification belongs to user
        const notification = await sdk.rawQuery(`
          SELECT id FROM kanglink_notification
          WHERE id = ? AND user_id = ?
        `, [notificationId, userId]);

        if (!notification || notification.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Notification not found",
          });
        }

        const notificationService = new NotificationService(sdk);
        const success = await notificationService.markNotificationAsRead(notificationId, userId);

        if (!success) {
          return res.status(500).json({
            error: true,
            message: "Failed to mark notification as read",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Notification marked as read",
        });
      } catch (error) {
        console.error("Error marking notification as read:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to mark notification as read",
        });
      }
    }
  );

  // Mark all notifications as read
  app.put(
    "/v2/api/kanglink/custom/notifications/read-all",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const userId = req.user_id;

        sdk.setProjectId("kanglink");

        const notificationService = new NotificationService(sdk);
        const success = await notificationService.markAllNotificationsAsRead(userId);

        if (!success) {
          return res.status(500).json({
            error: true,
            message: "Failed to mark all notifications as read",
          });
        }

        return res.status(200).json({
          error: false,
          message: "All notifications marked as read",
        });
      } catch (error) {
        console.error("Error marking all notifications as read:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to mark all notifications as read",
        });
      }
    }
  );

  // Get unread notification count
  app.get(
    "/v2/api/kanglink/custom/notifications/unread-count",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const userId = req.user_id;

        sdk.setProjectId("kanglink");

        const notificationService = new NotificationService(sdk);
        const count = await notificationService.getUnreadNotificationCount(userId);

        return res.status(200).json({
          error: false,
          message: "Unread count retrieved successfully",
          data: {
            unread_count: count,
          },
        });
      } catch (error) {
        console.error("Error getting unread notification count:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to get unread notification count",
        });
      }
    }
  );
}; 