module.exports = function (app) {
  app.get(
    "/v2/api/kanglink/custom/public/certifications",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const result = await sdk.rawQuery(`
          SELECT * FROM kanglink_qualification
        `);

        return res.status(200).json({
          error: false,
          message: "Certifications retrieved successfully",
          data: result,
        });

      } catch (error) {
        console.error("Error fetching certifications:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch certifications",
        });
      }
    }
  );

  app.get(
    "/v2/api/kanglink/custom/public/specializations",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const result = await sdk.rawQuery(`
          SELECT * FROM kanglink_specialization
        `);

        return res.status(200).json({
          error: false,
          message: "Specializations retrieved successfully",
          data: result,
        });
      } catch (error) {
        console.error("Error fetching specializations:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch specializations",
        });
      }
    }
  );



}; 