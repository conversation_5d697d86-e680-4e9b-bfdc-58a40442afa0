const AuthService = require("../../../baas/services/AuthService");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const UtilService = require("../../../baas/services/UtilService");
const StripeService = require("../../../baas/services/StripeService");

module.exports = function (app) {
  const stripe = new StripeService();

  // Create complete program with splits, weeks, days, sessions, and exercises
  app.post(
    "/v2/api/kanglink/custom/trainer/programs/:program_status",
    [TokenMiddleware({ role: "trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { data, image } = req.body;
        const programStatus = req.params.program_status;
        const userId = req.user_id;

        // Validate required data
        if (!data) {
          return res.status(400).json({
            error: true,
            message: "Data is required",
          });
        }

        sdk.setProjectId("kanglink");

        // Track created records for potential rollback
        const createdRecords = {
          program: null,
          splits: [],
          weeks: [],
          days: [],
          sessions: [],
          exercises: [],
        };

        try {
          // Step 1: Create the main program
          const now = new Date();
          const programData = {
            user_id: userId,
            program_name: data.program_name,
            type_of_program: data.type_of_program,
            program_description: data.program_description,
            payment_plan: JSON.stringify(data.payment_plan),
            track_progress: data.track_progress,
            allow_comments: data.allow_comments,
            allow_private_messages: data.allow_private_messages,
            target_levels: JSON.stringify(data.target_levels),
            split_program: data.split_program,
            currency: data.currency,
            image,
            days_for_preview: data.days_for_preview,
            status: programStatus,
            created_at: UtilService.sqlDateTimeFormat(now),
            updated_at: UtilService.sqlDateTimeFormat(now),
          };

          sdk.setTable("program");
          const program = await sdk.create("program", programData);
          createdRecords.program = program;

          // Step 2: Create splits
          for (const splitData of data.splits) {
            const split = {
              program_id: program.id,
              title: splitData.title,
              full_price: splitData.full_price,
              subscription: splitData.subscription,
              user_id: userId,
              equipment_required: splitData.equipment_required,
              created_at: UtilService.sqlDateTimeFormat(now),
              updated_at: UtilService.sqlDateTimeFormat(now),
            };

            sdk.setTable("split");
            const createdSplit = await sdk.create("split", split);
            createdRecords.splits.push({ ...createdSplit });

            // Create Stripe products and prices for this split
            await createStripeProductsForSplit(
              stripe,
              sdk,
              createdSplit,
              program.id,
              data.currency || "USD"
            );

            // Step 3: Create weeks for this split
            if (splitData.weeks && splitData.weeks.length > 0) {
              for (const weekData of splitData.weeks) {
                const week = {
                  split_id: createdSplit.id,
                  title: weekData.title,
                  user_id: userId,
                  week_order: weekData.week_order,
                  //   equipment_required: weekData.equipment_required || "",
                  created_at: UtilService.sqlDateTimeFormat(now),
                  updated_at: UtilService.sqlDateTimeFormat(now),
                };

                sdk.setTable("week");
                const createdWeek = await sdk.create("week", week);
                createdRecords.weeks.push(createdWeek);

                // Step 4: Create days for this week
                for (const dayData of weekData.days) {
                  const day = {
                    week_id: createdWeek.id,
                    title: dayData.title,
                    user_id: userId,
                    day_order: dayData.day_order,
                    is_rest_day: dayData.is_rest_day,
                    created_at: UtilService.sqlDateTimeFormat(now),
                    updated_at: UtilService.sqlDateTimeFormat(now),
                  };

                  sdk.setTable("day");
                  const createdDay = await sdk.create("day", day);
                  createdRecords.days.push(createdDay);

                  // Step 5: Create sessions for this day
                  for (const sessionData of dayData.sessions) {
                    const session = {
                      day_id: createdDay.id,
                      title: sessionData.title,
                      user_id: userId,
                      session_order: sessionData.session_order,
                      created_at: UtilService.sqlDateTimeFormat(now),
                      updated_at: UtilService.sqlDateTimeFormat(now),
                    };

                    sdk.setTable("session");
                    const createdSession = await sdk.create("session", session);
                    createdRecords.sessions.push(createdSession);

                    // Step 6: Create exercise instances for this session
                    for (const exerciseData of sessionData.exercises) {
                      const exerciseInstance = {
                        user_id: userId,
                        session_id: createdSession.id,
                        exercise_id: exerciseData.exercise_id || null,
                        exercise_name: exerciseData.exercise_name || null,
                        video_url: exerciseData.video_url || null,
                        sets: exerciseData.sets,
                        reps_or_time: exerciseData.reps_or_time,
                        reps_time_type: exerciseData.reps_time_type,
                        time_minutes: exerciseData.time_minutes || null,
                        time_seconds: exerciseData.time_seconds || null,
                        exercise_details: exerciseData.exercise_details,
                        rest_duration_minutes:
                          exerciseData.rest_duration_minutes,
                        rest_duration_seconds:
                          exerciseData.rest_duration_seconds,
                        label: exerciseData.label,
                        label_number: exerciseData.label_number,
                        is_linked: exerciseData.is_linked,
                        exercise_order: exerciseData.exercise_order,
                        created_at: UtilService.sqlDateTimeFormat(now),
                        updated_at: UtilService.sqlDateTimeFormat(now),
                      };

                      sdk.setTable("exercise_instance");
                      const createdExercise = await sdk.create(
                        "exercise_instance",
                        exerciseInstance
                      );
                      createdRecords.exercises.push(createdExercise);
                    }
                  }
                }
              }
            }
          }

          // Success - return the program ID
          return res.status(200).json({
            error: false,
            message: "Program created successfully",
            data: program.id,
          });
        } catch (createError) {
          // Rollback created records in reverse order
          console.error(
            "Error during program creation, attempting rollback:",
            createError
          );

          try {
            // Delete exercise instances
            for (const exercise of createdRecords.exercises.reverse()) {
              try {
                sdk.setTable("exercise_instance");
                await sdk.delete("exercise_instance", { id: exercise.id });
              } catch (rollbackError) {
                console.error("Rollback error for exercise:", rollbackError);
              }
            }



            // Delete sessions
            for (const session of createdRecords.sessions.reverse()) {
              try {
                sdk.setTable("session");
                await sdk.delete("session", { id: session.id });
              } catch (rollbackError) {
                console.error("Rollback error for session:", rollbackError);
              }
            }

            // Delete days
            for (const day of createdRecords.days.reverse()) {
              try {
                sdk.setTable("day");
                await sdk.delete("day", { id: day.id });
              } catch (rollbackError) {
                console.error("Rollback error for day:", rollbackError);
              }
            }

            // Delete weeks
            for (const week of createdRecords.weeks.reverse()) {
              try {
                sdk.setTable("week");
                await sdk.delete("week", { id: week.id });
              } catch (rollbackError) {
                console.error("Rollback error for week:", rollbackError);
              }
            }

            // Delete splits
            for (const split of createdRecords.splits.reverse()) {
              try {
                sdk.setTable("split");
                await sdk.delete("split", { id: split.id });
              } catch (rollbackError) {
                console.error("Rollback error for split:", rollbackError);
              }
            }

            // Delete program
            if (createdRecords.program) {
              try {
                sdk.setTable("program");
                await sdk.delete("program", { id: createdRecords.program.id });
              } catch (rollbackError) {
                console.error("Rollback error for program:", rollbackError);
              }
            }
          } catch (rollbackError) {
            console.error("Critical error during rollback:", rollbackError);
          }

          throw createError;
        }
      } catch (err) {
        console.error("Program creation error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to create program",
        });
      }
    }
  );

  // Update complete program with splits, weeks, days, sessions, and exercises
  app.put(
    "/v2/api/kanglink/custom/trainer/programs/:program_id",
    [TokenMiddleware({ role: "trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { data, status, image } = req.body;
        const programId = req.params.program_id;
        const userId = req.user_id;

        // Validate required data
        if (!data) {
          return res.status(400).json({
            error: true,
            message: "Data is required",
          });
        }

        sdk.setProjectId("kanglink");

        // Check if program exists and user owns it
        sdk.setTable("program");
        const existingProgram = await sdk.findOne("program", { id: programId });

        if (!existingProgram) {
          return res.status(404).json({
            error: true,
            message: "Program not found",
          });
        }

        if (existingProgram.user_id !== userId) {
          return res.status(403).json({
            error: true,
            message: "You can only update your own programs",
          });
        }

        try {
          // Step 1: Update the main program
          const now = new Date();
          const programData = {
            program_name: data.program_name,
            type_of_program: data.type_of_program,
            program_description: data.program_description,
            payment_plan: JSON.stringify(data.payment_plan),
            track_progress: data.track_progress,
            allow_comments: data.allow_comments,
            allow_private_messages: data.allow_private_messages,
            target_levels: JSON.stringify(data.target_levels),
            split_program: data.split_program,
            currency: data.currency,
            status: status ?? existingProgram.status,
            image,
            days_for_preview: data.days_for_preview,
            updated_at: UtilService.sqlDateTimeFormat(now),
          };

          await sdk.updateById("program", programId, programData);

          // // Step 2: Delete existing program structure (in reverse order)
          // // Delete exercise instances
          // await sdk.rawQuery(`
          //   DELETE ei FROM kanglink_exercise_instance ei
          //   JOIN kanglink_session s ON ei.session_id = s.id
          //   JOIN kanglink_day d ON s.day_id = d.id
          //   JOIN kanglink_week w ON d.week_id = w.id
          //   JOIN kanglink_split sp ON w.split_id = sp.id
          //   WHERE sp.program_id = ${programId}
          // `);

          // // Delete sessions
          // await sdk.rawQuery(`
          //   DELETE s FROM kanglink_session s
          //   JOIN kanglink_day d ON s.day_id = d.id
          //   JOIN kanglink_week w ON d.week_id = w.id
          //   JOIN kanglink_split sp ON w.split_id = sp.id
          //   WHERE sp.program_id = ${programId}
          // `);

          // // Delete days
          // await sdk.rawQuery(`
          //   DELETE d FROM kanglink_day d
          //   JOIN kanglink_week w ON d.week_id = w.id
          //   JOIN kanglink_split sp ON w.split_id = sp.id
          //   WHERE sp.program_id = ${programId}
          // `);

          // // Delete weeks
          // await sdk.rawQuery(`
          //   DELETE w FROM kanglink_week w
          //   JOIN kanglink_split sp ON w.split_id = sp.id
          //   WHERE sp.program_id = ${programId}
          // `);

          // Delete splits (but keep track of them for Stripe cleanup)
          // const existingSplits = await sdk.rawQuery(`
          //   SELECT * FROM kanglink_split WHERE program_id = ${programId}
          // `);

          // await sdk.rawQuery(`
          //   DELETE FROM kanglink_split WHERE program_id = ${programId}
          // `);

          // Step 3: Create new splits and structure (same as POST endpoint)
          for (const splitData of data.splits) {
            const split = {
              program_id: programId,
              title: splitData.title,
              full_price: splitData.full_price,
              subscription: splitData.subscription,
              user_id: userId,
              equipment_required: splitData.equipment_required,
              created_at: UtilService.sqlDateTimeFormat(now),
              updated_at: UtilService.sqlDateTimeFormat(now),
            };

            sdk.setTable("split");

            // create or update split
            // find if split exists
            const splitId = Number(splitData.split_id) ? Number(splitData.split_id) : splitData.split_id;
            let existingSplit = await sdk.findOne("split", { id: splitId });

            console.log("splitId >>", splitId);
            console.log("existingSplit >>", existingSplit);
            if (existingSplit) {
              await sdk.updateById("split", splitId, {
                ...existingSplit,
                ...split,
              });
            } else {
              existingSplit = await sdk.create("split", split);
              console.log("created split >>", existingSplit);
            }

            // Update Stripe products and prices for this split
            await updateStripeProductsForSplit(
              stripe,
              sdk,
              existingSplit,
              programId,
              data.currency || "USD"
            );

            // Create weeks for this split
            if (splitData.weeks && splitData.weeks.length > 0) {

              // create weeks
              for (const weekData of splitData.weeks) {
                const week = {
                  split_id: existingSplit.id,
                  title: weekData.title,
                  user_id: userId,
                  week_order: weekData.week_order,
                  created_at: UtilService.sqlDateTimeFormat(now),
                  updated_at: UtilService.sqlDateTimeFormat(now),
                };

                sdk.setTable("week");

                // create or update week
                const weekId = Number(weekData.id) ? Number(weekData.id) : weekData.id;
                let existingWeek = await sdk.findById("week", weekId);
                console.log("updated week >>", existingWeek);
                if (existingWeek) {
                  await sdk.updateById("week", weekId, {
                    ...existingWeek,
                    ...week,
                  });
                } else {
                  existingWeek = await sdk.create("week", week);
                  console.log("created week >>", existingWeek);
                }

                // Create days for this week
                for (const dayData of weekData.days) {
                  const dayId = Number(dayData.id) ? Number(dayData.id) : dayData.id;
                  const day = {
                    week_id: existingWeek.id,
                    title: dayData.title,
                    user_id: userId,
                    day_order: dayData.day_order,
                    is_rest_day: dayData.is_rest_day,
                    created_at: UtilService.sqlDateTimeFormat(now),
                    updated_at: UtilService.sqlDateTimeFormat(now),
                  };

                  sdk.setTable("day");

                  // create or update day
                  let existingDay = await sdk.findById("day", dayId);
                  console.log("updated day >>", existingDay);
                  if (existingDay) {
                    await sdk.updateById("day", dayId, {
                      ...existingDay,
                      ...day,
                    });
                  } else {
                    existingDay = await sdk.create("day", day);
                    console.log("created day >>", existingDay);
                  }

                  // Create sessions for this day
                  for (const sessionData of dayData.sessions) {
                    const sessionId = Number(sessionData.id) ? Number(sessionData.id) : sessionData.id;
                    const session = {
                      day_id: existingDay.id,
                      title: sessionData.title,
                      user_id: userId,
                      session_order: sessionData.session_order,
                      created_at: UtilService.sqlDateTimeFormat(now),
                      updated_at: UtilService.sqlDateTimeFormat(now),
                    };

                    sdk.setTable("session");

                    // create or update session
                    let existingSession = await sdk.findById("session", sessionId);
                    console.log("updated session >>", existingSession);
                    if (existingSession) {
                      await sdk.updateById("session", sessionId, {
                        ...existingSession,
                        ...session,
                      });
                    } else {
                      existingSession = await sdk.create("session", session);
                      console.log("created session >>", existingSession);
                    }

                    // Create exercise instances for this session
                    for (const exerciseData of sessionData.exercises) {
                      const exerciseInstanceId = Number(exerciseData.id) ? Number(exerciseData.id) : exerciseData.id;
                      const exerciseInstance = {
                        user_id: userId,
                        session_id: existingSession.id,
                        exercise_id: exerciseData.exercise_id || null,
                        exercise_name: exerciseData.exercise_name || null,
                        video_url: exerciseData.video_url || null,
                        sets: exerciseData.sets,
                        reps_or_time: exerciseData.reps_or_time,
                        reps_time_type: exerciseData.reps_time_type,
                        time_minutes: exerciseData.time_minutes || null,
                        time_seconds: exerciseData.time_seconds || null,
                        exercise_details: exerciseData.exercise_details,
                        rest_duration_minutes:
                          exerciseData.rest_duration_minutes,
                        rest_duration_seconds:
                          exerciseData.rest_duration_seconds,
                        label: exerciseData.label,
                        label_number: exerciseData.label_number,
                        is_linked: exerciseData.is_linked,
                        exercise_order: exerciseData.exercise_order,
                        created_at: UtilService.sqlDateTimeFormat(now),
                        updated_at: UtilService.sqlDateTimeFormat(now),
                      };

                      sdk.setTable("exercise_instance");

                      // create or update exercise instance
                        let existingExerciseInstance = await sdk.findById("exercise_instance", exerciseInstanceId);
                        console.log("updated exercise instance >>", existingExerciseInstance);
                      if (existingExerciseInstance) {
                        await sdk.updateById("exercise_instance", exerciseInstanceId, {
                          ...existingExerciseInstance,
                          ...exerciseInstance,
                        });
                      } else {
                        existingExerciseInstance = await sdk.create("exercise_instance", exerciseInstance);
                        console.log("created exercise instance >>", existingExerciseInstance);
                      }
                    }
                  }
                }
              }
            }
          }

          // Success - return the program ID
          return res.status(200).json({
            error: false,
            message: "Program updated successfully",
            data: programId,
          });
        } catch (updateError) {
          console.error("Error during program update:", updateError);
          throw updateError;
        }
      } catch (err) {
        console.error("Program update error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to update program",
        });
      }
    }
  );

  // Update split pricing and sync with Stripe
  app.put(
    "/v2/api/kanglink/custom/trainer/splits/:split_id",
    [TokenMiddleware({ role: "trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const splitId = req.params.split_id;
        const userId = req.user_id;
        const { title, full_price, subscription, equipment_required } =
          req.body;

        sdk.setProjectId("kanglink");

        // Get existing split with program info
        const existingSplit = await sdk.rawQuery(`
          SELECT s.*, p.program_name, p.currency, p.user_id as trainer_id
          FROM kanglink_split s
          JOIN kanglink_program p ON s.program_id = p.id
          WHERE s.id = ${splitId}
        `);

        if (!existingSplit || existingSplit.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Split not found",
          });
        }

        const split = existingSplit[0];

        // Check if user owns this program
        if (split.trainer_id !== userId) {
          return res.status(403).json({
            error: true,
            message: "You can only update your own program splits",
          });
        }

        // Update split in database
        const updateData = {
          updated_at: UtilService.sqlDateTimeFormat(new Date()),
        };

        if (title !== undefined) updateData.title = title;
        if (full_price !== undefined) updateData.full_price = full_price;
        if (subscription !== undefined) updateData.subscription = subscription;
        if (equipment_required !== undefined)
          updateData.equipment_required = equipment_required;

        sdk.setTable("split");
        await sdk.updateById("split", splitId, updateData);

        // Get updated split data
        const updatedSplit = { ...split, ...updateData };

        // Update Stripe products and prices if pricing changed
        if (full_price !== undefined || subscription !== undefined) {
          await updateStripeProductsForSplit(
            stripe,
            sdk,
            updatedSplit,
            split.program_id,
            split.currency || "USD"
          );
        }

        return res.status(200).json({
          error: false,
          message: "Split updated successfully",
          data: updatedSplit,
        });
      } catch (err) {
        console.error("Split update error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to update split",
        });
      }
    }
  );

  // Get program data with complete structure
  app.get(
    "/v2/api/kanglink/custom/trainer/programs/:program_id",
    [TokenMiddleware({ role: "trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const programId = req.params.program_id;
        const userId = req.user_id;

        sdk.setProjectId("kanglink");

        // Get program data
        sdk.setTable("program");
        const program = await sdk.findOne("program", { id: programId });

        if (!program) {
          return res.status(404).json({
            error: true,
            message: "Program not found",
          });
        }

        // Check if user owns this program
        if (program.user_id !== userId && req.role !== "super_admin") {
          return res.status(403).json({
            error: true,
            message: "You can only access your own programs",
          });
        }

        // Get all splits for this program
        const splits = await sdk.rawQuery(`
          SELECT * FROM kanglink_split
          WHERE program_id = ${programId}
          ORDER BY id
        `);

        // Build stepOneData
        const stepOneData = {
          program_name: program.program_name,
          type_of_program: program.type_of_program,
          program_description: program.program_description,
          payment_plan: program.payment_plan
            ? JSON.parse(program.payment_plan)
            : [],
          track_progress: program.track_progress,
          allow_comments: program.allow_comments,
          allow_private_messages: program.allow_private_messages,
          target_levels: program.target_levels
            ? JSON.parse(program.target_levels)
            : [],
          split_program: program.split_program,
          splits: splits.map((split) => ({
            title: split.title,
            full_price: split.full_price,
            subscription: split.subscription,
            equipment_required: split.equipment_required,
            split_id: split.id.toString(),
          })),
          currency: program.currency,
          days_for_preview: program.days_for_preview,
        };

        // Get complete program structure for stepTwoData
        const programStructure = await sdk.rawQuery(`
          SELECT
            w.id as week_id,
            w.title as week_title,
            w.week_order,
            w.split_id,
            d.id as day_id,
            d.title as day_title,
            d.day_order,
            d.is_rest_day,
            s.id as session_id,
            s.title as session_title,
            s.session_order,
            ei.id as exercise_instance_id,
            ei.sets,
            ei.reps_or_time,
            ei.time_minutes,
            ei.time_seconds,
            ei.exercise_details,
            ei.rest_duration_minutes,
            ei.rest_duration_seconds,
            ei.label,
            ei.label_number,
            ei.is_linked,
            ei.exercise_order,
            ei.exercise_id,
            ei.exercise_name,
            ei.video_url,
            ei.reps_time_type
          FROM kanglink_split sp
          LEFT JOIN kanglink_week w ON sp.id = w.split_id
          LEFT JOIN kanglink_day d ON w.id = d.week_id
          LEFT JOIN kanglink_session s ON d.id = s.day_id
          LEFT JOIN kanglink_exercise_instance ei ON s.id = ei.session_id
          WHERE sp.program_id = ${programId}
          ORDER BY sp.id, w.week_order, d.day_order, s.session_order, ei.exercise_order
        `);

        // Build splitConfigurations
        const splitConfigurations = {};

        // Initialize splitConfigurations for all splits
        splits.forEach((split) => {
          splitConfigurations[split.id.toString()] = [];
        });

        // Group the data by split
        const splitData = {};
        programStructure.forEach((row) => {
          if (!row.week_id) return; // Skip if no week data

          const splitId = row.split_id.toString();

          if (!splitData[splitId]) {
            splitData[splitId] = {};
          }

          // Group by week
          if (!splitData[splitId][row.week_id]) {
            splitData[splitId][row.week_id] = {
              id: row.week_id,
              week_id: row.week_id,
              title: row.week_title,
              week_order: row.week_order,
              days: {},
            };
          }

          if (!row.day_id) return; // Skip if no day data

          // Group by day
          if (!splitData[splitId][row.week_id].days[row.day_id]) {
            splitData[splitId][row.week_id].days[row.day_id] = {
              id: row.day_id,
              day_id: row.day_id,
              title: row.day_title,
              day_order: row.day_order,
              is_rest_day: row.is_rest_day,
              sessions: {},
            };
          }

          if (!row.session_id) return; // Skip if no session data

          // Group by session
          if (
            !splitData[splitId][row.week_id].days[row.day_id].sessions[
            row.session_id
            ]
          ) {
            splitData[splitId][row.week_id].days[row.day_id].sessions[
              row.session_id
            ] = {
              id: row.session_id,
              session_id: row.session_id,
              title: row.session_title,
              session_order: row.session_order,
              exercises: [],
            };
          }

          if (!row.exercise_instance_id) return; // Skip if no exercise data

          // Add exercise
          splitData[splitId][row.week_id].days[row.day_id].sessions[
            row.session_id
          ].exercises.push({
            id: row.exercise_instance_id,
            exercise_instance_id: row.exercise_instance_id,
            sets: row.sets,
            reps_or_time: row.reps_or_time,
            time_minutes: row.time_minutes,
            time_seconds: row.time_seconds,
            exercise_details: row.exercise_details,
            rest_duration_minutes: row.rest_duration_minutes,
            rest_duration_seconds: row.rest_duration_seconds,
            label: row.label,
            label_number: row.label_number,
            is_linked: row.is_linked,
            exercise_order: row.exercise_order,
            user_id: 0,
            session_id: "",
            exercise_id: row.exercise_id ? row.exercise_id.toString() : "",
            exercise_name: row.exercise_name || "",
            video_url: row.video_url || "",
            reps_time_type: row.reps_time_type,
          });
        });

        // Convert grouped data to arrays and build splitConfigurations
        Object.keys(splitData).forEach((splitId) => {
          const weeks = Object.values(splitData[splitId]).map((week) => ({
            ...week,
            days: Object.values(week.days).map((day) => ({
              ...day,
              sessions: Object.values(day.sessions),
            })),
          }));

          splitConfigurations[splitId] = weeks;
        });

        // Build stepTwoData - use the first split's data as the main program_split
        const firstSplitId = splits.length > 0 ? splits[0].id.toString() : "";
        const stepTwoData = {
          program_split: firstSplitId,
          description: program.program_description,
          weeks: splitConfigurations[firstSplitId] || [],
          splitConfigurations: splitConfigurations,
          status: program.status,
          image: program.image,
        };

        return res.status(200).json({
          error: false,
          data: {
            stepOneData,
            stepTwoData,
          },
        });
      } catch (err) {
        console.error("Get program error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get program",
        });
      }
    }
  );

  // Helper function to create Stripe products and prices for a split
  async function createStripeProductsForSplit(
    stripe,
    sdk,
    split,
    programId,
    currency
  ) {
    try {
      // Validate currency parameter
      if (!currency || typeof currency !== "string") {
        console.error(
          `Invalid currency provided for split ${split.id}: ${currency}`
        );
        currency = "USD"; // Default fallback
      }

      // Only create Stripe products/prices if the split has pricing
      if (!split.full_price && !split.subscription) {
        console.log(
          `Split ${split.id} has no pricing, skipping Stripe creation`
        );
        return;
      }

      // Create separate products for one-time and subscription
      let oneTimeProduct = null;
      let subscriptionProduct = null;
      let oneTimeProductRecord = null;
      let subscriptionProductRecord = null;

      // 1. Create Stripe Product for one-time payment (if full_price exists)
      if (split.full_price && split.full_price > 0) {
        try {
          const oneTimeProductName = `one_time - program ${programId} - split ${split.id}`;

          // Check if one-time product already exists
          sdk.setTable("stripe_product");
          const existingOneTimeProduct = await sdk.findOne("stripe_product", {
            name: oneTimeProductName,
          });

          if (existingOneTimeProduct) {
            oneTimeProduct = { id: existingOneTimeProduct.stripe_id };
            oneTimeProductRecord = existingOneTimeProduct;
            console.log(
              `Using existing one-time Stripe product: ${existingOneTimeProduct.stripe_id}`
            );
          } else {
            // Create new one-time Stripe product
            oneTimeProduct = await stripe.createStripeProduct({
              name: oneTimeProductName,
              description: `One-time access to ${split.title} workout split`,
              metadata: {
                projectId: "kanglink",
                split_id: split.id.toString(),
                program_id: programId.toString(),
                payment_type: "one_time",
              },
            });

            // Save product to database
            oneTimeProductRecord = await sdk.create("stripe_product", {
              stripe_id: oneTimeProduct.id,
              name: oneTimeProductName,
              object: JSON.stringify(oneTimeProduct),
              status: 1,
              created_at: UtilService.sqlDateFormat(new Date()),
              updated_at: UtilService.sqlDateTimeFormat(new Date()),
            });

            console.log(
              `Created one-time Stripe product: ${oneTimeProduct.id} for split ${split.id}`
            );
          }

          // Create one-time price
          const oneTimePriceName = `one_time - program ${split.program_id} - split ${split.id}`;

          // Check if one-time price already exists
          sdk.setTable("stripe_price");
          const existingOneTimePrice = await sdk.findOne("stripe_price", {
            name: oneTimePriceName,
          });

          if (!existingOneTimePrice) {
            // Ensure we have a valid one-time product
            if (!oneTimeProduct || !oneTimeProduct.id) {
              throw new Error(
                `One-time product not found or invalid for split ${split.id}`
              );
            }

            console.log(
              `Creating one-time price for product: ${oneTimeProduct.id}, split: ${split.id}`
            );

            // Validate amount
            const amount = parseFloat(split.full_price);
            if (isNaN(amount) || amount <= 0) {
              throw new Error(`Invalid full_price amount: ${split.full_price}`);
            }

            const priceParams = {
              productId: oneTimeProduct.id,
              name: oneTimePriceName,
              amount: amount,
              currency: currency.toLowerCase(),
              metadata: {
                projectId: "kanglink",
                split_id: split.id.toString(),
                program_id: programId.toString(),
                payment_type: "one_time",
              },
            };

            console.log(
              `One-time price params:`,
              JSON.stringify(priceParams, null, 2)
            );
            const oneTimePrice = await stripe.createStripeOnetimePrice(
              priceParams
            );

            // Save one-time price to database
            await sdk.create("stripe_price", {
              stripe_id: oneTimePrice.id,
              name: oneTimePriceName,
              object: JSON.stringify(oneTimePrice),
              product_id: oneTimeProductRecord ? oneTimeProductRecord.id : null,
              amount: parseFloat(split.full_price),
              type: "one_time",
              status: 1,
              created_at: UtilService.sqlDateFormat(new Date()),
              updated_at: UtilService.sqlDateTimeFormat(new Date()),
            });

            console.log(
              `Created Stripe one-time price: ${oneTimePrice.id} for split ${split.id}`
            );
          } else {
            console.log(`One-time price already exists for split ${split.id}`);
          }
        } catch (priceError) {
          console.error(
            `Failed to create one-time price for split ${split.id}:`,
            {
              error: priceError.message,
              stack: priceError.stack,
              splitData: {
                id: split.id,
                full_price: split.full_price,
                program_id: programId,
              },
              currency: currency,
            }
          );
        }
      }

      // 2. Create Stripe Product for subscription (if subscription price exists)
      if (split.subscription && split.subscription > 0) {
        try {
          const subscriptionProductName = `subscription - program ${programId} - split ${split.id}`;

          // Check if subscription product already exists
          sdk.setTable("stripe_product");
          const existingSubscriptionProduct = await sdk.findOne(
            "stripe_product",
            {
              name: subscriptionProductName,
            }
          );

          if (existingSubscriptionProduct) {
            subscriptionProduct = { id: existingSubscriptionProduct.stripe_id };
            subscriptionProductRecord = existingSubscriptionProduct;
            console.log(
              `Using existing subscription Stripe product: ${existingSubscriptionProduct.stripe_id}`
            );
          } else {
            // Create new subscription Stripe product
            subscriptionProduct = await stripe.createStripeProduct({
              name: subscriptionProductName,
              description: `Monthly subscription to ${split.title} workout split`,
              metadata: {
                projectId: "kanglink",
                split_id: split.id.toString(),
                program_id: programId.toString(),
                payment_type: "subscription",
              },
            });

            // Save product to database
            subscriptionProductRecord = await sdk.create("stripe_product", {
              stripe_id: subscriptionProduct.id,
              name: subscriptionProductName,
              object: JSON.stringify(subscriptionProduct),
              status: 1,
              created_at: UtilService.sqlDateFormat(new Date()),
              updated_at: UtilService.sqlDateTimeFormat(new Date()),
            });

            console.log(
              `Created subscription Stripe product: ${subscriptionProduct.id} for split ${split.id}`
            );
          }

          // Create subscription price
          const subscriptionPriceName = `subscription - program ${split.program_id} - split ${split.id}`;

          // Check if subscription price already exists
          sdk.setTable("stripe_price");
          const existingSubscriptionPrice = await sdk.findOne("stripe_price", {
            name: subscriptionPriceName,
          });

          if (!existingSubscriptionPrice) {
            // Ensure we have a valid subscription product
            if (!subscriptionProduct || !subscriptionProduct.id) {
              throw new Error(
                `Subscription product not found or invalid for split ${split.id}`
              );
            }

            console.log(
              `Creating subscription price for product: ${subscriptionProduct.id}, split: ${split.id}`
            );

            // Validate amount
            const subscriptionAmount = parseFloat(split.subscription);
            if (isNaN(subscriptionAmount) || subscriptionAmount <= 0) {
              throw new Error(
                `Invalid subscription amount: ${split.subscription}`
              );
            }

            const subscriptionParams = {
              productId: subscriptionProduct.id,
              name: subscriptionPriceName,
              amount: subscriptionAmount,
              currency: currency.toLowerCase(),
              interval: "month",
              interval_count: 1,
              trial_days: 0, // No trial period
              metadata: {
                projectId: "kanglink",
                split_id: split.id.toString(),
                program_id: programId.toString(),
                payment_type: "subscription",
              },
            };

            console.log(
              `Subscription price params:`,
              JSON.stringify(subscriptionParams, null, 2)
            );
            const subscriptionPrice = await stripe.createStripeRecurringPrice(
              subscriptionParams
            );

            // Save subscription price to database
            await sdk.create("stripe_price", {
              stripe_id: subscriptionPrice.id,
              name: subscriptionPriceName,
              object: JSON.stringify(subscriptionPrice),
              product_id: subscriptionProductRecord
                ? subscriptionProductRecord.id
                : null,
              amount: parseFloat(split.subscription),
              type: "recurring",
              status: 1,
              created_at: UtilService.sqlDateFormat(new Date()),
              updated_at: UtilService.sqlDateTimeFormat(new Date()),
            });

            console.log(
              `Created Stripe subscription price: ${subscriptionPrice.id} for split ${split.id}`
            );
          } else {
            console.log(
              `Subscription price already exists for split ${split.id}`
            );
          }
        } catch (priceError) {
          console.error(
            `Failed to create subscription price for split ${split.id}:`,
            {
              error: priceError.message,
              stack: priceError.stack,
              splitData: {
                id: split.id,
                subscription: split.subscription,
                program_id: programId,
              },
              currency: currency,
            }
          );
        }
      }
    } catch (error) {
      console.error(
        `Error creating Stripe products for split ${split.id}:`,
        error
      );
      // Don't throw error to avoid breaking program creation
    }
  }

  // Helper function to update Stripe products and prices for a split
  async function updateStripeProductsForSplit(
    stripe,
    sdk,
    split,
    programId,
    currency
  ) {
    try {
      // Validate currency parameter
      if (!currency || typeof currency !== "string") {
        console.error(
          `Invalid currency provided for split ${split.id}: ${currency}`
        );
        currency = "USD"; // Default fallback
      }

      console.log(`Updating Stripe products for split ${split.id}`);

      // Update one-time price if it exists and full_price changed
      if (split.full_price && split.full_price > 0) {
        try {
          const oneTimeProductName = `one_time - program ${programId} - split ${split.id}`;
          const oneTimePriceName = `one_time - program ${programId} - split ${split.id}`;

          // Get existing one-time product
          sdk.setTable("stripe_product");
          const existingOneTimeProduct = await sdk.findOne("stripe_product", {
            name: oneTimeProductName,
          });

          if (!existingOneTimeProduct) {
            // If no existing product, create new ones
            await createStripeProductsForSplit(
              stripe,
              sdk,
              split,
              programId,
              currency
            );
            return;
          }

          const oneTimeProductId = existingOneTimeProduct.stripe_id;

          sdk.setTable("stripe_price");
          const existingOneTimePrice = await sdk.findOne("stripe_price", {
            name: oneTimePriceName,
            status: 1, // Only active prices
          });

          if (existingOneTimePrice) {
            // Check if price actually changed
            const currentAmount = parseFloat(existingOneTimePrice.amount || 0);
            const newAmount = parseFloat(split.full_price);

            if (Math.abs(currentAmount - newAmount) > 0.01) {
              // Allow for floating point precision
              // We need to create a new Stripe price because Stripe prices are immutable
              // But we'll deactivate the old one and create a new one with the same name

              // Deactivate old price in Stripe
              await stripe.deactivateStripePrice({
                price_id: existingOneTimePrice.stripe_id,
              });

              // Create new Stripe price with updated amount
              const newOneTimePrice = await stripe.createStripeOnetimePrice({
                productId: oneTimeProductId,
                name: oneTimePriceName,
                amount: newAmount,
                currency: currency.toLowerCase(),
                metadata: {
                  projectId: "kanglink",
                  split_id: split.id.toString(),
                  program_id: programId.toString(),
                  payment_type: "one_time",
                },
              });

              // Update the existing database record with new Stripe price ID and amount
              await sdk.updateById("stripe_price", existingOneTimePrice.id, {
                stripe_id: newOneTimePrice.id,
                amount: newAmount,
                object: JSON.stringify(newOneTimePrice),
                updated_at: UtilService.sqlDateTimeFormat(new Date()),
              });

              console.log(
                `Updated one-time price amount from ${currentAmount} to ${newAmount} for split ${split.id}`
              );
            } else {
              console.log(`One-time price unchanged for split ${split.id}`);
            }
          } else {
            // Create new one-time price only if none exists
            const newOneTimePrice = await stripe.createStripeOnetimePrice({
              productId: oneTimeProductId,
              name: oneTimePriceName,
              amount: split.full_price,
              currency: currency.toLowerCase(),
              metadata: {
                projectId: "kanglink",
                split_id: split.id.toString(),
                program_id: programId.toString(),
                payment_type: "one_time",
              },
            });

            // Save new price to database
            await sdk.create("stripe_price", {
              stripe_id: newOneTimePrice.id,
              name: oneTimePriceName,
              object: JSON.stringify(newOneTimePrice),
              product_id: existingOneTimeProduct
                ? existingOneTimeProduct.id
                : null,
              amount: parseFloat(split.full_price),
              type: "one_time",
              status: 1,
              created_at: UtilService.sqlDateFormat(new Date()),
              updated_at: UtilService.sqlDateTimeFormat(new Date()),
            });

            console.log(
              `Created new Stripe one-time price: ${newOneTimePrice.id} for split ${split.id}`
            );
          }
        } catch (priceError) {
          console.error(
            `Failed to update one-time price for split ${split.id}:`,
            priceError
          );
        }
      }

      // Update subscription price if it exists and subscription changed
      console.log("\nsplit.subscription >>", split.subscription, "\n");
      if (split.subscription && split.subscription > 0) {
        console.log("Updating subscription price for split", split.id);
        try {
          const subscriptionProductName = `subscription - program ${programId} - split ${split.id}`;
          const subscriptionPriceName = `subscription - program ${programId} - split ${split.id}`;

          // Get existing subscription product
          sdk.setTable("stripe_product");
          const existingSubscriptionProduct = await sdk.findOne(
            "stripe_product",
            {
              name: subscriptionProductName,
            }
          );

          if (!existingSubscriptionProduct) {
            // If no existing product, create new ones
            await createStripeProductsForSplit(
              stripe,
              sdk,
              split,
              programId,
              currency
            );
            return;
          }

          const subscriptionProductId = existingSubscriptionProduct.stripe_id;

          sdk.setTable("stripe_price");
          const existingSubscriptionPrice = await sdk.findOne("stripe_price", {
            name: subscriptionPriceName,
            status: 1, // Only active prices
          });

          if (existingSubscriptionPrice) {
            // Check if price actually changed
            const currentAmount = parseFloat(
              existingSubscriptionPrice.amount || 0
            );
            const newAmount = parseFloat(split.subscription);

            if (Math.abs(currentAmount - newAmount) > 0.01) {
              // Allow for floating point precision
              // We need to create a new Stripe price because Stripe prices are immutable
              // But we'll deactivate the old one and create a new one with the same name

              // Deactivate old price in Stripe
              await stripe.deactivateStripePrice({
                price_id: existingSubscriptionPrice.stripe_id,
              });

              // Create new Stripe price with updated amount
              const newSubscriptionPrice =
                await stripe.createStripeRecurringPrice({
                  productId: subscriptionProductId,
                  name: subscriptionPriceName,
                  amount: newAmount,
                  currency: currency.toLowerCase(),
                  interval: "month",
                  interval_count: 1,
                  trial_days: 0, // No trial period
                  metadata: {
                    projectId: "kanglink",
                    split_id: split.id.toString(),
                    program_id: programId.toString(),
                    payment_type: "subscription",
                  },
                });

              // Update the existing database record with new Stripe price ID and amount
              await sdk.updateById(
                "stripe_price",
                existingSubscriptionPrice.id,
                {
                  stripe_id: newSubscriptionPrice.id,
                  amount: newAmount,
                  object: JSON.stringify(newSubscriptionPrice),
                  updated_at: UtilService.sqlDateTimeFormat(new Date()),
                }
              );

              console.log(
                `Updated subscription price amount from ${currentAmount} to ${newAmount} for split ${split.id}`
              );
            } else {
              console.log(`Subscription price unchanged for split ${split.id}`);
            }
          } else {
            // Create new subscription price only if none exists
            const newSubscriptionPrice =
              await stripe.createStripeRecurringPrice({
                productId: subscriptionProductId,
                name: subscriptionPriceName,
                amount: split.subscription,
                currency: currency.toLowerCase(),
                interval: "month",
                interval_count: 1,
                trial_days: 0, // No trial period
                metadata: {
                  projectId: "kanglink",
                  split_id: split.id.toString(),
                  program_id: programId.toString(),
                  payment_type: "subscription",
                },
              });

            // Save new price to database
            await sdk.create("stripe_price", {
              stripe_id: newSubscriptionPrice.id,
              name: subscriptionPriceName,
              object: JSON.stringify(newSubscriptionPrice),
              product_id: existingSubscriptionProduct
                ? existingSubscriptionProduct.id
                : null,
              amount: parseFloat(split.subscription),
              type: "recurring",
              status: 1,
              created_at: UtilService.sqlDateFormat(new Date()),
              updated_at: UtilService.sqlDateTimeFormat(new Date()),
            });

            console.log(
              `Created new Stripe subscription price: ${newSubscriptionPrice.id} for split ${split.id}`
            );
          }
        } catch (priceError) {
          console.error(
            `Failed to update subscription price for split ${split.id}:`,
            priceError
          );
        }
      }
    } catch (error) {
      console.error(
        `Error updating Stripe products for split ${split.id}:`,
        error
      );
      // Don't throw error to avoid breaking split update
    }
  }
};
