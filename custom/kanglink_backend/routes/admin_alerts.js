const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const AdminAlertService = require("../services/AdminAlertService");

module.exports = function (app) {
  // Get admin alerts
  app.get(
    "/v2/api/kanglink/custom/admin/alerts",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const {
          page = 1,
          limit = 20,
          activity_type,
          date_from,
          date_to,
        } = req.query;

        const adminAlertService = new AdminAlertService(sdk);
        const result = await adminAlertService.getAdminAlerts({
          page: parseInt(page),
          limit: parseInt(limit),
          activity_type,
          date_from,
          date_to,
        });

        if (!result.success) {
          return res.status(500).json({
            error: true,
            message: result.error || "Failed to fetch admin alerts",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Admin alerts retrieved successfully",
          data: result,
        });
      } catch (error) {
        console.error("Error fetching admin alerts:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch admin alerts",
        });
      }
    }
  );

  // Get admin alert statistics
  app.get(
    "/v2/api/kanglink/custom/admin/alerts/stats",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const adminAlertService = new AdminAlertService(sdk);
        const result = await adminAlertService.getAdminAlertStats();

        if (!result.success) {
          return res.status(500).json({
            error: true,
            message: result.error || "Failed to fetch admin alert statistics",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Admin alert statistics retrieved successfully",
          data: result.data,
        });
      } catch (error) {
        console.error("Error fetching admin alert statistics:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch admin alert statistics",
        });
      }
    }
  );

  // Get pending approval programs
  app.get(
    "/v2/api/kanglink/custom/admin/alerts/pending-programs",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const adminAlertService = new AdminAlertService(sdk);
        const result = await adminAlertService.getPendingApprovalPrograms();

        if (!result.success) {
          return res.status(500).json({
            error: true,
            message: result.error || "Failed to fetch pending approval programs",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Pending approval programs retrieved successfully",
          data: result,
        });
      } catch (error) {
        console.error("Error fetching pending approval programs:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch pending approval programs",
        });
      }
    }
  );

  // Get refund requests
  app.get(
    "/v2/api/kanglink/custom/admin/alerts/refund-requests",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const { status, athlete_id, trainer_id } = req.query;
        const filters = {};
        
        if (status) filters.status = status;
        if (athlete_id) filters.athlete_id = athlete_id;
        if (trainer_id) filters.trainer_id = trainer_id;

        const adminAlertService = new AdminAlertService(sdk);
        const result = await adminAlertService.getRefundRequests(filters);

        if (!result.success) {
          return res.status(500).json({
            error: true,
            message: result.error || "Failed to fetch refund requests",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Refund requests retrieved successfully",
          data: result,
        });
      } catch (error) {
        console.error("Error fetching refund requests:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch refund requests",
        });
      }
    }
  );

  // Create system alert (admin only)
  app.post(
    "/v2/api/kanglink/custom/admin/alerts/system",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const { title, description, metadata = {} } = req.body;

        if (!title || !description) {
          return res.status(400).json({
            error: true,
            message: "Title and description are required",
          });
        }

        const adminAlertService = new AdminAlertService(sdk);
        const result = await adminAlertService.createSystemAlert(title, description, metadata);

        if (!result) {
          return res.status(500).json({
            error: true,
            message: "Failed to create system alert",
          });
        }

        return res.status(201).json({
          error: false,
          message: "System alert created successfully",
        });
      } catch (error) {
        console.error("Error creating system alert:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to create system alert",
        });
      }
    }
  );
}; 