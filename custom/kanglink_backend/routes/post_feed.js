const AuthService = require("../../../baas/services/AuthService");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const UtilService = require("../../../baas/services/UtilService");
const NotificationService = require("../services/NotificationService");

module.exports = function (app) {
  // Get paginated posts for a specific program
  app.get(
    "/v2/api/kanglink/custom/trainer/feed",
    [TokenMiddleware({ role: "trainer|member|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const {
          program_id,
          page = 1,
          limit = 50,
          post_type,
          visibility_scope,
          user_id,
        } = req.query;
        const requestingUserId = req.user_id;
        const userRole = req.role;

        // Validate required parameters
        if (!program_id) {
          return res.status(400).json({
            error: true,
            message: "program_id is required",
          });
        }

        // Set project and table
        sdk.setProjectId("kanglink");
        sdk.setTable("post_feed");

        // Build the query with joins to get related data
        const projectId = "kanglink";
        let selectFields = `
          ${projectId}_post_feed.id,
          ${projectId}_post_feed.user_id,
          ${projectId}_post_feed.program_id,
          ${projectId}_post_feed.split_id,
          ${projectId}_post_feed.post_type,
          ${projectId}_post_feed.content,
          ${projectId}_post_feed.rating,
          ${projectId}_post_feed.attachments,
          ${projectId}_post_feed.is_private,
          ${projectId}_post_feed.is_anonymous,
          ${projectId}_post_feed.visibility_scope,
          ${projectId}_post_feed.is_pinned,
          ${projectId}_post_feed.pin_expiration,
          ${projectId}_post_feed.is_edited,
          ${projectId}_post_feed.is_flagged,
          ${projectId}_post_feed.flag_reason,
          ${projectId}_post_feed.reaction_count,
          ${projectId}_post_feed.comment_count,
          ${projectId}_post_feed.created_at,
          ${projectId}_post_feed.updated_at,
          ${projectId}_user.id as user_id,
          ${projectId}_user.email as user_email,
          ${projectId}_user.data as user_data,
          ${projectId}_program.id as program_id,
          ${projectId}_program.program_name,
          ${projectId}_program.type_of_program,
          ${projectId}_split.id as split_id,
          ${projectId}_split.title as split_title
        `;

        // Build WHERE conditions
        let whereConditions = [
          `${projectId}_post_feed.program_id = ${program_id}`,
        ];

        // Add visibility filtering based on user role and ownership
        if (userRole === "member") {
          // Members can only see public posts, program_members posts, and their own private posts
          whereConditions.push(`(
            ${projectId}_post_feed.visibility_scope = 'public' OR
            ${projectId}_post_feed.visibility_scope = 'program_members' OR
            (${projectId}_post_feed.visibility_scope = 'private' AND ${projectId}_post_feed.user_id = ${requestingUserId})
          )`);
        }

        // Add optional filters
        if (post_type) {
          whereConditions.push(
            `${projectId}_post_feed.post_type = '${post_type}'`
          );
        }

        if (
          visibility_scope &&
          (userRole === "trainer" || userRole === "super_admin")
        ) {
          whereConditions.push(
            `${projectId}_post_feed.visibility_scope = '${visibility_scope}'`
          );
        }

        if (user_id) {
          whereConditions.push(`${projectId}_post_feed.user_id = ${user_id}`);
        }

        // Filter out flagged posts for members
        if (userRole === "member") {
          whereConditions.push(`${projectId}_post_feed.is_flagged = 0`);
        }

        const whereClause = whereConditions.join(" AND ");

        // Execute paginated query with joins for program and split data
        const enhancedResult = await sdk.rawQuery(`
          SELECT ${selectFields}
          FROM ${projectId}_post_feed
          LEFT JOIN ${projectId}_user ON ${projectId}_post_feed.user_id = ${projectId}_user.id
          LEFT JOIN ${projectId}_program ON ${projectId}_post_feed.program_id = ${projectId}_program.id
          LEFT JOIN ${projectId}_split ON ${projectId}_post_feed.split_id = ${projectId}_split.id
          WHERE ${whereClause}
          ORDER BY
            ${projectId}_post_feed.is_pinned DESC,
            ${projectId}_post_feed.created_at DESC
          LIMIT ${parseInt(limit)} OFFSET ${
          (parseInt(page) - 1) * parseInt(limit)
        }
        `);

        // Get total count for pagination
        const countResult = await sdk.rawQuery(`
          SELECT COUNT(*) as total
          FROM ${projectId}_post_feed
          WHERE ${whereClause}
        `);

        const total = countResult[0]?.total || 0;
        const totalPages = Math.ceil(total / parseInt(limit));

        // Transform the data to match the interface structure
        const transformedPosts = enhancedResult.map((post) => {
          // Parse JSON fields
          let attachments = null;
          let userData = null;

          try {
            attachments = post.attachments
              ? JSON.parse(post.attachments)
              : null;
          } catch (e) {
            attachments = null;
          }

          try {
            userData = post.user_data ? JSON.parse(post.user_data) : null;
          } catch (e) {
            userData = null;
          }

          return {
            id: post.id,
            user_id: post.user_id,
            program_id: post.program_id,
            split_id: post.split_id,
            post_type: post.post_type,
            content: post.content,
            rating: post.rating,
            attachments: attachments,
            is_private: Boolean(post.is_private),
            is_anonymous: Boolean(post.is_anonymous),
            visibility_scope: post.visibility_scope,
            is_pinned: Boolean(post.is_pinned),
            pin_expiration: post.pin_expiration,
            is_edited: Boolean(post.is_edited),
            is_flagged: Boolean(post.is_flagged),
            flag_reason: post.flag_reason,
            reaction_count: post.reaction_count || 0,
            comment_count: post.comment_count || 0,
            created_at: post.created_at,
            updated_at: post.updated_at,
            user: post.is_anonymous ? {
              id: null,
              email: "Anonymous",
              data: { full_name: "Anonymous User", first_name: "Anonymous", last_name: "User", photo: "https://placehold.co/48x48" },
            } : {
              id: post.user_id,
              email: post.user_email,
              data: userData,
            },
            program: {
              id: post.program_id,
              program_name: post.program_name,
              type_of_program: post.type_of_program,
            },
            split: post.split_id
              ? {
                  id: post.split_id,
                  title: post.split_title,
                }
              : null,
          };
        });

        return res.json({
          error: false,
          data: transformedPosts,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: total,
            totalPages: totalPages,
            hasNext: parseInt(page) < totalPages,
            hasPrev: parseInt(page) > 1,
          },
          message: "Posts retrieved successfully",
        });
      } catch (error) {
        console.error("Error fetching posts:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error",
          error: error.message,
        });
      }
    }
  );

  // Create a new post
  app.post(
    "/v2/api/kanglink/custom/trainer/feed",
    [TokenMiddleware({ role: "trainer|member|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const userId = req.user_id;
        const userRole = req.role;
        const {
          program_id,
          split_id,
          post_type,
          content,
          rating,
          attachments,
          is_private = false,
          visibility_scope = "program_members",
          is_anonymous = false,
        } = req.body;

        // Validate required fields
        if (!program_id || !post_type || !content) {
          return res.status(400).json({
            error: true,
            message: "program_id, post_type, and content are required",
          });
        }

        // Validate post_type
        const validPostTypes = ["review", "announcement", "question", "update"];
        if (!validPostTypes.includes(post_type)) {
          return res.status(400).json({
            error: true,
            message:
              "Invalid post_type. Must be one of: " + validPostTypes.join(", "),
          });
        }

        // Validate rating for reviews
        if (post_type === "review" && (!rating || rating < 1 || rating > 5)) {
          return res.status(400).json({
            error: true,
            message:
              "Rating is required for reviews and must be between 1 and 5",
          });
        }

        // Only trainers and super_admins can create announcements
        if (post_type === "announcement" && userRole === "member") {
          return res.status(403).json({
            error: true,
            message: "Only trainers can create announcements",
          });
        }

        // Check enrollment for reviews - users must be enrolled in the program to write reviews
        if (post_type === "review") {
          sdk.setProjectId("kanglink");
          sdk.setTable("enrollment");
          
          // Check if user has an active enrollment for this program
          const enrollment = await sdk.findOne("enrollment", {
            athlete_id: userId,
            program_id: parseInt(program_id),
            status: "active",
          });

          if (!enrollment) {
            return res.status(403).json({
              error: true,
              message: "You must be enrolled in this program to write a review",
            });
          }

          // Check if user already has a review for this program
          sdk.setTable("post_feed");
          const existingReview = await sdk.findOne("post_feed", {
            user_id: userId,
            program_id: parseInt(program_id),
            post_type: "review",
          });

          if (existingReview) {
            return res.status(403).json({
              error: true,
              message: "You have already written a review for this program. You can update your existing review instead.",
            });
          }
        }

        // Set project and table
        sdk.setProjectId("kanglink");
        sdk.setTable("post_feed");

        const now = new Date();
        const postData = {
          user_id: userId,
          program_id: parseInt(program_id),
          split_id: split_id ? parseInt(split_id) : null,
          post_type,
          content,
          rating: post_type === "review" ? parseInt(rating) : null,
          attachments: attachments ? JSON.stringify(attachments) : null,
          is_private: Boolean(is_private),
          is_anonymous: Boolean(is_anonymous),
          visibility_scope: Boolean(is_private) ? "private" : visibility_scope,
          is_pinned: false,
          pin_expiration: null,
          is_edited: false,
          is_flagged: false,
          flag_reason: null,
          reaction_count: 0,
          comment_count: 0,
          created_at: UtilService.sqlDateTimeFormat(now),
          updated_at: UtilService.sqlDateTimeFormat(now),
        };

        const createdPost = await sdk.create("post_feed", postData);

        // Create notifications for enrolled athletes if this is a trainer post
        try {
          if (userRole === "trainer" && visibility_scope !== "private") {
            const notificationService = new NotificationService(sdk);
            
            // Get trainer data
            const trainerData = await sdk.rawQuery(`
              SELECT id, email, data
              FROM kanglink_user 
              WHERE id = ${userId}
            `);
            
            if (trainerData && trainerData.length > 0) {
              const trainer = trainerData[0];
              let trainerUserData = {};
              
              if (trainer.data) {
                try {
                  trainerUserData = JSON.parse(trainer.data);
                } catch (e) {
                  console.warn("Failed to parse trainer data:", e);
                }
              }

              const trainerDataForNotification = {
                id: trainer.id,
                email: trainer.email,
                full_name: trainerUserData.full_name || `${trainerUserData.first_name || ""} ${trainerUserData.last_name || ""}`.trim() || "Trainer",
              };

              const postData = {
                id: createdPost.id,
                content: content,
                post_type: post_type,
                program_id: parseInt(program_id),
                split_id: split_id ? parseInt(split_id) : null,
              };

              // Get enrolled athletes for this program
              const enrolledAthletes = await sdk.rawQuery(`
                SELECT DISTINCT e.athlete_id, u.email, u.data
                FROM kanglink_enrollment e
                JOIN kanglink_user u ON e.athlete_id = u.id
                WHERE e.program_id = ${program_id} 
                  AND e.status = 'active' 
                  AND e.athlete_id != ${userId}
              `);

              if (enrolledAthletes && enrolledAthletes.length > 0) {
                await notificationService.createPostFeedNotification(
                  postData,
                  trainerDataForNotification,
                  enrolledAthletes,
                  "post_feed_created"
                );
              }
            }
          }
        } catch (notificationError) {
          console.error("Error creating post feed notifications:", notificationError);
          // Don't fail the post creation if notification creation fails
        }

        return res.json({
          error: false,
          data: {
            post: {
              ...createdPost,
              attachments: attachments,
              is_private: Boolean(is_private),
              is_anonymous: Boolean(is_anonymous),
            },
          },
          message: "Post created successfully",
        });
      } catch (error) {
        console.error("Error creating post:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error",
          error: error.message,
        });
      }
    }
  );

  // Get a single post with comments and reactions
  app.get(
    "/v2/api/kanglink/custom/trainer/feed/:post_id",
    [TokenMiddleware({ role: "trainer|member|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { post_id } = req.params;
        const requestingUserId = req.user_id;
        const userRole = req.role;

        if (!post_id) {
          return res.status(400).json({
            error: true,
            message: "post_id is required",
          });
        }

        sdk.setProjectId("kanglink");
        const projectId = "kanglink";

        // Get the post with user and program data
        const postQuery = `
          SELECT
            ${projectId}_post_feed.*,
            ${projectId}_user.email as user_email,
            ${projectId}_user.data as user_data,
            ${projectId}_program.program_name,
            ${projectId}_program.type_of_program,
            ${projectId}_split.title as split_title
          FROM ${projectId}_post_feed
          LEFT JOIN ${projectId}_user ON ${projectId}_post_feed.user_id = ${projectId}_user.id
          LEFT JOIN ${projectId}_program ON ${projectId}_post_feed.program_id = ${projectId}_program.id
          LEFT JOIN ${projectId}_split ON ${projectId}_post_feed.split_id = ${projectId}_split.id
          WHERE ${projectId}_post_feed.id = ${post_id}
        `;

        const postResult = await sdk.rawQuery(postQuery);

        if (!postResult || postResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Post not found",
          });
        }

        const post = postResult[0];

        // Check visibility permissions
        if (userRole === "member") {
          if (
            post.visibility_scope === "private" &&
            post.user_id !== requestingUserId
          ) {
            return res.status(403).json({
              error: true,
              message: "Access denied",
            });
          }
          if (post.is_flagged) {
            return res.status(404).json({
              error: true,
              message: "Post not found",
            });
          }
        }

        // Transform the post data
        let attachments = null;
        let userData = null;

        try {
          attachments = post.attachments ? JSON.parse(post.attachments) : null;
          userData = post.user_data ? JSON.parse(post.user_data) : null;
        } catch (e) {
          // Handle JSON parse errors gracefully
        }

        const transformedPost = {
          id: post.id,
          user_id: post.user_id,
          program_id: post.program_id,
          split_id: post.split_id,
          post_type: post.post_type,
          content: post.content,
          rating: post.rating,
          attachments: attachments,
          is_private: Boolean(post.is_private),
          is_anonymous: Boolean(post.is_anonymous),
          visibility_scope: post.visibility_scope,
          is_pinned: Boolean(post.is_pinned),
          pin_expiration: post.pin_expiration,
          is_edited: Boolean(post.is_edited),
          is_flagged: Boolean(post.is_flagged),
          flag_reason: post.flag_reason,
          reaction_count: post.reaction_count || 0,
          comment_count: post.comment_count || 0,
          created_at: post.created_at,
          updated_at: post.updated_at,
          user: post.is_anonymous ? {
            id: null,
            email: "Anonymous",
            data: { first_name: "Anonymous", last_name: "User" },
          } : {
            id: post.user_id,
            email: post.user_email,
            data: userData,
          },
          program: {
            id: post.program_id,
            program_name: post.program_name,
            type_of_program: post.type_of_program,
          },
          split: post.split_id
            ? {
                id: post.split_id,
                title: post.split_title,
              }
            : null,
        };

        return res.json({
          error: false,
          data: transformedPost,
          message: "Post retrieved successfully",
        });
      } catch (error) {
        console.error("Error fetching post:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error",
          error: error.message,
        });
      }
    }
  );

  // Get reactions for a post
  app.get(
    "/v2/api/kanglink/custom/trainer/feed/:target_type/:target_id/reactions",
    [TokenMiddleware({ role: "trainer|member|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { target_id, target_type } = req.params;
        const { page = 1, limit = 50, reaction_type } = req.query;
        const requestingUserId = req.user_id;
        const userRole = req.role;

        if (!target_id) {
          return res.status(400).json({
            error: true,
            message: "target_id is required",
          });
        }

        sdk.setProjectId("kanglink");
        const projectId = "kanglink";

        // First check if the post exists and user has access to it
        const postCheckQuery = `
          SELECT visibility_scope, user_id, is_flagged
          FROM ${projectId}_post_feed
          WHERE id = ${target_id}
        `;

        const postCheck = await sdk.rawQuery(postCheckQuery);

        if (!postCheck || postCheck.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Post not found",
          });
        }

        const post = postCheck[0];

        // Check visibility permissions
        if (userRole === "member") {
          if (
            post.visibility_scope === "private" &&
            post.user_id !== requestingUserId
          ) {
            return res.status(403).json({
              error: true,
              message: "Access denied",
            });
          }
          if (post.is_flagged) {
            return res.status(404).json({
              error: true,
              message: "Post not found",
            });
          }
        }

        // Build WHERE conditions for reactions
        let whereConditions = [
          `${projectId}_reaction.target_type = '${target_type}'`,
          `${projectId}_reaction.target_id = ${target_id}`,
        ];
        if (reaction_type) {
          const validReactionTypes = ["like", "love", "fire", "strong"];
          if (!validReactionTypes.includes(reaction_type)) {
            return res.status(400).json({
              error: true,
              message:
                "Invalid reaction_type. Must be one of: " +
                validReactionTypes.join(", "),
            });
          }
          whereConditions.push(
            `${projectId}_reaction.reaction_type = '${reaction_type}'`
          );
        }

        const whereClause = whereConditions.join(" AND ");

        // Get reactions with user data
        const reactionsQuery = `
          SELECT
            ${projectId}_reaction.id,
            ${projectId}_reaction.user_id,
            ${projectId}_reaction.target_type,
            ${projectId}_reaction.target_id,
            ${projectId}_reaction.reaction_type,
            ${projectId}_reaction.created_at,
            ${projectId}_reaction.updated_at,
            ${projectId}_user.email as user_email,
            ${projectId}_user.data as user_data
          FROM ${projectId}_reaction
          LEFT JOIN ${projectId}_user ON ${projectId}_reaction.user_id = ${projectId}_user.id
          WHERE ${whereClause}
          ORDER BY ${projectId}_reaction.created_at DESC
          LIMIT ${parseInt(limit)} OFFSET ${
          (parseInt(page) - 1) * parseInt(limit)
        }
        `;

        const reactions = await sdk.rawQuery(reactionsQuery);

        // Get total count
        const countQuery = `
          SELECT COUNT(*) as total
          FROM ${projectId}_reaction
          WHERE ${whereClause}
        `;

        const countResult = await sdk.rawQuery(countQuery);
        const total = countResult[0]?.total || 0;
        const totalPages = Math.ceil(total / parseInt(limit));

        // Transform reactions data
        const transformedReactions = reactions.map((reaction) => {
          let userData = null;
          try {
            userData = reaction.user_data
              ? JSON.parse(reaction.user_data)
              : null;
          } catch (e) {
            userData = null;
          }

          return {
            id: reaction.id,
            user_id: reaction.user_id,
            target_type: reaction.target_type,
            target_id: reaction.target_id,
            reaction_type: reaction.reaction_type,
            created_at: reaction.created_at,
            updated_at: reaction.updated_at,
            user: {
              id: reaction.user_id,
              email: reaction.user_email,
              data: userData,
            },
          };
        });

        return res.json({
          error: false,
          data: transformedReactions,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: total,
            totalPages: totalPages,
            hasNext: parseInt(page) < totalPages,
            hasPrev: parseInt(page) > 1,
          },
          message: "Reactions retrieved successfully",
        });
      } catch (error) {
        console.error("Error fetching reactions:", error);
        return res.status(500).json({
          error: true,
          message: error?.message || "Internal server error",
        });
      }
    }
  );

  // Get comments for a post
  app.get(
    "/v2/api/kanglink/custom/trainer/feed/:post_id/comments",
    [TokenMiddleware({ role: "trainer|member|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { post_id } = req.params;
        const { page = 1, limit = 20, include_replies = true } = req.query;
        const requestingUserId = req.user_id;
        const userRole = req.role;

        if (!post_id) {
          return res.status(400).json({
            error: true,
            message: "post_id is required",
          });
        }

        sdk.setProjectId("kanglink");
        const projectId = "kanglink";

        // First check if the post exists and user has access to it
        const postCheckQuery = `
          SELECT visibility_scope, user_id, is_flagged
          FROM ${projectId}_post_feed
          WHERE id = ${post_id}
        `;

        const postCheck = await sdk.rawQuery(postCheckQuery);

        if (!postCheck || postCheck.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Post not found",
          });
        }

        const post = postCheck[0];

        // Check visibility permissions
        if (userRole === "member") {
          if (
            post.visibility_scope === "private" &&
            post.user_id !== requestingUserId
          ) {
            return res.status(403).json({
              error: true,
              message: "Access denied",
            });
          }
          if (post.is_flagged) {
            return res.status(404).json({
              error: true,
              message: "Post not found",
            });
          }
        }

        // Build WHERE conditions for comments
        let whereConditions = [`${projectId}_comment.post_id = ${post_id}`];

        // Filter out flagged comments for members
        if (userRole === "member") {
          whereConditions.push(`${projectId}_comment.is_flagged = 0`);
        }

        // Only get top-level comments initially (parent_comment_id IS NULL)
        if (include_replies !== "true") {
          whereConditions.push(
            `${projectId}_comment.parent_comment_id IS NULL`
          );
        }

        const whereClause = whereConditions.join(" AND ");

        // Get comments with user data
        const commentsQuery = `
          SELECT
            ${projectId}_comment.id,
            ${projectId}_comment.user_id,
            ${projectId}_comment.post_id,
            ${projectId}_comment.parent_comment_id,
            ${projectId}_comment.content,
            ${projectId}_comment.attachments,
            ${projectId}_comment.is_private,
            ${projectId}_comment.is_edited,
            ${projectId}_comment.is_flagged,
            ${projectId}_comment.flag_reason,
            ${projectId}_comment.mentioned_users,
            ${projectId}_comment.reaction_count,
            ${projectId}_comment.created_at,
            ${projectId}_comment.updated_at,
            ${projectId}_user.email as user_email,
            ${projectId}_user.data as user_data
          FROM ${projectId}_comment
          LEFT JOIN ${projectId}_user ON ${projectId}_comment.user_id = ${projectId}_user.id
          WHERE ${whereClause}
          ORDER BY ${projectId}_comment.created_at ASC
          LIMIT ${parseInt(limit)} OFFSET ${
          (parseInt(page) - 1) * parseInt(limit)
        }
        `;

        const comments = await sdk.rawQuery(commentsQuery);

        // Get total count
        const countQuery = `
          SELECT COUNT(*) as total
          FROM ${projectId}_comment
          WHERE ${whereClause}
        `;

        const countResult = await sdk.rawQuery(countQuery);
        const total = countResult[0]?.total || 0;
        const totalPages = Math.ceil(total / parseInt(limit));

        // Transform comments data
        const transformedComments = await Promise.all(
          comments.map(async (comment) => {
            let userData = null;
            let attachments = null;
            let mentionedUsers = null;

            try {
              userData = comment.user_data
                ? JSON.parse(comment.user_data)
                : null;
              attachments = comment.attachments
                ? JSON.parse(comment.attachments)
                : null;
              mentionedUsers = comment.mentioned_users
                ? JSON.parse(comment.mentioned_users)
                : null;
            } catch (e) {
              // Handle JSON parse errors gracefully
            }

            // Get replies if this is a top-level comment and include_replies is true
            let replies = [];
            if (!comment.parent_comment_id && include_replies === "true") {
              const repliesQuery = `
              SELECT
                ${projectId}_comment.id,
                ${projectId}_comment.user_id,
                ${projectId}_comment.post_id,
                ${projectId}_comment.parent_comment_id,
                ${projectId}_comment.content,
                ${projectId}_comment.attachments,
                ${projectId}_comment.is_private,
                ${projectId}_comment.is_edited,
                ${projectId}_comment.is_flagged,
                ${projectId}_comment.flag_reason,
                ${projectId}_comment.mentioned_users,
                ${projectId}_comment.reaction_count,
                ${projectId}_comment.created_at,
                ${projectId}_comment.updated_at,
                ${projectId}_user.email as reply_user_email,
                ${projectId}_user.data as reply_user_data
              FROM ${projectId}_comment
              LEFT JOIN ${projectId}_user ON ${projectId}_comment.user_id = ${projectId}_user.id
              WHERE ${projectId}_comment.parent_comment_id = ${comment.id}
              ${
                userRole === "member"
                  ? `AND ${projectId}_comment.is_flagged = 0`
                  : ""
              }
              ORDER BY ${projectId}_comment.created_at ASC
            `;

              const repliesResult = await sdk.rawQuery(repliesQuery);

              replies = repliesResult.map((reply) => {
                let replyUserData = null;
                let replyAttachments = null;
                let replyMentionedUsers = null;

                try {
                  replyUserData = reply.reply_user_data
                    ? JSON.parse(reply.reply_user_data)
                    : null;
                  replyAttachments = reply.attachments
                    ? JSON.parse(reply.attachments)
                    : null;
                  replyMentionedUsers = reply.mentioned_users
                    ? JSON.parse(reply.mentioned_users)
                    : null;
                } catch (e) {
                  // Handle JSON parse errors gracefully
                }

                return {
                  id: reply.id,
                  user_id: reply.user_id,
                  post_id: reply.post_id,
                  parent_comment_id: reply.parent_comment_id,
                  content: reply.content,
                  attachments: replyAttachments,
                  is_private: Boolean(reply.is_private),
                  is_edited: Boolean(reply.is_edited),
                  is_flagged: Boolean(reply.is_flagged),
                  flag_reason: reply.flag_reason,
                  mentioned_users: replyMentionedUsers,
                  reaction_count: reply.reaction_count || 0,
                  created_at: reply.created_at,
                  updated_at: reply.updated_at,
                  user: {
                    id: reply.user_id,
                    email: reply.reply_user_email,
                    data: replyUserData,
                  },
                };
              });
            }

            return {
              id: comment.id,
              user_id: comment.user_id,
              post_id: comment.post_id,
              parent_comment_id: comment.parent_comment_id,
              content: comment.content,
              attachments: attachments,
              is_private: Boolean(comment.is_private),
              is_edited: Boolean(comment.is_edited),
              is_flagged: Boolean(comment.is_flagged),
              flag_reason: comment.flag_reason,
              mentioned_users: mentionedUsers,
              reaction_count: comment.reaction_count || 0,
              created_at: comment.created_at,
              updated_at: comment.updated_at,
              user: {
                id: comment.user_id,
                email: comment.user_email,
                data: userData,
              },
              replies: replies,
            };
          })
        );

        return res.json({
          error: false,
          data: transformedComments,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: total,
            totalPages: totalPages,
            hasNext: parseInt(page) < totalPages,
            hasPrev: parseInt(page) > 1,
          },
          message: "Comments retrieved successfully",
        });
      } catch (error) {
        console.error("Error fetching comments:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error",
          error: error.message,
        });
      }
    }
  );

  // Get athlete enrollments where trainer has allowed comments
  app.get(
    "/v2/api/kanglink/custom/athlete/enrollments/with-comments",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const athleteId = req.user_id;
        const userRole = req.role;
        const { page = 1, limit = 50, search } = req.query;

        // Set project
        sdk.setProjectId("kanglink");
        const projectId = "kanglink";

        // Build the query to get enrollments with program details where comments are allowed
        let selectFields = `
          ${projectId}_enrollment.id,
          ${projectId}_enrollment.athlete_id,
          ${projectId}_enrollment.program_id,
          ${projectId}_enrollment.split_id,
          ${projectId}_enrollment.payment_type,
          ${projectId}_enrollment.amount,
          ${projectId}_enrollment.currency,
          ${projectId}_enrollment.enrollment_date,
          ${projectId}_enrollment.expiry_date,
          ${projectId}_enrollment.status,
          ${projectId}_enrollment.payment_status,
          ${projectId}_enrollment.access_type,
          ${projectId}_enrollment.created_at,
          ${projectId}_enrollment.updated_at,
          ${projectId}_program.program_name,
          ${projectId}_program.type_of_program,
          ${projectId}_program.program_description,
          ${projectId}_program.allow_comments,
          ${projectId}_program.allow_private_messages,
          ${projectId}_program.image,
          ${projectId}_program.status as program_status,
          ${projectId}_split.title as split_title,
          ${projectId}_split.description as split_description,
          ${projectId}_user.email as trainer_email,
          ${projectId}_user.data as trainer_data
        `;

        // Build WHERE conditions
        let whereConditions = [
          `${projectId}_enrollment.athlete_id = ${athleteId}`,
          `${projectId}_enrollment.status = 'active'`,
          `${projectId}_program.allow_comments = 1`,
          `${projectId}_program.status IN ('live', 'published', 'active')`
        ];

        // Add search functionality
        if (search) {
          whereConditions.push(`(
            ${projectId}_program.program_name LIKE '%${search}%' OR
            ${projectId}_split.title LIKE '%${search}%' OR
            ${projectId}_program.type_of_program LIKE '%${search}%'
          )`);
        }

        const whereClause = whereConditions.join(" AND ");

        // Execute paginated query with joins
        const enrollmentsResult = await sdk.rawQuery(`
          SELECT ${selectFields}
          FROM ${projectId}_enrollment
          LEFT JOIN ${projectId}_program ON ${projectId}_enrollment.program_id = ${projectId}_program.id
          LEFT JOIN ${projectId}_split ON ${projectId}_enrollment.split_id = ${projectId}_split.id
          LEFT JOIN ${projectId}_user ON ${projectId}_program.user_id = ${projectId}_user.id
          WHERE ${whereClause}
          ORDER BY ${projectId}_enrollment.created_at DESC
          LIMIT ${parseInt(limit)} OFFSET ${(parseInt(page) - 1) * parseInt(limit)}
        `);

        // Get total count for pagination
        const countResult = await sdk.rawQuery(`
          SELECT COUNT(*) as total
          FROM ${projectId}_enrollment
          LEFT JOIN ${projectId}_program ON ${projectId}_enrollment.program_id = ${projectId}_program.id
          LEFT JOIN ${projectId}_split ON ${projectId}_enrollment.split_id = ${projectId}_split.id
          WHERE ${whereClause}
        `);

        const total = countResult[0]?.total || 0;
        const totalPages = Math.ceil(total / parseInt(limit));

        // Transform the data to match the interface structure
        const transformedEnrollments = enrollmentsResult.map((enrollment) => {
          // Parse JSON fields
          let trainerData = null;

          try {
            trainerData = enrollment.trainer_data ? JSON.parse(enrollment.trainer_data) : null;
          } catch (e) {
            trainerData = null;
          }

          return {
            id: enrollment.id,
            athlete_id: enrollment.athlete_id,
            program_id: enrollment.program_id,
            split_id: enrollment.split_id,
            payment_type: enrollment.payment_type,
            amount: enrollment.amount,
            currency: enrollment.currency,
            enrollment_date: enrollment.enrollment_date,
            expiry_date: enrollment.expiry_date,
            status: enrollment.status,
            payment_status: enrollment.payment_status,
            access_type: enrollment.access_type,
            created_at: enrollment.created_at,
            updated_at: enrollment.updated_at,
            program_name: enrollment.program_name,
            split_title: enrollment.split_title,
            program: {
              id: enrollment.program_id,
              program_name: enrollment.program_name,
              type_of_program: enrollment.type_of_program,
              program_description: enrollment.program_description,
              allow_comments: Boolean(enrollment.allow_comments),
              allow_private_messages: Boolean(enrollment.allow_private_messages),
              image: enrollment.image,
              status: enrollment.program_status,
            },
            split: {
              id: enrollment.split_id,
              title: enrollment.split_title,
              description: enrollment.split_description,
            },
            trainer: {
              id: enrollment.trainer_id,
              email: enrollment.trainer_email,
              data: trainerData,
            },
          };
        });

        return res.json({
          error: false,
          data: transformedEnrollments,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: total,
            totalPages: totalPages,
            hasNext: parseInt(page) < totalPages,
            hasPrev: parseInt(page) > 1,
          },
          message: "Enrollments retrieved successfully",
        });
      } catch (error) {
        console.error("Error fetching enrollments:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error",
          error: error.message,
        });
      }
    }
  );

  // Create a new comment on a post
  app.post(
    "/v2/api/kanglink/custom/trainer/feed/:post_id/comments",
    [TokenMiddleware({ role: "trainer|member|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { post_id } = req.params;
        const userId = req.user_id;
        const userRole = req.role;
        const {
          content,
          parent_comment_id,
          attachments,
          is_private = false,
          mentioned_users
        } = req.body;

        if (!post_id || !content) {
          return res.status(400).json({
            error: true,
            message: "post_id and content are required",
          });
        }

        sdk.setProjectId("kanglink");
        const projectId = "kanglink";

        // First check if the post exists and user has access to it
        const postCheckQuery = `
          SELECT visibility_scope, user_id, is_flagged, program_id
          FROM ${projectId}_post_feed
          WHERE id = ${post_id}
        `;

        const postCheck = await sdk.rawQuery(postCheckQuery);

        if (!postCheck || postCheck.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Post not found",
          });
        }

        const post = postCheck[0];

        // Check visibility permissions
        if (userRole === "member") {
          if (
            post.visibility_scope === "private" &&
            post.user_id !== userId
          ) {
            return res.status(403).json({
              error: true,
              message: "Access denied",
            });
          }
          if (post.is_flagged) {
            return res.status(404).json({
              error: true,
              message: "Post not found",
            });
          }
        }

        // Check if parent comment exists (for replies)
        if (parent_comment_id) {
          const parentCommentQuery = `
            SELECT id FROM ${projectId}_comment
            WHERE id = ${parent_comment_id} AND post_id = ${post_id}
          `;
          const parentComment = await sdk.rawQuery(parentCommentQuery);
          
          if (!parentComment || parentComment.length === 0) {
            return res.status(404).json({
              error: true,
              message: "Parent comment not found",
            });
          }
        }

        const now = new Date();
        const commentData = {
          user_id: userId,
          post_id: parseInt(post_id),
          parent_comment_id: parent_comment_id ? parseInt(parent_comment_id) : null,
          content,
          attachments: attachments ? JSON.stringify(attachments) : null,
          is_private: Boolean(is_private),
          is_edited: false,
          is_flagged: false,
          flag_reason: null,
          mentioned_users: mentioned_users ? JSON.stringify(mentioned_users) : null,
          reaction_count: 0,
          created_at: UtilService.sqlDateTimeFormat(now),
          updated_at: UtilService.sqlDateTimeFormat(now),
        };

        sdk.setTable("comment");
        const createdComment = await sdk.create("comment", commentData);

        // Create notification for post author about new comment
        try {
          const notificationService = new NotificationService(sdk);
          
          // Get commenter data
          const commenterData = await sdk.rawQuery(`
            SELECT id, email, data
            FROM kanglink_user 
            WHERE id = ${userId}
          `);
          
          if (commenterData && commenterData.length > 0) {
            const commenter = commenterData[0];
            let commenterUserData = {};
            
            if (commenter.data) {
              try {
                commenterUserData = JSON.parse(commenter.data);
              } catch (e) {
                console.warn("Failed to parse commenter data:", e);
              }
            }

            const commenterDataForNotification = {
              id: commenter.id,
              email: commenter.email,
              full_name: commenterUserData.full_name || `${commenterUserData.first_name || ""} ${commenterUserData.last_name || ""}`.trim() || "User",
            };

            // Get post author data
            const postAuthorData = await sdk.rawQuery(`
              SELECT u.id, u.email, u.data, pf.content as post_content, pf.post_type
              FROM kanglink_post_feed pf
              JOIN kanglink_user u ON pf.user_id = u.id
              WHERE pf.id = ${post_id}
            `);
            
            if (postAuthorData && postAuthorData.length > 0) {
              const postAuthor = postAuthorData[0];
              let postAuthorUserData = {};
              
              if (postAuthor.data) {
                try {
                  postAuthorUserData = JSON.parse(postAuthor.data);
                } catch (e) {
                  console.warn("Failed to parse post author data:", e);
                }
              }

              const postAuthorDataForNotification = {
                id: postAuthor.id,
                email: postAuthor.email,
                full_name: postAuthorUserData.full_name || `${postAuthorUserData.first_name || ""} ${postAuthorUserData.last_name || ""}`.trim() || "User",
              };

              const commentData = {
                id: createdComment.id,
                content: content,
                post_id: parseInt(post_id),
                post_content: postAuthor.post_content,
                post_type: postAuthor.post_type,
              };

              // Only notify if commenter is not the post author
              if (userId !== postAuthor.id) {
                // Format post author data to match NotificationService expectations
                const formattedPostAuthor = {
                  athlete_id: postAuthor.id,
                  email: postAuthor.email,
                  data: postAuthor.data
                };

                await notificationService.createPostFeedNotification(
                  commentData,
                  commenterDataForNotification,
                  [formattedPostAuthor],
                  "post_feed_comment"
                );
              }
            }
          }
        } catch (notificationError) {
          console.error("Error creating comment notifications:", notificationError);
          // Don't fail the comment creation if notification creation fails
        }

        // Update the post's comment count
        await sdk.rawQuery(`
          UPDATE ${projectId}_post_feed 
          SET comment_count = comment_count + 1,
              updated_at = '${UtilService.sqlDateTimeFormat(now)}'
          WHERE id = ${post_id}
        `);

        // Get the comment with user data
        const commentWithUserQuery = `
          SELECT
            ${projectId}_comment.*,
            ${projectId}_user.email as user_email,
            ${projectId}_user.data as user_data
          FROM ${projectId}_comment
          LEFT JOIN ${projectId}_user ON ${projectId}_comment.user_id = ${projectId}_user.id
          WHERE ${projectId}_comment.id = ${createdComment.id}
        `;

        const commentResult = await sdk.rawQuery(commentWithUserQuery);
        const comment = commentResult[0];

        // Transform the comment data
        let userData = null;
        let commentAttachments = null;
        let commentMentionedUsers = null;

        try {
          userData = comment.user_data ? JSON.parse(comment.user_data) : null;
          commentAttachments = comment.attachments ? JSON.parse(comment.attachments) : null;
          commentMentionedUsers = comment.mentioned_users ? JSON.parse(comment.mentioned_users) : null;
        } catch (e) {
          // Handle JSON parse errors gracefully
        }

        const transformedComment = {
          id: comment.id,
          user_id: comment.user_id,
          post_id: comment.post_id,
          parent_comment_id: comment.parent_comment_id,
          content: comment.content,
          attachments: commentAttachments,
          is_private: Boolean(comment.is_private),
          is_edited: Boolean(comment.is_edited),
          is_flagged: Boolean(comment.is_flagged),
          flag_reason: comment.flag_reason,
          mentioned_users: commentMentionedUsers,
          reaction_count: comment.reaction_count || 0,
          created_at: comment.created_at,
          updated_at: comment.updated_at,
          user: {
            id: comment.user_id,
            email: comment.user_email,
            data: userData,
          },
        };

        return res.json({
          error: false,
          data: transformedComment,
          message: "Comment created successfully",
        });
      } catch (error) {
        console.error("Error creating comment:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error",
          error: error.message,
        });
      }
    }
  );

  // Toggle reaction on a post or comment
  app.post(
    "/v2/api/kanglink/custom/trainer/feed/:target_type/:target_id/reactions",
    [TokenMiddleware({ role: "trainer|member|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { target_type, target_id } = req.params;
        const userId = req.user_id;
        const userRole = req.role;
        const { reaction_type = "like" } = req.body;

        if (!target_id || !target_type) {
          return res.status(400).json({
            error: true,
            message: "target_id and target_type are required",
          });
        }

        // Validate target_type and reaction_type
        const validTargetTypes = ["post", "comment"];
        const validReactionTypes = ["like", "love", "fire", "strong"];

        if (!validTargetTypes.includes(target_type)) {
          return res.status(400).json({
            error: true,
            message: "Invalid target_type. Must be one of: " + validTargetTypes.join(", "),
          });
        }

        if (!validReactionTypes.includes(reaction_type)) {
          return res.status(400).json({
            error: true,
            message: "Invalid reaction_type. Must be one of: " + validReactionTypes.join(", "),
          });
        }

        sdk.setProjectId("kanglink");
        const projectId = "kanglink";

        // Check if target exists and user has access
        let targetTable = target_type === "post" ? "post_feed" : "comment";
        let accessCheckQuery;

        if (target_type === "post") {
          accessCheckQuery = `
            SELECT visibility_scope, user_id, is_flagged
            FROM ${projectId}_${targetTable}
            WHERE id = ${target_id}
          `;
        } else {
          accessCheckQuery = `
            SELECT c.id, c.is_flagged, p.visibility_scope, p.user_id, p.is_flagged as post_is_flagged
            FROM ${projectId}_comment c
            LEFT JOIN ${projectId}_post_feed p ON c.post_id = p.id
            WHERE c.id = ${target_id}
          `;
        }

        const targetCheck = await sdk.rawQuery(accessCheckQuery);

        if (!targetCheck || targetCheck.length === 0) {
          return res.status(404).json({
            error: true,
            message: `${target_type.charAt(0).toUpperCase() + target_type.slice(1)} not found`,
          });
        }

        const target = targetCheck[0];

        // Check visibility permissions
        if (userRole === "member") {
          const visibility = target_type === "post" ? target.visibility_scope : target.visibility_scope;
          const ownerId = target_type === "post" ? target.user_id : target.user_id;
          const isFlagged = target_type === "post" ? target.is_flagged : (target.is_flagged || target.post_is_flagged);
          
          if (visibility === "private" && ownerId !== userId) {
            return res.status(403).json({
              error: true,
              message: "Access denied",
            });
          }
          if (isFlagged) {
            return res.status(404).json({
              error: true,
              message: `${target_type.charAt(0).toUpperCase() + target_type.slice(1)} not found`,
            });
          }
        }

        // Check if reaction already exists
        sdk.setTable("reaction");
        const existingReactionQuery = `
          SELECT id, reaction_type FROM ${projectId}_reaction
          WHERE user_id = ${userId} AND target_type = '${target_type}' AND target_id = ${target_id}
        `;

        const existingReaction = await sdk.rawQuery(existingReactionQuery);
        const now = new Date();
        let result;

        if (existingReaction && existingReaction.length > 0) {
          // Reaction exists
          const currentReaction = existingReaction[0];
          
          if (currentReaction.reaction_type === reaction_type) {
            // Same reaction - remove it
            await sdk.rawQuery(`
              DELETE FROM ${projectId}_reaction 
              WHERE id = ${currentReaction.id}
            `);

            // Decrease reaction count
            await sdk.rawQuery(`
              UPDATE ${projectId}_${targetTable} 
              SET reaction_count = GREATEST(0, reaction_count - 1),
                  updated_at = '${UtilService.sqlDateTimeFormat(now)}'
              WHERE id = ${target_id}
            `);

            result = {
              action: "removed",
              reaction_type: reaction_type,
              user_reaction: null,
            };
          } else {
            // Different reaction - update it
            await sdk.rawQuery(`
              UPDATE ${projectId}_reaction 
              SET reaction_type = '${reaction_type}',
                  updated_at = '${UtilService.sqlDateTimeFormat(now)}'
              WHERE id = ${currentReaction.id}
            `);

            result = {
              action: "updated",
              reaction_type: reaction_type,
              user_reaction: reaction_type,
            };
          }
        } else {
          // No existing reaction - create new one
          const reactionData = {
            user_id: userId,
            target_type: target_type,
            target_id: parseInt(target_id),
            reaction_type: reaction_type,
            created_at: UtilService.sqlDateTimeFormat(now),
            updated_at: UtilService.sqlDateTimeFormat(now),
          };

          await sdk.create("reaction", reactionData);

          // Increase reaction count
          await sdk.rawQuery(`
            UPDATE ${projectId}_${targetTable} 
            SET reaction_count = reaction_count + 1,
                updated_at = '${UtilService.sqlDateTimeFormat(now)}'
            WHERE id = ${target_id}
          `);

          result = {
            action: "added",
            reaction_type: reaction_type,
            user_reaction: reaction_type,
          };
        }

        // Get updated reaction counts by type
        const reactionCountsQuery = `
          SELECT reaction_type, COUNT(*) as count
          FROM ${projectId}_reaction
          WHERE target_type = '${target_type}' AND target_id = ${target_id}
          GROUP BY reaction_type
        `;

        const reactionCounts = await sdk.rawQuery(reactionCountsQuery);

        return res.json({
          error: false,
          data: {
            ...result,
            reaction_counts: reactionCounts.reduce((acc, curr) => {
              acc[curr.reaction_type] = curr.count;
              return acc;
            }, {}),
          },
          message: `Reaction ${result.action} successfully`,
        });
      } catch (error) {
        console.error("Error toggling reaction:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error",
          error: error.message,
        });
      }
    }
  );

  // Delete a comment
  app.delete(
    "/v2/api/kanglink/custom/trainer/feed/comments/:comment_id",
    [TokenMiddleware({ role: "trainer|member|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { comment_id } = req.params;
        const userId = req.user_id;
        const userRole = req.role;

        if (!comment_id) {
          return res.status(400).json({
            error: true,
            message: "comment_id is required",
          });
        }

        sdk.setProjectId("kanglink");
        const projectId = "kanglink";

        // Get comment details with post info
        const commentQuery = `
          SELECT 
            c.id, c.user_id, c.post_id, c.parent_comment_id,
            p.user_id as post_owner_id, p.visibility_scope
          FROM ${projectId}_comment c
          LEFT JOIN ${projectId}_post_feed p ON c.post_id = p.id
          WHERE c.id = ${comment_id}
        `;

        const commentResult = await sdk.rawQuery(commentQuery);

        if (!commentResult || commentResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Comment not found",
          });
        }

        const comment = commentResult[0];

        // Check permissions - user can delete their own comments, trainers can delete any comments on their posts
        if (userRole === "member" && comment.user_id !== userId) {
          return res.status(403).json({
            error: true,
            message: "You can only delete your own comments",
          });
        }

        if (userRole === "trainer" && comment.user_id !== userId && comment.post_owner_id !== userId) {
          return res.status(403).json({
            error: true,
            message: "You can only delete your own comments or comments on your posts",
          });
        }

        // Count replies to this comment
        const repliesCountQuery = `
          SELECT COUNT(*) as replies_count
          FROM ${projectId}_comment
          WHERE parent_comment_id = ${comment_id}
        `;

        const repliesResult = await sdk.rawQuery(repliesCountQuery);
        const repliesCount = repliesResult[0]?.replies_count || 0;

        // Delete the comment and all its replies
        if (repliesCount > 0) {
          await sdk.rawQuery(`
            DELETE FROM ${projectId}_comment 
            WHERE parent_comment_id = ${comment_id}
          `);
        }

        await sdk.rawQuery(`
          DELETE FROM ${projectId}_comment 
          WHERE id = ${comment_id}
        `);

        // Update post comment count (subtract 1 + number of replies)
        const totalDeleted = 1 + parseInt(repliesCount);
        const now = new Date();

        await sdk.rawQuery(`
          UPDATE ${projectId}_post_feed 
          SET comment_count = GREATEST(0, comment_count - ${totalDeleted}),
              updated_at = '${UtilService.sqlDateTimeFormat(now)}'
          WHERE id = ${comment.post_id}
        `);

        return res.json({
          error: false,
          data: {
            deleted_comment_id: comment_id,
            deleted_replies_count: repliesCount,
          },
          message: "Comment deleted successfully",
        });
      } catch (error) {
        console.error("Error deleting comment:", error);
        return res.status(500).json({
          error: true,
          message: "Internal server error",
          error: error.message,
        });
      }
    }
  );
};
