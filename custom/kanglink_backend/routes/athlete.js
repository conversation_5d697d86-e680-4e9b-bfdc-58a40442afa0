const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const NotificationService = require("../services/NotificationService");

module.exports = function (app) {
  // Get athlete's enrollments with program, split, and trainer data
  // Categorized by status: owned, subscribed, pending refund, and refunded
  app.get(
    "/v2/api/kanglink/custom/athlete/library",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const athleteId = req.user_id;

        sdk.setProjectId("kanglink");

        // Get payout settings to determine refund time limit
        sdk.setTable("payout_settings");
        const payoutSettings = await sdk.findOne("payout_settings", {
          is_active: true,
        });
        const refundTimeHours = payoutSettings?.trainer_payout_time_hours || 24;

        // Get enrollments with comprehensive join to fetch all necessary data
        const enrollments = await sdk.rawQuery(`
          SELECT
            e.*,
            p.program_name,
            p.type_of_program,
            p.program_description,
            p.currency,
            p.image as program_image,
            s.title as split_title,
            s.equipment_required,
            s.full_price,
            s.subscription as subscription_price,
            -- Calculate duration by counting weeks for this split
            (SELECT COUNT(*) FROM kanglink_week w WHERE w.split_id = s.id) as duration_weeks,
            -- Calculate minimum price (excluding zero values)
            CASE
              WHEN s.full_price > 0 AND s.subscription > 0 THEN LEAST(s.full_price, s.subscription)
              WHEN s.full_price > 0 THEN s.full_price
              WHEN s.subscription > 0 THEN s.subscription
              ELSE 0
            END as price,
            -- Trainer information from user.data field
            trainer.id as trainer_id,
            JSON_UNQUOTE(JSON_EXTRACT(trainer.data, '$.full_name')) as trainer_full_name,
            JSON_UNQUOTE(JSON_EXTRACT(trainer.data, '$.first_name')) as trainer_first_name,
            JSON_UNQUOTE(JSON_EXTRACT(trainer.data, '$.last_name')) as trainer_last_name,
            JSON_UNQUOTE(JSON_EXTRACT(trainer.data, '$.photo')) as trainer_photo,
            trainer.email as trainer_email,
            -- Calculate if refund is still available (within time limit)
            CASE
              WHEN e.payment_type = 'one_time' AND e.status != 'refund' AND e.payment_status = 'paid'
                AND TIMESTAMPDIFF(HOUR, e.created_at, NOW()) <= ${refundTimeHours}
              THEN 1
              ELSE 0
            END as can_request_refund,
            -- Calculate hours remaining for refund
            CASE
              WHEN e.payment_type = 'one_time' AND e.status != 'refund' AND e.payment_status = 'paid'
                AND TIMESTAMPDIFF(HOUR, e.created_at, NOW()) <= ${refundTimeHours}
              THEN ${refundTimeHours} - TIMESTAMPDIFF(HOUR, e.created_at, NOW())
              ELSE 0
            END as refund_hours_remaining,
            -- Check if subscription billing failed (for expired subscriptions)
            CASE
              WHEN e.payment_type = 'subscription' AND e.payment_status = 'failed'
              THEN 1
              ELSE 0
            END as billing_failed,
            -- Calculate days until subscription expires
            CASE
              WHEN e.payment_type = 'subscription' AND e.expiry_date IS NOT NULL
              THEN DATEDIFF(e.expiry_date, NOW())
              ELSE NULL
            END as days_until_expiry,
            -- Get Stripe subscription details for more accurate expiration
            ss.status as stripe_subscription_status,
            ss.is_lifetime as stripe_is_lifetime,
            JSON_UNQUOTE(JSON_EXTRACT(ss.object, '$.current_period_end')) as stripe_period_end,
            JSON_UNQUOTE(JSON_EXTRACT(ss.object, '$.cancel_at_period_end')) as will_cancel_at_period_end,
            -- Refund request information
            rr.id as refund_request_id,
            rr.reason as refund_reason,
            rr.status as refund_request_status,
            rr.created_at as refund_request_created_at,
            rr.updated_at as refund_request_updated_at,
            rr.processed_at as refund_request_processed_at,
            rr.admin_notes as refund_admin_notes
          FROM kanglink_enrollment e
          JOIN kanglink_split s ON e.split_id = s.id
          JOIN kanglink_program p ON s.program_id = p.id
          JOIN kanglink_user trainer ON p.user_id = trainer.id
          LEFT JOIN kanglink_stripe_subscription ss ON e.stripe_subscription_id = ss.stripe_id
          LEFT JOIN kanglink_refund_request rr ON e.id = rr.enrollment_id
          WHERE e.athlete_id = ${athleteId}
          ORDER BY e.created_at DESC
        `);

        // Categorize enrollments based on status and payment_status
        const categorized = {
          owned: [],
          subscribed: [],
          cancelled: [],
          pending_refund: [],
          refunded: [],
        };

        enrollments.forEach((enrollment) => {
          // Add computed fields
          const enrichedEnrollment = {
            ...enrollment,
            // Determine category based on status and payment_status
            category: getCategoryFromStatus(enrollment),
            // Format pricing information
            pricing: {
              full_price: enrollment.full_price || 0,
              subscription_price: enrollment.subscription_price || 0,
              price: enrollment.price || 0, // Minimum price
              currency: enrollment.currency || "USD",
            },
            // Trainer information
            trainer: {
              id: enrollment.trainer_id,
              email: enrollment.trainer_email,
              full_name: enrollment.trainer_full_name,
              first_name: enrollment.trainer_first_name,
              last_name: enrollment.trainer_last_name,
              photo: enrollment.trainer_photo,
            },
            // Program information
            program: {
              id: enrollment.program_id,
              name: enrollment.program_name,
              type: enrollment.type_of_program,
              description: enrollment.program_description,
              image_url: enrollment.program_image,
            },
            // Split information
            split: {
              id: enrollment.split_id,
              title: enrollment.split_title,
              equipment_required: enrollment.equipment_required,
              duration_weeks: enrollment.duration_weeks,
            },
            // Refund information
            refund_info: {
              can_request: enrollment.can_request_refund === 1,
              hours_remaining: enrollment.refund_hours_remaining || 0,
              time_limit_hours: refundTimeHours,
              // Refund request details
              request_id: enrollment.refund_request_id,
              request_reason: enrollment.refund_reason,
              request_status: enrollment.refund_request_status,
              request_created_at: enrollment.refund_request_created_at,
              request_updated_at: enrollment.refund_request_updated_at,
              request_processed_at: enrollment.refund_request_processed_at,
              admin_notes: enrollment.refund_admin_notes,
            },
            // Subscription information
            subscription_info: {
              billing_failed: enrollment.billing_failed === 1,
              stripe_subscription_id: enrollment.stripe_subscription_id,
              days_until_expiry: calculateDaysUntilExpiry(enrollment),
              expiry_date: enrollment.expiry_date,
              stripe_period_end: enrollment.stripe_period_end,
              stripe_status: enrollment.stripe_subscription_status,
              will_cancel_at_period_end: enrollment.will_cancel_at_period_end == "false" ? false : true,
            },
          };

          // Remove redundant fields to clean up response
          delete enrichedEnrollment.trainer_full_name;
          delete enrichedEnrollment.trainer_first_name;
          delete enrichedEnrollment.trainer_last_name;
          delete enrichedEnrollment.trainer_photo;
          delete enrichedEnrollment.trainer_email;
          delete enrichedEnrollment.program_name;
          delete enrichedEnrollment.type_of_program;
          delete enrichedEnrollment.program_description;
          delete enrichedEnrollment.program_image;
          delete enrichedEnrollment.split_title;
          delete enrichedEnrollment.equipment_required;
          delete enrichedEnrollment.full_price;
          delete enrichedEnrollment.subscription_price;
          delete enrichedEnrollment.price;
          delete enrichedEnrollment.duration_weeks;
          delete enrichedEnrollment.can_request_refund;
          delete enrichedEnrollment.refund_hours_remaining;
          delete enrichedEnrollment.billing_failed;
          // Clean up refund request fields
          delete enrichedEnrollment.refund_request_id;
          delete enrichedEnrollment.refund_reason;
          delete enrichedEnrollment.refund_request_status;
          delete enrichedEnrollment.refund_request_created_at;
          delete enrichedEnrollment.refund_request_updated_at;
          delete enrichedEnrollment.refund_request_processed_at;
          delete enrichedEnrollment.refund_admin_notes;

          // Categorize enrollment
          const category = enrichedEnrollment.category;
          if (categorized[category]) {
            categorized[category].push(enrichedEnrollment);
          }
        });

        return res.status(200).json({
          error: false,
          data: categorized,
          meta: {
            total_enrollments: enrollments.length,
            refund_time_limit_hours: refundTimeHours,
            categories: {
              owned: categorized.owned.length,
              subscribed: categorized.subscribed.length,
              cancelled: categorized.cancelled.length,
              pending_refund: categorized.pending_refund.length,
              refunded: categorized.refunded.length,
            },
          },
        });
      } catch (err) {
        console.error("Get athlete enrollments error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get enrollments",
        });
      }
    }
  );

  // Athlete Favorite Programs
  app.get(
    "/v2/api/kanglink/custom/athlete/favorite/programs",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const userId = req.user_id;

        sdk.setProjectId("kanglink");

        const query = `
        SELECT
          f.id as favorite_id,
          f.created_at as favorited_at,
          p.id,
          p.program_name as name,
          p.program_description as description,
          p.type_of_program as type,
          p.image as image_url,
          p.status,
          p.created_at,
          p.updated_at,
          u.id as trainer_id,
          JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.full_name')) as trainer_full_name,
          JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.first_name')) as trainer_first_name,
          JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.last_name')) as trainer_last_name,
          JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.photo')) as trainer_photo,
          (
            SELECT AVG(pf.rating)
            FROM kanglink_post_feed pf
            WHERE pf.program_id = p.id AND pf.post_type = 'review' AND pf.rating IS NOT NULL
          ) as average_rating,
          (
            SELECT COUNT(*)
            FROM kanglink_post_feed pf
            WHERE pf.program_id = p.id AND pf.post_type = 'review' AND pf.rating IS NOT NULL
          ) as review_count,
          (
            SELECT MIN(
              CASE
                WHEN s.full_price > 0 AND s.subscription > 0 THEN
                  LEAST(s.full_price, s.subscription)
                WHEN s.full_price > 0 THEN s.full_price
                WHEN s.subscription > 0 THEN s.subscription
                ELSE NULL
              END
            )
            FROM kanglink_split s
            WHERE s.program_id = p.id
          ) as price
        FROM kanglink_favorite f
        JOIN kanglink_program p ON f.favorite_id = p.id
        JOIN kanglink_user u ON p.user_id = u.id
        WHERE f.user_id = ?
          AND f.favorite_type = 'program'
          AND p.status = 'published'
        ORDER BY f.created_at DESC
      `;

        const favoritePrograms = await sdk.rawQuery(query, [userId]);

        return res.json({
          error: false,
          data: favoritePrograms.map((program) => ({
            favorite_id: program.favorite_id,
            favorited_at: program.favorited_at,
            id: program.id,
            name: program.name,
            description: program.description,
            type: program.type,
            image_url: program.image_url,
            status: program.status,
            created_at: program.created_at,
            updated_at: program.updated_at,
            price: program.price,
            average_rating: program.average_rating
              ? parseFloat(program.average_rating)
              : null,
            review_count: parseInt(program.review_count) || 0,
            trainer: {
              id: program.trainer_id,
              full_name: program.trainer_full_name,
              first_name: program.trainer_first_name,
              last_name: program.trainer_last_name,
              photo: program.trainer_photo,
            },
          })),
        });
      } catch (error) {
        console.error("Error fetching favorite programs:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch favorite programs",
        });
      }
    }
  );

  app.put(
    "/v2/api/kanglink/custom/athlete/favorite/programs/:programId",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const userId = req.user_id;
        const programId = parseInt(req.params.programId);

        sdk.setProjectId("kanglink");

        if (!programId) {
          return res.status(400).json({
            error: true,
            message: "Invalid program ID",
          });
        }

        // Check if program exists and is active
        sdk.setTable("program");
        const program = await sdk.findById("program", programId);
        if (!program || program.status !== "published") {
          return res.status(404).json({
            error: true,
            message: "Program not found or inactive",
          });
        }

        // Check if already favorited
        const existingFavorite = await sdk.rawQuery(
          "SELECT id FROM kanglink_favorite WHERE user_id = ? AND favorite_type = 'program' AND favorite_id = ?",
          [userId, programId]
        );

        if (existingFavorite.length > 0) {
          // Remove from favorites
          const deleteResult = await sdk.rawQuery(
            "DELETE FROM kanglink_favorite WHERE user_id = ? AND favorite_type = 'program' AND favorite_id = ?",
            [userId, programId]
          );

          return res.json({
            error: false,
            data: {
              is_favorite: false,
              action: "removed"
            },
            message: "Program removed from favorites successfully",
          });
        } else {
          // Add to favorites
          sdk.setTable("favorite");
          const favoriteData = {
            user_id: userId,
            favorite_type: "program",
            favorite_id: programId,
          };

          const result = await sdk.create("favorite", favoriteData);

          return res.json({
            error: false,
            data: {
              is_favorite: true,
              action: "added",
              favorite_id: result.insertId
            },
            message: "Program added to favorites successfully",
          });
        }
      } catch (error) {
        console.error("Error toggling program favorite:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to toggle program favorite",
        });
      }
    }
  );

  // Athlete Favorite Trainers
  app.get(
    "/v2/api/kanglink/custom/athlete/favorite/trainers",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const userId = req.user_id;

        sdk.setProjectId("kanglink");

        const query = `
        SELECT
          f.id as favorite_id,
          f.created_at as favorited_at,
          u.id,
          u.email,
          JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.full_name')) as full_name,
          JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.first_name')) as first_name,
          JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.last_name')) as last_name,
          JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.photo')) as photo,
          JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.bio')) as bio,
          JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.location')) as location,
          (
            SELECT AVG(pf.rating)
            FROM kanglink_post_feed pf
            JOIN kanglink_program p ON pf.program_id = p.id
            WHERE p.user_id = u.id AND pf.post_type = 'review' AND pf.rating IS NOT NULL
          ) as average_rating,
          (
            SELECT COUNT(*)
            FROM kanglink_post_feed pf
            JOIN kanglink_program p ON pf.program_id = p.id
            WHERE p.user_id = u.id AND pf.post_type = 'review' AND pf.rating IS NOT NULL
          ) as review_count,
          (
            SELECT COUNT(*)
            FROM kanglink_program p
            WHERE p.user_id = u.id AND p.status = 'active'
          ) as program_count
        FROM kanglink_favorite f
        JOIN kanglink_user u ON f.favorite_id = u.id
        WHERE f.user_id = ?
          AND f.favorite_type = 'trainer'
          AND u.status = '1'
        ORDER BY f.created_at DESC
      `;

        const favoriteTrainers = await sdk.rawQuery(query, [userId]);

        return res.json({
          error: false,
          data: favoriteTrainers.map((trainer) => ({
            favorite_id: trainer.favorite_id,
            favorited_at: trainer.favorited_at,
            id: trainer.id,
            email: trainer.email,
            full_name: trainer.full_name,
            first_name: trainer.first_name,
            last_name: trainer.last_name,
            photo: trainer.photo,
            bio: trainer.bio,
            location: trainer.location,
            average_rating: trainer.average_rating
              ? parseFloat(trainer.average_rating)
              : null,
            review_count: parseInt(trainer.review_count) || 0,
            program_count: parseInt(trainer.program_count) || 0,
          })),
        });
      } catch (error) {
        console.error("Error fetching favorite trainers:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch favorite trainers",
        });
      }
    }
  );

  app.put(
    "/v2/api/kanglink/custom/athlete/favorite/trainers/:trainerId",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const userId = req.user_id;
        const trainerId = parseInt(req.params.trainerId);

        sdk.setProjectId("kanglink");

        if (!trainerId) {
          return res.status(400).json({
            error: true,
            message: "Invalid trainer ID",
          });
        }

        // Check if trainer exists and is active
        sdk.setTable("user");
        const trainer = await sdk.findById("user", trainerId);
        if (!trainer || !trainer.status) {
          return res.status(404).json({
            error: true,
            message: "Trainer not found or inactive",
          });
        }

        // Check if already favorited
        const existingFavorite = await sdk.rawQuery(
          "SELECT id FROM kanglink_favorite WHERE user_id = ? AND favorite_type = 'trainer' AND favorite_id = ?",
          [userId, trainerId]
        );

        if (existingFavorite.length > 0) {
          // Remove from favorites
          const deleteResult = await sdk.rawQuery(
            "DELETE FROM kanglink_favorite WHERE user_id = ? AND favorite_type = 'trainer' AND favorite_id = ?",
            [userId, trainerId]
          );

          return res.json({
            error: false,
            data: {
              is_favorite: false,
              action: "removed"
            },
            message: "Trainer removed from favorites successfully",
          });
        } else {
          // Add to favorites
          sdk.setTable("favorite");
          const favoriteData = {
            user_id: userId,
            favorite_type: "trainer",
            favorite_id: trainerId,
          };

          const result = await sdk.create("favorite", favoriteData);

          return res.json({
            error: false,
            data: {
              is_favorite: true,
              action: "added",
              favorite_id: result.insertId
            },
            message: "Trainer added to favorites successfully",
          });
        }
      } catch (error) {
        console.error("Error toggling trainer favorite:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to toggle trainer favorite",
        });
      }
    }
  );

  // Notification Endpoints for Athletes
  app.get(
    "/v2/api/kanglink/custom/athlete/notifications",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const athleteId = req.user_id;
        const { page = 1, limit = 20, unread_only = false } = req.query;

        sdk.setProjectId("kanglink");

        const notificationService = new NotificationService(sdk);
        const result = await notificationService.getUserNotifications(
          athleteId,
          parseInt(page),
          parseInt(limit),
          unread_only === "true"
        );

        if (!result.success) {
          return res.status(500).json({
            error: true,
            message: result.error || "Failed to fetch notifications",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Notifications retrieved successfully",
          data: result,
        });
      } catch (error) {
        console.error("Error fetching athlete notifications:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch notifications",
        });
      }
    }
  );

  // Mark notification as read
  app.put(
    "/v2/api/kanglink/custom/athlete/notifications/:id/read",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const athleteId = req.user_id;
        const notificationId = req.params.id;

        sdk.setProjectId("kanglink");

        // Verify notification belongs to athlete
        const notification = await sdk.rawQuery(`
          SELECT id FROM kanglink_notification
          WHERE id = ? AND user_id = ?
        `, [notificationId, athleteId]);

        if (!notification || notification.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Notification not found",
          });
        }

        const notificationService = new NotificationService(sdk);
        const success = await notificationService.markNotificationAsRead(notificationId, athleteId);

        if (!success) {
          return res.status(500).json({
            error: true,
            message: "Failed to mark notification as read",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Notification marked as read",
        });
      } catch (error) {
        console.error("Error marking notification as read:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to mark notification as read",
        });
      }
    }
  );

  // Mark all notifications as read
  app.put(
    "/v2/api/kanglink/custom/athlete/notifications/read-all",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const athleteId = req.user_id;

        sdk.setProjectId("kanglink");

        const notificationService = new NotificationService(sdk);
        const success = await notificationService.markAllNotificationsAsRead(athleteId);

        if (!success) {
          return res.status(500).json({
            error: true,
            message: "Failed to mark all notifications as read",
          });
        }

        return res.status(200).json({
          error: false,
          message: "All notifications marked as read",
        });
      } catch (error) {
        console.error("Error marking all notifications as read:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to mark all notifications as read",
        });
      }
    }
  );

  // Get unread notification count
  app.get(
    "/v2/api/kanglink/custom/athlete/notifications/unread-count",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const athleteId = req.user_id;

        sdk.setProjectId("kanglink");

        const notificationService = new NotificationService(sdk);
        const count = await notificationService.getUnreadNotificationCount(athleteId);

        return res.status(200).json({
          error: false,
          message: "Unread count retrieved successfully",
          data: {
            unread_count: count,
          },
        });
      } catch (error) {
        console.error("Error getting unread notification count:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to get unread notification count",
        });
      }
    }
  );

  // Helper function to calculate accurate days until subscription expiry
  function calculateDaysUntilExpiry(enrollment) {
    if (enrollment.payment_type !== "subscription") {
      return null; // Only applicable to subscriptions
    }

    let daysUntilExpiry = null;

    // Priority 1: Use Stripe subscription current_period_end (most accurate)
    if (enrollment.stripe_period_end) {
      const stripeEndDate = new Date(enrollment.stripe_period_end);
      const today = new Date();
      const diffTime = stripeEndDate - today;
      daysUntilExpiry = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    // Priority 2: Use enrollment expiry_date as fallback
    else if (enrollment.expiry_date) {
      const expiryDate = new Date(enrollment.expiry_date);
      const today = new Date();
      const diffTime = expiryDate - today;
      daysUntilExpiry = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    // Priority 3: Use calculated days_until_expiry from SQL
    else if (enrollment.days_until_expiry !== null) {
      daysUntilExpiry = enrollment.days_until_expiry;
    }

    return daysUntilExpiry;
  }

  // Helper function to determine enrollment category
  function getCategoryFromStatus(enrollment) {
    const { status, payment_status, payment_type } = enrollment;

    // Pending refund: status = 'refund' AND payment_status != 'refunded'
    if (status === "refund" && payment_status !== "refunded") {
      return "pending_refund";
    }

    // Complete refund: status = 'refund' AND payment_status = 'refunded'
    if (status === "refund" && payment_status === "refunded") {
      return "refunded";
    }

    // Cancelled: subscription that has been cancelled
    if (payment_type === "subscription" && status === "cancelled") {
      return "cancelled";
    }

    // Owned: one_time payment that is active and paid
    if (
      payment_type === "one_time" &&
      status === "active" &&
      payment_status === "paid"
    ) {
      return "owned";
    }

    // Subscribed: subscription payment (active or with billing issues)
    if (payment_type === "subscription") {
      return "subscribed";
    }

    // Default to owned for other active enrollments
    if (status === "active") {
      return "owned";
    }

    // For any other status, categorize based on payment type
    return payment_type === "subscription" ? "subscribed" : "owned";
  }
};
