const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const StripeService = require("../../../baas/services/StripeService");
const CommissionProcessingService = require("../services/CommissionProcessingService");
const NotificationService = require("../services/NotificationService");

module.exports = function (app) {
  // Test endpoint without auth
  app.get(
    "/v2/api/kanglink/custom/trainer/dashboard/test",
    async function (req, res) {
      return res.status(200).json({
        error: false,
        message: "Trainer dashboard routes are working!",
        timestamp: new Date().toISOString(),
      });
    }
  );

  // Trainer Dashboard Stats Endpoint
  app.get(
    "/v2/api/kanglink/custom/trainer/dashboard/stats",
    [TokenMiddleware()],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        const trainerId = req.user_id;

        // Get active programs count (published status)
        const activeProgramsQuery = `
        SELECT COUNT(*) as count
        FROM kanglink_program
        WHERE user_id = ? AND status = 'published'
      `;
        const activeProgramsResult = await sdk.rawQuery(activeProgramsQuery, [
          trainerId,
        ]);
        const activeProgramsCount = activeProgramsResult[0]?.count || 0;

        // Get currently enrolled athletes count
        const activeAthletesQuery = `
        SELECT COUNT(DISTINCT athlete_id) as count
        FROM kanglink_enrollment
        WHERE trainer_id = ? AND status = 'active' AND payment_status = 'paid'
      `;
        const activeAthletesResult = await sdk.rawQuery(activeAthletesQuery, [
          trainerId,
        ]);
        const activeAthletesCount = activeAthletesResult[0]?.count || 0;

        // Get revenue generated this month from commission table
        const currentMonth = new Date();
        const startOfMonth = new Date(
          currentMonth.getFullYear(),
          currentMonth.getMonth(),
          1
        );
        const endOfMonth = new Date(
          currentMonth.getFullYear(),
          currentMonth.getMonth() + 1,
          0,
          23,
          59,
          59
        );

        const revenueQuery = `
        SELECT COALESCE(SUM(trainer_amount), 0) as revenue
        FROM kanglink_commission
        WHERE trainer_id = ?
        AND payout_status IN ('pending', 'processed')
        AND created_at >= ?
        AND created_at <= ?
      `;
        const revenueResult = await sdk.rawQuery(revenueQuery, [
          trainerId,
          startOfMonth.toISOString().slice(0, 19).replace("T", " "),
          endOfMonth.toISOString().slice(0, 19).replace("T", " "),
        ]);
        const monthlyRevenue = revenueResult[0]?.revenue || 0;

        // Get additional stats
        const totalProgramsQuery = `
        SELECT COUNT(*) as count
        FROM kanglink_program
        WHERE user_id = ?
      `;
        const totalProgramsResult = await sdk.rawQuery(totalProgramsQuery, [
          trainerId,
        ]);
        const totalProgramsCount = totalProgramsResult[0]?.count || 0;

        const totalEnrollmentsQuery = `
        SELECT COUNT(DISTINCT enrollment_id) as count
        FROM kanglink_commission
        WHERE trainer_id = ?
      `;
        const totalEnrollmentsResult = await sdk.rawQuery(
          totalEnrollmentsQuery,
          [trainerId]
        );
        const totalEnrollmentsCount = totalEnrollmentsResult[0]?.count || 0;

        // Get additional revenue stats
        const revenueStatsQuery = `
        SELECT
          COALESCE(SUM(CASE WHEN payout_status IN ('pending', 'processed') THEN trainer_amount ELSE 0 END), 0) as total_earnings,
          COALESCE(SUM(CASE WHEN payout_status = 'pending' AND payout_scheduled_at <= NOW() THEN trainer_amount ELSE 0 END), 0) as available_to_withdraw,
          COALESCE(SUM(CASE WHEN payout_status = 'pending' AND payout_scheduled_at > NOW() THEN trainer_amount ELSE 0 END), 0) as pending_payouts,
          COALESCE(MAX(currency), 'USD') as currency
        FROM kanglink_commission
        WHERE trainer_id = ?
      `;
        const revenueStatsResult = await sdk.rawQuery(revenueStatsQuery, [
          trainerId,
        ]);
        const revenueStats = revenueStatsResult[0] || {};

        return res.status(200).json({
          error: false,
          data: {
            active_programs_count: activeProgramsCount,
            active_athletes_count: activeAthletesCount,
            monthly_revenue: parseFloat(monthlyRevenue),
            total_lifetime_earnings: parseFloat(
              revenueStats.total_earnings || 0
            ),
            available_to_withdraw: parseFloat(
              revenueStats.available_to_withdraw || 0
            ),
            pending_payouts: parseFloat(revenueStats.pending_payouts || 0),
            total_programs_count: totalProgramsCount,
            total_enrollments_count: totalEnrollmentsCount,
            currency: revenueStats.currency || "USD",
            period: {
              month: currentMonth.getMonth() + 1,
              year: currentMonth.getFullYear(),
            },
          },
        });
      } catch (error) {
        console.error("Error fetching trainer dashboard stats:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch dashboard stats",
        });
      }
    }
  );

  // Trainer Notifications Endpoint
  app.get(
    "/v2/api/kanglink/custom/trainer/dashboard/notifications",
    [TokenMiddleware()],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        const trainerId = req.user_id;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const unreadOnly = req.query.unread_only === "true";
        const category = req.query.category; // progress, enrollment, payment, communication, system, general

        const notificationService = new NotificationService(sdk);
        const result = await notificationService.getUserNotifications(
          trainerId,
          page,
          limit,
          unreadOnly
        );

        if (!result.success) {
          return res.status(500).json({
            error: true,
            message: result.error || "Failed to fetch notifications",
          });
        }

        // Filter by category if specified
        if (category && result.notifications) {
          result.notifications = result.notifications.filter(
            notification => notification.category === category
          );
        }

        return res.status(200).json({
          error: false,
          message: "Notifications retrieved successfully",
          data: result,
        });
      } catch (error) {
        console.error("Error fetching trainer notifications:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch notifications",
        });
      }
    }
  );

  // Trainer Activities Endpoint
  app.get(
    "/v2/api/kanglink/custom/trainer/dashboard/activities",
    [TokenMiddleware()],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        const trainerId = req.user_id;
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const activityType = req.query.activity_type;
        const visibility = req.query.visibility || "all"; // all, public, private, trainer_only
        const offset = (page - 1) * limit;

        // Build WHERE conditions for trainer-related activities
        let whereConditions = [
          "(a.user_id = ? OR a.actor_id = ? OR a.user_id IN (SELECT athlete_id FROM kanglink_enrollment WHERE trainer_id = ?))",
        ];
        let queryParams = [trainerId, trainerId, trainerId];

        if (activityType) {
          whereConditions.push("a.activity_type = ?");
          queryParams.push(activityType);
        }

        if (visibility !== "all") {
          whereConditions.push("a.visibility = ?");
          queryParams.push(visibility);
        }

        const whereClause = whereConditions.join(" AND ");

        // Get activities with user information
        const activitiesQuery = `
        SELECT
          a.*,
          u.data as user_data,
          actor.data as actor_data,
          CASE
            WHEN a.related_type = 'program' THEN p.program_name
            WHEN a.related_type = 'enrollment' THEN CONCAT('Enrollment #', a.related_id)
            ELSE NULL
          END as related_name
        FROM kanglink_activity a
        LEFT JOIN kanglink_user u ON a.user_id = u.id
        LEFT JOIN kanglink_user actor ON a.actor_id = actor.id
        LEFT JOIN kanglink_program p ON a.related_type = 'program' AND a.related_id = p.id
        WHERE ${whereClause}
        ORDER BY a.created_at DESC
        LIMIT ? OFFSET ?
      `;
        queryParams.push(limit, offset);

        const activities = await sdk.rawQuery(activitiesQuery, queryParams);

        // Get total count for pagination
        const countQuery = `
        SELECT COUNT(*) as total
        FROM kanglink_activity a
        WHERE ${whereClause}
      `;
        const countResult = await sdk.rawQuery(
          countQuery,
          queryParams.slice(0, -2)
        ); // Remove limit and offset
        const total = countResult[0]?.total || 0;

        // Format activities
        const formattedActivities = activities.map((activity) => {
          let user_name = null;
          let actor_name = null;

          // Parse user data
          if (activity.user_data) {
            try {
              const userData = JSON.parse(activity.user_data);
              user_name =
                userData.full_name ||
                (userData.first_name && userData.last_name
                  ? `${userData.first_name} ${userData.last_name}`
                  : null);
            } catch (e) {
              // Handle parsing error
            }
          }

          // Parse actor data
          if (activity.actor_data) {
            try {
              const actorData = JSON.parse(activity.actor_data);
              actor_name =
                actorData.full_name ||
                (actorData.first_name && actorData.last_name
                  ? `${actorData.first_name} ${actorData.last_name}`
                  : null);
            } catch (e) {
              // Handle parsing error
            }
          }

          return {
            id: activity.id,
            user_id: activity.user_id,
            user_name: user_name,
            actor_id: activity.actor_id,
            actor_name: actor_name,
            activity_type: activity.activity_type,
            title: activity.title,
            description: activity.description,
            metadata: activity.metadata ? JSON.parse(activity.metadata) : null,
            visibility: activity.visibility,
            related_id: activity.related_id,
            related_type: activity.related_type,
            related_name: activity.related_name,
            created_at: activity.created_at,
          };
        });

        return res.status(200).json({
          error: false,
          data: {
            activities: formattedActivities,
            pagination: {
              page,
              limit,
              total,
              total_pages: Math.ceil(total / limit),
              has_next: page < Math.ceil(total / limit),
              has_prev: page > 1,
            },
          },
        });
      } catch (error) {
        console.error("Error fetching trainer activities:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch activities",
        });
      }
    }
  );

  // Mark Notification as Read
  app.put(
    "/v2/api/kanglink/custom/trainer/dashboard/notifications/:id/read",
    [TokenMiddleware()],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        const trainerId = req.user_id;
        const notificationId = req.params.id;

        // Verify notification belongs to trainer
        const checkQuery = `
        SELECT id FROM kanglink_notification
        WHERE id = ? AND user_id = ?
      `;
        const checkResult = await sdk.rawQuery(checkQuery, [
          notificationId,
          trainerId,
        ]);

        if (checkResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Notification not found",
          });
        }

        const notificationService = new NotificationService(sdk);
        const success = await notificationService.markNotificationAsRead(notificationId, trainerId);

        if (!success) {
          return res.status(500).json({
            error: true,
            message: "Failed to mark notification as read",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Notification marked as read",
        });
      } catch (error) {
        console.error("Error marking notification as read:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to mark notification as read",
        });
      }
    }
  );

  // Mark All Notifications as Read
  app.put(
    "/v2/api/kanglink/custom/trainer/dashboard/notifications/read-all",
    [TokenMiddleware()],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        const trainerId = req.user_id;

        const notificationService = new NotificationService(sdk);
        const success = await notificationService.markAllNotificationsAsRead(trainerId);

        if (!success) {
          return res.status(500).json({
            error: true,
            message: "Failed to mark all notifications as read",
          });
        }

        return res.status(200).json({
          error: false,
          message: "All notifications marked as read",
        });
      } catch (error) {
        console.error("Error marking all notifications as read:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to mark all notifications as read",
        });
      }
    }
  );

  // Get Dashboard Summary (Combined endpoint for quick overview)
  app.get(
    "/v2/api/kanglink/custom/trainer/dashboard/summary",
    [TokenMiddleware()],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        const trainerId = req.user_id;

        // Get basic stats
        const statsQuery = `
        SELECT
          (SELECT COUNT(*) FROM kanglink_program WHERE user_id = ? AND status IN ('published', 'live', 'active')) as active_programs,
          (SELECT COUNT(DISTINCT athlete_id) FROM kanglink_enrollment WHERE trainer_id = ? AND status = 'active' AND payment_status = 'paid') as active_athletes,
          (SELECT COUNT(*) FROM kanglink_notification WHERE user_id = ? AND is_read = FALSE) as unread_notifications
      `;
        const statsResult = await sdk.rawQuery(statsQuery, [
          trainerId,
          trainerId,
          trainerId,
        ]);
        const stats = statsResult[0] || {};

        // Get recent notifications (last 5)
        const recentNotificationsQuery = `
        SELECT id, notification_type, title, message, created_at, is_read
        FROM kanglink_notification
        WHERE user_id = ?
        ORDER BY created_at DESC
        LIMIT 5
      `;
        const recentNotifications = await sdk.rawQuery(
          recentNotificationsQuery,
          [trainerId]
        );

        // Get recent activities (last 5)
        const recentActivitiesQuery = `
        SELECT a.id, a.activity_type, a.title, a.created_at, u.data as user_data
        FROM kanglink_activity a
        LEFT JOIN kanglink_user u ON a.user_id = u.id
        WHERE a.user_id = ? OR a.actor_id = ? OR a.user_id IN (SELECT athlete_id FROM kanglink_enrollment WHERE trainer_id = ?)
        ORDER BY a.created_at DESC
        LIMIT 5
      `;
        const recentActivities = await sdk.rawQuery(recentActivitiesQuery, [
          trainerId,
          trainerId,
          trainerId,
        ]);

        return res.status(200).json({
          error: false,
          data: {
            stats: {
              active_programs_count: stats.active_programs || 0,
              active_athletes_count: stats.active_athletes || 0,
              unread_notifications_count: stats.unread_notifications || 0,
            },
            recent_notifications: recentNotifications.map((n) => ({
              id: n.id,
              type: n.notification_type,
              title: n.title,
              message: n.message,
              created_at: n.created_at,
              is_read: Boolean(n.is_read),
            })),
            recent_activities: recentActivities.map((a) => {
              let user_name = null;
              if (a.user_data) {
                try {
                  const userData = JSON.parse(a.user_data);
                  user_name =
                    userData.full_name ||
                    (userData.first_name && userData.last_name
                      ? `${userData.first_name} ${userData.last_name}`
                      : null);
                } catch (e) {
                  // Handle parsing error
                }
              }
              return {
                id: a.id,
                type: a.activity_type,
                title: a.title,
                user_name: user_name,
                created_at: a.created_at,
              };
            }),
          },
        });
      } catch (error) {
        console.error("Error fetching dashboard summary:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch dashboard summary",
        });
      }
    }
  );
};
