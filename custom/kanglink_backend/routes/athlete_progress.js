const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const UtilService = require("../../../baas/services/UtilService");
const NotificationService = require("../services/NotificationService");
const ActivityService = require("../services/ActivityService");

module.exports = function (app) {
  // Mark an exercise as complete
  app.post(
    "/v2/api/kanglink/custom/athlete/exercise/complete",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const athleteId = req.user_id;
        const {
          enrollment_id,
          exercise_instance_id,
          sets_completed,
          reps_completed,
          weight_used,
          time_taken_seconds,
          difficulty_rating,
          notes,
        } = req.body;

        // Validate required fields
        if (!enrollment_id || !exercise_instance_id) {
          return res.status(400).json({
            error: true,
            message: "enrollment_id and exercise_instance_id are required",
          });
        }

        sdk.setProjectId("kanglink");

        // Get exercise instance details with session and day info
        const exerciseDetails = await sdk.rawQuery(`
          SELECT ei.*, s.day_id, d.week_id, w.split_id, sp.program_id, p.user_id as trainer_id
          FROM kanglink_exercise_instance ei
          JOIN kanglink_session s ON ei.session_id = s.id
          JOIN kanglink_day d ON s.day_id = d.id
          JOIN kanglink_week w ON d.week_id = w.id
          JOIN kanglink_split sp ON w.split_id = sp.id
          JOIN kanglink_program p ON sp.program_id = p.id
          WHERE ei.id = ${exercise_instance_id}
        `);

        if (!exerciseDetails || exerciseDetails.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Exercise instance not found",
          });
        }

        const exercise = exerciseDetails[0];

        // Verify athlete is enrolled in this program
        const enrollment = await sdk.rawQuery(`
          SELECT * FROM kanglink_enrollment 
          WHERE id = ${enrollment_id} AND athlete_id = ${athleteId} AND split_id = ${exercise.split_id}
        `);

        if (!enrollment || enrollment.length === 0) {
          return res.status(403).json({
            error: true,
            message: "Not enrolled in this program",
          });
        }

        // Create or update exercise progress
        const existingProgress = await sdk.rawQuery(`
          SELECT * FROM kanglink_exercise_progress 
          WHERE athlete_id = ${athleteId} AND enrollment_id = ${enrollment_id} AND exercise_instance_id = ${exercise_instance_id}
        `);

        const progressData = {
          athlete_id: athleteId,
          enrollment_id: enrollment_id,
          exercise_instance_id: exercise_instance_id,
          session_id: exercise.session_id,
          day_id: exercise.day_id,
          is_completed: true,
          completed_at: UtilService.sqlDateTimeFormat(new Date()),
          sets_completed: sets_completed || 0,
          reps_completed: reps_completed || "",
          weight_used: weight_used || "",
          time_taken_seconds: time_taken_seconds || 0,
          difficulty_rating: difficulty_rating || null,
          notes: notes || "",
          updated_at: UtilService.sqlDateTimeFormat(new Date()),
        };

        if (existingProgress && existingProgress.length > 0) {
          // Update existing progress
          await sdk.rawQuery(`
            UPDATE kanglink_exercise_progress 
            SET is_completed = true, completed_at = '${progressData.completed_at}',
                sets_completed = ${progressData.sets_completed}, reps_completed = '${progressData.reps_completed}',
                weight_used = '${progressData.weight_used}', time_taken_seconds = ${progressData.time_taken_seconds},
                difficulty_rating = ${progressData.difficulty_rating}, notes = '${progressData.notes}',
                updated_at = '${progressData.updated_at}'
            WHERE athlete_id = ${athleteId} AND enrollment_id = ${enrollment_id} AND exercise_instance_id = ${exercise_instance_id}
          `);
        } else {
          // Create new progress record
          await sdk.rawQuery(`
            INSERT INTO kanglink_exercise_progress 
            (athlete_id, enrollment_id, exercise_instance_id, session_id, day_id, is_completed, completed_at, 
             sets_completed, reps_completed, weight_used, time_taken_seconds, difficulty_rating, notes, created_at, updated_at)
            VALUES (${athleteId}, ${enrollment_id}, ${exercise_instance_id}, ${exercise.session_id}, ${exercise.day_id}, 
                    true, '${progressData.completed_at}', ${progressData.sets_completed}, '${progressData.reps_completed}', 
                    '${progressData.weight_used}', ${progressData.time_taken_seconds}, ${progressData.difficulty_rating}, 
                    '${progressData.notes}', '${progressData.completed_at}', '${progressData.updated_at}')
          `);
        }

        // Update day progress
        await updateDayProgress(
          sdk,
          athleteId,
          enrollment_id,
          exercise.day_id,
          exercise.week_id,
          exercise.split_id
        );

        // Update overall athlete progress
        await updateAthleteProgress(
          sdk,
          athleteId,
          enrollment_id,
          exercise.split_id,
          exercise.program_id,
          exercise.trainer_id
        );

        // Create trainer notification and activity
        try {
          // const notificationService = new NotificationService(sdk);
          const activityService = new ActivityService(sdk);
          
          // Get athlete and trainer data for notifications
          const athleteData = await sdk.rawQuery(`
            SELECT id, email, data
            FROM kanglink_user 
            WHERE id = ${athleteId}
          `);
          
          const trainerData = await sdk.rawQuery(`
            SELECT id, email, data
            FROM kanglink_user 
            WHERE id = ${exercise.trainer_id}
          `);

          if (athleteData && athleteData.length > 0 && trainerData && trainerData.length > 0) {
            const athlete = athleteData[0];
            const trainer = trainerData[0];
            
            // Parse user data
            let athleteUserData = {};
            let trainerUserData = {};
            
            if (athlete.data) {
              try {
                athleteUserData = JSON.parse(athlete.data);
              } catch (e) {
                console.warn("Failed to parse athlete data:", e);
              }
            }
            
            if (trainer.data) {
              try {
                trainerUserData = JSON.parse(trainer.data);
              } catch (e) {
                console.warn("Failed to parse trainer data:", e);
              }
            }

            const progressData = {
              enrollment_id: enrollment_id,
              program_name: exercise.program_name || "Program",
              exercise_name: exercise.label || "Exercise",
              day_id: exercise.day_id,
              session_id: exercise.session_id,
            };

            const athleteDataForNotification = {
              id: athlete.id,
              email: athlete.email,
              full_name: athleteUserData.full_name || `${athleteUserData.first_name || ""} ${athleteUserData.last_name || ""}`.trim() || "Athlete",
            };

            const trainerDataForNotification = {
              id: trainer.id,
              email: trainer.email,
              full_name: trainerUserData.full_name || `${trainerUserData.first_name || ""} ${trainerUserData.last_name || ""}`.trim() || "Trainer",
            };

            // Create notification
            // await notificationService.createProgressNotification(
            //   progressData,
            //   athleteDataForNotification,
            //   trainerDataForNotification,
            //   "exercise_completed"
            // );

            // Create activity
            const exerciseData = {
              exercise_instance_id: exercise_instance_id,
              exercise_name: exercise.label || "Exercise",
              session_id: exercise.session_id,
              day_id: exercise.day_id,
              sets_completed: sets_completed || 0,
              reps_completed: reps_completed || "",
              weight_used: weight_used || "",
              time_taken_seconds: time_taken_seconds || 0,
              difficulty_rating: difficulty_rating || null,
            };

            const enrollmentData = {
              id: enrollment_id,
              split_id: exercise.split_id,
              program_id: exercise.program_id,
            };

            await activityService.createExerciseActivity(
              exerciseData,
              athleteDataForNotification,
              trainerDataForNotification,
              enrollmentData
            );
          }
        } catch (notificationError) {
          console.error("Error creating exercise completion notification/activity:", notificationError);
          // Don't fail the exercise completion if notification/activity creation fails
        }

        return res.status(200).json({
          error: false,
          message: "Exercise marked as complete",
          data: {
            exercise_instance_id: exercise_instance_id,
            completed_at: progressData.completed_at,
          },
        });
      } catch (error) {
        console.error("Mark exercise complete error:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to mark exercise as complete",
          details: error.message,
        });
      }
    }
  );

  // Mark a day as complete
  app.post(
    "/v2/api/kanglink/custom/athlete/day/complete",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const athleteId = req.user_id;
        const { enrollment_id, day_id, notes } = req.body;

        // Validate required fields
        if (!enrollment_id || !day_id) {
          return res.status(400).json({
            error: true,
            message: "enrollment_id and day_id are required",
          });
        }

        sdk.setProjectId("kanglink");

        // Get day details
        const dayDetails = await sdk.rawQuery(`
          SELECT d.*, w.split_id, sp.program_id, p.user_id as trainer_id
          FROM kanglink_day d
          JOIN kanglink_week w ON d.week_id = w.id
          JOIN kanglink_split sp ON w.split_id = sp.id
          JOIN kanglink_program p ON sp.program_id = p.id
          WHERE d.id = ${day_id}
        `);

        if (!dayDetails || dayDetails.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Day not found",
          });
        }

        const day = dayDetails[0];

        // Verify athlete is enrolled
        const enrollment = await sdk.rawQuery(`
          SELECT * FROM kanglink_enrollment 
          WHERE id = ${enrollment_id} AND athlete_id = ${athleteId} AND split_id = ${day.split_id}
        `);

        if (!enrollment || enrollment.length === 0) {
          return res.status(403).json({
            error: true,
            message: "Not enrolled in this program",
          });
        }

        // Check if all exercises in the day are completed
        const exerciseStats = await sdk.rawQuery(`
          SELECT 
            COUNT(ei.id) as total_exercises,
            COUNT(ep.id) as completed_exercises
          FROM kanglink_exercise_instance ei
          JOIN kanglink_session s ON ei.session_id = s.id
          LEFT JOIN kanglink_exercise_progress ep ON ei.id = ep.exercise_instance_id 
            AND ep.athlete_id = ${athleteId} AND ep.enrollment_id = ${enrollment_id} AND ep.is_completed = true
          WHERE s.day_id = ${day_id}
        `);

        const stats = exerciseStats[0];
        
        // Check if this is a rest day (no exercises)
        const isRestDay = stats.total_exercises === 0;
        
        // For rest days, automatically mark as completed
        // For workout days, check if all exercises are completed
        const allExercisesCompleted = isRestDay ? true : (
          stats.total_exercises === stats.completed_exercises &&
          stats.total_exercises > 0
        );

        // Check if there are uncompleted exercises and prevent day completion (only for workout days)
        if (!isRestDay && stats.total_exercises > 0 && stats.completed_exercises < stats.total_exercises) {
          const remainingExercises = stats.total_exercises - stats.completed_exercises;
          return res.status(400).json({
            error: true,
            message: `Cannot complete day. You have ${remainingExercises} uncompleted exercise${remainingExercises > 1 ? 's' : ''} remaining.`,
            data: {
              total_exercises: stats.total_exercises,
              completed_exercises: stats.completed_exercises,
              remaining_exercises: remainingExercises,
            },
          });
        }

        // Create or update day progress
        const completedAt = UtilService.sqlDateTimeFormat(new Date());
        const existingDayProgress = await sdk.rawQuery(`
          SELECT * FROM kanglink_day_progress 
          WHERE athlete_id = ${athleteId} AND enrollment_id = ${enrollment_id} AND day_id = ${day_id}
        `);

        if (existingDayProgress && existingDayProgress.length > 0) {
          // Update existing day progress
          await sdk.rawQuery(`
            UPDATE kanglink_day_progress 
            SET is_completed = ${allExercisesCompleted}, completed_at = '${completedAt}',
                total_exercises = ${
                  stats.total_exercises
                }, completed_exercises = ${stats.completed_exercises},
                notes = '${notes || ""}', updated_at = '${completedAt}'
            WHERE athlete_id = ${athleteId} AND enrollment_id = ${enrollment_id} AND day_id = ${day_id}
          `);
        } else {
          // Create new day progress
          await sdk.rawQuery(`
            INSERT INTO kanglink_day_progress 
            (athlete_id, enrollment_id, day_id, week_id, split_id, is_completed, completed_at, 
             total_exercises, completed_exercises, notes, created_at, updated_at)
            VALUES (${athleteId}, ${enrollment_id}, ${day_id}, ${
            day.week_id
          }, ${day.split_id}, 
                    ${allExercisesCompleted}, '${completedAt}', ${
            stats.total_exercises
          }, ${stats.completed_exercises}, 
                    '${notes || ""}', '${completedAt}', '${completedAt}')
          `);
        }

        // Update overall athlete progress
        await updateAthleteProgress(
          sdk,
          athleteId,
          enrollment_id,
          day.split_id,
          day.program_id,
          day.trainer_id
        );

        // Create trainer notification and activity if day is fully completed
        if (allExercisesCompleted) {
          try {
            // const notificationService = new NotificationService(sdk);
            const activityService = new ActivityService(sdk);
            
            // Get athlete and trainer data for notifications
            const athleteData = await sdk.rawQuery(`
              SELECT id, email, data
              FROM kanglink_user 
              WHERE id = ${athleteId}
            `);
            
            const trainerData = await sdk.rawQuery(`
              SELECT id, email, data
              FROM kanglink_user 
              WHERE id = ${day.trainer_id}
            `);

            if (athleteData && athleteData.length > 0 && trainerData && trainerData.length > 0) {
              const athlete = athleteData[0];
              const trainer = trainerData[0];
              
              // Parse user data
              let athleteUserData = {};
              let trainerUserData = {};
              
              if (athlete.data) {
                try {
                  athleteUserData = JSON.parse(athlete.data);
                } catch (e) {
                  console.warn("Failed to parse athlete data:", e);
                }
              }
              
              if (trainer.data) {
                try {
                  trainerUserData = JSON.parse(trainer.data);
                } catch (e) {
                  console.warn("Failed to parse trainer data:", e);
                }
              }

              // Adjust day title for rest days
              const dayTitle = isRestDay 
                ? `${day.title || `Day ${day.day_order}`} (Rest Day)`
                : day.title || `Day ${day.day_order}`;

              const progressData = {
                enrollment_id: enrollment_id,
                program_name: day.program_name || "Program",
                day_title: dayTitle,
                day_id: day_id,
                week_id: day.week_id,
              };

              const athleteDataForNotification = {
                id: athlete.id,
                email: athlete.email,
                full_name: athleteUserData.full_name || `${athleteUserData.first_name || ""} ${athleteUserData.last_name || ""}`.trim() || "Athlete",
              };

              const trainerDataForNotification = {
                id: trainer.id,
                email: trainer.email,
                full_name: trainerUserData.full_name || `${trainerUserData.first_name || ""} ${trainerUserData.last_name || ""}`.trim() || "Trainer",
              };

              // Create notification
              // await notificationService.createProgressNotification(
              //   progressData,
              //   athleteDataForNotification,
              //   trainerDataForNotification,
              //   "day_completed"
              // );

              // Create activity
              const dayData = {
                day_id: day_id,
                day_title: dayTitle,
                week_id: day.week_id,
                total_exercises: stats.total_exercises,
                completed_exercises: stats.completed_exercises,
                is_rest_day: isRestDay,
              };

              const enrollmentData = {
                id: enrollment_id,
                split_id: day.split_id,
                program_id: day.program_id,
              };

              await activityService.createDayActivity(
                dayData,
                athleteDataForNotification,
                trainerDataForNotification,
                enrollmentData
              );
            }
          } catch (notificationError) {
            console.error("Error creating day completion notification/activity:", notificationError);
            // Don't fail the day completion if notification/activity creation fails
          }
        }

        return res.status(200).json({
          error: false,
          message: isRestDay 
            ? "Rest day marked as complete"
            : allExercisesCompleted
              ? "Day marked as complete"
              : "Day progress updated",
          data: {
            day_id: day_id,
            is_completed: allExercisesCompleted,
            completed_at: completedAt,
            total_exercises: stats.total_exercises,
            completed_exercises: stats.completed_exercises,
            is_rest_day: isRestDay,
          },
        });
      } catch (error) {
        console.error("Mark day complete error:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to mark day as complete",
          details: error.message,
        });
      }
    }
  );

  // Get athlete's progress for a specific enrollment
  app.get(
    "/v2/api/kanglink/custom/athlete/progress/:enrollment_id",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const athleteId = req.user_id;
        const enrollmentId = req.params.enrollment_id;

        sdk.setProjectId("kanglink");

        // Get overall progress
        const overallProgress = await sdk.rawQuery(`
          SELECT ap.*, p.program_name, sp.title as split_title, u.data as trainer_data
          FROM kanglink_athlete_progress ap
          JOIN kanglink_program p ON ap.program_id = p.id
          JOIN kanglink_split sp ON ap.split_id = sp.id
          JOIN kanglink_user u ON ap.trainer_id = u.id
          WHERE ap.athlete_id = ${athleteId} AND ap.enrollment_id = ${enrollmentId}
        `);

        // Get day progress
        const dayProgress = await sdk.rawQuery(`
          SELECT dp.*, d.title as day_title, d.day_order, w.title as week_title, w.week_order
          FROM kanglink_day_progress dp
          JOIN kanglink_day d ON dp.day_id = d.id
          JOIN kanglink_week w ON dp.week_id = w.id
          WHERE dp.athlete_id = ${athleteId} AND dp.enrollment_id = ${enrollmentId}
          ORDER BY w.week_order, d.day_order
        `);

        // Get exercise progress
        const exerciseProgress = await sdk.rawQuery(`
          SELECT ep.*, ei.label as exercise_label, ei.sets, ei.reps_or_time,
                 e.name as exercise_name, s.title as session_title, s.session_order
          FROM kanglink_exercise_progress ep
          JOIN kanglink_exercise_instance ei ON ep.exercise_instance_id = ei.id
          LEFT JOIN kanglink_exercise e ON ei.exercise_id = e.id
          JOIN kanglink_session s ON ep.session_id = s.id
          WHERE ep.athlete_id = ${athleteId} AND ep.enrollment_id = ${enrollmentId}
          ORDER BY ep.day_id, s.session_order, ei.exercise_order
        `);

        return res.status(200).json({
          error: false,
          data: {
            overall_progress: overallProgress[0] || null,
            day_progress: dayProgress,
            exercise_progress: exerciseProgress,
          },
        });
      } catch (error) {
        console.error("Get athlete progress error:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to get athlete progress",
          details: error.message,
        });
      }
    }
  );

  // Get trainer's athletes progress overview
  app.get(
    "/v2/api/kanglink/custom/trainer/athletes-progress",
    [TokenMiddleware({ role: "trainer" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const trainerId = req.user_id;
        const { program_id, split_id } = req.query;

        sdk.setProjectId("kanglink");

        let whereClause = `WHERE ap.trainer_id = ${trainerId}`;
        if (program_id) whereClause += ` AND ap.program_id = ${program_id}`;
        if (split_id) whereClause += ` AND ap.split_id = ${split_id}`;

        // Get athletes progress overview
        const athletesProgress = await sdk.rawQuery(`
          SELECT ap.*,
                 JSON_EXTRACT(au.data, '$.first_name') as athlete_first_name,
                 JSON_EXTRACT(au.data, '$.last_name') as athlete_last_name,
                 JSON_EXTRACT(au.data, '$.photo') as athlete_photo,
                 p.program_name, sp.title as split_title,
                 e.payment_type, e.status as enrollment_status
          FROM kanglink_athlete_progress ap
          JOIN kanglink_user au ON ap.athlete_id = au.id
          JOIN kanglink_program p ON ap.program_id = p.id
          JOIN kanglink_split sp ON ap.split_id = sp.id
          JOIN kanglink_enrollment e ON ap.enrollment_id = e.id
          ${whereClause}
          ORDER BY ap.last_activity_date DESC
        `);

        return res.status(200).json({
          error: false,
          data: athletesProgress,
        });
      } catch (error) {
        console.error("Get trainer athletes progress error:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to get athletes progress",
          details: error.message,
        });
      }
    }
  );

  // Get trainer notifications
  app.get(
    "/v2/api/kanglink/custom/trainer/notifications",
    [TokenMiddleware({ role: "trainer" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const trainerId = req.user_id;
        const { limit = 20, offset = 0, unread_only = false } = req.query;

        sdk.setProjectId("kanglink");

        let whereClause = `WHERE tn.trainer_id = ${trainerId}`;
        if (unread_only === "true") whereClause += ` AND tn.is_read = false`;

        const notifications = await sdk.rawQuery(`
          SELECT tn.*,
                 JSON_EXTRACT(au.data, '$.first_name') as athlete_first_name,
                 JSON_EXTRACT(au.data, '$.last_name') as athlete_last_name,
                 JSON_EXTRACT(au.data, '$.photo') as athlete_photo,
                 p.program_name, sp.title as split_title
          FROM kanglink_trainer_notifications tn
          JOIN kanglink_user au ON tn.athlete_id = au.id
          JOIN kanglink_enrollment e ON tn.enrollment_id = e.id
          JOIN kanglink_split sp ON e.split_id = sp.id
          JOIN kanglink_program p ON sp.program_id = p.id
          ${whereClause}
          ORDER BY tn.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `);

        // Get unread count
        const unreadCount = await sdk.rawQuery(`
          SELECT COUNT(*) as count FROM kanglink_trainer_notifications
          WHERE trainer_id = ${trainerId} AND is_read = false
        `);

        return res.status(200).json({
          error: false,
          data: {
            notifications: notifications,
            unread_count: unreadCount[0].count,
            pagination: {
              limit: parseInt(limit),
              offset: parseInt(offset),
              total: notifications.length,
            },
          },
        });
      } catch (error) {
        console.error("Get trainer notifications error:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to get trainer notifications",
          details: error.message,
        });
      }
    }
  );

  // Mark trainer notification as read
  app.put(
    "/v2/api/kanglink/custom/trainer/notifications/:notification_id/read",
    [TokenMiddleware({ role: "trainer" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const trainerId = req.user_id;
        const notificationId = req.params.notification_id;

        sdk.setProjectId("kanglink");

        const readAt = UtilService.sqlDateTimeFormat(new Date());
        await sdk.rawQuery(`
          UPDATE kanglink_trainer_notifications
          SET is_read = true, read_at = '${readAt}', updated_at = '${readAt}'
          WHERE id = ${notificationId} AND trainer_id = ${trainerId}
        `);

        return res.status(200).json({
          error: false,
          message: "Notification marked as read",
        });
      } catch (error) {
        console.error("Mark notification read error:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to mark notification as read",
          details: error.message,
        });
      }
    }
  );

  // Get athlete's enrollment with complete program structure and progress
  app.get(
    "/v2/api/kanglink/custom/athlete/enrollment/:enrollment_id/program",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const athleteId = req.user_id;
        const enrollmentId = req.params.enrollment_id;

        sdk.setProjectId("kanglink");

        // First verify the athlete owns this enrollment
        const enrollment = await sdk.rawQuery(`
          SELECT e.*, p.program_name, p.program_description, p.image as program_image,
                 sp.title as split_title, sp.equipment_required as split_equipment,
                 sp.full_price, sp.subscription, p.currency,
                 JSON_EXTRACT(u.data, '$.first_name') as trainer_first_name,
                 JSON_EXTRACT(u.data, '$.last_name') as trainer_last_name,
                 JSON_EXTRACT(u.data, '$.full_name') as trainer_full_name,
                 JSON_EXTRACT(u.data, '$.photo') as trainer_photo,
                 u.email as trainer_email
          FROM kanglink_enrollment e
          JOIN kanglink_split sp ON e.split_id = sp.id
          JOIN kanglink_program p ON sp.program_id = p.id
          JOIN kanglink_user u ON p.user_id = u.id
          WHERE e.id = ${enrollmentId} AND e.athlete_id = ${athleteId}
        `);

        if (!enrollment || enrollment.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Enrollment not found or access denied",
          });
        }

        const enrollmentData = enrollment[0];
        let weeks = [];
        let programData = {};
        let splitData = {};
        let trainerData = {};

        // Handle data fetching based on access type
        if (
          enrollmentData.access_type === "snapshot" &&
          enrollmentData.split_snapshot
        ) {
          // Use snapshot data for one-time purchases
          const snapshot = JSON.parse(enrollmentData.split_snapshot);

          programData = {
            id: snapshot.split.program_id,
            name: snapshot.split.program_name,
            description: snapshot.split.program_description,
            image_url: enrollmentData.program_image, // Keep from live query as it might be updated
            type_of_program: snapshot.split.type_of_program,
            currency: snapshot.split.currency,
          };

          splitData = {
            id: snapshot.split.id,
            title: snapshot.split.title,
            equipment_required: snapshot.split.equipment_required,
            full_price: parseFloat(snapshot.split.full_price) || 0,
            subscription_price: parseFloat(snapshot.split.subscription) || 0,
          };

          trainerData = {
            id: snapshot.split.trainer_id,
            email: enrollmentData.trainer_email, // Keep from live query
            full_name: enrollmentData.trainer_full_name,
            first_name: enrollmentData.trainer_first_name,
            last_name: enrollmentData.trainer_last_name,
            photo: enrollmentData.trainer_photo,
          };

          // Transform snapshot weeks data to match expected structure
          weeks = snapshot.weeks.map((week) => ({
            week_id: week.id,
            week_title: week.title,
            week_order: week.week_order,
            split_id: week.split_id,
            days: week.days.map((day) => ({
              day_id: day.id,
              day_title: day.title,
              day_order: day.day_order,
              is_rest_day: day.is_rest_day,
              week_id: day.week_id,
              sessions: day.sessions.map((session) => ({
                session_id: session.id,
                session_title: session.title,
                session_order: session.session_order,
                day_id: session.day_id,
                exercise_instances: [
                  ...(session?.exercise_instances || session.exercises || []),
                ].map((exercise) => ({
                  ...exercise,
                  exercise_instance_id: exercise.id,
                  exercise_label: exercise.label,
                  sets: exercise.sets,
                  reps_or_time: exercise?.reps_time_type === "reps" ? exercise.reps_or_time : `${exercise.time_minutes ?? 0} ${exercise.time_seconds ?? 0}`,
                  rest_duration_seconds: exercise.rest_duration_seconds,
                  exercise_details: exercise.exercise_details,
                  exercise_order: exercise.exercise_order,
                  session_id: exercise.session_id,
                  exercise_id: exercise.exercise_id,
                  exercise_name: exercise.exercise_name,
                  video_url: exercise.video_url || null,
                  label_number: exercise.label_number,
                  is_linked: exercise.is_linked,
                  rest_duration_minutes: exercise.rest_duration_minutes,
                  reps_time_type: exercise.reps_time_type,
                  time_minutes: exercise.time_minutes,
                  time_seconds: exercise.time_seconds,
                })),
              })),
            })),
          }));
        } else {
          // Use live data for subscription purchases
          programData = {
            id: enrollmentData.program_id,
            name: enrollmentData.program_name,
            description: enrollmentData.program_description,
            image_url: enrollmentData.program_image,
          };

          splitData = {
            id: enrollmentData.split_id,
            title: enrollmentData.split_title,
            equipment_required: enrollmentData.split_equipment,
            full_price: parseFloat(enrollmentData.full_price) || 0,
            subscription_price: parseFloat(enrollmentData.subscription) || 0,
          };

          trainerData = {
            id: enrollmentData.trainer_id,
            email: enrollmentData.trainer_email,
            full_name: enrollmentData.trainer_full_name,
            first_name: enrollmentData.trainer_first_name,
            last_name: enrollmentData.trainer_last_name,
            photo: enrollmentData.trainer_photo,
          };

          // Get live weeks with days, sessions, and exercises
          const weeksResult = await sdk.rawQuery(`
            SELECT w.id as week_id, w.title as week_title, w.week_order, w.split_id,
                   d.id as day_id, d.title as day_title, d.day_order, d.is_rest_day,
                   s.id as session_id, s.title as session_title, s.session_order,
                   ei.id as exercise_instance_id, ei.label as exercise_label, ei.sets, ei.reps_or_time, ei.reps_time_type, ei.time_minutes, ei.time_seconds,
                   ei.rest_duration_seconds, ei.exercise_details, ei.exercise_order,
                   ei.label_number, ei.is_linked, ei.rest_duration_minutes,
                   e.id as exercise_id, e.name as exercise_name,
                   e.video_url as exercise_video_url,
                   ei.exercise_name as instance_exercise_name,
                   ei.video_url
            FROM kanglink_week w
            JOIN kanglink_day d ON w.id = d.week_id
            LEFT JOIN kanglink_session s ON d.id = s.day_id
            LEFT JOIN kanglink_exercise_instance ei ON s.id = ei.session_id
            LEFT JOIN kanglink_exercise e ON ei.exercise_id = e.id
            WHERE w.split_id = ${enrollmentData.split_id}
            ORDER BY w.week_order, d.day_order, s.session_order, ei.exercise_order
          `);
          weeks = weeksResult;
        }
        console.log("\nweeks", weeks, `\n`);
        // Get progress data
        const exerciseProgress = await sdk.rawQuery(`
          SELECT ep.*, ei.id as exercise_instance_id
          FROM kanglink_exercise_progress ep
          JOIN kanglink_exercise_instance ei ON ep.exercise_instance_id = ei.id
          WHERE ep.athlete_id = ${athleteId} AND ep.enrollment_id = ${enrollmentId}
        `);

        const dayProgress = await sdk.rawQuery(`
          SELECT * FROM kanglink_day_progress
          WHERE athlete_id = ${athleteId} AND enrollment_id = ${enrollmentId}
        `);

        const overallProgress = await sdk.rawQuery(`
          SELECT * FROM kanglink_athlete_progress
          WHERE athlete_id = ${athleteId} AND enrollment_id = ${enrollmentId}
        `);

        // Create progress lookup maps
        const exerciseProgressMap = {};
        exerciseProgress.forEach((ep) => {
          exerciseProgressMap[ep.exercise_instance_id] = ep;
        });

        const dayProgressMap = {};
        dayProgress.forEach((dp) => {
          dayProgressMap[dp.day_id] = dp;
        });

        let formattedWeeks = [];

        // Process weeks data based on access type
        if (
          enrollmentData.access_type === "snapshot" &&
          enrollmentData.split_snapshot
        ) {
          // For snapshot data, weeks are already structured
          formattedWeeks = weeks
            .map((week) => ({
              id: week.week_id,
              title: week.week_title,
              week_order: week.week_order,
              split_id: week.split_id,
              days: week.days
                .map((day) => ({
                  id: day.day_id,
                  title: day.day_title,
                  day_order: day.day_order,
                  is_rest_day: Boolean(day.is_rest_day),
                  week_id: day.week_id,
                  progress: dayProgressMap[day.day_id] || {
                    is_completed: false,
                    total_exercises: 0,
                    completed_exercises: 0,
                    completed_at: null,
                    notes: "",
                  },
                  sessions: day.sessions
                    .map((session) => ({
                      id: session.session_id,
                      title: session.session_title,
                      session_order: session.session_order,
                      day_id: session.day_id,
                      exercise_instances: session.exercise_instances
                        .map((exercise) => ({
                          ...exercise,
                          id: exercise.exercise_instance_id,
                          label: exercise.exercise_label,
                          sets: exercise.sets,
                          reps_or_time: exercise?.reps_time_type === "reps" ? exercise.reps_or_time : `${exercise.time_minutes ?? 0} ${exercise.time_seconds ?? 0}`,
                          rest_duration_seconds: exercise.rest_duration_seconds,
                          exercise_details: exercise.exercise_details,
                          exercise_order: exercise.exercise_order,
                          session_id: exercise.session_id,
                          exercise_name: exercise.exercise_name,
                          video_url: exercise.video_url || null,
                          label_number: exercise.label_number,
                          is_linked: exercise.is_linked,
                          rest_duration_minutes: exercise.rest_duration_minutes,
                          reps_time_type: exercise.reps_time_type,
                          time_minutes: exercise.time_minutes,
                          time_seconds: exercise.time_seconds,
                          progress: exerciseProgressMap[
                            exercise.exercise_instance_id
                          ] || {
                            is_completed: false,
                            sets_completed: 0,
                            reps_completed: "",
                            weight_used: "",
                            time_taken_seconds: 0,
                            difficulty_rating: null,
                            notes: "",
                            completed_at: null,
                          },
                        }))
                        .sort((a, b) => a.exercise_order - b.exercise_order),
                    }))
                    .sort((a, b) => a.session_order - b.session_order),
                }))
                .sort((a, b) => a.day_order - b.day_order),
              progress: {
                total_days: 0,
                completed_days: 0,
                is_completed: false,
              },
            }))
            .sort((a, b) => a.week_order - b.week_order);
        } else {
          // For live data, structure the flat weeks data
          const weeksMap = {};
          const daysMap = {};
          const sessionsMap = {};

          weeks.forEach((row) => {
            // Initialize week
            if (!weeksMap[row.week_id]) {
              weeksMap[row.week_id] = {
                id: row.week_id,
                title: row.week_title,
                week_order: row.week_order,
                split_id: row.split_id,
                days: [],
                progress: {
                  total_days: 0,
                  completed_days: 0,
                  is_completed: false,
                },
              };
            }

            // Initialize day
            if (!daysMap[row.day_id]) {
              daysMap[row.day_id] = {
                id: row.day_id,
                title: row.day_title,
                day_order: row.day_order,
                is_rest_day: row.is_rest_day,
                week_id: row.week_id,
                sessions: [],
                progress: dayProgressMap[row.day_id] || {
                  is_completed: false,
                  total_exercises: 0,
                  completed_exercises: 0,
                  completed_at: null,
                  notes: "",
                },
              };
              weeksMap[row.week_id].days.push(daysMap[row.day_id]);
            }

            // Only process sessions and exercises if they exist (not rest days)
            if (row.session_id) {
              // Initialize session
              if (!sessionsMap[row.session_id]) {
                sessionsMap[row.session_id] = {
                  id: row.session_id,
                  title: row.session_title,
                  session_order: row.session_order,
                  day_id: row.day_id,
                  exercise_instances: [],
                };
                daysMap[row.day_id].sessions.push(sessionsMap[row.session_id]);
              }

              // Add exercise instance if it exists
              if (row.exercise_instance_id) {
                const exerciseInstance = {
                  id: row.exercise_instance_id,
                  label: row.exercise_label,
                  sets: row.sets,
                  reps_or_time: row?.reps_time_type === "reps" ? row.reps_or_time : `${row.time_minutes ?? 0} ${row.time_seconds ?? 0}`,
                  rest_duration_seconds: row.rest_duration_seconds,
                  exercise_details: row.exercise_details,
                  exercise_order: row.exercise_order,
                  session_id: row.session_id,
                  label_number: row.label_number,
                  is_linked: row.is_linked,
                  rest_duration_minutes: row.rest_duration_minutes,
                  reps_time_type: row.reps_time_type,
                  time_minutes: row.time_minutes,
                  time_seconds: row.time_seconds,
                  ...(row.exercise_id ? {
                        exercise_name: row.exercise_name,
                        video_url: row.exercise_video_url,
                      }
                    : row.instance_exercise_name
                    ? {
                        exercise_name: row.instance_exercise_name,
                        video_url: row.video_url,
                      }
                    : {}),
                  progress: exerciseProgressMap[row.exercise_instance_id] || {
                    is_completed: false,
                    sets_completed: 0,
                    reps_completed: "",
                    weight_used: "",
                    time_taken_seconds: 0,
                    difficulty_rating: null,
                    notes: "",
                    completed_at: null,
                  },
                };

                sessionsMap[row.session_id].exercise_instances.push(
                  exerciseInstance
                );
              }
            }
          });

          // Sort everything by order
          formattedWeeks = Object.values(weeksMap).sort(
            (a, b) => a.week_order - b.week_order
          );
          formattedWeeks.forEach((week) => {
            week.days.sort((a, b) => a.day_order - b.day_order);
            week.days.forEach((day) => {
              if (day.sessions && day.sessions.length > 0) {
                day.sessions.sort((a, b) => a.session_order - b.session_order);
                day.sessions.forEach((session) => {
                  if (session.exercise_instances && session.exercise_instances.length > 0) {
                    session.exercise_instances.sort(
                      (a, b) => a.exercise_order - b.exercise_order
                    );
                  }
                });
              }
            });
          });
        }

        // Calculate progress for all weeks (both snapshot and live)
        formattedWeeks.forEach((week) => {
          week.progress.total_days = week.days.length;
          week.progress.completed_days = week.days.filter(
            (day) => day.progress.is_completed
          ).length;
          week.progress.is_completed =
            week.progress.completed_days === week.progress.total_days &&
            week.progress.total_days > 0;

          // For snapshot data, also calculate exercise progress for each day
          if (
            enrollmentData.access_type === "snapshot" &&
            enrollmentData.split_snapshot
          ) {
            week.days.forEach((day) => {
              if (!day.progress.total_exercises) {
                let totalExercises = 0;
                let completedExercises = 0;

                day.sessions.forEach((session) => {
                  totalExercises += session.exercise_instances.length;
                  completedExercises += session.exercise_instances.filter(
                    (ei) => ei.progress && ei.progress.is_completed
                  ).length;
                });

                day.progress.total_exercises = totalExercises;
                day.progress.completed_exercises = completedExercises;
              }
            });
          }
        });

        // Calculate minimum price using the appropriate data source
        let fullPrice, subscriptionPrice;
        if (
          enrollmentData.access_type === "snapshot" &&
          enrollmentData.split_snapshot
        ) {
          fullPrice = splitData.full_price;
          subscriptionPrice = splitData.subscription_price;
        } else {
          fullPrice = parseFloat(enrollmentData.full_price) || 0;
          subscriptionPrice = parseFloat(enrollmentData.subscription) || 0;
        }

        let minPrice = 0;
        if (fullPrice > 0 && subscriptionPrice > 0) {
          minPrice = Math.min(fullPrice, subscriptionPrice);
        } else if (fullPrice > 0) {
          minPrice = fullPrice;
        } else if (subscriptionPrice > 0) {
          minPrice = subscriptionPrice;
        }

        const responseData = {
          enrollment: {
            id: enrollmentData.id,
            trainer_id: enrollmentData.trainer_id,
            athlete_id: enrollmentData.athlete_id,
            program_id: enrollmentData.program_id,
            split_id: enrollmentData.split_id,
            payment_type: enrollmentData.payment_type,
            amount: enrollmentData.amount,
            currency: enrollmentData.currency,
            enrollment_date: enrollmentData.enrollment_date,
            status: enrollmentData.status,
            payment_status: enrollmentData.payment_status,
            access_type: enrollmentData.access_type,
          },
          program: programData,
          split: {
            ...splitData,
            pricing: {
              full_price: fullPrice,
              subscription_price: subscriptionPrice,
              price: minPrice,
              currency: enrollmentData.currency || "USD",
            },
          },
          trainer: trainerData,
          weeks: formattedWeeks,
          overall_progress: overallProgress[0] || {
            current_week_id: null,
            current_day_id: null,
            total_days_completed: 0,
            total_exercises_completed: 0,
            progress_percentage: 0,
            last_activity_date: null,
          },
        };

        return res.status(200).json({
          error: false,
          data: responseData,
        });
      } catch (error) {
        console.error("Get athlete enrollment program error:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to get enrollment program data",
          details: error.message,
        });
      }
    }
  );
};

// Helper function to update day progress
async function updateDayProgress(
  sdk,
  athleteId,
  enrollmentId,
  dayId,
  weekId,
  splitId
) {
  try {
    // First check if this is a snapshot enrollment
    const enrollment = await sdk.rawQuery(`
      SELECT access_type, split_snapshot FROM kanglink_enrollment
      WHERE id = ${enrollmentId} AND athlete_id = ${athleteId}
    `);

    if (!enrollment || enrollment.length === 0) {
      throw new Error("Enrollment not found");
    }

    const enrollmentData = enrollment[0];
    let stats = { total_exercises: 0, completed_exercises: 0 };

    if (
      enrollmentData.access_type === "snapshot" &&
      enrollmentData.split_snapshot
    ) {
      // For snapshot data, count exercises from the snapshot
      const snapshot = JSON.parse(enrollmentData.split_snapshot);
      const targetWeek = snapshot.weeks.find((w) => w.id === weekId);
      if (targetWeek) {
        const targetDay = targetWeek.days.find((d) => d.id === dayId);
        if (targetDay) {
          stats.total_exercises = targetDay.sessions.reduce(
            (total, session) =>
              total +
              [...(session?.exercise_instances || session.exercises || [])]
                .length,
            0
          );
        }
      }

      // Count completed exercises from progress table
      const completedStats = await sdk.rawQuery(`
        SELECT COUNT(*) as completed_exercises
        FROM kanglink_exercise_progress ep
        WHERE ep.athlete_id = ${athleteId} AND ep.enrollment_id = ${enrollmentId}
          AND ep.day_id = ${dayId} AND ep.is_completed = true
      `);
      stats.completed_exercises = completedStats[0]?.completed_exercises || 0;
    } else {
      // For live data, use the existing logic
      const exerciseStats = await sdk.rawQuery(`
        SELECT
          COUNT(ei.id) as total_exercises,
          COUNT(ep.id) as completed_exercises
        FROM kanglink_exercise_instance ei
        JOIN kanglink_session s ON ei.session_id = s.id
        LEFT JOIN kanglink_exercise_progress ep ON ei.id = ep.exercise_instance_id
          AND ep.athlete_id = ${athleteId} AND ep.enrollment_id = ${enrollmentId} AND ep.is_completed = true
        WHERE s.day_id = ${dayId}
      `);
      stats = exerciseStats[0];
    }

    const allExercisesCompleted =
      stats.total_exercises === stats.completed_exercises &&
      stats.total_exercises > 0;
    const completedAt = UtilService.sqlDateTimeFormat(new Date());

    // Check if day progress record exists
    const existingDayProgress = await sdk.rawQuery(`
      SELECT * FROM kanglink_day_progress
      WHERE athlete_id = ${athleteId} AND enrollment_id = ${enrollmentId} AND day_id = ${dayId}
    `);

    if (existingDayProgress && existingDayProgress.length > 0) {
      // Update existing day progress
      await sdk.rawQuery(`
        UPDATE kanglink_day_progress
        SET total_exercises = ${stats.total_exercises}, completed_exercises = ${
        stats.completed_exercises
      },
            is_completed = ${allExercisesCompleted},
            ${allExercisesCompleted ? `completed_at = '${completedAt}',` : ""}
            updated_at = '${completedAt}'
        WHERE athlete_id = ${athleteId} AND enrollment_id = ${enrollmentId} AND day_id = ${dayId}
      `);
    } else {
      // Create new day progress record
      await sdk.rawQuery(`
        INSERT INTO kanglink_day_progress
        (athlete_id, enrollment_id, day_id, week_id, split_id, is_completed,
         ${
           allExercisesCompleted ? "completed_at," : ""
         } total_exercises, completed_exercises, created_at, updated_at)
        VALUES (${athleteId}, ${enrollmentId}, ${dayId}, ${weekId}, ${splitId}, ${allExercisesCompleted},
                ${allExercisesCompleted ? `'${completedAt}',` : ""} ${
        stats.total_exercises
      }, ${stats.completed_exercises},
                '${completedAt}', '${completedAt}')
      `);
    }
  } catch (error) {
    console.error("Update day progress error:", error);
  }
}

// Helper function to update overall athlete progress
async function updateAthleteProgress(
  sdk,
  athleteId,
  enrollmentId,
  splitId,
  programId,
  trainerId
) {
  try {
    // First check if this is a snapshot enrollment
    const enrollment = await sdk.rawQuery(`
      SELECT access_type, split_snapshot FROM kanglink_enrollment
      WHERE id = ${enrollmentId} AND athlete_id = ${athleteId}
    `);

    if (!enrollment || enrollment.length === 0) {
      throw new Error("Enrollment not found");
    }

    const enrollmentData = enrollment[0];
    let stats = {
      total_days_completed: 0,
      total_exercises_completed: 0,
      total_days_in_split: 0,
      total_exercises_in_split: 0,
    };

    if (
      enrollmentData.access_type === "snapshot" &&
      enrollmentData.split_snapshot
    ) {
      // For snapshot data, count from the snapshot
      const snapshot = JSON.parse(enrollmentData.split_snapshot);

      // Count total days and exercises from snapshot
      stats.total_days_in_split = snapshot.weeks.reduce(
        (total, week) => total + week.days.length,
        0
      );
      stats.total_exercises_in_split = snapshot.weeks.reduce(
        (total, week) =>
          total +
          week.days.reduce(
            (dayTotal, day) =>
              dayTotal +
              day.sessions.reduce(
                (sessionTotal, session) =>
                  sessionTotal +
                  [...(session?.exercise_instances || session.exercises || [])]
                    .length,
                0
              ),
            0
          ),
        0
      );

      // Count completed days and exercises separately to handle rest days properly
      const completedDays = await sdk.rawQuery(`
        SELECT COUNT(DISTINCT dp.day_id) as total_days_completed
        FROM kanglink_day_progress dp
        WHERE dp.athlete_id = ${athleteId} AND dp.enrollment_id = ${enrollmentId}
          AND dp.is_completed = true
      `);

      const completedExercises = await sdk.rawQuery(`
        SELECT COUNT(DISTINCT ep.exercise_instance_id) as total_exercises_completed
        FROM kanglink_exercise_progress ep
        WHERE ep.athlete_id = ${athleteId} AND ep.enrollment_id = ${enrollmentId}
          AND ep.is_completed = true
      `);

      stats.total_days_completed = completedDays[0]?.total_days_completed || 0;
      stats.total_exercises_completed = completedExercises[0]?.total_exercises_completed || 0;
    } else {
      // For live data, use the existing logic
      // Count completed days and exercises separately to handle rest days properly
      const completedDays = await sdk.rawQuery(`
        SELECT COUNT(DISTINCT dp.day_id) as total_days_completed
        FROM kanglink_day_progress dp
        WHERE dp.athlete_id = ${athleteId} AND dp.enrollment_id = ${enrollmentId}
          AND dp.is_completed = true
      `);

      const completedExercises = await sdk.rawQuery(`
        SELECT COUNT(DISTINCT ep.exercise_instance_id) as total_exercises_completed
        FROM kanglink_exercise_progress ep
        WHERE ep.athlete_id = ${athleteId} AND ep.enrollment_id = ${enrollmentId}
          AND ep.is_completed = true
      `);

      const totalDaysInSplit = await sdk.rawQuery(`
        SELECT COUNT(*) as total_days_in_split
        FROM kanglink_day d
        JOIN kanglink_week w ON d.week_id = w.id
        WHERE w.split_id = ${splitId}
      `);

      const totalExercisesInSplit = await sdk.rawQuery(`
        SELECT COUNT(*) as total_exercises_in_split
        FROM kanglink_exercise_instance ei
        JOIN kanglink_session s ON ei.session_id = s.id
        JOIN kanglink_day d ON s.day_id = d.id
        JOIN kanglink_week w ON d.week_id = w.id
        WHERE w.split_id = ${splitId}
      `);

      stats = {
        total_days_completed: completedDays[0]?.total_days_completed || 0,
        total_exercises_completed: completedExercises[0]?.total_exercises_completed || 0,
        total_days_in_split: totalDaysInSplit[0]?.total_days_in_split || 0,
        total_exercises_in_split: totalExercisesInSplit[0]?.total_exercises_in_split || 0,
      };
    }

    const progressPercentage =
      stats.total_days_in_split > 0
        ? (
            (stats.total_days_completed / stats.total_days_in_split) *
            100
          ).toFixed(2)
        : 0;

    const lastActivityDate = UtilService.sqlDateTimeFormat(new Date());

    // Get current week and day
    let currentDay = null;

    if (
      enrollmentData.access_type === "snapshot" &&
      enrollmentData.split_snapshot
    ) {
      // For snapshot data, find current day from snapshot structure
      const snapshot = JSON.parse(enrollmentData.split_snapshot);

      // Get completed days from progress table
      const completedDays = await sdk.rawQuery(`
        SELECT day_id FROM kanglink_day_progress
        WHERE athlete_id = ${athleteId} AND enrollment_id = ${enrollmentId} AND is_completed = true
      `);

      const completedDayIds = new Set(completedDays.map((d) => d.day_id));

      // Find first incomplete day
      for (const week of snapshot.weeks.sort(
        (a, b) => a.week_order - b.week_order
      )) {
        for (const day of week.days.sort((a, b) => a.day_order - b.day_order)) {
          if (!completedDayIds.has(day.id)) {
            currentDay = {
              current_day_id: day.id,
              current_week_id: week.id,
            };
            break;
          }
        }
        if (currentDay) break;
      }
    } else {
      // For live data, use the existing logic
      const currentProgress = await sdk.rawQuery(`
        SELECT d.id as current_day_id, w.id as current_week_id
        FROM kanglink_day d
        JOIN kanglink_week w ON d.week_id = w.id
        LEFT JOIN kanglink_day_progress dp ON d.id = dp.day_id
          AND dp.athlete_id = ${athleteId} AND dp.enrollment_id = ${enrollmentId}
        WHERE w.split_id = ${splitId} AND (dp.is_completed = false OR dp.is_completed IS NULL)
        ORDER BY w.week_order, d.day_order
        LIMIT 1
      `);
      currentDay = currentProgress[0];
    }

    // Check if athlete progress record exists
    const existingProgress = await sdk.rawQuery(`
      SELECT * FROM kanglink_athlete_progress
      WHERE athlete_id = ${athleteId} AND enrollment_id = ${enrollmentId}
    `);

    if (existingProgress && existingProgress.length > 0) {
      // Update existing progress
      await sdk.rawQuery(`
        UPDATE kanglink_athlete_progress
        SET current_week_id = ${
          currentDay ? currentDay.current_week_id : "NULL"
        },
            current_day_id = ${currentDay ? currentDay.current_day_id : "NULL"},
            total_days_completed = ${stats.total_days_completed},
            total_exercises_completed = ${stats.total_exercises_completed},
            progress_percentage = ${progressPercentage},
            last_activity_date = '${lastActivityDate}',
            updated_at = '${lastActivityDate}'
        WHERE athlete_id = ${athleteId} AND enrollment_id = ${enrollmentId}
      `);
    } else {
      // Create new progress record
      await sdk.rawQuery(`
        INSERT INTO kanglink_athlete_progress
        (athlete_id, enrollment_id, split_id, program_id, trainer_id, current_week_id, current_day_id,
         total_days_completed, total_exercises_completed, progress_percentage, last_activity_date, created_at, updated_at)
        VALUES (${athleteId}, ${enrollmentId}, ${splitId}, ${programId}, ${trainerId},
                ${currentDay ? currentDay.current_week_id : "NULL"}, ${
        currentDay ? currentDay.current_day_id : "NULL"
      },
                ${stats.total_days_completed}, ${
        stats.total_exercises_completed
      }, ${progressPercentage},
                '${lastActivityDate}', '${lastActivityDate}', '${lastActivityDate}')
      `);
    }
  } catch (error) {
    console.error("Update athlete progress error:", error);
  }
}


