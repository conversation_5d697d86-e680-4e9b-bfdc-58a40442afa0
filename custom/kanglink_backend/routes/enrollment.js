const AuthService = require("../../../baas/services/AuthService");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const UtilService = require("../../../baas/services/UtilService");
const StripeService = require("../../../baas/services/StripeService");
const CommissionService = require("../services/CommissionService");
const DiscountService = require("../services/DiscountService");
const NotificationService = require("../services/NotificationService");

module.exports = function (app) {
  const stripe = new StripeService();

  // Create enrollment with Stripe payment/subscription
  app.post(
    "/v2/api/kanglink/custom/athlete/enrollment",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const {
          split_id,
          payment_type, // "subscription" or "one_time"
          payment_method_id, // Stripe payment method ID
          affiliate_code, // Optional affiliate code
          coupon_code, // Optional coupon code
        } = req.body;
        const athleteId = req.user_id;

        // Validate required fields
        if (!split_id || !payment_type || !payment_method_id) {
          return res.status(400).json({
            error: true,
            message:
              "split_id, payment_type, and payment_method_id are required",
          });
        }

        if (!["subscription", "one_time"].includes(payment_type)) {
          return res.status(400).json({
            error: true,
            message: "payment_type must be 'subscription' or 'one_time'",
          });
        }

        sdk.setProjectId("kanglink");

        // Get split details with program and trainer info
        sdk.setTable("split");
        const split = await sdk.rawQuery(`
          SELECT s.*, p.user_id as trainer_id, p.program_name, p.currency
          FROM kanglink_split s
          JOIN kanglink_program p ON s.program_id = p.id
          WHERE s.id = ${split_id}
        `);

        if (!split || split.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Split not found",
          });
        }

        const splitData = split[0];
        const originalAmount =
          payment_type === "subscription"
            ? splitData.subscription
            : splitData.full_price;
        const currency = splitData.currency || "USD";

        if (!originalAmount || originalAmount <= 0) {
          return res.status(400).json({
            error: true,
            message: `${payment_type === "subscription" ? "Subscription" : "One-time"
              } price not available for this split`,
          });
        }

        // Calculate discounted amount using comprehensive discount service
        const discountService = new DiscountService(sdk);
        const discountResult = await discountService.calculateDiscountedAmount({
          program_id: splitData.program_id,
          split_id: split_id,
          payment_type: payment_type,
          original_amount: originalAmount,
          coupon_code: coupon_code,
          user_id: athleteId,
        });

        if (!discountResult.success) {
          return res.status(400).json({
            error: true,
            message: discountResult.error || "Error calculating discounts",
          });
        }

        const finalAmount = discountResult.final_amount;
        const totalDiscountAmount = discountResult.total_discount_amount;
        const appliedDiscounts = discountResult.applied_discounts;

        // Validate affiliate code if provided
        let affiliateUserId = null;
        let validatedAffiliateCode = null;

        if (affiliate_code) {
          const commissionService = new CommissionService(sdk);
          const affiliateValidation =
            await commissionService.validateAffiliateCode(affiliate_code);

          if (!affiliateValidation.valid) {
            return res.status(400).json({
              error: true,
              message: affiliateValidation.message || "Invalid affiliate code",
            });
          }

          // Ensure affiliate code is for the same program
          if (affiliateValidation.program_id !== splitData.program_id) {
            return res.status(400).json({
              error: true,
              message: "Affiliate code is not valid for this program",
            });
          }

          affiliateUserId = affiliateValidation.trainer_id;
          validatedAffiliateCode = affiliate_code;
        }

        // Get athlete details
        sdk.setTable("user");
        const athlete = await sdk.findOne("user", { id: athleteId });
        if (!athlete) {
          return res.status(404).json({
            error: true,
            message: "Athlete not found",
          });
        }

        // Check if athlete is already enrolled in this split
        sdk.setTable("enrollment");
        const existingEnrollment = await sdk.findOne("enrollment", {
          athlete_id: athleteId,
          split_id: split_id,
          status: "active",
        });

        if (existingEnrollment) {
          return res.status(400).json({
            error: true,
            message: "Already enrolled in this split",
          });
        }

        // Create or get Stripe customer
        let stripeCustomer;
        if (athlete.stripe_uid) {
          stripeCustomer = await stripe.retrieveStripeCustomer({
            customerId: athlete.stripe_uid,
          });
        } else {
          stripeCustomer = await stripe.createStripeCustomer({
            email: athlete.email,
            metadata: {
              projectId: "kanglink",
              user_id: athleteId,
            },
          });

          // Update user with Stripe customer ID
          await sdk.updateById("user", athleteId, {
            stripe_uid: stripeCustomer.id,
          });
        }

        const now = new Date();
        let stripeSubscriptionId = null;
        let stripePaymentIntentId = null;
        let stripeCouponId = null; // For storing Stripe coupon ID when discounts are applied
        let paymentIntent = null;

        try {
          // Find existing Stripe price (should be created during program creation)
          const stripePrice = await findStripePriceForSplit(
            sdk,
            splitData,
            payment_type
          );

          if (!stripePrice) {
            const paymentTypeText =
              payment_type === "subscription" ? "Subscription" : "One-time";
            return res.status(400).json({
              error: true,
              message: `${paymentTypeText} pricing not configured for this split. Please contact the trainer to republish the program.`,
            });
          }

          if (payment_type === "subscription") {
            // Create Stripe coupon for discount if applicable
            if (totalDiscountAmount > 0) {
              try {
                // Calculate discount percentage based on original amount
                const discountPercentage = Math.round(
                  (totalDiscountAmount / originalAmount) * 100
                );

                // Create a Stripe coupon for this discount
                const stripeCoupon = await stripe.createStripeCoupon({
                  name: `Discount for Split ${split_id} - ${discountPercentage}% off`,
                  percent_off: discountPercentage,
                  duration: "forever", // Apply discount for the lifetime of the subscription
                  currency: currency.toLowerCase(),
                  metadata: {
                    projectId: "kanglink",
                    split_id: split_id,
                    program_id: splitData.program_id,
                    athlete_id: athleteId,
                    trainer_id: splitData.trainer_id,
                    discount_amount: totalDiscountAmount,
                    original_amount: originalAmount,
                  },
                });
                stripeCouponId = stripeCoupon.id;
              } catch (couponError) {
                console.error("Error creating Stripe coupon:", couponError);
                return res.status(500).json({
                  error: true,
                  message: "Failed to apply discount to subscription",
                });
              }
            }

            // Create Stripe subscription using existing price with coupon if applicable
            const subscription = await stripe.createStripeSubscription({
              customerId: stripeCustomer.id,
              priceId: stripePrice.stripe_id,
              coupon: stripeCouponId, // Apply discount coupon if available
              default_payment_method: payment_method_id,
              payment_behavior: "error_if_incomplete", // Attempt payment immediately, fail if incomplete
              expand: ["latest_invoice.payment_intent"], // Get payment intent details
              metadata: {
                projectId: "kanglink",
                split_id: split_id,
                athlete_id: athleteId,
                trainer_id: splitData.trainer_id,
                original_amount: originalAmount,
                discount_amount: totalDiscountAmount,
                has_discount: appliedDiscounts.length > 0 ? "true" : "false",
                stripe_coupon_id: stripeCouponId || "",
              },
            });
            stripeSubscriptionId = subscription.id;

            // Handle incomplete subscription (only for cases requiring 3D Secure or similar authentication)
            if (subscription.status === "incomplete") {
              const paymentIntent = subscription.latest_invoice?.payment_intent;

              if (paymentIntent && paymentIntent.status === "requires_action") {
                // Return client_secret for 3D Secure or similar authentication
                return res.status(200).json({
                  error: false,
                  message:
                    "Subscription created, additional authentication required",
                  requires_action: true,
                  payment_intent: {
                    id: paymentIntent.id,
                    client_secret: paymentIntent.client_secret,
                    status: paymentIntent.status,
                  },
                  subscription_id: subscription.id,
                  enrollment_status: "pending_authentication",
                });
              } else {
                // Payment failed or other issue - this should be rare with error_if_incomplete
                return res.status(400).json({
                  error: true,
                  message:
                    "Subscription payment failed. Please check your payment method and try again.",
                  subscription_id: subscription.id,
                  payment_intent_status: paymentIntent?.status || "unknown",
                });
              }
            }
          } else {
            // Create one-time payment intent
            paymentIntent = await stripe.createPaymentIntentAutomatic({
              amount: Math.round(finalAmount * 100), // Convert to cents (using discounted amount)
              currency: currency.toLowerCase(),
              customer: stripeCustomer.id,
              payment_method: payment_method_id,
              confirm: true,
              automatic_payment_methods: {
                enabled: true,
                allow_redirects: "never", // Disable redirect-based payment methods for server-side confirmation
              },
              metadata: {
                projectId: "kanglink",
                split_id: split_id,
                athlete_id: athleteId,
                trainer_id: splitData.trainer_id,
                payment_type: "one_time",
                stripe_price_id: stripePrice.stripe_id, // Include price ID for reference
                original_amount: originalAmount,
                discount_amount: totalDiscountAmount,
                has_discount: appliedDiscounts.length > 0 ? "true" : "false",
              },
            });
            stripePaymentIntentId = paymentIntent.id;

            // Check if payment requires additional authentication
            if (paymentIntent.status === "requires_action") {
              return res.status(400).json({
                error: true,
                message: "Payment requires additional authentication",
                requires_action: true,
                payment_intent: {
                  id: paymentIntent.id,
                  client_secret: paymentIntent.client_secret,
                },
              });
            }
          }

          // For one-time purchases, capture the current split version/snapshot
          let splitSnapshot = null;
          if (payment_type === "one_time") {
            try {
              // First check if the split has any content before attempting to capture
              const splitContentCheck = await sdk.rawQuery(`
                SELECT 
                  (SELECT COUNT(*) FROM kanglink_week WHERE split_id = ${split_id}) as weeks_count,
                  (SELECT COUNT(*) FROM kanglink_day d JOIN kanglink_week w ON d.week_id = w.id WHERE w.split_id = ${split_id}) as days_count,
                  (SELECT COUNT(*) FROM kanglink_session s JOIN kanglink_day d ON s.day_id = d.id JOIN kanglink_week w ON d.week_id = w.id WHERE w.split_id = ${split_id}) as sessions_count
              `);
              
              if (splitContentCheck && splitContentCheck.length > 0) {
                const content = splitContentCheck[0];
                if (content.weeks_count === 0 || content.days_count === 0 || content.sessions_count === 0) {
                  throw new Error(
                    "This split does not have sufficient content (weeks, days, or sessions) to create a snapshot. Please ensure the split has content before enrolling."
                  );
                }
              }

              // Capture the current state of the split for lifetime access
              splitSnapshot = await captureSplitSnapshot(sdk, split_id);

              // Validate that snapshot was successfully created for one-time purchases
              if (!splitSnapshot) {
                throw new Error(
                  "Failed to capture split snapshot for one-time purchase. This is required for lifetime access."
                );
              }
            } catch (snapshotError) {
              console.error(`Error capturing split snapshot for split ${split_id}:`, snapshotError);
              throw new Error(
                `Failed to capture split snapshot for one-time purchase: ${snapshotError.message}. This is required for lifetime access.`
              );
            }
          }

          // Determine enrollment status based on payment type and payment status
          let enrollmentStatus = "pending";
          let paymentStatus = "pending";

          if (payment_type === "subscription") {
            // For subscriptions, check if the subscription is active or incomplete
            if (stripeSubscriptionId) {
              // If we reach here, subscription was created successfully and is active
              enrollmentStatus = "active";
              paymentStatus = "paid";
            } else {
              // This shouldn't happen if we got here, but handle gracefully
              enrollmentStatus = "pending";
              paymentStatus = "pending";
            }
          } else if (payment_type === "one_time" && stripePaymentIntentId) {
            // For one-time payments, check the payment intent status
            if (paymentIntent && paymentIntent.status === "succeeded") {
              enrollmentStatus = "active";
              paymentStatus = "paid";
            } else {
              // Payment is pending or requires action
              enrollmentStatus = "pending";
              paymentStatus = "pending";
            }
          }

          // Create enrollment record
          const enrollmentData = {
            trainer_id: splitData.trainer_id,
            athlete_id: athleteId,
            program_id: splitData.program_id,
            split_id: split_id,
            payment_type: payment_type,
            amount: finalAmount, // Use discounted amount
            currency: currency,
            original_amount: originalAmount, // Store original amount for reference
            discount_amount: totalDiscountAmount, // Store total discount amount
            discount_details: JSON.stringify(appliedDiscounts), // Store discount breakdown
            enrollment_date: UtilService.sqlDateTimeFormat(now),
            expiry_date: payment_type === "one_time" ? null : null, // One-time purchases never expire
            status: enrollmentStatus,
            payment_status: paymentStatus,
            stripe_subscription_id: stripeSubscriptionId,
            stripe_payment_intent_id: stripePaymentIntentId,
            stripe_customer_id: stripeCustomer.id,
            stripe_coupon_id:
              payment_type === "subscription" && totalDiscountAmount > 0
                ? stripeCouponId
                : null, // Store Stripe coupon ID for subscriptions
            // Store snapshot for one-time purchases to freeze content at purchase time
            split_snapshot:
              payment_type === "one_time"
                ? JSON.stringify(splitSnapshot)
                : null,
            access_type: payment_type === "one_time" ? "snapshot" : "live", // Track access type
            // Add affiliate tracking fields
            affiliate_code: validatedAffiliateCode,
            affiliate_user_id: affiliateUserId,
            commission_calculated: false, // Will be set to true after commission is created
            created_at: UtilService.sqlDateTimeFormat(now),
            updated_at: UtilService.sqlDateTimeFormat(now),
          };

          sdk.setTable("enrollment");
          const enrollment = await sdk.create("enrollment", enrollmentData);

          // Create notifications for successful enrollment
          try {
            const notificationService = new NotificationService(sdk);

            // Get athlete and trainer data for notifications
            const athleteData = await sdk.rawQuery(`
              SELECT id, email, data
              FROM kanglink_user 
              WHERE id = ${athleteId}
            `);

            const trainerData = await sdk.rawQuery(`
              SELECT id, email, data
              FROM kanglink_user 
              WHERE id = ${splitData.trainer_id}
            `);

            if (athleteData && athleteData.length > 0 && trainerData && trainerData.length > 0) {
              const athlete = athleteData[0];
              const trainer = trainerData[0];

              // Parse user data
              let athleteUserData = {};
              let trainerUserData = {};

              if (athlete.data) {
                try {
                  athleteUserData = JSON.parse(athlete.data);
                } catch (e) {
                  console.warn("Failed to parse athlete data:", e);
                }
              }

              if (trainer.data) {
                try {
                  trainerUserData = JSON.parse(trainer.data);
                } catch (e) {
                  console.warn("Failed to parse trainer data:", e);
                }
              }

              const enrollmentNotificationData = {
                id: enrollment.id,
                trainer_id: splitData.trainer_id,
                athlete_id: athleteId,
                program_id: splitData.program_id,
                program_name: splitData.program_name,
                split_name: splitData.title,
                payment_amount: finalAmount,
                payment_currency: currency,
                payment_type: payment_type,
              };

              const athleteDataForNotification = {
                id: athlete.id,
                email: athlete.email,
                full_name: athleteUserData.full_name || `${athleteUserData.first_name || ""} ${athleteUserData.last_name || ""}`.trim() || "Athlete",
              };

              const trainerDataForNotification = {
                id: trainer.id,
                email: trainer.email,
                full_name: trainerUserData.full_name || `${trainerUserData.first_name || ""} ${trainerUserData.last_name || ""}`.trim() || "Trainer",
              };

              await notificationService.createEnrollmentNotification(
                enrollmentNotificationData,
                athleteDataForNotification,
                trainerDataForNotification
              );
            }
          } catch (notificationError) {
            console.error("Error creating enrollment notifications:", notificationError);
            // Don't fail the enrollment if notification creation fails
          }

          // Create commission record for successful enrollments
          if (enrollmentStatus === "active" && paymentStatus === "paid") {
            try {
              const commissionService = new CommissionService(sdk);
              const commissionEnrollmentData = {
                trainer_id: splitData.trainer_id,
                athlete_id: athleteId,
                program_id: splitData.program_id,
                split_id: split_id,
                amount: finalAmount, // Use discounted amount for commission calculation
                currency: currency,
                affiliate_code: validatedAffiliateCode,
                affiliate_user_id: affiliateUserId,
                original_amount: originalAmount,
                discount_amount: totalDiscountAmount,
              };

              await commissionService.createCommissionRecord(
                enrollment.id,
                commissionEnrollmentData
              );

              // Mark enrollment as having commission calculated
              await sdk.updateById("enrollment", enrollment.id, {
                commission_calculated: true,
                updated_at: UtilService.sqlDateTimeFormat(new Date()),
              });
            } catch (commissionError) {
              console.error(
                "Error creating commission record:",
                commissionError
              );
              // Don't fail the enrollment if commission creation fails
              // This can be handled by a background job later
            }
          }

          // Record coupon usage if coupon was applied
          if (appliedDiscounts.length > 0) {
            try {
              for (const discount of appliedDiscounts) {
                if (discount.type === "coupon" && discount.coupon_id) {
                  await discountService.recordCouponUsage(
                    discount.coupon_id,
                    athleteId,
                    splitData.program_id,
                    split_id,
                    discount.discount_amount
                  );
                }
              }
            } catch (couponError) {
              console.error("Error recording coupon usage:", couponError);
              // Don't fail the enrollment if coupon recording fails
            }
          }

          return res.status(200).json({
            error: false,
            message:
              enrollmentStatus === "active"
                ? "Enrollment successful"
                : "Enrollment created, payment processing",
            data: {
              enrollment_id: enrollment.id,
              payment_type: payment_type,
              amount: finalAmount,
              currency: currency,
              status: enrollmentStatus,
              payment_status: paymentStatus,
              stripe_subscription_id: stripeSubscriptionId,
              stripe_payment_intent_id: stripePaymentIntentId,
            },
          });
        } catch (stripeError) {
          console.error("Stripe error:", stripeError);
          return res.status(400).json({
            error: true,
            message: "Payment processing failed: " + stripeError.message,
          });
        }
      } catch (err) {
        console.error("Enrollment error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to create enrollment",
        });
      }
    }
  );

  // Backward compatibility endpoint (old URL) - just redirect to new endpoint logic
  app.post(
    "/v2/api/kanglink/custom/athlete/enroll",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (_req, res) {
      // Forward to the main enrollment handler
      return res.status(301).json({
        error: true,
        message:
          "This endpoint has moved. Please use /v2/api/kanglink/custom/athlete/enrollment instead.",
        new_endpoint: "/v2/api/kanglink/custom/athlete/enrollment",
      });
    }
  );

  // Check enrollment status (for webhook processing confirmation)
  app.get(
    "/v2/api/kanglink/custom/athlete/enrollment/status/:subscription_id",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const subscriptionId = req.params.subscription_id;
        const userId = req.user_id;

        sdk.setProjectId("kanglink");

        // Find enrollment by subscription ID or payment intent ID
        sdk.setTable("enrollment");
        const enrollment = await sdk.findOne("enrollment", {
          stripe_subscription_id: subscriptionId,
        });

        if (!enrollment) {
          // Try finding by payment intent ID if subscription ID doesn't work
          const enrollmentByPayment = await sdk.findOne("enrollment", {
            stripe_payment_intent_id: subscriptionId,
          });

          if (!enrollmentByPayment) {
            return res.status(404).json({
              error: true,
              message: "Enrollment not found",
            });
          }

          // Check if user has access to this enrollment
          if (
            enrollmentByPayment.athlete_id !== userId &&
            req.role !== "trainer" &&
            req.role !== "super_admin"
          ) {
            return res.status(403).json({
              error: true,
              message: "Access denied",
            });
          }

          return res.status(200).json({
            error: false,
            status: enrollmentByPayment.status,
            payment_status: enrollmentByPayment.payment_status,
            enrollment_id: enrollmentByPayment.id,
          });
        }

        // Check if user has access to this enrollment
        if (
          enrollment.athlete_id !== userId &&
          req.role !== "trainer" &&
          req.role !== "super_admin"
        ) {
          return res.status(403).json({
            error: true,
            message: "Access denied",
          });
        }

        return res.status(200).json({
          error: false,
          status: enrollment.status,
          payment_status: enrollment.payment_status,
          enrollment_id: enrollment.id,
        });
      } catch (err) {
        console.error("Check enrollment status error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to check enrollment status",
        });
      }
    }
  );

  // Get athlete's enrollments
  app.get(
    "/v2/api/kanglink/custom/athlete/enrollments",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const athleteId = req.user_id;

        sdk.setProjectId("kanglink");

        // Get enrollments with related data
        const enrollments = await sdk.rawQuery(`
          SELECT
            e.*,
            p.program_name,
            p.type_of_program,
            s.title as split_title,
            u.email as trainer_email
          FROM kanglink_enrollment e
          JOIN kanglink_split s ON e.split_id = s.id
          JOIN kanglink_program p ON e.program_id = p.id
          JOIN kanglink_user u ON e.trainer_id = u.id
          WHERE e.athlete_id = ${athleteId}
          ORDER BY e.created_at DESC
        `);

        return res.status(200).json({
          error: false,
          data: enrollments,
        });
      } catch (err) {
        console.error("Get enrollments error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get enrollments",
        });
      }
    }
  );

  // Get trainer's enrollments (students)
  app.get(
    "/v2/api/kanglink/custom/trainer/enrollments",
    [TokenMiddleware({ role: "trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const trainerId = req.user_id;

        sdk.setProjectId("kanglink");

        // Get enrollments with related data
        const enrollments = await sdk.rawQuery(`
          SELECT
            e.*,
            p.program_name,
            p.type_of_program,
            s.title as split_title,
            u.email as athlete_email
          FROM kanglink_enrollment e
          JOIN kanglink_split s ON e.split_id = s.id
          JOIN kanglink_program p ON e.program_id = p.id
          JOIN kanglink_user u ON e.athlete_id = u.id
          WHERE e.trainer_id = ${trainerId}
          ORDER BY e.created_at DESC
        `);

        return res.status(200).json({
          error: false,
          data: enrollments,
        });
      } catch (err) {
        console.error("Get trainer enrollments error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get enrollments",
        });
      }
    }
  );

  // Cancel enrollment/subscription
  app.post(
    "/v2/api/kanglink/custom/enrollment/:enrollment_id/cancel",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const enrollmentId = req.params.enrollment_id;
        const userId = req.user_id;
        const userRole = req.role;
        const { cancel_type = "immediate" } = req.body; // "immediate" or "at_period_end"

        sdk.setProjectId("kanglink");

        // Get enrollment details
        sdk.setTable("enrollment");
        const enrollment = await sdk.findOne("enrollment", {
          id: enrollmentId,
        });

        if (!enrollment) {
          return res.status(404).json({
            error: true,
            message: "Enrollment not found",
          });
        }

        // Check permissions - athlete can cancel their own, trainer can cancel their students
        if (userRole === "member" && enrollment.athlete_id !== userId) {
          return res.status(403).json({
            error: true,
            message: "You can only cancel your own enrollments",
          });
        }

        if (userRole === "trainer" && enrollment.trainer_id !== userId) {
          return res.status(403).json({
            error: true,
            message: "You can only cancel enrollments for your programs",
          });
        }

        if (enrollment.status === "cancelled") {
          return res.status(400).json({
            error: true,
            message: "Enrollment is already cancelled",
          });
        }

        // Only allow cancellation of subscription enrollments
        if (enrollment.payment_type !== "subscription") {
          return res.status(400).json({
            error: true,
            message: "Only subscription enrollments can be cancelled",
          });
        }

        try {
          // Cancel Stripe subscription if it exists
          if (enrollment.stripe_subscription_id) {
            if (cancel_type === "at_period_end") {
              await stripe.cancelStripeSubscriptionAtPeriodEnd({
                subscriptionId: enrollment.stripe_subscription_id,
              });
            } else {
              await stripe.cancelStripeSubscription({
                subscriptionId: enrollment.stripe_subscription_id,
              });
            }
          }

          // Update enrollment status
          const now = new Date();
          await sdk.updateById("enrollment", enrollmentId, {
            status: "cancelled",
            updated_at: UtilService.sqlDateTimeFormat(now),
          });

          return res.status(200).json({
            error: false,
            message: cancel_type === "at_period_end" 
              ? "Subscription will be cancelled at the end of the current billing period"
              : "Enrollment cancelled successfully",
            cancel_type: cancel_type,
          });
        } catch (stripeError) {
          console.error("Stripe cancellation error:", stripeError);
          // Still update local status even if Stripe fails
          const now = new Date();
          await sdk.updateById("enrollment", enrollmentId, {
            status: "cancelled",
            updated_at: UtilService.sqlDateTimeFormat(now),
          });

          return res.status(200).json({
            error: false,
            message: "Enrollment cancelled (with Stripe error)",
            warning: stripeError.message,
          });
        }
      } catch (err) {
        console.error("Cancel enrollment error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to cancel enrollment",
        });
      }
    }
  );

  // Get split details for enrollment
  app.get(
    "/v2/api/kanglink/custom/splits/:split_id",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const splitId = req.params.split_id;

        sdk.setProjectId("kanglink");

        // Get split details with program info
        const split = await sdk.rawQuery(`
          SELECT
            s.*,
            p.program_name,
            p.type_of_program,
            p.program_description,
            p.currency,
            p.user_id as trainer_id,
            u.email as trainer_email
          FROM kanglink_split s
          JOIN kanglink_program p ON s.program_id = p.id
          JOIN kanglink_user u ON p.user_id = u.id
          WHERE s.id = ${splitId}
        `);

        if (!split || split.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Split not found",
          });
        }

        return res.status(200).json({
          error: false,
          data: split[0],
        });
      } catch (err) {
        console.error("Get split details error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get split details",
        });
      }
    }
  );

  // Get available splits for enrollment (public splits)
  app.get(
    "/v2/api/kanglink/custom/splits",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { trainer_id, program_id } = req.query;

        sdk.setProjectId("kanglink");

        let whereClause = "WHERE p.status = 'published'";
        if (trainer_id) {
          whereClause += ` AND p.user_id = ${trainer_id}`;
        }
        if (program_id) {
          whereClause += ` AND p.id = ${program_id}`;
        }

        // Get available splits
        const splits = await sdk.rawQuery(`
          SELECT
            s.*,
            p.program_name,
            p.type_of_program,
            p.program_description,
            p.currency,
            p.user_id as trainer_id,
            u.email as trainer_email
          FROM kanglink_split s
          JOIN kanglink_program p ON s.program_id = p.id
          JOIN kanglink_user u ON p.user_id = u.id
          ${whereClause}
          ORDER BY p.created_at DESC, s.created_at ASC
        `);

        return res.status(200).json({
          error: false,
          data: splits,
        });
      } catch (err) {
        console.error("Get splits error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get splits",
        });
      }
    }
  );

  // Check enrollment eligibility for a split
  app.get(
    "/v2/api/kanglink/custom/splits/:split_id/eligibility",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const splitId = req.params.split_id;
        const userId = req.user_id;

        sdk.setProjectId("kanglink");

        // Get split details with program info
        const split = await sdk.rawQuery(`
          SELECT
            s.*,
            p.program_name,
            p.status as program_status,
            p.user_id as trainer_id
          FROM kanglink_split s
          JOIN kanglink_program p ON s.program_id = p.id
          WHERE s.id = ${splitId}
        `);

        if (!split || split.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Split not found",
          });
        }

        const splitData = split[0];

        // Check if program is published
        const isProgramPublished = splitData.program_status === "published";

        // Check for existing enrollment
        sdk.setTable("enrollment");
        const existingEnrollment = await sdk.findOne("enrollment", {
          athlete_id: userId,
          split_id: splitId,
          status: "active",
        });

        // Check pricing availability
        const hasOneTimePrice = splitData.full_price > 0;
        const hasSubscriptionPrice = splitData.subscription > 0;

        // Check Stripe configuration
        const oneTimeStripePrice = hasOneTimePrice
          ? await findStripePriceForSplit(sdk, splitData, "one_time")
          : null;
        const subscriptionStripePrice = hasSubscriptionPrice
          ? await findStripePriceForSplit(sdk, splitData, "subscription")
          : null;

        const eligibility = {
          can_enroll:
            isProgramPublished &&
            !existingEnrollment &&
            (hasOneTimePrice || hasSubscriptionPrice),
          reasons: [],
          payment_options: {
            one_time: {
              available: hasOneTimePrice && !!oneTimeStripePrice,
              reason: !hasOneTimePrice
                ? "No one-time price set"
                : !oneTimeStripePrice
                  ? "Stripe price not configured"
                  : null,
            },
            subscription: {
              available: hasSubscriptionPrice && !!subscriptionStripePrice,
              reason: !hasSubscriptionPrice
                ? "No subscription price set"
                : !subscriptionStripePrice
                  ? "Stripe price not configured"
                  : null,
            },
          },
        };

        // Add specific reasons why enrollment might not be possible
        if (!isProgramPublished) {
          eligibility.reasons.push("Program is not published");
        }
        if (existingEnrollment) {
          eligibility.reasons.push("Already enrolled in this split");
        }
        if (!hasOneTimePrice && !hasSubscriptionPrice) {
          eligibility.reasons.push("No pricing configured for this split");
        }
        if (
          (hasOneTimePrice && !oneTimeStripePrice) ||
          (hasSubscriptionPrice && !subscriptionStripePrice)
        ) {
          eligibility.reasons.push("Stripe payment processing not configured");
        }

        return res.status(200).json({
          error: false,
          data: {
            split_id: splitData.id,
            program_name: splitData.program_name,
            eligibility: eligibility,
            existing_enrollment: existingEnrollment
              ? {
                id: existingEnrollment.id,
                payment_type: existingEnrollment.payment_type,
                status: existingEnrollment.status,
                enrollment_date: existingEnrollment.enrollment_date,
              }
              : null,
          },
        });
      } catch (err) {
        console.error("Check eligibility error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to check enrollment eligibility",
        });
      }
    }
  );

  // Resubscribe to a failed subscription enrollment
  app.post(
    "/v2/api/kanglink/custom/enrollment/:enrollment_id/resubscribe",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        return res.status(200).json({
          error: false,
          message: "Resubscribe to a failed subscription enrollment",
        });
        
        const sdk = app.get("sdk");
        const enrollmentId = req.params.enrollment_id;
        const userId = req.user_id;
        const userRole = req.role;
        const { payment_method_id } = req.body;

        if (!payment_method_id) {
          return res.status(400).json({
            error: true,
            message: "payment_method_id is required",
          });
        }

        sdk.setProjectId("kanglink");

        // Get enrollment details
        sdk.setTable("enrollment");
        const enrollment = await sdk.findOne("enrollment", {
          id: enrollmentId,
        });

        if (!enrollment) {
          return res.status(404).json({
            error: true,
            message: "Enrollment not found",
          });
        }

        // Check permissions - athlete can resubscribe their own, trainer can help their students
        if (userRole === "member" && enrollment.athlete_id !== userId) {
          return res.status(403).json({
            error: true,
            message: "You can only resubscribe to your own enrollments",
          });
        }

        if (userRole === "trainer" && enrollment.trainer_id !== userId) {
          return res.status(403).json({
            error: true,
            message: "You can only resubscribe enrollments for your programs",
          });
        }

        // Only allow resubscription for subscription enrollments with failed payment
        if (enrollment.payment_type !== "subscription") {
          return res.status(400).json({
            error: true,
            message: "Only subscription enrollments can be resubscribed",
          });
        }

        if (enrollment.payment_status !== "failed" || enrollment.status !== "payment_failed") {
          return res.status(400).json({
            error: true,
            message: "This enrollment does not have a failed payment status",
          });
        }

        if (!enrollment.stripe_subscription_id) {
          return res.status(400).json({
            error: true,
            message: "No Stripe subscription found for this enrollment",
          });
        }

        try {
          // Update the payment method on the existing Stripe subscription
          const updatedSubscription = await stripe.updateStripeSubscription({
            subscriptionId: enrollment.stripe_subscription_id,
            default_payment_method: payment_method_id,
            cancel_at_period_end: false, // Ensure subscription doesn't cancel at period end
          });

          // If the subscription is still in a failed state, we may need to handle pending invoices
          // Get customer invoices to check for unpaid invoices
          const invoices = await stripe.retrieveCustomerInvoices({
            customerId: updatedSubscription.customer,
            limit: 5,
          });

          let hasPendingInvoice = false;
          if (invoices && invoices.data) {
            for (const invoice of invoices.data) {
              if (invoice.subscription === enrollment.stripe_subscription_id && 
                  (invoice.status === "open" || invoice.status === "past_due")) {
                hasPendingInvoice = true;
                // For subscription invoices, Stripe will automatically retry with the new payment method
                console.log(`Found pending invoice ${invoice.id}, Stripe will retry automatically`);
                break;
              }
            }
          }

          // Update enrollment status - mark as pending while we wait for Stripe to process
          const now = new Date();
          await sdk.updateById("enrollment", enrollmentId, {
            status: hasPendingInvoice ? "pending_payment" : "active",
            payment_status: hasPendingInvoice ? "pending" : "paid",
            updated_at: UtilService.sqlDateTimeFormat(now),
          });

          return res.status(200).json({
            error: false,
            message: hasPendingInvoice 
              ? "Payment method updated successfully. Stripe will retry the pending payment automatically." 
              : "Subscription reactivated successfully",
            data: {
              enrollment_id: enrollmentId,
              subscription_id: enrollment.stripe_subscription_id,
              status: hasPendingInvoice ? "pending_payment" : "active",
              payment_status: hasPendingInvoice ? "pending" : "paid",
              has_pending_invoice: hasPendingInvoice,
            },
          });

        } catch (stripeError) {
          console.error("Stripe resubscribe error:", stripeError);
          return res.status(400).json({
            error: true,
            message: "Failed to reactivate subscription: " + stripeError.message,
          });
        }

      } catch (err) {
        console.error("Resubscribe error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to resubscribe",
        });
      }
    }
  );

  // Get enrollment pricing information for a split
  app.get(
    "/v2/api/kanglink/custom/splits/:split_id/pricing",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const splitId = req.params.split_id;

        sdk.setProjectId("kanglink");

        // Get split details with program info
        const split = await sdk.rawQuery(`
          SELECT
            s.*,
            p.program_name,
            p.type_of_program,
            p.currency,
            p.user_id as trainer_id
          FROM kanglink_split s
          JOIN kanglink_program p ON s.program_id = p.id
          WHERE s.id = ${splitId}
        `);

        if (!split || split.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Split not found",
          });
        }

        const splitData = split[0];

        // Check if Stripe prices are configured
        const oneTimePrice = await findStripePriceForSplit(
          sdk,
          splitData,
          "one_time"
        );
        const subscriptionPrice = await findStripePriceForSplit(
          sdk,
          splitData,
          "subscription"
        );

        // Calculate minimum price (excluding zero values)
        const prices = [splitData.full_price, splitData.subscription].filter(
          (p) => p > 0
        );
        const minPrice = prices.length > 0 ? Math.min(...prices) : 0;

        return res.status(200).json({
          error: false,
          data: {
            split_id: splitData.id,
            program_name: splitData.program_name,
            split_title: splitData.title,
            currency: splitData.currency || "USD",
            pricing: {
              one_time: {
                amount: splitData.full_price,
                available: splitData.full_price > 0,
                stripe_configured: !!oneTimePrice,
                description:
                  "Lifetime access, no updates when trainer modifies split",
              },
              subscription: {
                amount: splitData.subscription,
                available: splitData.subscription > 0,
                stripe_configured: !!subscriptionPrice,
                description: "Monthly billing, access with automatic updates",
              },
              minimum: minPrice,
            },
            recommendations: {
              best_value:
                splitData.full_price > 0 && splitData.subscription > 0
                  ? splitData.full_price / splitData.subscription < 6
                    ? "one_time"
                    : "subscription"
                  : null,
              savings_months:
                splitData.full_price > 0 && splitData.subscription > 0
                  ? Math.ceil(splitData.full_price / splitData.subscription)
                  : null,
            },
          },
        });
      } catch (err) {
        console.error("Get pricing error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get pricing information",
        });
      }
    }
  );

  // Get split content based on enrollment access type
  app.get(
    "/v2/api/kanglink/custom/athlete/enrollment/:enrollment_id/content",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const enrollmentId = req.params.enrollment_id;
        const userId = req.user_id;

        sdk.setProjectId("kanglink");

        // Get enrollment details
        sdk.setTable("enrollment");
        const enrollment = await sdk.findOne("enrollment", {
          id: enrollmentId,
        });

        if (!enrollment) {
          return res.status(404).json({
            error: true,
            message: "Enrollment not found",
          });
        }

        // Verify user has access to this enrollment
        if (
          enrollment.athlete_id !== userId &&
          req.role !== "trainer" &&
          req.role !== "super_admin"
        ) {
          return res.status(403).json({
            error: true,
            message: "Access denied to this enrollment",
          });
        }

        // Check if enrollment is active
        if (enrollment.status !== "active") {
          return res.status(400).json({
            error: true,
            message: "Enrollment is not active",
          });
        }

        let splitContent;

        if (
          enrollment.access_type === "snapshot" &&
          enrollment.split_snapshot
        ) {
          // Return frozen snapshot for one-time purchases
          try {
            splitContent = JSON.parse(enrollment.split_snapshot);
            splitContent.access_info = {
              type: "snapshot",
              message:
                "This is your lifetime access version. Content is frozen at purchase time and won't receive updates.",
              purchase_date: enrollment.enrollment_date,
              payment_type: enrollment.payment_type,
            };
          } catch (parseError) {
            console.error("Error parsing split snapshot:", parseError);
            return res.status(500).json({
              error: true,
              message: "Error retrieving snapshot content",
            });
          }
        } else {
          // Return live content for subscriptions
          try {
            splitContent = await captureSplitSnapshot(sdk, enrollment.split_id);
            splitContent.access_info = {
              type: "live",
              message:
                "This is your subscription access. You'll automatically receive all updates to this split.",
              enrollment_date: enrollment.enrollment_date,
              payment_type: enrollment.payment_type,
            };
          } catch (snapshotError) {
            console.error("Error capturing live split content:", snapshotError);
            return res.status(500).json({
              error: true,
              message: "Error retrieving current split content",
            });
          }
        }

        if (!splitContent) {
          return res.status(404).json({
            error: true,
            message: "Split content not found",
          });
        }

        return res.status(200).json({
          error: false,
          data: {
            enrollment_id: enrollmentId,
            access_type: enrollment.access_type || "live",
            content: splitContent,
          },
        });
      } catch (err) {
        console.error("Get enrollment content error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get enrollment content",
        });
      }
    }
  );

  // Helper function to capture split snapshot for one-time purchases
  async function captureSplitSnapshot(sdk, splitId) {
    try {
      // Get complete split data with all related content
      const splitData = await sdk.rawQuery(`
        SELECT
          s.*,
          p.program_name,
          p.program_description,
          p.type_of_program,
          p.currency,
          p.user_id as trainer_id
        FROM kanglink_split s
        JOIN kanglink_program p ON s.program_id = p.id
        WHERE s.id = ${splitId}
      `);

      if (!splitData || splitData.length === 0) {
        throw new Error(`Split with ID ${splitId} not found`);
      }

      const split = splitData[0];

    // Get all weeks for this split
    const weeks = await sdk.rawQuery(`
        SELECT * FROM kanglink_week
        WHERE split_id = ${splitId}
        ORDER BY week_order
      `) || [];

    // Get all days for these weeks - handle empty arrays properly
    let days = [];
    if (weeks && weeks.length > 0) {
      const weekIds = weeks.map((w) => w.id);
      if (weekIds.length > 0) {
        days = await sdk.rawQuery(`
          SELECT * FROM kanglink_day
          WHERE week_id IN (${weekIds.join(",")})
          ORDER BY week_id, day_order
        `);
      }
    }

    // Get all sessions for these days - handle empty arrays properly
    let sessions = [];
    if (days && days.length > 0) {
      const dayIds = days.map((d) => d.id);
      if (dayIds.length > 0) {
        sessions = await sdk.rawQuery(`
          SELECT * FROM kanglink_session
          WHERE day_id IN (${dayIds.join(",")})
          ORDER BY day_id, session_order
        `);
      }
    }

    // Get all exercise instances for these sessions - handle empty arrays properly
    let exerciseInstances = [];
    if (sessions && sessions.length > 0) {
      const sessionIds = sessions.map((s) => s.id);
      if (sessionIds.length > 0) {
        exerciseInstances = await sdk.rawQuery(`
          SELECT ei.*, e.name as exercise_name
          FROM kanglink_exercise_instance ei
          LEFT JOIN kanglink_exercise e ON ei.exercise_id = e.id
          WHERE ei.session_id IN (${sessionIds.join(",")})
          ORDER BY ei.session_id, ei.exercise_order
        `);
      }
    }

    // Get exercises for exercises - handle empty arrays properly
    let exercises = [];
    if (exerciseInstances && exerciseInstances.length > 0) {
      const exerciseIds = exerciseInstances
        .map((ei) => ei.exercise_id)
        .filter((id) => id != null); // Filter out null/undefined exercise IDs
      
      if (exerciseIds.length > 0) {
        exercises = await sdk.rawQuery(`
          SELECT * FROM kanglink_exercise
          WHERE id IN (${exerciseIds.join(",")})
        `);
      }
    }

    // Build the complete snapshot
    const snapshot = {
      split: split,
      weeks: weeks.map((week) => ({
        ...week,
        days: days
          .filter((day) => day.week_id === week.id)
          .map((day) => ({
            ...day,
            sessions: sessions
              .filter((session) => session.day_id === day.id)
              .map((session) => ({
                ...session,
                exercise_instances: exerciseInstances
                  .filter((ei) => ei.session_id === session.id)
                  .map((exercise) => ({
                    ...exercise,
                    video: exercise.video_url ? { url: exercise.video_url } : null,
                    exercise: exercise.exercise_id
                      ? exercises.find(e => e.id === exercise.exercise_id) || null
                      : exercise.exercise_name
                        ? { name: exercise.exercise_name }
                        : null,
                  })),
              })),
          })),
      })),
      snapshot_date: new Date().toISOString(),
      snapshot_version: "1.0",
    };

    // Log snapshot creation for debugging
    console.log(`Successfully created snapshot for split ${splitId}:`, {
      weeks_count: weeks.length,
      days_count: days.length,
      sessions_count: sessions.length,
      exercise_instances_count: exerciseInstances.length,
      exercises_count: exercises.length
    });

      return snapshot;
    } catch (error) {
      console.error(`Error capturing split snapshot for split ${splitId}:`, error);
      throw new Error(`Failed to capture split snapshot: ${error.message}`);
    }
  }

  // Helper function to find existing Stripe price for a split
  async function findStripePriceForSplit(sdk, splitData, paymentType) {
    const priceName =
      paymentType === "subscription"
        ? `subscription - program ${splitData.program_id} - split ${splitData.id}`
        : `one_time - program ${splitData.program_id} - split ${splitData.id}`;

    sdk.setTable("stripe_price");
    const stripePrice = await sdk.findOne("stripe_price", {
      name: priceName,
      status: 1, // Only active prices
    });

    return stripePrice;
  }


};

// {
//   "split": {
//     "id": 244,
//     "user_id": 11,
//     "program_id": 71,
//     "title": "Split 1",
//     "full_price": "500.00",
//     "subscription": null,
//     "program_split": null,
//     "description": null,
//     "equipment_required": "",
//     "created_at": "2025-08-04T11:40:15.000Z",
//     "updated_at": "2025-08-04T11:40:15.000Z",
//     "program_name": "Biceps",
//     "program_description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit",
//     "type_of_program": "cross-fit",
//     "currency": "USD",
//     "trainer_id": 11
//   },
//   "weeks": [
//     {
//       "id": 253,
//       "user_id": 11,
//       "split_id": 244,
//       "title": "Week 1",
//       "week_order": 1,
//       "created_at": "2025-08-04T11:40:15.000Z",
//       "updated_at": "2025-08-04T11:40:15.000Z",
//       "days": [
//         {
//           "id": 482,
//           "user_id": 11,
//           "week_id": 253,
//           "title": "Day 1",
//           "is_rest_day": 0,
//           "is_collapsed": 0,
//           "day_order": 1,
//           "created_at": "2025-08-04T11:40:15.000Z",
//           "updated_at": "2025-08-04T11:40:15.000Z",
//           "sessions": [
//             {
//               "id": 460,
//               "user_id": 11,
//               "day_id": 482,
//               "title": "Session 1",
//               "session_letter": null,
//               "session_number": null,
//               "is_collapsed": 0,
//               "session_order": 1,
//               "created_at": "2025-08-04T11:40:15.000Z",
//               "updated_at": "2025-08-04T11:40:15.000Z",
//               "exercise_instances": [
//                 {
//                   "id": 580,
//                   "user_id": 11,
//                   "session_id": 460,
//                   "exercise_id": 17,
//                   "exercise_name": "stress hype",
//                   "video_url": "https://www.youtube.com/watch?v=IT94xC35u6k",
//                   "sets": "10",
//                   "reps_or_time": "10",
//                   "time_minutes": null,
//                   "time_seconds": null,
//                   "reps_time_type": "reps",
//                   "exercise_details": "Lorem ipsum dolor sit amet, consectetur adipiscing elit",
//                   "rest_duration_minutes": 15,
//                   "rest_duration_seconds": 30,
//                   "label": "A",
//                   "label_number": "1",
//                   "is_linked": 0,
//                   "exercise_order": 1,
//                   "created_at": "2025-08-04T11:40:15.000Z",
//                   "updated_at": "2025-08-04T11:40:15.000Z",
//                   "video": {
//                     "url": "https://www.youtube.com/watch?v=IT94xC35u6k"
//                   },
//                   "exercise": {
//                     "id": 17,
//                     "name": "stress hype",
//                     "type": 2,
//                     "temp": 0,
//                     "exercise_type": null,
//                     "user_id": 11,
//                     "video_url": null,
//                     "created_at": "2025-07-29T17:08:21.000Z",
//                     "updated_at": "2025-07-29T17:08:21.000Z"
//                   }
//                 }
//               ]
//             }
//           ]
//         }
//       ]
//     }
//   ],
//   "snapshot_date": "2025-08-04T11:47:05.881Z",
//   "snapshot_version": "1.0"
// }