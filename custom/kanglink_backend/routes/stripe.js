const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const StripeService = require("../../../baas/services/StripeService");
const UtilService = require("../../../baas/services/UtilService");

module.exports = function (app) {
  // Attach PaymentMethod to Customer
  app.post(
    "/v1/api/kanglink/custom/member/stripe/customer/payment-method/attach",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { payment_method_id } = req.body;
        const userId = req.user_id;

        // Validate required parameters
        if (!payment_method_id) {
          return res.status(400).json({
            error: true,
            message: "payment_method_id is required",
          });
        }

        sdk.setProjectId("kanglink");
        sdk.setTable("user");

        // Get user and their Stripe customer ID
        const user = await sdk.findOne("user", { id: userId });
        if (!user) {
          return res.status(404).json({
            error: true,
            message: "User not found",
          });
        }

        let customerStripeId = user.stripe_uid;

        // Create Stripe customer if doesn't exist
        const stripe = new StripeService();
        if (!customerStripeId) {
          const customer = await stripe.createStripeCustomer({
            email: user.email,
            metadata: {
              projectId: "kanglink",
              user_id: userId.toString(),
            },
          });

          customerStripeId = customer.id;

          // Update user with Stripe customer ID
          await sdk.updateById("user", userId, {
            stripe_uid: customerStripeId,
            updated_at: UtilService.sqlDateTimeFormat(new Date()),
          });
        }

        // Attach PaymentMethod to customer
        const attachedPaymentMethod =
          await stripe.attachPaymentMethodToCustomer({
            payment_method_id: payment_method_id,
            customer_id: customerStripeId,
          });

        return res.status(200).json({
          error: false,
          message: "PaymentMethod attached successfully",
          data: {
            payment_method: attachedPaymentMethod,
            customer_id: customerStripeId,
          },
        });
      } catch (err) {
        console.error("PaymentMethod attachment error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to attach PaymentMethod",
        });
      }
    }
  );

  // Detach PaymentMethod from Customer
  app.post(
    "/v1/api/kanglink/custom/member/stripe/customer/payment-method/detach",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const { payment_method_id } = req.body;

        // Validate required parameters
        if (!payment_method_id) {
          return res.status(400).json({
            error: true,
            message: "payment_method_id is required",
          });
        }

        const stripe = new StripeService();

        // Detach PaymentMethod from customer
        const detachedPaymentMethod =
          await stripe.detachPaymentMethodFromCustomer({
            payment_method_id: payment_method_id,
          });

        return res.status(200).json({
          error: false,
          message: "PaymentMethod detached successfully",
          data: {
            payment_method: detachedPaymentMethod,
          },
        });
      } catch (err) {
        console.error("PaymentMethod detachment error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to detach PaymentMethod",
        });
      }
    }
  );

  // List Customer PaymentMethods
  app.get(
    "/v1/api/kanglink/custom/member/stripe/customer/payment-methods",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const userId = req.user_id;
        const { type = "card", limit = 10 } = req.query;

        sdk.setProjectId("kanglink");
        sdk.setTable("user");

        // Get user and their Stripe customer ID
        const user = await sdk.findOne("user", { id: userId });
        if (!user) {
          return res.status(404).json({
            error: true,
            message: "User not found",
          });
        }

        const customerStripeId = user.stripe_uid;
        if (!customerStripeId) {
          return res.status(200).json({
            error: false,
            message: "No payment methods found",
            data: {
              payment_methods: [],
              has_more: false,
            },
          });
        }

        const stripe = new StripeService();

        // List customer's PaymentMethods
        const paymentMethods = await stripe.listCustomerPaymentMethods({
          customer_id: customerStripeId,
          type: type,
          limit: parseInt(limit),
        });

        return res.status(200).json({
          error: false,
          message: "PaymentMethods retrieved successfully",
          data: {
            payment_methods: paymentMethods.data || [],
            has_more: paymentMethods.has_more || false,
          },
        });
      } catch (err) {
        console.error("PaymentMethods list error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to retrieve PaymentMethods",
        });
      }
    }
  );
};
