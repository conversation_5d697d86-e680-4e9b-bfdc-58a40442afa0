const AuthService = require("../../../baas/services/AuthService");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const UtilService = require("../../../baas/services/UtilService");
const DiscountService = require("../services/DiscountService");

module.exports = function (app) {
  // GET - Fetch Program Discount Details
  app.get(
    "/v2/api/kanglink/custom/trainer/programs/:programId/discounts",
    [TokenMiddleware({ role: "trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const programId = req.params.programId;
        const userId = req.user_id;

        // Validate program ID
        if (!programId) {
          return res.status(400).json({
            error: true,
            message: "Program ID is required",
          });
        }

        sdk.setProjectId("kanglink");

        // First, verify the program exists and belongs to the trainer using raw query with joins
        const programWithSplitsQuery = `
          SELECT
            p.*,
            s.id as split_id,
            s.title as split_title,
            s.full_price as split_full_price,
            s.subscription as split_subscription,
            s.created_at as split_created_at,
            s.updated_at as split_updated_at
          FROM kanglink_program p
          LEFT JOIN kanglink_split s ON p.id = s.program_id
          WHERE p.id = ? AND p.user_id = ?
        `;

        const programWithSplitsResult = await sdk.rawQuery(
          programWithSplitsQuery,
          [programId, userId]
        );

        if (!programWithSplitsResult || programWithSplitsResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Program not found or access denied",
          });
        }

        // Extract and organize splits data
        const programSplits = [];
        const splitMap = {};

        programWithSplitsResult.forEach((row) => {
          if (row.split_id && !splitMap[row.split_id]) {
            const split = {
              id: row.split_id,
              title: row.split_title,
              full_price: row.split_full_price,
              subscription: row.split_subscription,
              created_at: row.split_created_at,
              updated_at: row.split_updated_at,
            };
            programSplits.push(split);
            splitMap[row.split_id] = split;
          }
        });

        // Extract program data (first row contains program info)
        const program = {
          id: programWithSplitsResult[0].id,
          user_id: programWithSplitsResult[0].user_id,
          program_name: programWithSplitsResult[0].program_name,
          program_plan: programWithSplitsResult[0].program_plan,
          type_of_program: programWithSplitsResult[0].type_of_program,
          program_description: programWithSplitsResult[0].program_description,
          payment_plan: programWithSplitsResult[0].payment_plan,
          track_progress: programWithSplitsResult[0].track_progress,
          allow_comments: programWithSplitsResult[0].allow_comments,
          allow_private_messages:
            programWithSplitsResult[0].allow_private_messages,
          target_levels: programWithSplitsResult[0].target_levels,
          split_program: programWithSplitsResult[0].split_program,
          splits: programSplits,
          // Add other program fields as needed
        };

        // Fetch program discount settings
        const programDiscountResult = await sdk.find("program_discount", {
          program_id: programId,
        });
        const programDiscount =
          programDiscountResult.length > 0 ? programDiscountResult[0] : null;

        // Fetch split-specific discounts
        const subscription_discounts = await sdk.find("discount", {
          program_id: programId,
          applies_to: "subscription",
          is_active: true,
        });

        const full_price_discounts = await sdk.find("discount", {
          program_id: programId,
          applies_to: "full_payment",
          is_active: true,
        });

        const bothDiscounts = await sdk.find("discount", {
          program_id: programId,
          applies_to: "both",
          is_active: true,
        });

        // Fetch active coupon for this program
        const couponsResult = await sdk.find("coupon", {
          program_id: programId,
          is_active: true,
        });
        const activeCoupon = couponsResult.length > 0 ? couponsResult[0] : null;

        // Build response data
        const responseData = {
          program,
          id: programDiscount?.id || null,
          affiliate_link: programDiscount?.affiliate_link || "",
          sale_discount: programDiscount
            ? {
                type: programDiscount.sale_discount_type,
                value: programDiscount.sale_discount_value,
                apply_to_all: programDiscount.sale_apply_to_all,
              }
            : null,
          subscription_discounts: [
            ...subscription_discounts.map((discount) => ({
              id: discount.id,
              tier_id: discount.split_id,
              tier_title: splitMap[discount.split_id]?.title || "Unknown Split",
              discount_type: discount.discount_type,
              discount_value: discount.discount_value,
              split_info: splitMap[discount.split_id] || null,
            })),
            ...bothDiscounts.map((discount) => ({
              id: discount.id,
              tier_id: discount.split_id,
              tier_title: splitMap[discount.split_id]?.title || "Unknown Split",
              discount_type: discount.discount_type,
              discount_value: discount.discount_value,
              split_info: splitMap[discount.split_id] || null,
            })),
          ],
          full_price_discounts: [
            ...full_price_discounts.map((discount) => ({
              id: discount.id,
              tier_id: discount.split_id,
              tier_title: splitMap[discount.split_id]?.title || "Unknown Split",
              discount_type: discount.discount_type,
              discount_value: discount.discount_value,
              split_info: splitMap[discount.split_id] || null,
            })),
            ...bothDiscounts.map((discount) => ({
              id: discount.id,
              tier_id: discount.split_id,
              tier_title: splitMap[discount.split_id]?.title || "Unknown Split",
              discount_type: discount.discount_type,
              discount_value: discount.discount_value,
              split_info: splitMap[discount.split_id] || null,
            })),
          ],
          promo_code: activeCoupon
            ? {
                id: activeCoupon.id,
                code: activeCoupon.code,
                discount_type: activeCoupon.discount_type,
                discount_value: activeCoupon.discount_value,
                applies_to: {
                  subscription:
                    activeCoupon.applies_to === "subscription" ||
                    activeCoupon.applies_to === "both",
                  full_payment:
                    activeCoupon.applies_to === "full_payment" ||
                    activeCoupon.applies_to === "both",
                },
                is_active: activeCoupon.is_active,
                expiry_date: activeCoupon.expiry_date,
                usage_limit: activeCoupon.usage_limit,
                used_count: activeCoupon.used_count,
              }
            : null,
          last_updated: programDiscount?.updated_at || new Date().toISOString(),
        };

        return res.status(200).json({
          success: true,
          error: false,
          data: responseData,
        });
      } catch (err) {
        console.error("Get discount error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to fetch discount details",
        });
      }
    }
  );

  // POST - Create Program Discount Settings
  app.post(
    "/v2/api/kanglink/custom/trainer/programs/:programId/discounts",
    [TokenMiddleware({ role: "trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const programId = req.params.programId;
        const userId = req.user_id;
        const {
          affiliate_link,
          sale_discount,
          subscription_discounts,
          full_price_discounts,
          promo_code,
        } = req.body;

        // Validate program ID
        if (!programId) {
          return res.status(400).json({
            error: true,
            message: "Program ID is required",
          });
        }

        sdk.setProjectId("kanglink");

        // First, verify the program exists and belongs to the trainer using raw query
        const programQuery = `
          SELECT p.* FROM kanglink_program p WHERE p.id = ? AND p.user_id = ?
        `;

        const programResult = await sdk.rawQuery(programQuery, [
          programId,
          userId,
        ]);

        if (!programResult || programResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Program not found or access denied",
          });
        }

        const program = programResult[0];

        // Check if discount configuration already exists
        const existingProgramDiscount = await sdk.find("program_discount", {
          program_id: programId,
        });

        if (existingProgramDiscount.length > 0) {
          return res.status(409).json({
            error: true,
            message:
              "Discount configuration already exists for this program. Use PUT to update.",
          });
        }

        const now = new Date();
        const sqlDateTime = UtilService.sqlDateTimeFormat(now);

        // Validation helper function (same as PUT)
        const validateDiscount = (discount, fieldName) => {
          if (!discount) return null;

          if (
            discount.type &&
            !["fixed", "percentage"].includes(discount.type)
          ) {
            throw new Error(
              `${fieldName} type must be 'fixed' or 'percentage'`
            );
          }

          if (discount.value !== undefined) {
            if (typeof discount.value !== "number" || discount.value < 0) {
              throw new Error(`${fieldName} value must be a positive number`);
            }

            if (discount.type === "percentage" && discount.value > 100) {
              throw new Error(`${fieldName} percentage cannot exceed 100%`);
            }
          }

          return null;
        };

        // Validate sale discount
        if (sale_discount) {
          validateDiscount(sale_discount, "Sale discount");
        }

        // Get program splits for validation
        const programSplits = await sdk.find("split", {
          program_id: programId,
        });
        const validSplitIds = programSplits.map((split) => split.id);

        // Validate subscription and full price discounts
        if (subscription_discounts && Array.isArray(subscription_discounts)) {
          subscription_discounts.forEach((discount, index) => {
            validateDiscount(discount, `Subscription discount ${index + 1}`);
            if (!discount.tier_id) {
              throw new Error(
                `Subscription discount ${index + 1} must have a tier_id`
              );
            }
            if (!validSplitIds.includes(discount.tier_id)) {
              throw new Error(
                `Subscription discount ${
                  index + 1
                } has invalid tier_id - split does not belong to this program`
              );
            }
          });
        }

        if (full_price_discounts && Array.isArray(full_price_discounts)) {
          full_price_discounts.forEach((discount, index) => {
            validateDiscount(discount, `Full price discount ${index + 1}`);
            if (!discount.tier_id) {
              throw new Error(
                `Full price discount ${index + 1} must have a tier_id`
              );
            }
            if (!validSplitIds.includes(discount.tier_id)) {
              throw new Error(
                `Full price discount ${
                  index + 1
                } has invalid tier_id - split does not belong to this program`
              );
            }
          });
        }

        // Validate promo code
        if (promo_code) {
          validateDiscount(promo_code, "Promo code");

          if (!promo_code.code || promo_code.code.length < 3) {
            throw new Error("Promo code must be at least 3 characters long");
          }

          if (promo_code.code.length > 50) {
            throw new Error("Promo code cannot exceed 50 characters");
          }

          // Check for valid characters (alphanumeric and common symbols)
          if (!/^[A-Za-z0-9_-]+$/.test(promo_code.code)) {
            throw new Error(
              "Promo code can only contain letters, numbers, hyphens, and underscores"
            );
          }

          if (
            !["subscription", "full_payment", "both"].includes(
              promo_code.applies_to
            )
          ) {
            throw new Error(
              "Promo code must apply to subscription, full payment, or both"
            );
          }

          if (promo_code.usage_limit && promo_code.usage_limit < 1) {
            throw new Error("Promo code usage limit must be positive");
          }

          if (promo_code.expiry_date) {
            const expiry_date = new Date(promo_code.expiry_date);
            if (isNaN(expiry_date.getTime())) {
              throw new Error("Invalid expiry date format");
            }
            if (expiry_date <= new Date()) {
              throw new Error("Expiry date must be in the future");
            }
          }

          // Check for duplicate coupon codes
          const duplicateCoupons = await sdk.find("coupon", {
            code: promo_code.code.toUpperCase(),
          });

          const activeDuplicates = duplicateCoupons.filter(
            (coupon) => coupon.is_active
          );

          if (activeDuplicates.length > 0) {
            throw new Error(
              "This coupon code is already in use by another program"
            );
          }
        }

        // Create discount configuration
        try {
          // 1. Create program discount settings
          const programDiscountData = {
            program_id: programId,
            affiliate_link: affiliate_link || "",
            sale_discount_type: sale_discount?.type || null,
            sale_discount_value: sale_discount?.value || null,
            sale_apply_to_all: sale_discount?.apply_to_all || false,
            created_at: sqlDateTime,
            updated_at: sqlDateTime,
          };

          const createdProgramDiscount = await sdk.create(
            "program_discount",
            programDiscountData
          );

          // 2. Create subscription discounts
          const createdsubscription_discounts = [];
          if (subscription_discounts && Array.isArray(subscription_discounts)) {
            for (const discount of subscription_discounts) {
              const discountData = {
                program_id: programId,
                split_id: discount.tier_id,
                discount_type: discount.discount_type,
                discount_value: discount.discount_value,
                applies_to: "subscription",
                is_active: true,
                created_at: sqlDateTime,
                updated_at: sqlDateTime,
              };
              const createdDiscount = await sdk.create(
                "discount",
                discountData
              );
              createdsubscription_discounts.push(createdDiscount);
            }
          }

          // 3. Create full price discounts
          const createdfull_price_discounts = [];
          if (full_price_discounts && Array.isArray(full_price_discounts)) {
            for (const discount of full_price_discounts) {
              const discountData = {
                program_id: programId,
                split_id: discount.tier_id,
                discount_type: discount.discount_type,
                discount_value: discount.discount_value,
                applies_to: "full_payment",
                is_active: true,
                created_at: sqlDateTime,
                updated_at: sqlDateTime,
              };
              const createdDiscount = await sdk.create(
                "discount",
                discountData
              );
              createdfull_price_discounts.push(createdDiscount);
            }
          }

          // 4. Create promo code
          let createdCoupon = null;
          if (promo_code) {
            const couponData = {
              program_id: programId,
              code: promo_code.code.toUpperCase(),
              discount_type: promo_code.discount_type,
              discount_value: promo_code.discount_value,
              applies_to: promo_code.applies_to,
              is_active: promo_code.is_active !== false,
              expiry_date: promo_code.expiry_date || null,
              usage_limit: promo_code.usage_limit || null,
              used_count: 0,
              created_at: sqlDateTime,
              updated_at: sqlDateTime,
            };

            createdCoupon = await sdk.create("coupon", couponData);
          }

          return res.status(201).json({
            success: true,
            error: false,
            message: "Discount configuration created successfully",
            data: {
              programId: programId,
              programDiscountId: createdProgramDiscount.id,
              subscription_discountsCount: createdsubscription_discounts.length,
              full_price_discountsCount: createdfull_price_discounts.length,
              couponId: createdCoupon?.id || null,
              createdAt: sqlDateTime,
            },
          });
        } catch (createError) {
          console.error("Error creating discount configuration:", createError);
          throw createError;
        }
      } catch (err) {
        console.error("Create discount error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to create discount configuration",
        });
      }
    }
  );

  // PUT - Update Program Discount Settings
  app.put(
    "/v2/api/kanglink/custom/trainer/programs/:programId/discounts",
    [TokenMiddleware({ role: "trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const programId = req.params.programId;
        const userId = req.user_id;
        const {
          affiliate_link,
          sale_discount,
          subscription_discounts,
          full_price_discounts,
          promo_code,
        } = req.body;

        // Validate program ID
        if (!programId) {
          return res.status(400).json({
            error: true,
            message: "Program ID is required",
          });
        }

        sdk.setProjectId("kanglink");

        // First, verify the program exists and belongs to the trainer using raw query
        const programQuery = `
          SELECT p.* FROM kanglink_program p WHERE p.id = ? AND p.user_id = ?
        `;

        const programResult = await sdk.rawQuery(programQuery, [
          programId,
          userId,
        ]);

        if (!programResult || programResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Program not found or access denied",
          });
        }

        const now = new Date();
        const sqlDateTime = UtilService.sqlDateTimeFormat(now);

        // Validation helper function
        const validateDiscount = (discount, fieldName) => {
          if (!discount) return null;

          if (
            discount.type &&
            !["fixed", "percentage"].includes(discount.type)
          ) {
            throw new Error(
              `${fieldName} type must be 'fixed' or 'percentage'`
            );
          }

          if (discount.value !== undefined) {
            if (typeof discount.value !== "number" || discount.value < 0) {
              throw new Error(`${fieldName} value must be a positive number`);
            }

            if (discount.type === "percentage" && discount.value > 100) {
              throw new Error(`${fieldName} percentage cannot exceed 100%`);
            }
          }

          return null;
        };

        // Validate sale discount
        if (sale_discount) {
          validateDiscount(sale_discount, "Sale discount");
        }

        // Get program splits for validation
        const programSplits = await sdk.find("split", {
          program_id: programId,
        });
        const validSplitIds = programSplits.map((split) => split.id);

        // Validate subscription and full price discounts
        if (subscription_discounts && Array.isArray(subscription_discounts)) {
          subscription_discounts.forEach((discount, index) => {
            validateDiscount(discount, `Subscription discount ${index + 1}`);
            if (!discount.tier_id) {
              throw new Error(
                `Subscription discount ${index + 1} must have a tier_id`
              );
            }
            if (!validSplitIds.includes(discount.tier_id)) {
              throw new Error(
                `Subscription discount ${
                  index + 1
                } has invalid tier_id - split does not belong to this program`
              );
            }
          });
        }

        if (full_price_discounts && Array.isArray(full_price_discounts)) {
          full_price_discounts.forEach((discount, index) => {
            validateDiscount(discount, `Full price discount ${index + 1}`);
            if (!discount.tier_id) {
              throw new Error(
                `Full price discount ${index + 1} must have a tier_id`
              );
            }
            if (!validSplitIds.includes(discount.tier_id)) {
              throw new Error(
                `Full price discount ${
                  index + 1
                } has invalid tier_id - split does not belong to this program`
              );
            }
          });
        }

        // Validate promo code
        if (promo_code) {
          validateDiscount(promo_code, "Promo code");

          if (!promo_code.code || promo_code.code.length < 3) {
            throw new Error("Promo code must be at least 3 characters long");
          }

          if (promo_code.code.length > 50) {
            throw new Error("Promo code cannot exceed 50 characters");
          }

          // Check for valid characters (alphanumeric and common symbols)
          if (!/^[A-Za-z0-9_-]+$/.test(promo_code.code)) {
            throw new Error(
              "Promo code can only contain letters, numbers, hyphens, and underscores"
            );
          }

          if (
            !["subscription", "full_payment", "both"].includes(
              promo_code.applies_to
            )
          ) {
            throw new Error(
              "Promo code must apply to subscription, full payment, or both"
            );
          }

          if (promo_code.usage_limit && promo_code.usage_limit < 1) {
            throw new Error("Promo code usage limit must be positive");
          }

          if (promo_code.expiry_date) {
            const expiry_date = new Date(promo_code.expiry_date);
            if (isNaN(expiry_date.getTime())) {
              throw new Error("Invalid expiry date format");
            }
            if (expiry_date <= new Date()) {
              throw new Error("Expiry date must be in the future");
            }
          }

          // Check for duplicate coupon codes (excluding current program's coupons)
          const duplicateCoupons = await sdk.find("coupon", {
            code: promo_code.code.toUpperCase(),
          });

          const activeDuplicates = duplicateCoupons.filter(
            (coupon) =>
              coupon.program_id !== parseInt(programId) && coupon.is_active
          );

          if (activeDuplicates.length > 0) {
            throw new Error(
              "This coupon code is already in use by another program"
            );
          }
        }

        // Start transaction-like operations
        try {
          // 1. Handle program discount settings
          const existingProgramDiscount = await sdk.find("program_discount", {
            program_id: programId,
          });

          const programDiscountData = {
            program_id: programId,
            affiliate_link:
              affiliate_link ||
              existingProgramDiscount[0]?.affiliate_link ||
              "",
            sale_discount_type: sale_discount?.type || null,
            sale_discount_value: sale_discount?.value || null,
            sale_apply_to_all: sale_discount?.apply_to_all || false,
            updated_at: sqlDateTime,
          };

          if (existingProgramDiscount.length > 0) {
            // Update existing
            await sdk.updateById(
              "program_discount",
              existingProgramDiscount[0].id,
              programDiscountData
            );
          } else {
            // Create new
            programDiscountData.created_at = sqlDateTime;
            await sdk.create("program_discount", programDiscountData);
          }

          // 2. Handle subscription discounts
          if (subscription_discounts && Array.isArray(subscription_discounts)) {
            // Get existing subscription discounts
            const existingSubDiscounts = await sdk.find("discount", {
              program_id: programId,
              applies_to: "subscription",
            });

            // Create a map of existing discounts by split_id
            const existingDiscountMap = {};
            existingSubDiscounts.forEach((discount) => {
              existingDiscountMap[discount.split_id] = discount;
            });

            // Process each subscription discount
            for (const discount of subscription_discounts) {
              const discountData = {
                program_id: programId,
                split_id: discount.tier_id,
                discount_type: discount.discount_type,
                discount_value: discount.discount_value,
                applies_to: "subscription",
                is_active: true,
                updated_at: sqlDateTime,
              };

              const existingDiscount = existingDiscountMap[discount.tier_id];

              if (existingDiscount) {
                // Update existing discount
                await sdk.updateById(
                  "discount",
                  existingDiscount.id,
                  discountData
                );
              } else {
                // Create new discount
                discountData.created_at = sqlDateTime;
                await sdk.create("discount", discountData);
              }
            }

            // Remove discounts for splits that are no longer in the request
            const requestedSplitIds = subscription_discounts.map(
              (d) => d.tier_id
            );
            for (const existingDiscount of existingSubDiscounts) {
              if (!requestedSplitIds.includes(existingDiscount.split_id)) {
                await sdk.deleteById("discount", existingDiscount.id);
              }
            }
          }

          // 3. Handle full price discounts
          if (full_price_discounts && Array.isArray(full_price_discounts)) {
            // Get existing full price discounts
            const existingFullDiscounts = await sdk.find("discount", {
              program_id: programId,
              applies_to: "full_payment",
            });

            // Create a map of existing discounts by split_id
            const existingDiscountMap = {};
            existingFullDiscounts.forEach((discount) => {
              existingDiscountMap[discount.split_id] = discount;
            });

            // Process each full price discount
            for (const discount of full_price_discounts) {
              const discountData = {
                program_id: programId,
                split_id: discount.tier_id,
                discount_type: discount.discount_type,
                discount_value: discount.discount_value,
                applies_to: "full_payment",
                is_active: true,
                updated_at: sqlDateTime,
              };

              const existingDiscount = existingDiscountMap[discount.tier_id];

              if (existingDiscount) {
                // Update existing discount
                await sdk.updateById(
                  "discount",
                  existingDiscount.id,
                  discountData
                );
              } else {
                // Create new discount
                discountData.created_at = sqlDateTime;
                await sdk.create("discount", discountData);
              }
            }

            // Remove discounts for splits that are no longer in the request
            const requestedSplitIds = full_price_discounts.map(
              (d) => d.tier_id
            );
            for (const existingDiscount of existingFullDiscounts) {
              if (!requestedSplitIds.includes(existingDiscount.split_id)) {
                await sdk.deleteById("discount", existingDiscount.id);
              }
            }
          }

          // 4. Handle promo code
          if (promo_code) {
            // Get existing coupons for this program
            const existingCoupons = await sdk.find("coupon", {
              program_id: programId,
            });

            const couponData = {
              program_id: programId,
              code: promo_code.code.toUpperCase(),
              discount_type: promo_code.discount_type,
              discount_value: promo_code.discount_value,
              applies_to: promo_code.applies_to,
              is_active: promo_code.is_active !== false,
              expiry_date: promo_code.expiry_date || null,
              usage_limit: promo_code.usage_limit || null,
              updated_at: sqlDateTime,
            };

            if (existingCoupons.length > 0) {
              // Update existing coupon (assuming one coupon per program)
              const existingCoupon = existingCoupons[0];
              await sdk.updateById("coupon", existingCoupon.id, couponData);
            } else {
              // Create new coupon
              couponData.created_at = sqlDateTime;
              couponData.used_count = 0;
              await sdk.create("coupon", couponData);
            }
          }

          return res.status(200).json({
            success: true,
            error: false,
            message: "Discount settings updated successfully",
            data: {
              programId: programId,
              lastUpdated: sqlDateTime,
            },
          });
        } catch (updateError) {
          console.error("Error updating discount settings:", updateError);
          throw updateError;
        }
      } catch (err) {
        console.error("Update discount error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to update discount settings",
        });
      }
    }
  );

  // DELETE - Remove Program Discount Configuration
  app.delete(
    "/v2/api/kanglink/custom/trainer/programs/:programId/discounts",
    [TokenMiddleware({ role: "trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const programId = req.params.programId;
        const userId = req.user_id;

        // Validate program ID
        if (!programId) {
          return res.status(400).json({
            error: true,
            message: "Program ID is required",
          });
        }

        sdk.setProjectId("kanglink");

        // First, verify the program exists and belongs to the trainer using raw query
        const programQuery = `
          SELECT p.* FROM kanglink_program p WHERE p.id = ? AND p.user_id = ?
        `;

        const programResult = await sdk.rawQuery(programQuery, [
          programId,
          userId,
        ]);

        if (!programResult || programResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Program not found or access denied",
          });
        }

        // Check if discount configuration exists
        const existingProgramDiscount = await sdk.find("program_discount", {
          program_id: programId,
        });

        if (existingProgramDiscount.length === 0) {
          return res.status(404).json({
            error: true,
            message: "No discount configuration found for this program",
          });
        }

        const now = new Date();
        const sqlDateTime = UtilService.sqlDateTimeFormat(now);

        try {
          // Track what we're deleting for the response
          const deletionSummary = {
            programDiscountDeleted: false,
            discountsDeleted: 0,
            couponsDeleted: 0,
            couponUsagesDeleted: 0,
          };

          // 1. Delete coupon usages first (foreign key dependency)
          const couponUsages = await sdk.find("coupon_usage", {
            program_id: programId,
          });

          for (const usage of couponUsages) {
            await sdk.deleteById("coupon_usage", usage.id);
            deletionSummary.couponUsagesDeleted++;
          }

          // 2. Delete coupons
          const coupons = await sdk.find("coupon", {
            program_id: programId,
          });

          for (const coupon of coupons) {
            await sdk.deleteById("coupon", coupon.id);
            deletionSummary.couponsDeleted++;
          }

          // 3. Delete split-specific discounts
          const discounts = await sdk.find("discount", {
            program_id: programId,
          });

          for (const discount of discounts) {
            await sdk.deleteById("discount", discount.id);
            deletionSummary.discountsDeleted++;
          }

          // 4. Delete program discount configuration
          for (const programDiscount of existingProgramDiscount) {
            await sdk.deleteById("program_discount", programDiscount.id);
            deletionSummary.programDiscountDeleted = true;
          }

          return res.status(200).json({
            success: true,
            error: false,
            message: "Discount configuration deleted successfully",
            data: {
              programId: programId,
              deletionSummary: deletionSummary,
              deletedAt: sqlDateTime,
            },
          });
        } catch (deleteError) {
          console.error("Error deleting discount configuration:", deleteError);
          throw deleteError;
        }
      } catch (err) {
        console.error("Delete discount error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to delete discount configuration",
        });
      }
    }
  );

  // GET - Discount Preview
  app.get(
    "/v2/api/kanglink/custom/discount/preview",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { split_id, payment_type, coupon_code } = req.query;

        // Validate required parameters
        if (!split_id) {
          return res.status(400).json({
            error: true,
            message: "split_id is required",
          });
        }

        if (!payment_type) {
          return res.status(400).json({
            error: true,
            message: "payment_type is required",
          });
        }

        if (!["subscription", "one_time"].includes(payment_type)) {
          return res.status(400).json({
            error: true,
            message: "payment_type must be 'subscription' or 'one_time'",
          });
        }

        sdk.setProjectId("kanglink");

        // Get split information with pricing
        const splitQuery = `
          SELECT s.*, p.id as program_id, p.program_name
          FROM kanglink_split s
          JOIN kanglink_program p ON s.program_id = p.id
          WHERE s.id = ?
        `;

        const splitResult = await sdk.rawQuery(splitQuery, [split_id]);

        if (!splitResult || splitResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Split not found",
          });
        }

        const split = splitResult[0];
        const program_id = split.program_id;

        // Determine original amount based on payment type
        let original_amount;
        if (payment_type === "subscription") {
          original_amount = parseFloat(split.subscription || 0);
        } else {
          original_amount = parseFloat(split.full_price || 0);
        }

        if (original_amount <= 0) {
          return res.status(400).json({
            error: true,
            message: `No valid ${
              payment_type === "subscription" ? "subscription" : "full"
            } price found for this split`,
          });
        }

        // Initialize discount service
        const discountService = new DiscountService(sdk);

        // If coupon code is provided, validate it first
        if (coupon_code) {
          const couponValidation = await discountService.validateAndApplyCoupon(
            coupon_code,
            program_id,
            parseInt(split_id),
            payment_type,
            original_amount,
            null // For preview, we don't validate user-specific usage
          );

          if (!couponValidation.valid) {
            return res.status(400).json({
              success: false,
              error: true,
              message: couponValidation.message || "Invalid coupon code",
            });
          }
        }

        // Calculate discounted amount
        const discountResult = await discountService.calculateDiscountedAmount({
          program_id: program_id,
          split_id: parseInt(split_id),
          payment_type: payment_type,
          original_amount: original_amount,
          coupon_code: coupon_code || null,
          user_id: null, // For preview, we don't validate user-specific usage
        });

        // Build response
        const responseData = {
          split_id: parseInt(split_id),
          program_id: program_id,
          program_name: split.program_name,
          split_title: split.title,
          payment_type: payment_type,
          original_amount: original_amount,
          final_amount: discountResult.final_amount,
          total_discount_amount: discountResult.total_discount_amount,
          savings_percentage:
            original_amount > 0
              ? Math.round(
                  (discountResult.total_discount_amount / original_amount) * 100
                )
              : 0,
          has_discounts: discountResult.has_discounts,
          applied_discounts: discountResult.applied_discounts,
        };

        return res.status(200).json({
          error: false,
          data: responseData,
        });
      } catch (err) {
        console.error("Discount preview error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to calculate discount preview",
        });
      }
    }
  );

  // Validate referral code
  app.get(
    "/v2/api/kanglink/custom/validate-referral",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { program_id, ref } = req.query;

        // Validate required parameters
        if (!program_id || !ref) {
          return res.status(400).json({
            error: true,
            message: "Program ID and referral code are required",
          });
        }

        sdk.setProjectId("kanglink");

        // Check if program exists
        sdk.setTable("program");
        const program = await sdk.findOne("program", {
          id: program_id,
        });

        if (!program) {
          return res.status(404).json({
            error: true,
            message: "Program not found",
            data: {
              is_valid: false,
              reason: "Program not found"
            }
          });
        }

        // Check if program has discount settings
        sdk.setTable("program_discount");
        const programDiscount = await sdk.findOne("program_discount", {
          program_id: program_id,
        });

        if (!programDiscount) {
          return res.status(200).json({
            error: false,
            message: "No discount settings found for this program",
            data: {
              is_valid: false,
              reason: "No discount settings available for this program"
            }
          });
        }

        // Check if affiliate_link matches the ref
        if (programDiscount.affiliate_link === ref) {
          return res.status(200).json({
            error: false,
            message: "Referral code is valid",
            data: {
              is_valid: true,
              program_discount: {
                id: programDiscount.id,
                sale_discount_type: programDiscount.sale_discount_type,
                sale_discount_value: programDiscount.sale_discount_value,
                sale_apply_to_all: programDiscount.sale_apply_to_all
              }
            }
          });
        } else {
          return res.status(200).json({
            error: false,
            message: "Invalid referral code",
            data: {
              is_valid: false,
              reason: "Referral code does not match"
            }
          });
        }

      } catch (error) {
        console.error("Error validating referral code:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to validate referral code",
        });
      }
    }
  );
};
