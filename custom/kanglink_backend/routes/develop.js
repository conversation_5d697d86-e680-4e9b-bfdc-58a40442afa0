// we'd do testing and developments here
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");

module.exports = function (app) {
  // Check database tables for enrollment system
  app.get("/v2/api/kanglink/develop/check-tables", async function (req, res) {
    try {
      const sdk = app.get("sdk");
      sdk.setProjectId("kanglink");

      // Check what tables exist in the database
      const tables = await sdk.rawQuery("SHOW TABLES LIKE 'kanglink_%'");

      // Required tables for enrollment system
      const requiredTables = [
        "kanglink_user",
        "kanglink_program",
        "kanglink_split",
        "kanglink_enrollment",
        "kanglink_stripe_price",
        "kanglink_stripe_product",
        "kanglink_preference",
        "kanglink_tokens",
        "kanglink_uploads",
        "kanglink_job",
        "kanglink_cms",
      ];

      // Optional tables for enhanced features
      const optionalTables = [
        "kanglink_week",
        "kanglink_day",
        "kanglink_session",
        "kanglink_exercise",
        "kanglink_exercise_instance",

        "kanglink_post_feed",
        "kanglink_comment",
        "kanglink_reaction",
        "kanglink_discount",
        "kanglink_coupon",
        "kanglink_coupon_usage",
        "kanglink_program_discount",
        "kanglink_stripe_webhook",
        "kanglink_stripe_subscription",
        "kanglink_stripe_invoice",
      ];

      const existingTables = tables.map((t) => Object.values(t)[0]);
      const missingRequired = requiredTables.filter(
        (table) => !existingTables.includes(table)
      );
      const missingOptional = optionalTables.filter(
        (table) => !existingTables.includes(table)
      );
      const extraTables = existingTables.filter(
        (table) =>
          !requiredTables.includes(table) && !optionalTables.includes(table)
      );

      return res.status(200).json({
        error: false,
        data: {
          existing_tables: existingTables,
          required_tables: {
            total: requiredTables.length,
            existing: requiredTables.filter((table) =>
              existingTables.includes(table)
            ),
            missing: missingRequired,
          },
          optional_tables: {
            total: optionalTables.length,
            existing: optionalTables.filter((table) =>
              existingTables.includes(table)
            ),
            missing: missingOptional,
          },
          extra_tables: extraTables,
          enrollment_ready: missingRequired.length === 0,
        },
      });
    } catch (err) {
      console.error("Check tables error:", err);
      return res.status(500).json({
        error: true,
        message: err.message || "Failed to check tables",
      });
    }
  });

  // Check table structure for specific table
  app.get(
    "/v2/api/kanglink/develop/table-structure/:table",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const tableName = req.params.table;

        sdk.setProjectId("kanglink");

        // Get table structure
        const structure = await sdk.rawQuery(`DESCRIBE ${tableName}`);

        return res.status(200).json({
          error: false,
          data: {
            table: tableName,
            structure: structure,
          },
        });
      } catch (err) {
        console.error("Check table structure error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to check table structure",
        });
      }
    }
  );

  // Create missing tables for enrollment system
  app.post(
    "/v2/api/kanglink/develop/create-missing-tables",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const createdTables = [];
        const errors = [];

        // Define table creation SQL
        const tableDefinitions = {
          kanglink_stripe_product: `
          CREATE TABLE IF NOT EXISTS kanglink_stripe_product (
            id INT AUTO_INCREMENT PRIMARY KEY,
            stripe_id VARCHAR(255) NOT NULL UNIQUE,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            object TEXT,
            status INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
          )
        `,
          kanglink_stripe_price: `
          CREATE TABLE IF NOT EXISTS kanglink_stripe_price (
            id INT AUTO_INCREMENT PRIMARY KEY,
            stripe_id VARCHAR(255) NOT NULL UNIQUE,
            product_id INT,
            name VARCHAR(255) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'USD',
            type ENUM('one_time', 'recurring', 'lifetime') NOT NULL,
            interval_type VARCHAR(20),
            interval_count INT DEFAULT 1,
            is_usage_metered BOOLEAN DEFAULT FALSE,
            usage_limit INT,
            object TEXT,
            status INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES kanglink_stripe_product(id)
          )
        `,
        };

        // Create tables that don't exist
        for (const [tableName, sql] of Object.entries(tableDefinitions)) {
          try {
            await sdk.rawQuery(sql);
            createdTables.push(tableName);
          } catch (err) {
            errors.push({
              table: tableName,
              error: err.message,
            });
          }
        }

        return res.status(200).json({
          error: false,
          data: {
            created_tables: createdTables,
            errors: errors,
            message: `Created ${createdTables.length} tables${
              errors.length > 0 ? ` with ${errors.length} errors` : ""
            }`,
          },
        });
      } catch (err) {
        console.error("Create tables error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to create tables",
        });
      }
    }
  );

  // Create stripe_webhook table specifically
  app.post(
    "/v2/api/kanglink/develop/create-stripe-webhook-table",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        const createTableSQL = `
        CREATE TABLE IF NOT EXISTS kanglink_stripe_webhook (
          id INT AUTO_INCREMENT PRIMARY KEY,
          stripe_event_id VARCHAR(255) NOT NULL,
          event_type VARCHAR(100) NOT NULL,
          object_id VARCHAR(255) NOT NULL,
          object_type ENUM('customer', 'subscription', 'invoice', 'payment_intent', 'payment_method', 'price', 'product') NOT NULL,
          livemode BOOLEAN DEFAULT FALSE,
          api_version VARCHAR(50),
          status ENUM('pending', 'processed', 'failed', 'ignored') DEFAULT 'pending',
          processed_at DATETIME NULL,
          error_message TEXT NULL,
          retry_count INT DEFAULT 0,
          webhook_data JSON NULL,
          response_data JSON NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

          INDEX idx_stripe_event_id (stripe_event_id),
          INDEX idx_event_type (event_type),
          INDEX idx_status (status),
          INDEX idx_object_id (object_id),
          INDEX idx_created_at (created_at),

          UNIQUE KEY unique_webhook (stripe_event_id, event_type)
        )
      `;

        await sdk.rawQuery(createTableSQL);

        res.status(200).json({
          error: false,
          message: "stripe_webhook table created successfully",
          table: "kanglink_stripe_webhook",
        });
      } catch (error) {
        console.error("Error creating stripe_webhook table:", error);
        res.status(500).json({
          error: true,
          message: "Failed to create stripe_webhook table",
          details: error.message,
        });
      }
    }
  );

  // Check models vs tables
  app.get("/v2/api/kanglink/develop/check-models", async function (req, res) {
    try {
      const fs = require("fs");
      const path = require("path");

      const modelsDir = path.join(__dirname, "../models");
      const modelFiles = fs
        .readdirSync(modelsDir)
        .filter((file) => file.endsWith(".js"));
      const existingModels = modelFiles.map((file) => file.replace(".js", ""));

      // Required models for enrollment system
      const requiredModels = [
        "user",
        "program",
        "split",
        "enrollment",
        "stripe_price",
        "stripe_product",
        "preference",
        "tokens",
        "uploads",
        "job",
        "cms",
      ];

      // Optional models
      const optionalModels = [
        "week",
        "day",
        "session",
        "exercise",
        "exercise_instance",

        "post_feed",
        "comment",
        "reaction",
        "discount",
        "coupon",
        "coupon_usage",
        "program_discount",
      ];

      const missingRequired = requiredModels.filter(
        (model) => !existingModels.includes(model)
      );
      const missingOptional = optionalModels.filter(
        (model) => !existingModels.includes(model)
      );
      const extraModels = existingModels.filter(
        (model) =>
          !requiredModels.includes(model) && !optionalModels.includes(model)
      );

      return res.status(200).json({
        error: false,
        data: {
          existing_models: existingModels,
          required_models: {
            total: requiredModels.length,
            existing: requiredModels.filter((model) =>
              existingModels.includes(model)
            ),
            missing: missingRequired,
          },
          optional_models: {
            total: optionalModels.length,
            existing: optionalModels.filter((model) =>
              existingModels.includes(model)
            ),
            missing: missingOptional,
          },
          extra_models: extraModels,
          enrollment_ready: missingRequired.length === 0,
        },
      });
    } catch (err) {
      console.error("Check models error:", err);
      return res.status(500).json({
        error: true,
        message: err.message || "Failed to check models",
      });
    }
  });

  // Create missing models
  app.post(
    "/v2/api/kanglink/develop/create-missing-models",
    async function (req, res) {
      try {
        const fs = require("fs");
        const path = require("path");

        const modelsDir = path.join(__dirname, "../models");
        const createdModels = [];
        const errors = [];

        // Model templates
        const modelTemplates = {
          stripe_product: `const BaseModel = require("../../../baas/core/BaseModel");

class stripe_product extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_id",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "name",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "description",
        type: "long text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "object",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "status",
        type: "mapping",
        validation: "required",
        defaultValue: "1",
        mapping: "0:Inactive,1:Active",
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformStatus(value) {
    const mappings = {
      0: "Inactive",
      1: "Active",
    };
    return mappings[value] || value;
  }
}

module.exports = stripe_product;
`,
          stripe_price: `const BaseModel = require("../../../baas/core/BaseModel");

class stripe_price extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_id",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "product_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "name",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "amount",
        type: "float",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "currency",
        type: "string",
        validation: [],
        defaultValue: "USD",
        mapping: null,
      },
      {
        name: "type",
        type: "mapping",
        validation: "required,enum:one_time,recurring,lifetime",
        defaultValue: null,
        mapping: "one_time:One Time,recurring:Recurring,lifetime:Lifetime",
      },
      {
        name: "interval_type",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "interval_count",
        type: "integer",
        validation: [],
        defaultValue: "1",
        mapping: null,
      },
      {
        name: "is_usage_metered",
        type: "boolean",
        validation: [],
        defaultValue: "0",
        mapping: null,
      },
      {
        name: "usage_limit",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "object",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "status",
        type: "mapping",
        validation: "required",
        defaultValue: "1",
        mapping: "0:Inactive,1:Active",
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformType(value) {
    const mappings = {
      one_time: "One Time",
      recurring: "Recurring",
      lifetime: "Lifetime",
    };
    return mappings[value] || value;
  }

  transformStatus(value) {
    const mappings = {
      0: "Inactive",
      1: "Active",
    };
    return mappings[value] || value;
  }
}

module.exports = stripe_price;
`,
        };

        // Create missing models
        for (const [modelName, template] of Object.entries(modelTemplates)) {
          const modelPath = path.join(modelsDir, `${modelName}.js`);

          if (!fs.existsSync(modelPath)) {
            try {
              fs.writeFileSync(modelPath, template);
              createdModels.push(modelName);
            } catch (err) {
              errors.push({
                model: modelName,
                error: err.message,
              });
            }
          }
        }

        return res.status(200).json({
          error: false,
          data: {
            created_models: createdModels,
            errors: errors,
            message: `Created ${createdModels.length} models${
              errors.length > 0 ? ` with ${errors.length} errors` : ""
            }`,
          },
        });
      } catch (err) {
        console.error("Create models error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to create models",
        });
      }
    }
  );

  // Create progress tracking tables
  app.post(
    "/v2/api/kanglink/develop/create-progress-tables",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");

        // Create athlete_progress table for tracking overall enrollment progress
        await sdk.rawQuery(`
          CREATE TABLE IF NOT EXISTS kanglink_athlete_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            athlete_id INT NOT NULL,
            enrollment_id INT NOT NULL,
            split_id INT NOT NULL,
            program_id INT NOT NULL,
            trainer_id INT NOT NULL,
            current_week_id INT,
            current_day_id INT,
            total_days_completed INT DEFAULT 0,
            total_exercises_completed INT DEFAULT 0,
            progress_percentage DECIMAL(5,2) DEFAULT 0.00,
            last_activity_date DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (athlete_id) REFERENCES kanglink_user(id) ON DELETE CASCADE,
            FOREIGN KEY (enrollment_id) REFERENCES kanglink_enrollment(id) ON DELETE CASCADE,
            FOREIGN KEY (split_id) REFERENCES kanglink_split(id) ON DELETE CASCADE,
            FOREIGN KEY (program_id) REFERENCES kanglink_program(id) ON DELETE CASCADE,
            FOREIGN KEY (trainer_id) REFERENCES kanglink_user(id) ON DELETE CASCADE,
            UNIQUE KEY unique_athlete_enrollment (athlete_id, enrollment_id)
          )
        `);

        // Create day_progress table for tracking day completion
        await sdk.rawQuery(`
          CREATE TABLE IF NOT EXISTS kanglink_day_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            athlete_id INT NOT NULL,
            enrollment_id INT NOT NULL,
            day_id INT NOT NULL,
            week_id INT NOT NULL,
            split_id INT NOT NULL,
            is_completed BOOLEAN DEFAULT FALSE,
            completed_at DATETIME NULL,
            total_exercises INT DEFAULT 0,
            completed_exercises INT DEFAULT 0,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (athlete_id) REFERENCES kanglink_user(id) ON DELETE CASCADE,
            FOREIGN KEY (enrollment_id) REFERENCES kanglink_enrollment(id) ON DELETE CASCADE,
            FOREIGN KEY (day_id) REFERENCES kanglink_day(id) ON DELETE CASCADE,
            FOREIGN KEY (week_id) REFERENCES kanglink_week(id) ON DELETE CASCADE,
            FOREIGN KEY (split_id) REFERENCES kanglink_split(id) ON DELETE CASCADE,
            UNIQUE KEY unique_athlete_day (athlete_id, enrollment_id, day_id)
          )
        `);

        // Create exercise_progress table for tracking individual exercise completion
        await sdk.rawQuery(`
          CREATE TABLE IF NOT EXISTS kanglink_exercise_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            athlete_id INT NOT NULL,
            enrollment_id INT NOT NULL,
            exercise_instance_id INT NOT NULL,
            session_id INT NOT NULL,
            day_id INT NOT NULL,
            is_completed BOOLEAN DEFAULT FALSE,
            completed_at DATETIME NULL,
            sets_completed INT DEFAULT 0,
            reps_completed TEXT,
            weight_used TEXT,
            time_taken_seconds INT,
            difficulty_rating INT CHECK (difficulty_rating BETWEEN 1 AND 5),
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (athlete_id) REFERENCES kanglink_user(id) ON DELETE CASCADE,
            FOREIGN KEY (enrollment_id) REFERENCES kanglink_enrollment(id) ON DELETE CASCADE,
            FOREIGN KEY (exercise_instance_id) REFERENCES kanglink_exercise_instance(id) ON DELETE CASCADE,
            FOREIGN KEY (session_id) REFERENCES kanglink_session(id) ON DELETE CASCADE,
            FOREIGN KEY (day_id) REFERENCES kanglink_day(id) ON DELETE CASCADE,
            UNIQUE KEY unique_athlete_exercise (athlete_id, enrollment_id, exercise_instance_id)
          )
        `);

        // Create trainer_notifications table for progress notifications
        await sdk.rawQuery(`
          CREATE TABLE IF NOT EXISTS kanglink_trainer_notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            trainer_id INT NOT NULL,
            athlete_id INT NOT NULL,
            enrollment_id INT NOT NULL,
            notification_type ENUM('exercise_completed', 'day_completed', 'week_completed', 'program_completed', 'milestone_reached') NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            data JSON,
            is_read BOOLEAN DEFAULT FALSE,
            read_at DATETIME NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (trainer_id) REFERENCES kanglink_user(id) ON DELETE CASCADE,
            FOREIGN KEY (athlete_id) REFERENCES kanglink_user(id) ON DELETE CASCADE,
            FOREIGN KEY (enrollment_id) REFERENCES kanglink_enrollment(id) ON DELETE CASCADE,
            INDEX idx_trainer_unread (trainer_id, is_read),
            INDEX idx_created_at (created_at)
          )
        `);

        return res.status(200).json({
          error: false,
          message: "Progress tracking tables created successfully",
        });
      } catch (error) {
        console.error("Create progress tables error:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to create progress tables",
          details: error.message,
        });
      }
    }
  );

  // Test endpoint to check Stripe price fields
  app.get(
    "/v2/api/kanglink/custom/develop/test-stripe-price-fields",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        sdk.setTable("stripe_price");

        // Get recent Stripe prices to check their structure
        const stripePrices = await sdk.rawQuery(`
          SELECT sp.*, pr.name as product_name
          FROM kanglink_stripe_price sp
          LEFT JOIN kanglink_stripe_product pr ON sp.product_id = pr.id
          WHERE sp.status = 1
          ORDER BY sp.created_at DESC
          LIMIT 5
        `);

        return res.status(200).json({
          error: false,
          message: "Stripe price fields test",
          data: {
            stripe_prices: stripePrices,
            field_analysis: {
              total_prices: stripePrices.length,
              has_product_id: stripePrices.filter((p) => p.product_id !== null)
                .length,
              has_amount: stripePrices.filter((p) => p.amount !== null).length,
              has_type: stripePrices.filter((p) => p.type !== null).length,
              types_found: [
                ...new Set(stripePrices.map((p) => p.type).filter(Boolean)),
              ],
            },
          },
        });
      } catch (err) {
        console.error("Test Stripe price fields error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to test Stripe price fields",
        });
      }
    }
  );

  // Cleanup old deactivated Stripe price records
  app.post(
    "/v2/api/kanglink/custom/develop/cleanup-old-stripe-prices",
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        sdk.setTable("stripe_price");

        // Find old deactivated prices (status = 0)
        const oldPrices = await sdk.rawQuery(`
          SELECT sp.*, pr.name as product_name
          FROM kanglink_stripe_price sp
          LEFT JOIN kanglink_stripe_product pr ON sp.product_id = pr.id
          WHERE sp.status = 0
          ORDER BY sp.created_at DESC
        `);

        if (oldPrices.length === 0) {
          return res.status(200).json({
            error: false,
            message: "No old deactivated prices found",
            data: { deleted_count: 0 },
          });
        }

        // Optional: Delete old prices (uncomment if you want to actually delete)
        // const deleteResult = await sdk.rawQuery(`
        //   DELETE FROM kanglink_stripe_price WHERE status = 0
        // `);

        return res.status(200).json({
          error: false,
          message: `Found ${oldPrices.length} old deactivated prices`,
          data: {
            old_prices: oldPrices,
            note: "To actually delete these records, uncomment the delete query in the code",
          },
        });
      } catch (err) {
        console.error("Cleanup old Stripe prices error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to cleanup old Stripe prices",
        });
      }
    }
  );
};
