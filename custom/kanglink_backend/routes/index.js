const fs = require("fs");
const path = require("path");

module.exports = function (app) {
  try {
    console.log("routes index.js file read");
    fs.readdirSync(__dirname).forEach(function (file) {
      if (file === "index.js" || file === ".DS_Store") return;

      const name = file.substr(0, file.indexOf("."));

      require("./" + name)(app);
    });
  } catch (error) {
    console.log("error->>", error?.message);
  }
};
