const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const UtilService = require("../../../baas/services/UtilService");
const NotificationService = require("../services/NotificationService");
const MailService = require("../../../baas/services/MailService");


module.exports = function (app) {
  // Admin approves a program and affiliate link is created
  app.put(
    "/v2/api/kanglink/custom/super_admin/programs/:program_id/approve",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const programId = req.params.program_id;
        const config = app.get("configuration");

        // Validate program ID
        if (!programId || isNaN(programId)) {
          return res.status(400).json({
            error: true,
            message: "Invalid program ID provided",
          });
        }

        sdk.setProjectId("kanglink");

        // Check if program exists and is pending approval
        sdk.setTable("program");
        const program = await sdk.findOne("program", { id: programId });

        if (!program) {
          return res.status(404).json({
            error: true,
            message: "Program not found",
          });
        }

        if (program.status !== "pending_approval") {
          return res.status(400).json({
            error: true,
            message: `Program is currently ${program.status}. Only programs with 'pending_approval' status can be approved.`,
          });
        }

        // Check if payout settings are configured
        sdk.setTable("payout_settings");
        const payoutSettings = await sdk.findOne("payout_settings", {
          is_active: true,
        });

        if (!payoutSettings) {
          return res.status(400).json({
            error: true,
            message:
              "Payout settings must be configured before approving programs. Please configure payout settings first.",
          });
        }

        // Generate affiliate link
        const affiliateCode = generateAffiliateCode(programId, program.user_id);
        // get the origin from the request
        // const origin = req.headers.origin;
        // const affiliateLink = `${origin}/athlete/program/${programId}?ref=${affiliateCode}`;

        // Update program status to 'published' and add affiliate data
        const updateData = {
          status: "published",
          updated_at: UtilService.sqlDateTimeFormat(new Date()),
          approval_date: UtilService.sqlDateTimeFormat(new Date()),
        };

        await sdk.updateById("program", programId, updateData);

        // Store affiliate data in program_dicount
        sdk.setTable("program_dicount");

        // Check if program discount already exists
        const existingProgramDiscount = await sdk.findOne("program_discount", {
          program_id: programId,
        });

        if (!existingProgramDiscount) {
          const programDiscountData = {
            program_id: programId,
            affiliate_link: affiliateCode || "",
            created_at: UtilService.sqlDateTimeFormat(new Date()),
            updated_at: UtilService.sqlDateTimeFormat(new Date()),
          };
          await sdk.create("program_discount", programDiscountData);
        } else {
          await sdk.updateById("program_discount", existingProgramDiscount.id, {
            affiliate_link: affiliateCode || "",
            updated_at: UtilService.sqlDateTimeFormat(new Date()),
          });
        }

        // Send notification and email to trainer
        try {
          const mailService = new MailService(config);
          
          // Get trainer information
          sdk.setTable("user");
          const trainer = await sdk.findOne("user", { id: program.user_id });
          
          if (trainer) {
            let trainerData = {};
            if (trainer.data) {
              try {
                trainerData = JSON.parse(trainer.data);
              } catch (e) {
                trainerData = {};
              }
            }

            const trainerName = trainerData.full_name || 
              `${trainerData.first_name || ""} ${trainerData.last_name || ""}`.trim() || 
              "Unknown Trainer";

            // Create notification data
            const notificationData = {
              user_id: program.user_id,
              sender_id: req.user_id, // Admin who approved the program
              related_id: programId,
              related_type: "program",
              notification_type: "program_approved",
              category: "program",
              title: "Program Approved",
              message: `Your program "${program.program_name}" has been approved and is now live!`,
              data: JSON.stringify({
                program_id: programId,
                program_name: program.program_name,
                status: "published",
                affiliate_code: affiliateCode,
                action_by: req.user_id,
              }),
              is_read: false,
              created_at: UtilService.sqlDateTimeFormat(new Date()),
              updated_at: UtilService.sqlDateTimeFormat(new Date()),
            };

            // Create notification
            sdk.setTable("notification");
            await sdk.create("notification", notificationData);

            // Send email notification if trainer has email
            if (trainer.email) {
              const emailSubject = `Program Approved - ${program.program_name}`;
              const emailBody = `
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                  <h2>🎉 Program Approved!</h2>
                  <p>Dear ${trainerName},</p>
                  <p>Great news! Your program "<strong>${program.program_name}</strong>" has been approved and is now live on our platform.</p>
                  <p><strong>Program Details:</strong></p>
                  <ul>
                    <li><strong>Program Name:</strong> ${program.program_name}</li>
                    <li><strong>Status:</strong> Published</li>
                    <li><strong>Approval Date:</strong> ${new Date().toLocaleDateString()}</li>
                  </ul>
                  <p>Your program is now available for athletes to discover and enroll in. You can start promoting your program and earning from enrollments.</p>
                  <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
                  <p>Best regards,<br>The TrainerIQ Team</p>
                </div>
              `;

              await mailService.send(
                mailService.from,
                trainer.email,
                emailSubject,
                emailBody
              );
            }
          }
        } catch (notificationError) {
          console.error("Error sending program approval notification:", notificationError);
          // Don't fail the approval if notification fails
        }

        return res.status(200).json({
          error: false,
          message: "Program approved successfully and affiliate link created",
        });
      } catch (err) {
        console.error("Program approval error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to approve program",
        });
      }
    }
  );

  // Admin updates the status of a program
  app.put(
    "/v2/api/kanglink/custom/super_admin/programs/:program_id/status",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      const config = app.get("configuration");
      try {
        const sdk = app.get("sdk");
        const programId = req.params.program_id;
        const { status, rejection_reason } = req.body;

        // Validate program ID
        if (!programId || isNaN(programId)) {
          return res.status(400).json({
            error: true,
            message: "Invalid program ID provided",
          });
        }

        // Validate status
        const validStatuses = [
          "draft",
          "pending_approval",
          "live",
          "published",
          "rejected",
          "archived",
          "deleted",
        ];

        if (!status || !validStatuses.includes(status)) {
          return res.status(400).json({
            error: true,
            message: `Invalid status. Must be one of: ${validStatuses.join(
              ", "
            )}`,
          });
        }

        // If rejecting, require rejection reason
        if (["rejected", "archived", "deleted"].includes(status) && !rejection_reason) {
          return res.status(400).json({
            error: true,
            message: "Rejection reason is required when rejecting a program",
          });
        }

        sdk.setProjectId("kanglink");

        // Check if program exists
        sdk.setTable("program");
        const program = await sdk.findOne("program", { id: programId });

        if (!program) {
          return res.status(404).json({
            error: true,
            message: "Program not found",
          });
        }

        // Prepare update data
        const updateData = {
          status: status,
          updated_at: UtilService.sqlDateTimeFormat(new Date()),
        };

        // Handle rejection reason
        if (["rejected", "archived", "deleted"].includes(status) && rejection_reason) {
          updateData.reason = rejection_reason;
        }

        // Update program status
        await sdk.updateById("program", programId, updateData);

        // Send notification to trainer if program is rejected, archived, or deleted
        if (["rejected", "archived", "deleted", "published", "live"].includes(status)) {
          try {
            const mailService = new MailService(config);
            // Get trainer information
            sdk.setTable("user");
            const trainer = await sdk.findOne("user", { id: program.user_id });
            
            if (trainer) {
              let trainerData = {};
              if (trainer.data) {
                try {
                  trainerData = JSON.parse(trainer.data);
                } catch (e) {
                  trainerData = {};
                }
              }

              const trainerName = trainerData.full_name || 
                `${trainerData.first_name || ""} ${trainerData.last_name || ""}`.trim() || 
                "Unknown Trainer";

              // Create notification data
              const notificationData = {
                user_id: program.user_id,
                sender_id: req.user_id, // Admin who performed the action
                related_id: programId,
                related_type: "program",
                notification_type: status === "rejected" ? "program_rejected" : `program_${status}`,
                category: "program",
                title: status === "rejected" ? "Program Rejected" : `Program ${status.charAt(0).toUpperCase() + status.slice(1)}`,
                message: `Your program "${program.program_name}" has been ${status}${rejection_reason ? `: ${rejection_reason}` : ""}`,
                data: JSON.stringify({
                  program_id: programId,
                  program_name: program.program_name,
                  status: status,
                  reason: rejection_reason,
                  action_by: req.user_id,
                }),
                is_read: false,
                created_at: UtilService.sqlDateTimeFormat(new Date()),
                updated_at: UtilService.sqlDateTimeFormat(new Date()),
              };

              // Create notification
              sdk.setTable("notification");
              await sdk.create("notification", notificationData);

              // Send email notification if trainer has email
              if (trainer.email) {
                const emailSubject = `Program ${status.charAt(0).toUpperCase() + status.slice(1)} - ${program.program_name}`;
                const emailBody = `
                  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <h2>Program ${status.charAt(0).toUpperCase() + status.slice(1)}</h2>
                    <p>Dear ${trainerName},</p>
                    <p>Your program "<strong>${program.program_name}</strong>" has been ${status}.</p>
                    ${rejection_reason ? `<p><strong>Reason:</strong> ${rejection_reason}</p>` : ""}
                    <p>If you have any questions, please contact our support team.</p>
                    <p>Best regards,<br>The TrainerIQ Team</p>
                  </div>
                `;

                await mailService.send(
                  mailService.from,
                  trainer.email,
                  emailSubject,
                  emailBody
                );
              }
            }
          } catch (notificationError) {
            console.error("Error sending notification:", notificationError);
            // Don't fail the main operation if notification fails
          }
        }

        // Get updated program with trainer info
        const updatedProgramQuery = `
          SELECT
            p.*,
            u.email as trainer_email,
            u.data as trainer_data
          FROM kanglink_program p
          JOIN kanglink_user u ON p.user_id = u.id
          WHERE p.id = ?
        `;

        const updatedProgram = await sdk.rawQuery(updatedProgramQuery, [
          programId,
        ]);

        if (updatedProgram && updatedProgram.length > 0) {
          const programData = updatedProgram[0];
          let trainerData = {};

          if (programData.trainer_data) {
            try {
              trainerData = JSON.parse(programData.trainer_data);
            } catch (e) {
              trainerData = {};
            }
          }

          const responseData = {
            program_id: programId,
            status: status,
            trainer: {
              id: programData.user_id,
              email: programData.trainer_email,
              name:
                trainerData.full_name ||
                `${trainerData.first_name || ""} ${
                  trainerData.last_name || ""
                }`.trim(),
            },
            updated_at: updateData.updated_at,
          };

          if (["rejected", "archived", "deleted"].includes(status) && rejection_reason) {
            responseData.rejection_reason = rejection_reason;
          }

          return res.status(200).json({
            error: false,
            message: `Program status updated to ${status} successfully`,
            data: responseData,
          });
        }

        return res.status(200).json({
          error: false,
          message: `Program status updated to ${status} successfully`,
          data: {
            program_id: programId,
            status: status,
            updated_at: updateData.updated_at,
          },
        });
      } catch (err) {
        console.error("Program status update error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to update program status",
        });
      }
    }
  );

  // Get programs pending approval for admin review
  app.get(
    "/v2/api/kanglink/custom/super_admin/programs/pending",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { page = 1, limit = 10 } = req.query;

        sdk.setProjectId("kanglink");

        // Calculate offset for pagination
        const offset = (parseInt(page) - 1) * parseInt(limit);

        // Get programs pending approval with trainer info
        const pendingProgramsQuery = `
          SELECT
            p.*,
            u.email as trainer_email,
            u.data as trainer_data,
            COUNT(s.id) as split_count
          FROM kanglink_program p
          JOIN kanglink_user u ON p.user_id = u.id
          LEFT JOIN kanglink_split s ON p.id = s.program_id
          WHERE p.status = 'pending_approval'
          GROUP BY p.id, u.email, u.data
          ORDER BY p.created_at DESC
          LIMIT ? OFFSET ?
        `;

        const programs = await sdk.rawQuery(pendingProgramsQuery, [
          parseInt(limit),
          offset,
        ]);

        // Get total count for pagination
        const countQuery = `
          SELECT COUNT(*) as total
          FROM kanglink_program p
          WHERE p.status = 'pending_approval'
        `;

        const countResult = await sdk.rawQuery(countQuery);
        const total = countResult[0]?.total || 0;

        // Format response data
        const formattedPrograms = programs.map((program) => {
          let trainerData = {};
          if (program.trainer_data) {
            try {
              trainerData = JSON.parse(program.trainer_data);
            } catch (e) {
              trainerData = {};
            }
          }

          let paymentPlan = {};
          if (program.payment_plan) {
            try {
              paymentPlan = JSON.parse(program.payment_plan);
            } catch (e) {
              paymentPlan = {};
            }
          }

          return {
            id: program.id,
            program_name: program.program_name,
            type_of_program: program.type_of_program,
            program_description: program.program_description,
            status: program.status,
            split_count: parseInt(program.split_count) || 0,
            payment_plan: paymentPlan,
            image: program.image,
            trainer: {
              id: program.user_id,
              email: program.trainer_email,
              name:
                trainerData.full_name ||
                `${trainerData.first_name || ""} ${
                  trainerData.last_name || ""
                }`.trim(),
              photo: trainerData.photo || null,
            },
            created_at: program.created_at,
            updated_at: program.updated_at,
          };
        });

        return res.status(200).json({
          error: false,
          message: "Pending programs retrieved successfully",
          data: {
            programs: formattedPrograms,
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: total,
              total_pages: Math.ceil(total / parseInt(limit)),
            },
          },
        });
      } catch (err) {
        console.error("Get pending programs error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get pending programs",
        });
      }
    }
  );

  // Get all programs with filtering and pagination for admin management
  app.get(
    "/v2/api/kanglink/custom/super_admin/programs",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const {
          page = 1,
          limit = 10,
          status,
          trainer_id,
          search,
          sort_by = "created_at",
          sort_order = "DESC",
        } = req.query;

        sdk.setProjectId("kanglink");

        // Calculate offset for pagination
        const offset = (parseInt(page) - 1) * parseInt(limit);

        // Build WHERE clause
        let whereConditions = [];
        let queryParams = [];

        if (status) {
          whereConditions.push("p.status = ?");
          queryParams.push(status);
        }

        if (trainer_id) {
          whereConditions.push("p.user_id = ?");
          queryParams.push(trainer_id);
        }

        if (search) {
          whereConditions.push(
            "(p.program_name LIKE ? OR p.program_description LIKE ? OR u.email LIKE ?)"
          );
          const searchTerm = `%${search}%`;
          queryParams.push(searchTerm, searchTerm, searchTerm);
        }

        const whereClause =
          whereConditions.length > 0
            ? `WHERE ${whereConditions.join(" AND ")}`
            : "";

        // Validate sort parameters
        const validSortFields = [
          "created_at",
          "updated_at",
          "program_name",
          "status",
        ];
        const validSortOrders = ["ASC", "DESC"];

        const sortField = validSortFields.includes(sort_by)
          ? sort_by
          : "created_at";
        const sortDirection = validSortOrders.includes(sort_order.toUpperCase())
          ? sort_order.toUpperCase()
          : "DESC";

        // Get programs with trainer info
        const programsQuery = `
          SELECT
            p.*,
            u.email as trainer_email,
            u.data as trainer_data,
            COUNT(s.id) as split_count
          FROM kanglink_program p
          JOIN kanglink_user u ON p.user_id = u.id
          LEFT JOIN kanglink_split s ON p.id = s.program_id
          ${whereClause}
          GROUP BY p.id, u.email, u.data
          ORDER BY p.${sortField} ${sortDirection}
          LIMIT ? OFFSET ?
        `;

        const programs = await sdk.rawQuery(programsQuery, [
          ...queryParams,
          parseInt(limit),
          offset,
        ]);

        // Get total count for pagination
        const countQuery = `
          SELECT COUNT(DISTINCT p.id) as total
          FROM kanglink_program p
          JOIN kanglink_user u ON p.user_id = u.id
          ${whereClause}
        `;

        const countResult = await sdk.rawQuery(countQuery, queryParams);
        const total = countResult[0]?.total || 0;

        // Format response data
        const formattedPrograms = programs.map((program) => {
          let trainerData = {};
          if (program.trainer_data) {
            try {
              trainerData = JSON.parse(program.trainer_data);
            } catch (e) {
              trainerData = {};
            }
          }

          let paymentPlan = {};
          if (program.payment_plan) {
            try {
              paymentPlan = JSON.parse(program.payment_plan);
            } catch (e) {
              paymentPlan = {};
            }
          }

          return {
            id: program.id,
            program_name: program.program_name,
            type_of_program: program.type_of_program,
            program_description: program.program_description,
            status: program.status,
            split_count: parseInt(program.split_count) || 0,
            payment_plan: paymentPlan,
            image: program.image,
            rating: program.rating,
            trainer: {
              id: program.user_id,
              email: program.trainer_email,
              name:
                trainerData.full_name ||
                `${trainerData.first_name || ""} ${
                  trainerData.last_name || ""
                }`.trim(),
              photo: trainerData.photo || null,
            },
            created_at: program.created_at,
            updated_at: program.updated_at,
          };
        });

        return res.status(200).json({
          error: false,
          message: "Programs retrieved successfully",
          data: {
            programs: formattedPrograms,
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: total,
              total_pages: Math.ceil(total / parseInt(limit)),
            },
            filters: {
              status,
              trainer_id,
              search,
              sort_by: sortField,
              sort_order: sortDirection,
            },
          },
        });
      } catch (err) {
        console.error("Get programs error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get programs",
        });
      }
    }
  );

  // Get detailed program information for admin review
  app.get(
    "/v2/api/kanglink/custom/super_admin/programs/:program_id",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const programId = req.params.program_id;

        // Validate program ID
        if (!programId || isNaN(programId)) {
          return res.status(400).json({
            error: true,
            message: "Invalid program ID provided",
          });
        }

        sdk.setProjectId("kanglink");

        // Get program with trainer info and splits
        const programQuery = `
          SELECT
            p.*,
            u.email as trainer_email,
            u.data as trainer_data,
            u.created_at as trainer_joined_at
          FROM kanglink_program p
          JOIN kanglink_user u ON p.user_id = u.id
          WHERE p.id = ?
        `;

        const programResult = await sdk.rawQuery(programQuery, [programId]);

        if (!programResult || programResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Program not found",
          });
        }

        const program = programResult[0];

        // Get splits for this program
        const splitsQuery = `
          SELECT * FROM kanglink_split
          WHERE program_id = ?
          ORDER BY id
        `;

        const splits = await sdk.rawQuery(splitsQuery, [programId]);

        // Get program structure summary (weeks, days, sessions count)
        const structureQuery = `
          SELECT
            COUNT(DISTINCT w.id) as week_count,
            COUNT(DISTINCT d.id) as day_count,
            COUNT(DISTINCT s.id) as session_count,
            COUNT(DISTINCT ei.id) as exercise_count
          FROM kanglink_split sp
          LEFT JOIN kanglink_week w ON sp.id = w.split_id
          LEFT JOIN kanglink_day d ON w.id = d.week_id
          LEFT JOIN kanglink_session s ON d.id = s.day_id
          LEFT JOIN kanglink_exercise_instance ei ON s.id = ei.session_id
          WHERE sp.program_id = ?
        `;

        const structureResult = await sdk.rawQuery(structureQuery, [programId]);
        const structure = structureResult[0] || {};

        // Parse trainer data
        let trainerData = {};
        if (program.trainer_data) {
          try {
            trainerData = JSON.parse(program.trainer_data);
          } catch (e) {
            trainerData = {};
          }
        }

        // Parse payment plan
        let paymentPlan = {};
        if (program.payment_plan) {
          try {
            paymentPlan = JSON.parse(program.payment_plan);
          } catch (e) {
            paymentPlan = {};
          }
        }

        // Format splits data
        const formattedSplits = splits.map((split) => ({
          id: split.id,
          title: split.title,
          full_price: split.full_price,
          subscription: split.subscription,
          equipment_required: split.equipment_required,
          created_at: split.created_at,
          updated_at: split.updated_at,
        }));

        // Calculate price range
        const prices = splits
          .map((s) => [s.full_price, s.subscription])
          .flat()
          .filter((p) => p && p > 0);
        const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
        const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;

        const responseData = {
          id: program.id,
          program_name: program.program_name,
          type_of_program: program.type_of_program,
          program_description: program.program_description,
          status: program.status,
          rating: program.rating,
          track_progress: program.track_progress,
          allow_comments: program.allow_comments,
          allow_private_messages: program.allow_private_messages,
          target_levels: program.target_levels
            ? JSON.parse(program.target_levels)
            : [],
          split_program: program.split_program,
          currency: program.currency,
          days_for_preview: program.days_for_preview,
          image: program.image,
          payment_plan: paymentPlan,
          pricing: {
            min_price: minPrice,
            max_price: maxPrice,
            currency: program.currency || "USD",
          },
          structure: {
            split_count: splits.length,
            week_count: parseInt(structure.week_count) || 0,
            day_count: parseInt(structure.day_count) || 0,
            session_count: parseInt(structure.session_count) || 0,
            exercise_count: parseInt(structure.exercise_count) || 0,
          },
          splits: formattedSplits,
          trainer: {
            id: program.user_id,
            email: program.trainer_email,
            name:
              trainerData.full_name ||
              `${trainerData.first_name || ""} ${
                trainerData.last_name || ""
              }`.trim(),
            photo: trainerData.photo || null,
            bio: trainerData.bio || "",
            specializations: trainerData.specializations || [],
            qualifications: trainerData.qualifications || [],
            years_of_experience: trainerData.years_of_experience || 0,
            joined_at: program.trainer_joined_at,
          },
          created_at: program.created_at,
          updated_at: program.updated_at,
        };

        return res.status(200).json({
          error: false,
          message: "Program details retrieved successfully",
          data: responseData,
        });
      } catch (err) {
        console.error("Get program details error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get program details",
        });
      }
    }
  );

  // Get current payout settings
  app.get(
    "/v2/api/kanglink/custom/super_admin/payout-settings",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        sdk.setTable("payout_settings");

        const payoutSettings = await sdk.findOne("payout_settings", {
          is_active: true,
        });

        if (!payoutSettings) {
          return res.status(404).json({
            error: true,
            message: "No active payout settings found",
          });
        }

        return res.status(200).json({
          error: false,
          data: payoutSettings,
        });
      } catch (err) {
        console.error("Get payout settings error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to retrieve payout settings",
        });
      }
    }
  );

  // Create or update payout settings
  app.post(
    "/v2/api/kanglink/custom/super_admin/payout-settings",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const {
          trainer_payout_time_hours,
          split_company_percentage,
          split_trainer_percentage,
          affiliate_company_percentage,
          affiliate_trainer_percentage,
        } = req.body;

        // Validate required fields
        if (
          trainer_payout_time_hours === undefined ||
          split_company_percentage === undefined ||
          split_trainer_percentage === undefined ||
          affiliate_company_percentage === undefined ||
          affiliate_trainer_percentage === undefined
        ) {
          return res.status(400).json({
            error: true,
            message: "All payout setting fields are required",
          });
        }

        // Validate percentage totals
        const splitTotal =
          parseFloat(split_company_percentage) +
          parseFloat(split_trainer_percentage);
        const affiliateTotal =
          parseFloat(affiliate_company_percentage) +
          parseFloat(affiliate_trainer_percentage);

        if (Math.abs(splitTotal - 100.0) > 0.01) {
          return res.status(400).json({
            error: true,
            message:
              "Split company and trainer percentages must add up to 100%",
          });
        }

        if (Math.abs(affiliateTotal - 100.0) > 0.01) {
          return res.status(400).json({
            error: true,
            message:
              "Affiliate company and trainer percentages must add up to 100%",
          });
        }

        sdk.setProjectId("kanglink");
        sdk.setTable("payout_settings");

        // Deactivate existing settings
        const existingSettings = await sdk.find("payout_settings", {
          is_active: true,
        });
        for (const setting of existingSettings) {
          await sdk.updateById("payout_settings", setting.id, {
            is_active: false,
            updated_at: UtilService.sqlDateTimeFormat(new Date()),
          });
        }

        // Create new settings
        const newSettings = {
          trainer_payout_time_hours: parseInt(trainer_payout_time_hours),
          split_company_percentage: parseFloat(split_company_percentage),
          split_trainer_percentage: parseFloat(split_trainer_percentage),
          affiliate_company_percentage: parseFloat(
            affiliate_company_percentage
          ),
          affiliate_trainer_percentage: parseFloat(
            affiliate_trainer_percentage
          ),
          is_active: true,
          created_at: UtilService.sqlDateTimeFormat(new Date()),
          updated_at: UtilService.sqlDateTimeFormat(new Date()),
        };

        const createdSettings = await sdk.create(
          "payout_settings",
          newSettings
        );

        return res.status(201).json({
          error: false,
          message: "Payout settings created successfully",
          data: createdSettings,
        });
      } catch (err) {
        console.error("Create payout settings error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to create payout settings",
        });
      }
    }
  );

  // Get pending payouts ready for processing
  app.get(
    "/v2/api/kanglink/custom/super_admin/payouts/pending",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const CommissionService = require("../services/CommissionService");
        const commissionService = new CommissionService(sdk);

        sdk.setProjectId("kanglink");

        const pendingPayouts = await commissionService.getPendingPayouts();

        return res.status(200).json({
          error: false,
          data: pendingPayouts,
          count: pendingPayouts.length,
        });
      } catch (err) {
        console.error("Get pending payouts error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to retrieve pending payouts",
        });
      }
    }
  );

  // Process a specific commission payout
  app.put(
    "/v2/api/kanglink/custom/super_admin/payouts/:commission_id/process",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const CommissionService = require("../services/CommissionService");
        const commissionService = new CommissionService(sdk);
        const commissionId = req.params.commission_id;

        if (!commissionId || isNaN(commissionId)) {
          return res.status(400).json({
            error: true,
            message: "Invalid commission ID provided",
          });
        }

        sdk.setProjectId("kanglink");

        // Check if commission exists and is pending
        sdk.setTable("commission");
        const commission = await sdk.findOne("commission", {
          id: commissionId,
        });

        if (!commission) {
          return res.status(404).json({
            error: true,
            message: "Commission not found",
          });
        }

        if (commission.payout_status !== "pending") {
          return res.status(400).json({
            error: true,
            message: `Commission is currently ${commission.payout_status}. Only pending commissions can be processed.`,
          });
        }

        // Mark commission as processed
        const updatedCommission =
          await commissionService.markCommissionProcessed(commissionId);

        return res.status(200).json({
          error: false,
          message: "Commission payout processed successfully",
          data: updatedCommission,
        });
      } catch (err) {
        console.error("Process commission payout error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to process commission payout",
        });
      }
    }
  );

  // Get commission summary for a trainer
  app.get(
    "/v2/api/kanglink/custom/super_admin/commissions/trainer/:trainer_id",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const CommissionService = require("../services/CommissionService");
        const commissionService = new CommissionService(sdk);
        const trainerId = req.params.trainer_id;
        const { status } = req.query;

        if (!trainerId || isNaN(trainerId)) {
          return res.status(400).json({
            error: true,
            message: "Invalid trainer ID provided",
          });
        }

        sdk.setProjectId("kanglink");

        const commissionSummary =
          await commissionService.getTrainerCommissionSummary(
            trainerId,
            status
          );

        return res.status(200).json({
          error: false,
          data: commissionSummary,
        });
      } catch (err) {
        console.error("Get trainer commission summary error:", err);
        return res.status(500).json({
          error: true,
          message:
            err.message || "Failed to retrieve trainer commission summary",
        });
      }
    }
  );

  // Get affiliate commission summary for a trainer
  app.get(
    "/v2/api/kanglink/custom/super_admin/commissions/affiliate/:trainer_id",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const CommissionService = require("../services/CommissionService");
        const commissionService = new CommissionService(sdk);
        const trainerId = req.params.trainer_id;
        const { status } = req.query;

        if (!trainerId || isNaN(trainerId)) {
          return res.status(400).json({
            error: true,
            message: "Invalid trainer ID provided",
          });
        }

        sdk.setProjectId("kanglink");

        const affiliateCommissionSummary =
          await commissionService.getAffiliateCommissionSummary(
            trainerId,
            status
          );

        return res.status(200).json({
          error: false,
          data: affiliateCommissionSummary,
        });
      } catch (err) {
        console.error("Get affiliate commission summary error:", err);
        return res.status(500).json({
          error: true,
          message:
            err.message || "Failed to retrieve affiliate commission summary",
        });
      }
    }
  );

  // Helper function to generate affiliate code
  function generateAffiliateCode(programId, trainerId) {
    const timestamp = Date.now().toString(36);
    const programCode = programId.toString(36);
    const trainerCode = trainerId.toString(36);
    return `${programCode}${trainerCode}${timestamp}`.toUpperCase();
  }

  // Super Admin Notification Endpoints
  app.get(
    "/v2/api/kanglink/custom/super_admin/notifications",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const adminId = req.user_id;
        const { page = 1, limit = 20, unread_only = false } = req.query;

        sdk.setProjectId("kanglink");

        const notificationService = new NotificationService(sdk);
        const result = await notificationService.getUserNotifications(
          adminId,
          parseInt(page),
          parseInt(limit),
          unread_only === "true"
        );

        if (!result.success) {
          return res.status(500).json({
            error: true,
            message: result.error || "Failed to fetch notifications",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Notifications retrieved successfully",
          data: result,
        });
      } catch (error) {
        console.error("Error fetching super admin notifications:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch notifications",
        });
      }
    }
  );

  // Mark notification as read
  app.put(
    "/v2/api/kanglink/custom/super_admin/notifications/:id/read",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const adminId = req.user_id;
        const notificationId = req.params.id;

        sdk.setProjectId("kanglink");

        // Verify notification belongs to admin
        const notification = await sdk.rawQuery(`
          SELECT id FROM kanglink_notification
          WHERE id = ? AND user_id = ?
        `, [notificationId, adminId]);

        if (!notification || notification.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Notification not found",
          });
        }

        const notificationService = new NotificationService(sdk);
        const success = await notificationService.markNotificationAsRead(notificationId, adminId);

        if (!success) {
          return res.status(500).json({
            error: true,
            message: "Failed to mark notification as read",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Notification marked as read",
        });
      } catch (error) {
        console.error("Error marking notification as read:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to mark notification as read",
        });
      }
    }
  );

  // Mark all notifications as read
  app.put(
    "/v2/api/kanglink/custom/super_admin/notifications/read-all",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const adminId = req.user_id;

        sdk.setProjectId("kanglink");

        const notificationService = new NotificationService(sdk);
        const success = await notificationService.markAllNotificationsAsRead(adminId);

        if (!success) {
          return res.status(500).json({
            error: true,
            message: "Failed to mark all notifications as read",
          });
        }

        return res.status(200).json({
          error: false,
          message: "All notifications marked as read",
        });
      } catch (error) {
        console.error("Error marking all notifications as read:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to mark all notifications as read",
        });
      }
    }
  );

  // Get unread notification count
  app.get(
    "/v2/api/kanglink/custom/super_admin/notifications/unread-count",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const adminId = req.user_id;

        sdk.setProjectId("kanglink");

        const notificationService = new NotificationService(sdk);
        const count = await notificationService.getUnreadNotificationCount(adminId);

        return res.status(200).json({
          error: false,
          message: "Unread count retrieved successfully",
          data: {
            unread_count: count,
          },
        });
      } catch (error) {
        console.error("Error getting unread notification count:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to get unread notification count",
        });
      }
    }
  );

  // Create system alert notification
  app.post(
    "/v2/api/kanglink/custom/super_admin/notifications/system-alert",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const adminId = req.user_id;
        const { title, message, alert_type = "info", severity = "medium", details = {} } = req.body;

        sdk.setProjectId("kanglink");

        if (!title || !message) {
          return res.status(400).json({
            error: true,
            message: "Title and message are required",
          });
        }

        // Get all super admin IDs
        const superAdmins = await sdk.rawQuery(`
          SELECT id FROM kanglink_user 
          WHERE role_id LIKE '%super_admin%' AND status = 'active'
        `);

        if (superAdmins.length === 0) {
          return res.status(404).json({
            error: true,
            message: "No super admins found",
          });
        }

        const adminIds = superAdmins.map(admin => admin.id);

        const notificationService = new NotificationService(sdk);
        const alertData = {
          id: Date.now(), // Simple ID for this example
          title,
          message,
          type: alert_type,
          severity,
          details,
        };

        const success = await notificationService.createSystemAlertNotification(alertData, adminIds);

        if (!success) {
          return res.status(500).json({
            error: true,
            message: "Failed to create system alert notification",
          });
        }

        return res.status(200).json({
          error: false,
          message: "System alert notification created successfully",
          data: {
            alert_id: alertData.id,
            recipients_count: adminIds.length,
          },
        });
      } catch (error) {
        console.error("Error creating system alert notification:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to create system alert notification",
        });
      }
    }
  );
};
