const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const ActivityService = require("../services/ActivityService");

module.exports = function (app) {
  // Get trainer activities for their programs
  app.get(
    "/v2/api/kanglink/custom/trainer/activities",
    [TokenMiddleware({ role: "trainer" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        const trainerId = req.user_id;

        const {
          page = 1,
          limit = 20,
          activity_type,
          visibility = "all",
          program_id,
          athlete_id,
          date_from,
          date_to,
        } = req.query;

        const activityService = new ActivityService(sdk);
        const result = await activityService.getTrainerActivities(trainerId, {
          page: parseInt(page),
          limit: parseInt(limit),
          activity_type,
          visibility,
          program_id: program_id ? parseInt(program_id) : undefined,
          athlete_id: athlete_id ? parseInt(athlete_id) : undefined,
          date_from,
          date_to,
        });

        if (!result.success) {
          return res.status(500).json({
            error: true,
            message: result.error || "Failed to fetch activities",
          });
        }

        return res.status(200).json({
          error: false,
          message: "Activities retrieved successfully",
          data: result,
        });
      } catch (error) {
        console.error("Error fetching trainer activities:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch activities",
        });
      }
    }
  );

  // Get trainer activity statistics
  app.get(
    "/v2/api/kanglink/custom/trainer/activities/stats",
    [TokenMiddleware({ role: "trainer" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        const trainerId = req.user_id;

        const { program_id, date_from, date_to } = req.query;

        // Build WHERE conditions
        let whereConditions = [
          "a.visibility IN ('public', 'trainer_only')",
          "p.user_id = ?",
        ];
        let queryParams = [trainerId];

        if (program_id) {
          whereConditions.push("e.program_id = ?");
          queryParams.push(program_id);
        }

        if (date_from) {
          whereConditions.push("a.created_at >= ?");
          queryParams.push(date_from);
        }

        if (date_to) {
          whereConditions.push("a.created_at <= ?");
          queryParams.push(date_to);
        }

        const whereClause = whereConditions.join(" AND ");

        // Get activity type statistics
        const activityTypeStats = await sdk.rawQuery(`
          SELECT 
            a.activity_type,
            COUNT(*) as count
          FROM kanglink_activity a
          JOIN kanglink_user u ON a.user_id = u.id
          LEFT JOIN kanglink_enrollment e ON a.related_id = e.id AND a.related_type = 'enrollment'
          LEFT JOIN kanglink_program p ON e.program_id = p.id
          LEFT JOIN kanglink_split sp ON e.split_id = sp.id
          WHERE ${whereClause}
          GROUP BY a.activity_type
          ORDER BY count DESC
        `, queryParams);

        // Get recent activity count (last 7 days)
        const recentActivityCount = await sdk.rawQuery(`
          SELECT COUNT(*) as count
          FROM kanglink_activity a
          JOIN kanglink_user u ON a.user_id = u.id
          LEFT JOIN kanglink_enrollment e ON a.related_id = e.id AND a.related_type = 'enrollment'
          LEFT JOIN kanglink_program p ON e.program_id = p.id
          LEFT JOIN kanglink_split sp ON e.split_id = sp.id
          WHERE ${whereClause} AND a.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        `, queryParams);

        // Get top athletes by activity count
        const topAthletes = await sdk.rawQuery(`
          SELECT 
            a.user_id,
            u.data as user_data,
            u.email as user_email,
            COUNT(*) as activity_count
          FROM kanglink_activity a
          JOIN kanglink_user u ON a.user_id = u.id
          LEFT JOIN kanglink_enrollment e ON a.related_id = e.id AND a.related_type = 'enrollment'
          LEFT JOIN kanglink_program p ON e.program_id = p.id
          LEFT JOIN kanglink_split sp ON e.split_id = sp.id
          WHERE ${whereClause}
          GROUP BY a.user_id, u.data, u.email
          ORDER BY activity_count DESC
          LIMIT 10
        `, queryParams);

        // Format top athletes data
        const formattedTopAthletes = topAthletes.map((athlete) => {
          let userData = null;
          if (athlete.user_data) {
            try {
              userData = JSON.parse(athlete.user_data);
            } catch (e) {
              console.warn("Failed to parse user data:", e);
            }
          }

          return {
            user_id: athlete.user_id,
            user_name: userData ? `${userData.first_name || ""} ${userData.last_name || ""}`.trim() : "Unknown User",
            user_email: athlete.user_email,
            activity_count: athlete.activity_count,
          };
        });

        return res.status(200).json({
          error: false,
          message: "Activity statistics retrieved successfully",
          data: {
            activity_type_stats: activityTypeStats,
            recent_activity_count: recentActivityCount[0]?.count || 0,
            top_athletes: formattedTopAthletes,
          },
        });
      } catch (error) {
        console.error("Error fetching trainer activity statistics:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch activity statistics",
        });
      }
    }
  );

  // Get trainer activity summary by program
  app.get(
    "/v2/api/kanglink/custom/trainer/activities/programs",
    [TokenMiddleware({ role: "trainer" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        const trainerId = req.user_id;

        const { date_from, date_to } = req.query;

        // Build WHERE conditions
        let whereConditions = [
          "a.visibility IN ('public', 'trainer_only')",
          "p.user_id = ?",
        ];
        let queryParams = [trainerId];

        if (date_from) {
          whereConditions.push("a.created_at >= ?");
          queryParams.push(date_from);
        }

        if (date_to) {
          whereConditions.push("a.created_at <= ?");
          queryParams.push(date_to);
        }

        const whereClause = whereConditions.join(" AND ");

        // Get activity summary by program
        const programActivitySummary = await sdk.rawQuery(`
          SELECT 
            p.id as program_id,
            p.program_name,
            COUNT(a.id) as total_activities,
            COUNT(DISTINCT a.user_id) as unique_athletes,
            MAX(a.created_at) as last_activity_date,
            COUNT(CASE WHEN a.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent_activities
          FROM kanglink_activity a
          JOIN kanglink_user u ON a.user_id = u.id
          LEFT JOIN kanglink_enrollment e ON a.related_id = e.id AND a.related_type = 'enrollment'
          LEFT JOIN kanglink_program p ON e.program_id = p.id
          LEFT JOIN kanglink_split sp ON e.split_id = sp.id
          WHERE ${whereClause}
          GROUP BY p.id, p.program_name
          ORDER BY total_activities DESC
        `, queryParams);

        return res.status(200).json({
          error: false,
          message: "Program activity summary retrieved successfully",
          data: programActivitySummary,
        });
      } catch (error) {
        console.error("Error fetching program activity summary:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch program activity summary",
        });
      }
    }
  );

  // Get trainer activity summary by athlete
  app.get(
    "/v2/api/kanglink/custom/trainer/activities/athletes",
    [TokenMiddleware({ role: "trainer" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        sdk.setProjectId("kanglink");
        const trainerId = req.user_id;

        const { program_id, date_from, date_to } = req.query;

        // Build WHERE conditions
        let whereConditions = [
          "a.visibility IN ('public', 'trainer_only')",
          "p.user_id = ?",
        ];
        let queryParams = [trainerId];

        if (program_id) {
          whereConditions.push("e.program_id = ?");
          queryParams.push(program_id);
        }

        if (date_from) {
          whereConditions.push("a.created_at >= ?");
          queryParams.push(date_from);
        }

        if (date_to) {
          whereConditions.push("a.created_at <= ?");
          queryParams.push(date_to);
        }

        const whereClause = whereConditions.join(" AND ");

        // Get activity summary by athlete
        const athleteActivitySummary = await sdk.rawQuery(`
          SELECT 
            a.user_id,
            u.data as user_data,
            u.email as user_email,
            COUNT(a.id) as total_activities,
            COUNT(DISTINCT e.program_id) as programs_count,
            MAX(a.created_at) as last_activity_date,
            COUNT(CASE WHEN a.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent_activities,
            COUNT(CASE WHEN a.activity_type = 'workout_completed' THEN 1 END) as workouts_completed,
            COUNT(CASE WHEN a.activity_type = 'day_completed' THEN 1 END) as days_completed,
            COUNT(CASE WHEN a.activity_type = 'week_completed' THEN 1 END) as weeks_completed,
            COUNT(CASE WHEN a.activity_type = 'program_completed' THEN 1 END) as programs_completed
          FROM kanglink_activity a
          JOIN kanglink_user u ON a.user_id = u.id
          LEFT JOIN kanglink_enrollment e ON a.related_id = e.id AND a.related_type = 'enrollment'
          LEFT JOIN kanglink_program p ON e.program_id = p.id
          LEFT JOIN kanglink_split sp ON e.split_id = sp.id
          WHERE ${whereClause}
          GROUP BY a.user_id, u.data, u.email
          ORDER BY total_activities DESC
        `, queryParams);

        // Format athlete data
        const formattedAthleteSummary = athleteActivitySummary.map((athlete) => {
          let userData = null;
          if (athlete.user_data) {
            try {
              userData = JSON.parse(athlete.user_data);
            } catch (e) {
              console.warn("Failed to parse user data:", e);
            }
          }

          return {
            user_id: athlete.user_id,
            user_name: userData ? `${userData.first_name || ""} ${userData.last_name || ""}`.trim() : "Unknown User",
            user_email: athlete.user_email,
            total_activities: athlete.total_activities,
            programs_count: athlete.programs_count,
            last_activity_date: athlete.last_activity_date,
            recent_activities: athlete.recent_activities,
            workouts_completed: athlete.workouts_completed,
            days_completed: athlete.days_completed,
            weeks_completed: athlete.weeks_completed,
            programs_completed: athlete.programs_completed,
          };
        });

        return res.status(200).json({
          error: false,
          message: "Athlete activity summary retrieved successfully",
          data: formattedAthleteSummary,
        });
      } catch (error) {
        console.error("Error fetching athlete activity summary:", error);
        return res.status(500).json({
          error: true,
          message: "Failed to fetch athlete activity summary",
        });
      }
    }
  );
}; 