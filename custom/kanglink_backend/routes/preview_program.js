const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");

module.exports = function (app) {
  // Get program split preview details
  app.get(
    "/v2/api/kanglink/custom/preview/program/:split_id",
    [TokenMiddleware({ role: "member|trainer|super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const splitId = req.params.split_id;

        sdk.setProjectId("kanglink");

        // Validate split ID
        if (!splitId || isNaN(splitId)) {
          return res.status(400).json({
            error: true,
            message: "Invalid split ID provided",
          });
        }

        // Get split with program details
        const splitQuery = `
          SELECT 
            s.*,
            p.id as program_id,
            p.program_name,
            p.program_description,
            p.type_of_program,
            p.currency,
            p.image as program_image,
            p.status as program_status,
            p.approval_date,
            p.created_at as program_created_at,
            p.updated_at as program_updated_at,
            p.days_for_preview,
            u.id as trainer_id,
            JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.full_name')) as trainer_full_name,
            JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.first_name')) as trainer_first_name,
            JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.last_name')) as trainer_last_name,
            JSON_UNQUOTE(JSON_EXTRACT(u.data, '$.photo')) as trainer_photo,
            u.email as trainer_email
          FROM kanglink_split s
          JOIN kanglink_program p ON s.program_id = p.id
          JOIN kanglink_user u ON p.user_id = u.id
          WHERE s.id = ?
        `;

        const splitResult = await sdk.rawQuery(splitQuery, [splitId]);
        
        if (!splitResult || splitResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Split not found",
          });
        }

        const split = splitResult[0];

        // Get days for preview based on days_for_preview setting
        const daysForPreview = split.days_for_preview || 7; // Default to 7 if not set
        
        // Get all days for this split with week information, ordered by week and day
        const allDaysQuery = `
          SELECT 
            d.*,
            w.id as week_id,
            w.title as week_title,
            w.week_order,
            (SELECT COUNT(*) FROM kanglink_session s WHERE s.day_id = d.id) as sessions_count
          FROM kanglink_day d
          JOIN kanglink_week w ON d.week_id = w.id
          WHERE w.split_id = ?
          ORDER BY w.week_order ASC, d.day_order ASC
          LIMIT ?
        `;

        const allDays = await sdk.rawQuery(allDaysQuery, [splitId, daysForPreview]);

        // Group days by week for the preview
        const previewWeeks = [];
        const weekMap = new Map();

        for (const day of allDays) {
          const weekId = day.week_id;
          
          if (!weekMap.has(weekId)) {
            weekMap.set(weekId, {
              id: weekId,
              title: day.week_title,
              week_order: day.week_order,
              days_count: 0,
              created_at: null,
              updated_at: null,
              days: []
            });
          }

          const week = weekMap.get(weekId);
          
          // Get sessions for this day
          const sessionsQuery = `
            SELECT 
              s.*,
              (SELECT COUNT(*) FROM kanglink_exercise_instance ei WHERE ei.session_id = s.id) as exercises_count
            FROM kanglink_session s
            WHERE s.day_id = ?
            ORDER BY s.session_order ASC
          `;

          const sessions = await sdk.rawQuery(sessionsQuery, [day.id]);

          for (const session of sessions) {
            // Get exercise instances with exercise and video details
            const exercisesQuery = `
              SELECT 
                ei.*,
                e.*,
                ei.exercise_name as exercise_instance_name,
                ei.video_url as exercise_instance_video_url
              FROM kanglink_exercise_instance ei
              LEFT JOIN kanglink_exercise e ON ei.exercise_id = e.id
              WHERE ei.session_id = ?
              ORDER BY ei.exercise_order ASC
            `;

            const exercises = await sdk.rawQuery(exercisesQuery, [session.id]);

            // Add exercises to session
            session.exercises = exercises.map(exercise => ({
              ...exercise,
              ...(exercise.exercise_id
                ? {
                    exercise_name: exercise.name || exercise.exercise_instance_name,
                    video_url: exercise.video_url || exercise.exercise_instance_video_url
                  }
                : exercise.exercise_instance_name
                ? {
                    name: exercise.exercise_instance_name,
                    video_url: exercise.exercise_instance_video_url
                  }
                : null),
            }));
          }

          // Add sessions to day
          day.sessions = sessions;
          
          // Add day to week
          week.days.push(day);
          week.days_count = week.days.length;
        }

        // Convert week map to array and sort by week_order
        split.weeks = Array.from(weekMap.values()).sort((a, b) => a.week_order - b.week_order);

        // Format the response
        const response = {
          split: {
            id: split.id,
            title: split.title,
            equipment_required: split.equipment_required,
            full_price: split.full_price,
            subscription: split.subscription,
            created_at: split.created_at,
            updated_at: split.updated_at,
            preview_info: {
              days_for_preview: daysForPreview,
              total_days_retrieved: allDays.length,
              weeks_in_preview: split.weeks.length
            },
            program: {
              id: split.program_id,
              name: split.program_name,
              description: split.program_description,
              type: split.type_of_program,
              currency: split.currency,
              image_url: split.program_image,
              status: split.program_status,
              approval_date: split.approval_date,
              created_at: split.program_created_at,
              updated_at: split.program_updated_at,
              trainer: {
                id: split.trainer_id,
                full_name: split.trainer_full_name,
                first_name: split.trainer_first_name,
                last_name: split.trainer_last_name,
                photo: split.trainer_photo,
                email: split.trainer_email
              }
            },
            weeks: split.weeks.map(week => ({
              id: week.id,
              title: week.title,
              week_order: week.week_order,
              days_count: week.days_count,
              created_at: week.created_at,
              updated_at: week.updated_at,
              days: week.days.map(day => ({
                id: day.id,
                title: day.title,
                is_rest_day: day.is_rest_day,
                day_order: day.day_order,
                sessions_count: day.sessions_count,
                created_at: day.created_at,
                updated_at: day.updated_at,
                sessions: day.sessions.map(session => ({
                  id: session.id,
                  title: session.title,
                  session_order: session.session_order,
                  exercises_count: session.exercises_count,
                  created_at: session.created_at,
                  updated_at: session.updated_at,
                  exercises: session.exercises
                }))
              }))
            }))
          }
        };

        return res.status(200).json({
          error: false,
          message: "Program preview retrieved successfully",
          data: response
        });

      } catch (err) {
        console.error("Program preview error:", err);
        return res.status(500).json({
          error: true,
          message: err.message || "Failed to get program preview",
        });
      }
    }
  );
}; 