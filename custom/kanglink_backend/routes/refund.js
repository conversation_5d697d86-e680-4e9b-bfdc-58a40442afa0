const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const StripeService = require("../../../baas/services/StripeService");
const CommissionProcessingService = require("../services/CommissionProcessingService");
const UtilService = require("../../../baas/services/UtilService");
const RefundNotificationService = require("../services/RefundNotificationService");

module.exports = function (app) {
  const stripe = new StripeService();

  // Athlete requests refund for enrollment
  app.post(
    "/v2/api/kanglink/custom/athlete/refund/request",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { enrollment_id, reason } = req.body;
        const athleteId = req.user_id;

        // Validate required fields
        if (!enrollment_id || !reason) {
          return res.status(400).json({
            error: true,
            message: "enrollment_id and reason are required",
          });
        }

        if (reason.trim().length < 10) {
          return res.status(400).json({
            error: true,
            message: "Reason must be at least 10 characters long",
          });
        }

        sdk.setProjectId("kanglink");

        // Get enrollment details with related data
        const enrollmentQuery = `
          SELECT
            e.*,
            p.program_name,
            p.user_id as trainer_id,
            s.title as split_name,
            u.email as athlete_email,
            JSON_EXTRACT(u.data, '$.full_name') as athlete_full_name,
            JSON_EXTRACT(u.data, '$.first_name') as athlete_first_name,
            JSON_EXTRACT(u.data, '$.last_name') as athlete_last_name
          FROM kanglink_enrollment e
          JOIN kanglink_program p ON e.program_id = p.id
          JOIN kanglink_split s ON e.split_id = s.id
          JOIN kanglink_user u ON e.athlete_id = u.id
          WHERE e.id = ? AND e.athlete_id = ?
        `;

        const enrollmentResult = await sdk.rawQuery(enrollmentQuery, [
          enrollment_id,
          athleteId,
        ]);

        if (!enrollmentResult || enrollmentResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Enrollment not found or you don't have access to it",
          });
        }

        const enrollment = enrollmentResult[0];

        // Check if enrollment is eligible for refund
        if (enrollment.payment_type !== "one_time") {
          return res.status(400).json({
            error: true,
            message: "Only one-time purchases are eligible for refunds",
          });
        }

        if (enrollment.payment_status !== "paid") {
          return res.status(400).json({
            error: true,
            message: "Only paid enrollments are eligible for refunds",
          });
        }

        if (enrollment.status === "refund") {
          return res.status(400).json({
            error: true,
            message: "This enrollment is already marked for refund",
          });
        }

        // Check if there's already a pending refund request
        sdk.setTable("refund_request");
        const existingRequest = await sdk.rawQuery(
          `
          SELECT id FROM kanglink_refund_request
          WHERE enrollment_id = ? AND status IN ('pending', 'approved')
        `,
          [enrollment_id]
        );

        if (existingRequest && existingRequest.length > 0) {
          return res.status(400).json({
            error: true,
            message:
              "A refund request for this enrollment is already pending or approved",
          });
        }

        // Get payout settings to check time window
        sdk.setTable("payout_settings");
        const payoutSettings = await sdk.rawQuery(`
          SELECT trainer_payout_time_hours FROM kanglink_payout_settings
          WHERE is_active = 1
          ORDER BY created_at DESC
          LIMIT 1
        `);

        const payoutTimeHours =
          payoutSettings && payoutSettings.length > 0
            ? payoutSettings[0].trainer_payout_time_hours
            : 24; // Default 24 hours

        // Check if enrollment is within refund window
        const enrollmentDate = new Date(enrollment.enrollment_date);
        const currentDate = new Date();
        const hoursDifference =
          (currentDate - enrollmentDate) / (1000 * 60 * 60);

        if (hoursDifference > payoutTimeHours) {
          return res.status(400).json({
            error: true,
            message: `Refund requests must be made within ${payoutTimeHours} hours of enrollment. This enrollment is ${Math.floor(
              hoursDifference
            )} hours old.`,
          });
        }

        // Create refund request
        const refundRequestData = {
          enrollment_id: enrollment.id,
          athlete_id: athleteId,
          trainer_id: enrollment.trainer_id,
          program_id: enrollment.program_id,
          split_id: enrollment.split_id,
          amount: enrollment.amount,
          currency: enrollment.currency,
          reason: reason.trim(),
          status: "pending",
          requested_at: UtilService.sqlDateTimeFormat(new Date()),
          created_at: UtilService.sqlDateTimeFormat(new Date()),
          updated_at: UtilService.sqlDateTimeFormat(new Date()),
        };

        const refundRequest = await sdk.create(
          "refund_request",
          refundRequestData
        );

        // Update the status of enrollment to refund
        sdk.setTable("enrollment");
        await sdk.update(
          "enrollment",
          { id: enrollment_id },
          {
            status: "refund",
            updated_at: UtilService.sqlDateTimeFormat(new Date()),
          }
        );

        // Send notification to trainer
        const notificationService = new RefundNotificationService(sdk);
        await notificationService.createRefundRequestNotification(
          { ...refundRequestData, id: refundRequest.id },
          {
            program_name: enrollment.program_name,
            split_name: enrollment.split_name,
            athlete_name:
              enrollment.athlete_full_name ||
              `${enrollment.athlete_first_name || ""} ${
                enrollment.athlete_last_name || ""
              }`.trim(),
          }
        );

        res.status(201).json({
          error: false,
          message: "Refund request submitted successfully",
          data: {
            refund_request_id: refundRequest.id,
            status: "pending",
            amount: enrollment.amount,
            currency: enrollment.currency,
            requested_at: refundRequestData.requested_at,
          },
        });
      } catch (error) {
        console.error("Error creating refund request:", error);
        res.status(500).json({
          error: true,
          message: error.message || "Failed to create refund request",
        });
      }
    }
  );

  // Admin gets all refund requests with filters
  app.get(
    "/v2/api/kanglink/custom/admin/refund/requests",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const {
          page = 1,
          limit = 10,
          status,
          trainer_name,
          date_from,
          date_to,
        } = req.query;

        sdk.setProjectId("kanglink");

        let whereConditions = [];
        let queryParams = [];

        // Filter by status
        if (
          status &&
          ["pending", "approved", "rejected", "processed"].includes(status)
        ) {
          whereConditions.push("rr.status = ?");
          queryParams.push(status);
        }

        // Filter by trainer name
        if (trainer_name) {
          whereConditions.push(`(
            JSON_EXTRACT(trainer.data, '$.full_name') LIKE ? OR
            CONCAT(JSON_EXTRACT(trainer.data, '$.first_name'), ' ', JSON_EXTRACT(trainer.data, '$.last_name')) LIKE ?
          )`);
          queryParams.push(`%${trainer_name}%`, `%${trainer_name}%`);
        }

        // Filter by date range
        if (date_from) {
          whereConditions.push("DATE(rr.requested_at) >= ?");
          queryParams.push(date_from);
        }
        if (date_to) {
          whereConditions.push("DATE(rr.requested_at) <= ?");
          queryParams.push(date_to);
        }

        const whereClause =
          whereConditions.length > 0
            ? `WHERE ${whereConditions.join(" AND ")}`
            : "";

        // Get total count
        const countQuery = `
          SELECT COUNT(*) as total
          FROM kanglink_refund_request rr
          JOIN kanglink_enrollment e ON rr.enrollment_id = e.id
          JOIN kanglink_program p ON rr.program_id = p.id
          JOIN kanglink_split s ON rr.split_id = s.id
          JOIN kanglink_user athlete ON rr.athlete_id = athlete.id
          JOIN kanglink_user trainer ON rr.trainer_id = trainer.id
          ${whereClause}
        `;

        const countResult = await sdk.rawQuery(countQuery, queryParams);
        const total = countResult[0].total;

        // Get payout settings for commission calculations
        const payoutSettingsQuery = `
          SELECT
            split_company_percentage,
            split_trainer_percentage,
            affiliate_company_percentage,
            affiliate_trainer_percentage
          FROM kanglink_payout_settings
          WHERE is_active = 1
          ORDER BY created_at DESC
          LIMIT 1
        `;
        const payoutSettingsResult = await sdk.rawQuery(payoutSettingsQuery);
        const payoutSettings =
          payoutSettingsResult && payoutSettingsResult.length > 0
            ? payoutSettingsResult[0]
            : {
                split_company_percentage: 30.0,
                split_trainer_percentage: 70.0,
                affiliate_company_percentage: 20.0,
                affiliate_trainer_percentage: 80.0,
              };

        // Get paginated results
        const offset = (page - 1) * limit;
        const dataQuery = `
          SELECT
            rr.id,
            rr.enrollment_id,
            rr.amount,
            rr.currency,
            rr.reason,
            rr.status,
            rr.requested_at,
            rr.processed_at,
            rr.admin_notes,
            rr.refund_amount,
            p.program_name,
            s.title as split_name,
            athlete.email as athlete_email,
            JSON_EXTRACT(athlete.data, '$.full_name') as athlete_full_name,
            JSON_EXTRACT(athlete.data, '$.first_name') as athlete_first_name,
            JSON_EXTRACT(athlete.data, '$.last_name') as athlete_last_name,
            JSON_EXTRACT(trainer.data, '$.full_name') as trainer_full_name,
            JSON_EXTRACT(trainer.data, '$.first_name') as trainer_first_name,
            JSON_EXTRACT(trainer.data, '$.last_name') as trainer_last_name,
            e.enrollment_date,
            e.payment_status,
            e.affiliate_code,
            e.affiliate_user_id,
            e.original_amount,
            e.discount_amount
          FROM kanglink_refund_request rr
          JOIN kanglink_enrollment e ON rr.enrollment_id = e.id
          JOIN kanglink_program p ON rr.program_id = p.id
          JOIN kanglink_split s ON rr.split_id = s.id
          JOIN kanglink_user athlete ON rr.athlete_id = athlete.id
          JOIN kanglink_user trainer ON rr.trainer_id = trainer.id
          ${whereClause}
          ORDER BY rr.requested_at DESC
          LIMIT ? OFFSET ?
        `;

        const refundRequests = await sdk.rawQuery(dataQuery, [
          ...queryParams,
          parseInt(limit),
          offset,
        ]);

        // Format the response with commission calculations
        const formattedRequests = refundRequests.map((request) => {
          // Determine if this was an affiliate purchase
          const isAffiliatePurchase =
            request.affiliate_code || request.affiliate_user_id;

          // Calculate coach revenue and platform earnings based on payout settings
          const amount = request.amount;
          let coachRevenue, platformEarnings;

          if (isAffiliatePurchase) {
            // Use affiliate percentages
            coachRevenue =
              (amount * payoutSettings.affiliate_trainer_percentage) / 100;
            platformEarnings =
              (amount * payoutSettings.affiliate_company_percentage) / 100;
          } else {
            // Use regular split percentages
            coachRevenue =
              (amount * payoutSettings.split_trainer_percentage) / 100;
            platformEarnings =
              (amount * payoutSettings.split_company_percentage) / 100;
          }

          return {
            id: request.id,
            enrollment_id: request.enrollment_id,
            amount: request.amount,
            currency: request.currency,
            reason: request.reason,
            status: request.status,
            purchase_date: request.enrollment_date, // Date athlete bought the program
            refund_date: request.requested_at, // Date refund request was made
            processed_at: request.processed_at,
            admin_notes: request.admin_notes,
            refund_amount: request.refund_amount,
            coach_revenue: Math.round(coachRevenue * 100) / 100, // Round to 2 decimal places
            platform_earnings: Math.round(platformEarnings * 100) / 100,
            is_affiliate_purchase: isAffiliatePurchase,
            program: {
              name: request.program_name,
              split_name: request.split_name,
            },
            athlete: {
              email: request.athlete_email,
              full_name:
                request.athlete_full_name ||
                `${request.athlete_first_name || ""} ${
                  request.athlete_last_name || ""
                }`.trim(),
            },
            trainer: {
              full_name:
                request.trainer_full_name ||
                `${request.trainer_first_name || ""} ${
                  request.trainer_last_name || ""
                }`.trim(),
            },
            enrollment: {
              enrollment_date: request.enrollment_date,
              payment_status: request.payment_status,
              original_amount: request.original_amount,
              discount_amount: request.discount_amount,
              affiliate_code: request.affiliate_code,
            },
          };
        });

        res.status(200).json({
          error: false,
          data: {
            refund_requests: formattedRequests,
            pagination: {
              current_page: parseInt(page),
              per_page: parseInt(limit),
              total: total,
              total_pages: Math.ceil(total / limit),
            },
          },
        });
      } catch (error) {
        console.error("Error fetching refund requests:", error);
        res.status(500).json({
          error: true,
          message: error.message || "Failed to fetch refund requests",
        });
      }
    }
  );

  // Admin gets specific refund request details
  app.get(
    "/v2/api/kanglink/custom/admin/refund/request/:id",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { id } = req.params;

        sdk.setProjectId("kanglink");

        const query = `
          SELECT
            rr.*,
            p.program_name,
            s.title as split_name,
            athlete.email as athlete_email,
            JSON_EXTRACT(athlete.data, '$.full_name') as athlete_full_name,
            JSON_EXTRACT(athlete.data, '$.first_name') as athlete_first_name,
            JSON_EXTRACT(athlete.data, '$.last_name') as athlete_last_name,
            JSON_EXTRACT(trainer.data, '$.full_name') as trainer_full_name,
            JSON_EXTRACT(trainer.data, '$.first_name') as trainer_first_name,
            JSON_EXTRACT(trainer.data, '$.last_name') as trainer_last_name,
            e.enrollment_date,
            e.payment_status,
            e.stripe_payment_intent_id,
            processed_by_user.email as processed_by_email,
            JSON_EXTRACT(processed_by_user.data, '$.full_name') as processed_by_full_name
          FROM kanglink_refund_request rr
          JOIN kanglink_enrollment e ON rr.enrollment_id = e.id
          JOIN kanglink_program p ON rr.program_id = p.id
          JOIN kanglink_split s ON rr.split_id = s.id
          JOIN kanglink_user athlete ON rr.athlete_id = athlete.id
          JOIN kanglink_user trainer ON rr.trainer_id = trainer.id
          LEFT JOIN kanglink_user processed_by_user ON rr.processed_by = processed_by_user.id
          WHERE rr.id = ?
        `;

        const result = await sdk.rawQuery(query, [id]);

        if (!result || result.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Refund request not found",
          });
        }

        const request = result[0];

        const formattedRequest = {
          id: request.id,
          enrollment_id: request.enrollment_id,
          amount: request.amount,
          currency: request.currency,
          reason: request.reason,
          status: request.status,
          requested_at: request.requested_at,
          processed_at: request.processed_at,
          admin_notes: request.admin_notes,
          stripe_refund_id: request.stripe_refund_id,
          refund_amount: request.refund_amount,
          program: {
            name: request.program_name,
            split_name: request.split_name,
          },
          athlete: {
            email: request.athlete_email,
            full_name:
              request.athlete_full_name ||
              `${request.athlete_first_name || ""} ${
                request.athlete_last_name || ""
              }`.trim(),
          },
          trainer: {
            full_name:
              request.trainer_full_name ||
              `${request.trainer_first_name || ""} ${
                request.trainer_last_name || ""
              }`.trim(),
          },
          enrollment: {
            enrollment_date: request.enrollment_date,
            payment_status: request.payment_status,
            stripe_payment_intent_id: request.stripe_payment_intent_id,
          },
          processed_by: request.processed_by
            ? {
                email: request.processed_by_email,
                full_name: request.processed_by_full_name,
              }
            : null,
        };

        res.status(200).json({
          error: false,
          data: formattedRequest,
        });
      } catch (error) {
        console.error("Error fetching refund request details:", error);
        res.status(500).json({
          error: true,
          message: error.message || "Failed to fetch refund request details",
        });
      }
    }
  );

  // Admin approves or rejects refund request
  app.put(
    "/v2/api/kanglink/custom/admin/refund/request/:id/decision",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { id } = req.params;
        const { decision, admin_notes } = req.body; // decision: 'approve' or 'reject'
        const adminId = req.user_id;

        // Validate input
        if (!decision || !["approve", "reject"].includes(decision)) {
          return res.status(400).json({
            error: true,
            message: "Decision must be 'approve' or 'reject'",
          });
        }

        sdk.setProjectId("kanglink");

        // Get refund request details
        sdk.setTable("refund_request");
        const refundRequest = await sdk.findOne("refund_request", { id });

        if (!refundRequest) {
          return res.status(404).json({
            error: true,
            message: "Refund request not found",
          });
        }

        if (refundRequest.status !== "pending") {
          return res.status(400).json({
            error: true,
            message: "Only pending refund requests can be approved or rejected",
          });
        }

        // Update refund request status
        const updateData = {
          status: decision === "approve" ? "approved" : "rejected",
          processed_at: UtilService.sqlDateTimeFormat(new Date()),
          processed_by: adminId,
          admin_notes: admin_notes || null,
          updated_at: UtilService.sqlDateTimeFormat(new Date()),
        };

        await sdk.update("refund_request", { id }, updateData);

        // Send notification about decision
        const notificationService = new RefundNotificationService(sdk);
        await notificationService.createRefundDecisionNotification(
          refundRequest,
          decision,
          admin_notes
        );

        res.status(200).json({
          error: false,
          message: `Refund request ${
            decision === "approve" ? "approved" : "rejected"
          } successfully`,
          data: {
            id: refundRequest.id,
            status: updateData.status,
            processed_at: updateData.processed_at,
            admin_notes: updateData.admin_notes,
          },
        });
      } catch (error) {
        console.error("Error processing refund decision:", error);
        res.status(500).json({
          error: true,
          message: error.message || "Failed to process refund decision",
        });
      }
    }
  );

  // Admin processes approved refund (actually executes the refund)
  app.post(
    "/v2/api/kanglink/custom/admin/refund/request/:id/process",
    [TokenMiddleware({ role: "super_admin" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { id } = req.params;
        const { refund_amount } = req.body; // Optional partial refund amount
        const adminId = req.user_id;

        sdk.setProjectId("kanglink");

        // Get refund request with enrollment details
        const refundQuery = `
          SELECT
            rr.*,
            e.stripe_payment_intent_id,
            e.stripe_customer_id,
            e.amount as enrollment_amount,
            e.currency as enrollment_currency
          FROM kanglink_refund_request rr
          JOIN kanglink_enrollment e ON rr.enrollment_id = e.id
          WHERE rr.id = ?
        `;

        const refundResult = await sdk.rawQuery(refundQuery, [id]);

        if (!refundResult || refundResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Refund request not found",
          });
        }

        const refundRequest = refundResult[0];

        if (refundRequest.status !== "approved") {
          return res.status(400).json({
            error: true,
            message: "Only approved refund requests can be processed",
          });
        }

        if (refundRequest.stripe_refund_id) {
          return res.status(400).json({
            error: true,
            message: "This refund has already been processed",
          });
        }

        // Validate refund amount
        const finalRefundAmount = refund_amount || refundRequest.amount;
        if (finalRefundAmount > refundRequest.amount) {
          return res.status(400).json({
            error: true,
            message: "Refund amount cannot exceed the original payment amount",
          });
        }

        if (finalRefundAmount <= 0) {
          return res.status(400).json({
            error: true,
            message: "Refund amount must be greater than 0",
          });
        }

        // Get the charge ID from the payment intent
        if (!refundRequest.stripe_payment_intent_id) {
          return res.status(400).json({
            error: true,
            message: "No Stripe payment intent found for this enrollment",
          });
        }

        // Retrieve payment intent to get charge ID
        const paymentIntent = await stripe.retrievePaymentIntent(
          refundRequest.stripe_payment_intent_id
        );

        if (
          !paymentIntent ||
          !paymentIntent.charges ||
          paymentIntent.charges.data.length === 0
        ) {
          return res.status(400).json({
            error: true,
            message: "No charge found for this payment intent",
          });
        }

        const chargeId = paymentIntent.charges.data[0].id;

        // Process Stripe refund
        const stripeRefund = await stripe.createStripeRefund({
          charge_id: chargeId,
          amount: finalRefundAmount,
          reason: "requested_by_customer",
          metadata: {
            projectId: "kanglink",
            enrollmentId: refundRequest.enrollment_id,
            refundRequestId: refundRequest.id,
            athleteId: refundRequest.athlete_id,
            trainerId: refundRequest.trainer_id,
          },
        });

        // Start transaction for database updates
        await sdk.rawQuery("START TRANSACTION");

        try {
          // Update refund request
          sdk.setTable("refund_request");
          await sdk.update(
            "refund_request",
            { id },
            {
              status: "processed",
              stripe_refund_id: stripeRefund.id,
              refund_amount: finalRefundAmount,
              processed_at: UtilService.sqlDateTimeFormat(new Date()),
              processed_by: adminId,
              updated_at: UtilService.sqlDateTimeFormat(new Date()),
            }
          );

          // Update enrollment status
          sdk.setTable("enrollment");
          await sdk.update(
            "enrollment",
            { id: refundRequest.enrollment_id },
            {
              status: "refund",
              payment_status: "refunded",
              updated_at: UtilService.sqlDateTimeFormat(new Date()),
            }
          );

          // Update commission records if they exist
          sdk.setTable("commission");
          const commissionQuery = `
            UPDATE kanglink_commission
            SET status = 'refunded',
                refunded_amount = ?,
                updated_at = ?
            WHERE enrollment_id = ? AND status != 'refunded'
          `;

          await sdk.rawQuery(commissionQuery, [
            finalRefundAmount,
            UtilService.sqlDateTimeFormat(new Date()),
            refundRequest.enrollment_id,
          ]);

          // Commit transaction
          await sdk.rawQuery("COMMIT");

          // Send notification about processed refund
          const notificationService = new RefundNotificationService(sdk);
          await notificationService.createRefundProcessedNotification(
            { ...refundRequest, stripe_refund_id: stripeRefund.id },
            finalRefundAmount
          );

          res.status(200).json({
            error: false,
            message: "Refund processed successfully",
            data: {
              refund_request_id: refundRequest.id,
              stripe_refund_id: stripeRefund.id,
              refund_amount: finalRefundAmount,
              currency: refundRequest.currency,
              status: "processed",
              processed_at: UtilService.sqlDateTimeFormat(new Date()),
            },
          });
        } catch (dbError) {
          // Rollback transaction
          await sdk.rawQuery("ROLLBACK");

          // Try to cancel the Stripe refund if possible
          try {
            await stripe.cancelStripeRefund({ refund_id: stripeRefund.id });
          } catch (cancelError) {
            console.error("Failed to cancel Stripe refund:", cancelError);
          }

          throw dbError;
        }
      } catch (error) {
        console.error("Error processing refund:", error);
        res.status(500).json({
          error: true,
          message: error.message || "Failed to process refund",
        });
      }
    }
  );

  // Athlete checks refund eligibility and status for their enrollments
  app.get(
    "/v2/api/kanglink/custom/athlete/refund/status/:enrollment_id",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { enrollment_id } = req.params;
        const athleteId = req.user_id;

        sdk.setProjectId("kanglink");

        // Get enrollment details
        const enrollmentQuery = `
          SELECT
            e.*,
            p.program_name,
            s.title as split_name
          FROM kanglink_enrollment e
          JOIN kanglink_program p ON e.program_id = p.id
          JOIN kanglink_split s ON e.split_id = s.id
          WHERE e.id = ? AND e.athlete_id = ?
        `;

        const enrollmentResult = await sdk.rawQuery(enrollmentQuery, [
          enrollment_id,
          athleteId,
        ]);

        if (!enrollmentResult || enrollmentResult.length === 0) {
          return res.status(404).json({
            error: true,
            message: "Enrollment not found or you don't have access to it",
          });
        }

        const enrollment = enrollmentResult[0];

        // Check for existing refund request
        sdk.setTable("refund_request");
        const refundRequestQuery = `
          SELECT * FROM kanglink_refund_request
          WHERE enrollment_id = ?
          ORDER BY created_at DESC
          LIMIT 1
        `;

        const refundRequestResult = await sdk.rawQuery(refundRequestQuery, [
          enrollment_id,
        ]);
        const refundRequest =
          refundRequestResult && refundRequestResult.length > 0
            ? refundRequestResult[0]
            : null;

        // Get payout settings
        sdk.setTable("payout_settings");
        const payoutSettings = await sdk.rawQuery(`
          SELECT trainer_payout_time_hours FROM kanglink_payout_settings
          WHERE is_active = 1
          ORDER BY created_at DESC
          LIMIT 1
        `);

        const payoutTimeHours =
          payoutSettings && payoutSettings.length > 0
            ? payoutSettings[0].trainer_payout_time_hours
            : 24;

        // Calculate eligibility
        const enrollmentDate = new Date(enrollment.enrollment_date);
        const currentDate = new Date();
        const hoursDifference =
          (currentDate - enrollmentDate) / (1000 * 60 * 60);

        const isEligible =
          enrollment.payment_type === "one_time" &&
          enrollment.payment_status === "paid" &&
          enrollment.status !== "refund" &&
          hoursDifference <= payoutTimeHours &&
          (!refundRequest || refundRequest.status === "rejected");

        const response = {
          enrollment: {
            id: enrollment.id,
            program_name: enrollment.program_name,
            split_name: enrollment.split_name,
            amount: enrollment.amount,
            currency: enrollment.currency,
            payment_type: enrollment.payment_type,
            payment_status: enrollment.payment_status,
            status: enrollment.status,
            enrollment_date: enrollment.enrollment_date,
          },
          refund_eligibility: {
            is_eligible: isEligible,
            reasons: [],
            time_remaining_hours: Math.max(
              0,
              payoutTimeHours - hoursDifference
            ),
            payout_time_limit_hours: payoutTimeHours,
          },
          refund_request: refundRequest
            ? {
                id: refundRequest.id,
                status: refundRequest.status,
                reason: refundRequest.reason,
                amount: refundRequest.amount,
                requested_at: refundRequest.requested_at,
                processed_at: refundRequest.processed_at,
                admin_notes: refundRequest.admin_notes,
                refund_amount: refundRequest.refund_amount,
              }
            : null,
        };

        // Add reasons why not eligible
        if (!isEligible) {
          if (enrollment.payment_type !== "one_time") {
            response.refund_eligibility.reasons.push(
              "Only one-time purchases are eligible for refunds"
            );
          }
          if (enrollment.payment_status !== "paid") {
            response.refund_eligibility.reasons.push(
              "Only paid enrollments are eligible for refunds"
            );
          }
          if (enrollment.status === "refund") {
            response.refund_eligibility.reasons.push(
              "This enrollment is already marked for refund"
            );
          }
          if (hoursDifference > payoutTimeHours) {
            response.refund_eligibility.reasons.push(
              `Refund requests must be made within ${payoutTimeHours} hours of enrollment`
            );
          }
          if (refundRequest && refundRequest.status === "pending") {
            response.refund_eligibility.reasons.push(
              "A refund request is already pending for this enrollment"
            );
          }
          if (refundRequest && refundRequest.status === "approved") {
            response.refund_eligibility.reasons.push(
              "A refund request has been approved and is being processed"
            );
          }
          if (refundRequest && refundRequest.status === "processed") {
            response.refund_eligibility.reasons.push(
              "This enrollment has already been refunded"
            );
          }
        }

        res.status(200).json({
          error: false,
          data: response,
        });
      } catch (error) {
        console.error("Error checking refund status:", error);
        res.status(500).json({
          error: true,
          message: error.message || "Failed to check refund status",
        });
      }
    }
  );

  // Athlete gets their refund requests history
  app.get(
    "/v2/api/kanglink/custom/athlete/refund/requests",
    [TokenMiddleware({ role: "member" })],
    async function (req, res) {
      try {
        const sdk = app.get("sdk");
        const { page = 1, limit = 10, status } = req.query;
        const athleteId = req.user_id;

        sdk.setProjectId("kanglink");

        let whereConditions = ["rr.athlete_id = ?"];
        let queryParams = [athleteId];

        // Filter by status if provided
        if (
          status &&
          ["pending", "approved", "rejected", "processed"].includes(status)
        ) {
          whereConditions.push("rr.status = ?");
          queryParams.push(status);
        }

        const whereClause = `WHERE ${whereConditions.join(" AND ")}`;

        // Get total count
        const countQuery = `
          SELECT COUNT(*) as total
          FROM kanglink_refund_request rr
          ${whereClause}
        `;

        const countResult = await sdk.rawQuery(countQuery, queryParams);
        const total = countResult[0].total;

        // Get paginated results
        const offset = (page - 1) * limit;
        const dataQuery = `
          SELECT
            rr.*,
            p.program_name,
            s.title as split_name,
            e.enrollment_date
          FROM kanglink_refund_request rr
          JOIN kanglink_enrollment e ON rr.enrollment_id = e.id
          JOIN kanglink_program p ON rr.program_id = p.id
          JOIN kanglink_split s ON rr.split_id = s.id
          ${whereClause}
          ORDER BY rr.requested_at DESC
          LIMIT ? OFFSET ?
        `;

        const refundRequests = await sdk.rawQuery(dataQuery, [
          ...queryParams,
          parseInt(limit),
          offset,
        ]);

        const formattedRequests = refundRequests.map((request) => ({
          id: request.id,
          enrollment_id: request.enrollment_id,
          amount: request.amount,
          currency: request.currency,
          reason: request.reason,
          status: request.status,
          requested_at: request.requested_at,
          processed_at: request.processed_at,
          admin_notes: request.admin_notes,
          refund_amount: request.refund_amount,
          program: {
            name: request.program_name,
            split_name: request.split_name,
          },
          enrollment: {
            enrollment_date: request.enrollment_date,
          },
        }));

        res.status(200).json({
          error: false,
          data: {
            refund_requests: formattedRequests,
            pagination: {
              current_page: parseInt(page),
              per_page: parseInt(limit),
              total: total,
              total_pages: Math.ceil(total / limit),
            },
          },
        });
      } catch (error) {
        console.error("Error fetching athlete refund requests:", error);
        res.status(500).json({
          error: true,
          message: error.message || "Failed to fetch refund requests",
        });
      }
    }
  );
};
