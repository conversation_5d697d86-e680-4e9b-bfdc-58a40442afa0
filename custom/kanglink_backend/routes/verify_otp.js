const JwtService = require("../../../baas/services/JwtService");
const { sqlDateTimeFormat, sqlDateFormat } = require("../utils/date");

// Helper function to validate OTP input
const validateOTPInput = (body) => {
  const validationErrors = [];

  if (!body.otp) {
    validationErrors.push({
      field: "otp",
      message: "OTP is required",
    });
  }

  if (!body.temp_token) {
    validationErrors.push({
      field: "temp_token",
      message: "Temporary token is required",
    });
  }

  if (body.otp && (body.otp.length !== 6 || isNaN(body.otp))) {
    validationErrors.push({
      field: "otp",
      message: "OTP must be a 6-digit number",
    });
  }

  if (validationErrors.length > 0) {
    return {
      status: 400,
      response: {
        error: true,
        message: validationErrors[0].message,
        validation: validationErrors,
      },
    };
  }

  return null;
};

const handleOTPVerification = async (req, res, sdk, config) => {
  try {
    // Validate input
    const validationError = validateOTPInput(req.body);
    if (validationError) {
      return res.status(validationError.status).json(validationError.response);
    }

    // Verify temporary token
    const tempTokenData = JwtService.verifyAccessToken(
      req.body.temp_token,
      config.jwt_key
    );
    if (!tempTokenData || tempTokenData.type !== "forgot_password") {
      return res.status(403).json({
        error: true,
        message: "Invalid or expired temporary token",
      });
    }

    sdk.setProjectId("kanglink");
    sdk.setTable("tokens");

    // Find the OTP token in database
    const tokenRecord = await sdk.findOne("tokens", {
      code: req.body.otp,
      user_id: tempTokenData.user_id,
      type: 2, // Reset type
      status: 1, // Active
    });

    if (!tokenRecord) {
      return res.status(403).json({
        error: true,
        message: "Invalid OTP",
      });
    }

    // Check if token is expired
    if (new Date(tokenRecord.expired_at) < new Date()) {
      // delete token from database
      await sdk.delete("tokens", {
        id: tokenRecord.id,
      });

      return res.status(403).json({
        error: true,
        message: "OTP has expired",
      });
    }

    // Verify OTP matches
    const tokenData = JSON.parse(tokenRecord.data);
    if (tokenData.otp !== req.body.otp) {
      // delete token from database
      await sdk.delete("tokens", {
        id: tokenRecord.id,
      });

      return res.status(403).json({
        error: true,
        message: "Invalid OTP",
      });
    }

    // Generate reset token for password change (valid for 15 minutes)
    const resetToken = JwtService.createAccessToken(
      {
        user_id: tempTokenData.user_id,
        email: tempTokenData.email,
        role: tempTokenData.role,
        type: "password_reset",
      },
      900 * 1000, // 15 minutes
      config.jwt_key
    );

    // Update the token record to mark as used and store reset token
    await sdk.updateById("tokens", tokenRecord.id, {
      status: 0, // Mark as inactive
      data: JSON.stringify({
        ...tokenData,
        reset_token: resetToken,
        verified_at: new Date().toISOString(),
      }),
      updated_at: sqlDateTimeFormat(new Date()),
    });

    return res.status(200).json({
      error: false,
      message: "OTP verified successfully",
      data: {
        reset_token: resetToken,
        expires_in: 900 * 1000, // 15 minutes
      },
    });
  } catch (err) {
    console.error("OTP verification error:", err);
    return res.status(500).json({
      error: true,
      message: "Internal server error",
    });
  }
};

module.exports = function (app) {
  const config = app.get("configuration");

  // OTP verification endpoint
  app.post("/v2/api/kanglink/custom/verify-otp", async (req, res) => {
    await handleOTPVerification(req, res, app.get("sdk"), config);
  });

  return [
    {
      method: "POST",
      name: "Verify OTP API",
      url: "/v2/api/kanglink/custom/verify-otp",
      successBody: '{ "otp": "123456", "temp_token": "jwt_temp_token" }',
      successPayload:
        '{"error": false, "message": "OTP verified successfully", "data": {"reset_token": "jwt_reset_token", "expires_in": 900}}',
      errors: [
        {
          name: "400",
          body: '{ "otp": "", "temp_token": "jwt_temp_token" }',
          response:
            '{"error": true, "message": "OTP is required", "validation": [{"field": "otp", "message": "OTP is required"}]}',
        },
        {
          name: "403",
          body: '{ "otp": "123456", "temp_token": "invalid_token" }',
          response:
            '{"error": true, "message": "Invalid or expired temporary token"}',
        },
        {
          name: "403",
          body: '{ "otp": "999999", "temp_token": "jwt_temp_token" }',
          response: '{"error": true, "message": "Invalid OTP"}',
        },
      ],
      needToken: false,
    },
  ];
};
