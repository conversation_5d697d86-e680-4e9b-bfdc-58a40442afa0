-- Create the stripe_webhook table for kanglink project
-- This table is needed for webhook deduplication and tracking

CREATE TABLE IF NOT EXISTS kanglink_stripe_webhook (
  id INT AUTO_INCREMENT PRIMARY KEY,
  stripe_event_id VARCHAR(255) NOT NULL,
  event_type VARCHAR(100) NOT NULL,
  object_id VARCHAR(255) NOT NULL,
  object_type ENUM('customer', 'subscription', 'invoice', 'payment_intent', 'payment_method', 'price', 'product') NOT NULL,
  livemode BOOLEAN DEFAULT FALSE,
  api_version VARCHAR(50),
  status ENUM('pending', 'processed', 'failed', 'ignored') DEFAULT 'pending',
  processed_at DATETIME NULL,
  error_message TEXT NULL,
  retry_count INT DEFAULT 0,
  webhook_data JSON NULL,
  response_data JSON NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Indexes for performance
  INDEX idx_stripe_event_id (stripe_event_id),
  INDEX idx_event_type (event_type),
  INDEX idx_status (status),
  INDEX idx_object_id (object_id),
  INDEX idx_created_at (created_at),
  
  -- Unique constraint to prevent duplicate webhook processing
  UNIQUE KEY unique_webhook (stripe_event_id, event_type)
);

-- Insert some sample data to verify table structure
-- (This will be ignored if the webhook already exists due to UNIQUE constraint)
INSERT IGNORE INTO kanglink_stripe_webhook 
(stripe_event_id, event_type, object_id, object_type, status, webhook_data, created_at, updated_at)
VALUES 
('evt_sample_test', 'payment_intent.created', 'pi_sample_test', 'payment_intent', 'processed', '{"test": true}', NOW(), NOW());

-- Verify the table was created successfully
SELECT 'stripe_webhook table created successfully' as status;
