const BaseModel = require("../../../baas/core/BaseModel");

class athlete_progress extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "athlete_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "enrollment_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "split_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "program_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "trainer_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "current_week_id",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "current_day_id",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "total_days_completed",
        type: "integer",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "total_exercises_completed",
        type: "integer",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "progress_percentage",
        type: "decimal",
        validation: [],
        defaultValue: 0.00,
        mapping: null,
      },
      {
        name: "last_activity_date",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }

  // Transform methods for better data handling
  transformProgressPercentage(value) {
    return parseFloat(value) || 0;
  }

  transformTotalDaysCompleted(value) {
    return parseInt(value) || 0;
  }

  transformTotalExercisesCompleted(value) {
    return parseInt(value) || 0;
  }

  // Relationships
  static relationships() {
    return {
      athlete: {
        type: "belongsTo",
        model: "user",
        foreignKey: "athlete_id",
      },
      trainer: {
        type: "belongsTo",
        model: "user",
        foreignKey: "trainer_id",
      },
      enrollment: {
        type: "belongsTo",
        model: "enrollment",
        foreignKey: "enrollment_id",
      },
      split: {
        type: "belongsTo",
        model: "split",
        foreignKey: "split_id",
      },
      program: {
        type: "belongsTo",
        model: "program",
        foreignKey: "program_id",
      },
      current_week: {
        type: "belongsTo",
        model: "week",
        foreignKey: "current_week_id",
      },
      current_day: {
        type: "belongsTo",
        model: "day",
        foreignKey: "current_day_id",
      },
    };
  }
}

module.exports = athlete_progress;
