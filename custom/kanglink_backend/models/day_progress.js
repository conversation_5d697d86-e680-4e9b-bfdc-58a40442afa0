const BaseModel = require("../../../baas/core/BaseModel");

class day_progress extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "athlete_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "enrollment_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "day_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "week_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "split_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "is_completed",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "completed_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "total_exercises",
        type: "integer",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "completed_exercises",
        type: "integer",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "notes",
        type: "text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }

  // Transform methods
  transformIsCompleted(value) {
    return Boolean(value);
  }

  transformTotalExercises(value) {
    return parseInt(value) || 0;
  }

  transformCompletedExercises(value) {
    return parseInt(value) || 0;
  }

  // Calculate completion percentage
  getCompletionPercentage() {
    if (this.total_exercises === 0) return 0;
    return ((this.completed_exercises / this.total_exercises) * 100).toFixed(2);
  }

  // Relationships
  static relationships() {
    return {
      athlete: {
        type: "belongsTo",
        model: "user",
        foreignKey: "athlete_id",
      },
      enrollment: {
        type: "belongsTo",
        model: "enrollment",
        foreignKey: "enrollment_id",
      },
      day: {
        type: "belongsTo",
        model: "day",
        foreignKey: "day_id",
      },
      week: {
        type: "belongsTo",
        model: "week",
        foreignKey: "week_id",
      },
      split: {
        type: "belongsTo",
        model: "split",
        foreignKey: "split_id",
      },
    };
  }
}

module.exports = day_progress;
