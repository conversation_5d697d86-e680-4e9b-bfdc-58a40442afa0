const BaseModel = require("../../../baas/core/BaseModel");

class stripe_webhook extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_event_id",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "event_type",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "object_id",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "object_type",
        type: "mapping",
        validation: "required,enum:customer,subscription,invoice,payment_intent,payment_method,price,product",
        defaultValue: null,
        mapping: "customer:Customer,subscription:Subscription,invoice:Invoice,payment_intent:Payment Intent,payment_method:Payment Method,price:Price,product:Product",
      },
      {
        name: "livemode",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "api_version",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "status",
        type: "mapping",
        validation: "required,enum:pending,processed,failed,ignored",
        defaultValue: "pending",
        mapping: "pending:Pending,processed:Processed,failed:Failed,ignored:Ignored",
      },
      {
        name: "processed_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "error_message",
        type: "text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "retry_count",
        type: "integer",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "webhook_data",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "response_data",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }

  // Transform methods
  transformObjectType(value) {
    const mappings = {
      'customer': 'Customer',
      'subscription': 'Subscription',
      'invoice': 'Invoice',
      'payment_intent': 'Payment Intent',
      'payment_method': 'Payment Method',
      'price': 'Price',
      'product': 'Product'
    };
    return mappings[value] || value;
  }

  transformStatus(value) {
    const mappings = {
      'pending': 'Pending',
      'processed': 'Processed',
      'failed': 'Failed',
      'ignored': 'Ignored'
    };
    return mappings[value] || value;
  }

  transformLivemode(value) {
    return Boolean(value);
  }

  transformRetryCount(value) {
    return parseInt(value) || 0;
  }

  transformWebhookData(value) {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch (e) {
        return value;
      }
    }
    return value;
  }

  transformResponseData(value) {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch (e) {
        return value;
      }
    }
    return value;
  }

  // Helper methods
  isPending() {
    return this.status === 'pending';
  }

  isProcessed() {
    return this.status === 'processed';
  }

  isFailed() {
    return this.status === 'failed';
  }

  isIgnored() {
    return this.status === 'ignored';
  }

  canRetry() {
    return this.isFailed() && this.retry_count < 5; // Max 5 retries
  }

  getEventCategory() {
    const eventType = this.event_type || '';
    if (eventType.startsWith('customer.')) return 'Customer';
    if (eventType.startsWith('invoice.')) return 'Invoice';
    if (eventType.startsWith('subscription.')) return 'Subscription';
    if (eventType.startsWith('payment_intent.')) return 'Payment';
    if (eventType.startsWith('payment_method.')) return 'Payment Method';
    if (eventType.startsWith('price.')) return 'Price';
    if (eventType.startsWith('product.')) return 'Product';
    return 'Other';
  }

  getStatusBadgeColor() {
    const colors = {
      'pending': 'yellow',
      'processed': 'green',
      'failed': 'red',
      'ignored': 'gray'
    };
    return colors[this.status] || 'gray';
  }

  getProcessingTime() {
    if (!this.processed_at) return null;
    const processedAt = new Date(this.processed_at);
    const createdAt = new Date(this.created_at);
    const diffMs = processedAt - createdAt;
    return Math.round(diffMs / 1000); // Return seconds
  }

  // Mark as processed
  markAsProcessed(responseData = null) {
    this.status = 'processed';
    this.processed_at = new Date();
    this.response_data = responseData;
    this.error_message = null;
  }

  // Mark as failed
  markAsFailed(errorMessage, responseData = null) {
    this.status = 'failed';
    this.error_message = errorMessage;
    this.response_data = responseData;
    this.retry_count = (this.retry_count || 0) + 1;
  }

  // Mark as ignored
  markAsIgnored(reason = null) {
    this.status = 'ignored';
    this.processed_at = new Date();
    this.error_message = reason;
  }
}

module.exports = stripe_webhook;
