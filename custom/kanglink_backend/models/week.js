const BaseModel = require("../../../baas/core/BaseModel");

class week extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "split_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "title",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "week_order",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }
}

module.exports = week;
