const BaseModel = require("../../../baas/core/BaseModel");

class coupon extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "program_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "code",
        type: "string",
        validation: "required,min:3,max:50",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "discount_type",
        type: "mapping",
        validation: "required,enum:fixed,percentage",
        defaultValue: null,
        mapping: "fixed:Fixed Amount,percentage:Percentage",
      },
      {
        name: "discount_value",
        type: "float",
        validation: "required,positive",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "applies_to",
        type: "mapping",
        validation: "required,enum:subscription,full_payment,both",
        defaultValue: null,
        mapping:
          "subscription:Subscription,full_payment:Full Payment,both:Both",
      },
      {
        name: "is_active",
        type: "boolean",
        validation: [],
        defaultValue: true,
        mapping: null,
      },
      {
        name: "expiry_date",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "usage_limit",
        type: "integer",
        validation: "positive",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "used_count",
        type: "integer",
        validation: "",
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformDiscountType(value) {
    const mappings = {
      fixed: "Fixed Amount",
      percentage: "Percentage",
    };
    return mappings[value] || value;
  }

  transformAppliesTo(value) {
    const mappings = {
      subscription: "Subscription",
      full_payment: "Full Payment",
      both: "Both",
    };
    return mappings[value] || value;
  }
}

module.exports = coupon;
