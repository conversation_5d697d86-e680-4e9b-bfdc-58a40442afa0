const BaseModel = require("../../../baas/core/BaseModel");

class refund_request extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "enrollment_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "athlete_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "trainer_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "program_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "split_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "amount",
        type: "float",
        validation: "required,positive",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "currency",
        type: "string",
        validation: "required",
        defaultValue: "USD",
        mapping: null,
      },
      {
        name: "reason",
        type: "long text",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "status",
        type: "mapping",
        validation: "required",
        defaultValue: "pending",
        mapping: "pending:Pending,approved:Approved,rejected:Rejected,processed:Processed",
      },
      {
        name: "requested_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "processed_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "processed_by",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "admin_notes",
        type: "long text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_refund_id",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "refund_amount",
        type: "float",
        validation: "positive",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformStatus(value) {
    const mappings = {
      pending: "Pending",
      approved: "Approved",
      rejected: "Rejected",
      processed: "Processed",
    };
    return mappings[value] || value;
  }
}

module.exports = refund_request;
