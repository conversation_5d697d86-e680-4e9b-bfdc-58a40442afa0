const BaseModel = require("../../../baas/core/BaseModel");

class commission extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "enrollment_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "program_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "split_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "trainer_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "athlete_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "commission_type",
        type: "mapping",
        validation: "required,enum:regular,affiliate",
        defaultValue: null,
        mapping: "regular:Regular,affiliate:Affiliate",
      },
      {
        name: "total_amount",
        type: "float",
        validation: "required,positive",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "original_amount",
        type: "float",
        validation: "positive",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "discount_amount",
        type: "float",
        validation: "min:0",
        defaultValue: 0.0,
        mapping: null,
      },
      {
        name: "company_amount",
        type: "float",
        validation: "required,min:0",
        defaultValue: 0.0,
        mapping: null,
      },
      {
        name: "trainer_amount",
        type: "float",
        validation: "required,min:0",
        defaultValue: 0.0,
        mapping: null,
      },
      {
        name: "affiliate_code",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "affiliate_user_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "payout_status",
        type: "mapping",
        validation: "required,enum:pending,processed,failed,cancelled",
        defaultValue: "pending",
        mapping:
          "pending:Pending,processed:Processed,failed:Failed,cancelled:Cancelled",
      },
      {
        name: "payout_scheduled_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "payout_processed_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "currency",
        type: "string",
        validation: "required",
        defaultValue: "USD",
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformCommissionType(value) {
    const mappings = {
      regular: "Regular",
      affiliate: "Affiliate",
    };
    return mappings[value] || value;
  }

  transformPayoutStatus(value) {
    const mappings = {
      pending: "Pending",
      processed: "Processed",
      failed: "Failed",
      cancelled: "Cancelled",
    };
    return mappings[value] || value;
  }
}

module.exports = commission;
