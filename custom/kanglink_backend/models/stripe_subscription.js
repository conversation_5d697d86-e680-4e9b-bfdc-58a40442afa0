const BaseModel = require("../../../baas/core/BaseModel");

class stripe_subscription extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_id",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "price_id",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "object",
        type: "text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "status",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "is_lifetime",
        type: "boolean",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "date",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  // Transform methods
  transformStatus(value) {
    const mappings = {
      incomplete: "Incomplete",
      incomplete_expired: "Incomplete Expired",
      trialing: "Trialing",
      active: "Active",
      past_due: "Past Due",
      canceled: "Canceled",
      unpaid: "Unpaid",
      paused: "Paused",
    };
    return mappings[value] || value;
  }

  transformObject(value) {
    if (typeof value === "string") {
      try {
        return JSON.parse(value);
      } catch (e) {
        return value;
      }
    }
    return value;
  }

  transformIsLifetime(value) {
    return Boolean(value);
  }

  // Helper methods
  isActive() {
    return ["active", "trialing"].includes(this.status);
  }

  isCanceled() {
    return this.status === "canceled" || this.status === "cancelled";
  }

  isTrialing() {
    return this.status === "trialing";
  }

  isPastDue() {
    return this.status === "past_due";
  }

  isIncomplete() {
    return this.status === "incomplete" || this.status === "incomplete_expired";
  }

  isLifetime() {
    return this.is_lifetime === true || this.is_lifetime === 1;
  }

  // Parse Stripe object data
  getStripeData() {
    if (!this.object) return null;
    try {
      return typeof this.object === "string"
        ? JSON.parse(this.object)
        : this.object;
    } catch (error) {
      console.error("Error parsing Stripe object data:", error);
      return null;
    }
  }

  getStatusBadgeColor() {
    const colors = {
      active: "green",
      trialing: "blue",
      past_due: "orange",
      canceled: "red",
      unpaid: "red",
      paused: "gray",
      incomplete: "yellow",
      incomplete_expired: "red",
    };
    return colors[this.status] || "gray";
  }

  // Get subscription summary
  getSummary() {
    return {
      id: this.id,
      stripe_id: this.stripe_id,
      price_id: this.price_id,
      status: this.status,
      user_id: this.user_id,
      is_lifetime: this.isLifetime(),
      stripe_data: this.getStripeData(),
      created_at: this.created_at,
      updated_at: this.updated_at,
    };
  }

  // Relationships
  static relationships() {
    return {
      user: {
        type: "belongsTo",
        model: "user",
        foreignKey: "user_id",
      },
      stripe_price: {
        type: "belongsTo",
        model: "stripe_price",
        foreignKey: "price_id",
        localKey: "stripe_id",
      },
    };
  }
}

module.exports = stripe_subscription;
