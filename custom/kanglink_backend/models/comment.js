const BaseModel = require("../../../baas/core/BaseModel");

class comment extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "post_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "parent_comment_id",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "content",
        type: "long text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "attachments",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "is_private",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "is_edited",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "is_flagged",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "flag_reason",
        type: "long text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "mentioned_users",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "reaction_count",
        type: "integer",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "created_at",
        type: "timestamp",
        validation: "date",
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "timestamp",
        validation: "date",
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }
}

module.exports = comment;
