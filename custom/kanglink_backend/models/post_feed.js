const BaseModel = require("../../../baas/core/BaseModel");

class post_feed extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "program_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "split_id",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "post_type",
        type: "mapping",
        validation: "required,enum:review,announcement,question,update",
        defaultValue: null,
        mapping:
          "review:Review,announcement:Announcement,question:Question,update:Update",
      },
      {
        name: "content",
        type: "long text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "rating",
        type: "integer",
        validation: "min:1,max:5",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "attachments",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "is_private",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "is_anonymous",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "visibility_scope",
        type: "mapping",
        validation: "enum:public,program_members,private",
        defaultValue: "public",
        mapping:
          "public:Public,program_members:Program Members,private:Private",
      },
      {
        name: "is_pinned",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "pin_expiration",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "is_edited",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "is_flagged",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "flag_reason",
        type: "long text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "reaction_count",
        type: "integer",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "comment_count",
        type: "integer",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "created_at",
        type: "timestamp",
        validation: "date",
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "timestamp",
        validation: "date",
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }

  transformPostType(value) {
    const mappings = {
      review: "Review",
      announcement: "Announcement",
      question: "Question",
      update: "Update",
    };
    return mappings[value] || value;
  }

  transformVisibilityScope(value) {
    const mappings = {
      public: "Public",
      program_members: "Program Members",
      private: "Private",
    };
    return mappings[value] || value;
  }

  static mapping() {
    return {
      post_type: {
        review: "Review",
        announcement: "Announcement",
        question: "Question",
        update: "Update",
      },
      visibility_scope: {
        public: "Public",
        program_members: "Program Members",
        private: "Private",
      },
    };
  }
}

module.exports = post_feed;
