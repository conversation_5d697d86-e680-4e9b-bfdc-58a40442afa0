const BaseModel = require("../../../baas/core/BaseModel");

class stripe_product extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_id",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "name",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "description",
        type: "long text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "object",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "status",
        type: "mapping",
        validation: "required",
        defaultValue: "1",
        mapping: "0:Inactive,1:Active",
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformStatus(value) {
    const mappings = {
      0: "Inactive",
      1: "Active",
    };
    return mappings[value] || value;
  }
}

module.exports = stripe_product;
