const BaseModel = require("../../../baas/core/BaseModel");

class exercise_progress extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "athlete_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "enrollment_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "exercise_instance_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "session_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "day_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "is_completed",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "completed_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "sets_completed",
        type: "integer",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "reps_completed",
        type: "text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "weight_used",
        type: "text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "time_taken_seconds",
        type: "integer",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "difficulty_rating",
        type: "integer",
        validation: "min:1,max:5",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "notes",
        type: "text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }

  // Transform methods
  transformIsCompleted(value) {
    return Boolean(value);
  }

  transformSetsCompleted(value) {
    return parseInt(value) || 0;
  }

  transformTimeTakenSeconds(value) {
    return parseInt(value) || 0;
  }

  transformDifficultyRating(value) {
    const rating = parseInt(value);
    return rating >= 1 && rating <= 5 ? rating : null;
  }

  // Helper methods
  getFormattedTime() {
    if (!this.time_taken_seconds) return null;
    const minutes = Math.floor(this.time_taken_seconds / 60);
    const seconds = this.time_taken_seconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  getDifficultyText() {
    const ratings = {
      1: "Very Easy",
      2: "Easy", 
      3: "Moderate",
      4: "Hard",
      5: "Very Hard"
    };
    return ratings[this.difficulty_rating] || null;
  }

  // Relationships
  static relationships() {
    return {
      athlete: {
        type: "belongsTo",
        model: "user",
        foreignKey: "athlete_id",
      },
      enrollment: {
        type: "belongsTo",
        model: "enrollment",
        foreignKey: "enrollment_id",
      },
      exercise_instance: {
        type: "belongsTo",
        model: "exercise_instance",
        foreignKey: "exercise_instance_id",
      },
      session: {
        type: "belongsTo",
        model: "session",
        foreignKey: "session_id",
      },
      day: {
        type: "belongsTo",
        model: "day",
        foreignKey: "day_id",
      },
    };
  }
}

module.exports = exercise_progress;
