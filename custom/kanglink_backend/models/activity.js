const BaseModel = require("../../../baas/core/BaseModel");

class activity extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "actor_id",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "activity_type",
        type: "mapping",
        validation: "required,enum:workout_started,workout_completed,day_completed,week_completed,program_completed,new_enrollment,payment_made,program_created,program_updated,session_scheduled,milestone_reached,refund_requested,subscription_cancelled,profile_updated,program_approval_pending,new_athlete_signup,new_trainer_signup,new_transaction,refund_approved,refund_rejected,low_rated_trainer,system_alert",
        defaultValue: null,
        mapping: "workout_started:Workout Started,workout_completed:Workout Completed,day_completed:Day Completed,week_completed:Week Completed,program_completed:Program Completed,new_enrollment:New Enrollment,payment_made:Payment Made,program_created:Program Created,program_updated:Program Updated,session_scheduled:Session Scheduled,milestone_reached:Milestone Reached,refund_requested:Refund Requested,subscription_cancelled:Subscription Cancelled,profile_updated:Profile Updated,program_approval_pending:Program Approval Pending,new_athlete_signup:New Athlete Signup,new_trainer_signup:New Trainer Signup,new_transaction:New Transaction,refund_approved:Refund Approved,refund_rejected:Refund Rejected,low_rated_trainer:Low Rated Trainer,system_alert:System Alert",
      },
      {
        name: "related_id",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "related_type",
        type: "mapping",
        validation: [],
        defaultValue: null,
        mapping: "enrollment:Enrollment,program:Program,split:Split,exercise:Exercise,payment:Payment,user:User,session:Session,day:Day,week:Week",
      },
      {
        name: "title",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "description",
        type: "text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "metadata",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "visibility",
        type: "mapping",
        validation: "required",
        defaultValue: "private",
        mapping: "public:Public,private:Private,trainer_only:Trainer Only,admin_only:Admin Only",
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }

  // Transform methods
  transformActivityType(value) {
    const mappings = {
      workout_started: "Workout Started",
      workout_completed: "Workout Completed",
      day_completed: "Day Completed",
      week_completed: "Week Completed",
      program_completed: "Program Completed",
      new_enrollment: "New Enrollment",
      payment_made: "Payment Made",
      program_created: "Program Created",
      program_updated: "Program Updated",
      session_scheduled: "Session Scheduled",
      milestone_reached: "Milestone Reached",
      refund_requested: "Refund Requested",
      subscription_cancelled: "Subscription Cancelled",
      profile_updated: "Profile Updated",
      program_approval_pending: "Program Approval Pending",
      new_athlete_signup: "New Athlete Signup",
      new_trainer_signup: "New Trainer Signup",
      new_transaction: "New Transaction",
      refund_approved: "Refund Approved",
      refund_rejected: "Refund Rejected",
      low_rated_trainer: "Low Rated Trainer",
      system_alert: "System Alert"
    };
    return mappings[value] || value;
  }

  transformRelatedType(value) {
    const mappings = {
      enrollment: "Enrollment",
      program: "Program",
      split: "Split",
      exercise: "Exercise",
      payment: "Payment",
      user: "User",
      session: "Session",
      day: "Day",
      week: "Week"
    };
    return mappings[value] || value;
  }

  transformVisibility(value) {
    const mappings = {
      public: "Public",
      private: "Private",
      trainer_only: "Trainer Only"
    };
    return mappings[value] || value;
  }

  transformMetadata(value) {
    if (typeof value === "string") {
      try {
        return JSON.parse(value);
      } catch (e) {
        return value;
      }
    }
    return value;
  }

  // Helper methods
  getTimeAgo() {
    if (!this.created_at) return null;
    
    const now = new Date();
    const created = new Date(this.created_at);
    const diffMs = now - created;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return created.toLocaleDateString();
  }

  getActivityIcon() {
    const icons = {
      workout_started: "▶️",
      workout_completed: "✅",
      day_completed: "📅",
      week_completed: "🗓️",
      program_completed: "🏆",
      new_enrollment: "👥",
      payment_made: "💰",
      program_created: "🆕",
      program_updated: "📝",
      session_scheduled: "📋",
      milestone_reached: "🎯",
      refund_requested: "↩️",
      subscription_cancelled: "❌",
      profile_updated: "👤",
      program_approval_pending: "⏳",
      new_athlete_signup: "🏃‍♂️",
      new_trainer_signup: "💪",
      new_transaction: "💳",
      refund_approved: "✅",
      refund_rejected: "❌",
      low_rated_trainer: "⭐",
      system_alert: "⚠️"
    };
    return icons[this.activity_type] || "📢";
  }

  // Relationships
  static relationships() {
    return {
      user: {
        type: "belongsTo",
        model: "user",
        foreignKey: "user_id",
      },
      actor: {
        type: "belongsTo",
        model: "user",
        foreignKey: "actor_id",
      },
    };
  }
}

module.exports = activity;
