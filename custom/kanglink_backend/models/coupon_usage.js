const BaseModel = require("../../../baas/core/BaseModel");

class coupon_usage extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "coupon_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "program_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "split_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "discount_amount",
        type: "float",
        validation: "required,positive",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "used_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }
}

module.exports = coupon_usage;
