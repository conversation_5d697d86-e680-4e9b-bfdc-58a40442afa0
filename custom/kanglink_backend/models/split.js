const BaseModel = require("../../../baas/core/BaseModel");

class split extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "program_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "equipment_required",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "title",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "full_price",
        type: "float",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "subscription",
        type: "float",
        validation: [],
        defaultValue: null,
        mapping: null,
      },

      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }
}

module.exports = split;
