const BaseModel = require("../../../baas/core/BaseModel");

class favorite extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "favorite_type",
        type: "mapping",
        validation: "required,enum:program,trainer",
        defaultValue: null,
        mapping: "program:Program,trainer:Trainer",
      },
      {
        name: "favorite_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformFavoriteType(value) {
    const mappings = {
      program: "Program",
      trainer: "Trainer",
    };
    return mappings[value] || value;
  }

  // Custom validation to ensure favorite_id references correct table
  static validateFavoriteReference(data) {
    const errors = [];

    if (data.favorite_type && data.favorite_id) {
      // This validation would need to be implemented in the service layer
      // to check if the favorite_id exists in the corresponding table
      if (data.favorite_type === "program") {
        // Should validate against kanglink_program table
      } else if (data.favorite_type === "trainer") {
        // Should validate against kanglink_user table with trainer role
      }
    }

    return errors;
  }
}

module.exports = favorite;
