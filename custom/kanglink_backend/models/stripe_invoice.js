const BaseModel = require("../../../baas/core/BaseModel");

class stripe_invoice extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_invoice_id",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_customer_id",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_subscription_id",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "enrollment_id",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "amount_due",
        type: "decimal",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "amount_paid",
        type: "decimal",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "amount_remaining",
        type: "decimal",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "currency",
        type: "string",
        validation: "required",
        defaultValue: "usd",
        mapping: null,
      },
      {
        name: "status",
        type: "mapping",
        validation: "required,enum:draft,open,paid,void,uncollectible",
        defaultValue: "draft",
        mapping: "draft:Draft,open:Open,paid:Paid,void:Void,uncollectible:Uncollectible",
      },
      {
        name: "invoice_pdf",
        type: "string",
        validation: "url",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "hosted_invoice_url",
        type: "string",
        validation: "url",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "invoice_number",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "description",
        type: "text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "period_start",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "period_end",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "due_date",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "paid_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_data",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }

  // Transform methods
  transformStatus(value) {
    const mappings = {
      'draft': 'Draft',
      'open': 'Open',
      'paid': 'Paid',
      'void': 'Void',
      'uncollectible': 'Uncollectible'
    };
    return mappings[value] || value;
  }

  transformAmountDue(value) {
    return parseFloat(value) || 0;
  }

  transformAmountPaid(value) {
    return parseFloat(value) || 0;
  }

  transformAmountRemaining(value) {
    return parseFloat(value) || 0;
  }

  transformStripeData(value) {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch (e) {
        return value;
      }
    }
    return value;
  }

  // Helper methods
  isPaid() {
    return this.status === 'paid';
  }

  isOverdue() {
    if (!this.due_date || this.isPaid()) return false;
    return new Date(this.due_date) < new Date();
  }

  getFormattedAmount(field = 'amount_due') {
    const amount = this[field] || 0;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: this.currency?.toUpperCase() || 'USD'
    }).format(amount / 100); // Assuming amounts are in cents
  }

  getDaysUntilDue() {
    if (!this.due_date || this.isPaid()) return null;
    const dueDate = new Date(this.due_date);
    const today = new Date();
    const diffTime = dueDate - today;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  // Relationships
  static relationships() {
    return {
      user: {
        type: "belongsTo",
        model: "user",
        foreignKey: "user_id",
      },
      enrollment: {
        type: "belongsTo",
        model: "enrollment",
        foreignKey: "enrollment_id",
      },
      stripe_subscription: {
        type: "belongsTo",
        model: "stripe_subscription",
        foreignKey: "stripe_subscription_id",
        localKey: "stripe_subscription_id",
      },
    };
  }
}

module.exports = stripe_invoice;
