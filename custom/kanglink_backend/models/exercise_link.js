const BaseModel = require("../../../baas/core/BaseModel");

class exercise_link extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "exercise_instance_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "linked_exercise_instance_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "link_type",
        type: "mapping",
        validation: "required,enum:superset,circuit,dropset,rest_pause",
        defaultValue: "superset",
        mapping: "superset:Superset,circuit:Circuit,dropset:Drop Set,rest_pause:Rest Pause",
      },
      {
        name: "link_order",
        type: "integer",
        validation: [],
        defaultValue: 1,
        mapping: null,
      },
      {
        name: "rest_between_seconds",
        type: "integer",
        validation: [],
        defaultValue: 0,
        mapping: null,
      },
      {
        name: "notes",
        type: "text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }

  // Transform methods
  transformLinkType(value) {
    const mappings = {
      'superset': 'Superset',
      'circuit': 'Circuit',
      'dropset': 'Drop Set',
      'rest_pause': 'Rest Pause'
    };
    return mappings[value] || value;
  }

  transformLinkOrder(value) {
    return parseInt(value) || 1;
  }

  transformRestBetweenSeconds(value) {
    return parseInt(value) || 0;
  }

  // Helper methods
  getFormattedRestTime() {
    if (!this.rest_between_seconds) return "No rest";
    const minutes = Math.floor(this.rest_between_seconds / 60);
    const seconds = this.rest_between_seconds % 60;
    if (minutes > 0) {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${seconds}s`;
  }

  // Relationships
  static relationships() {
    return {
      exercise_instance: {
        type: "belongsTo",
        model: "exercise_instance",
        foreignKey: "exercise_instance_id",
      },
      linked_exercise_instance: {
        type: "belongsTo",
        model: "exercise_instance",
        foreignKey: "linked_exercise_instance_id",
      },
    };
  }
}

module.exports = exercise_link;
