const BaseModel = require("../../../baas/core/BaseModel");

class payout_settings extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "trainer_payout_time_hours",
        type: "integer",
        validation: "required,positive,min:1,max:168", // Max 1 week
        defaultValue: 24,
        mapping: null,
      },
      {
        name: "split_company_percentage",
        type: "float",
        validation: "required,min:0,max:100",
        defaultValue: 30.0,
        mapping: null,
      },
      {
        name: "split_trainer_percentage",
        type: "float",
        validation: "required,min:0,max:100",
        defaultValue: 70.0,
        mapping: null,
      },
      {
        name: "affiliate_company_percentage",
        type: "float",
        validation: "required,min:0,max:100",
        defaultValue: 20.0,
        mapping: null,
      },
      {
        name: "affiliate_trainer_percentage",
        type: "float",
        validation: "required,min:0,max:100",
        defaultValue: 80.0,
        mapping: null,
      },
      {
        name: "is_active",
        type: "boolean",
        validation: [],
        defaultValue: true,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  // Custom validation to ensure percentages add up correctly
  static validatePercentages(data) {
    const errors = [];

    // Check if split percentages add up to 100
    if (
      data.split_company_percentage !== undefined &&
      data.split_trainer_percentage !== undefined
    ) {
      const splitTotal =
        parseFloat(data.split_company_percentage) +
        parseFloat(data.split_trainer_percentage);
      if (Math.abs(splitTotal - 100.0) > 0.01) {
        // Allow for small floating point differences
        errors.push({
          field: "split_percentages",
          message: "Split company and trainer percentages must add up to 100%",
        });
      }
    }

    // Check if affiliate percentages add up to 100
    if (
      data.affiliate_company_percentage !== undefined &&
      data.affiliate_trainer_percentage !== undefined
    ) {
      const affiliateTotal =
        parseFloat(data.affiliate_company_percentage) +
        parseFloat(data.affiliate_trainer_percentage);
      if (Math.abs(affiliateTotal - 100.0) > 0.01) {
        // Allow for small floating point differences
        errors.push({
          field: "affiliate_percentages",
          message:
            "Affiliate company and trainer percentages must add up to 100%",
        });
      }
    }

    return errors;
  }
}

module.exports = payout_settings;
