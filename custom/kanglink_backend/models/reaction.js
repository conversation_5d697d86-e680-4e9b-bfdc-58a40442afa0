const BaseModel = require("../../../baas/core/BaseModel");

class reaction extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "target_type",
        type: "mapping",
        validation: "required,enum:post,comment",
        defaultValue: null,
        mapping: "post:Post,comment:Comment",
      },
      {
        name: "target_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "reaction_type",
        type: "mapping",
        validation: "enum:like,love,fire,strong",
        defaultValue: "like",
        mapping: "like:Like,love:Love,fire:Fire,strong:Strong",
      },
      {
        name: "created_at",
        type: "timestamp",
        validation: "date",
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "timestamp",
        validation: "date",
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }

  transformTargetType(value) {
    const mappings = {
      post: "Post",
      comment: "Comment",
    };
    return mappings[value] || value;
  }

  transformReactionType(value) {
    const mappings = {
      like: "Like",
      love: "Love",
      fire: "Fire",
      strong: "Strong",
    };
    return mappings[value] || value;
  }

  static mapping() {
    return {
      target_type: {
        post: "Post",
        comment: "Comment",
      },
      reaction_type: {
        like: "Like",
        love: "Love",
        fire: "Fire",
        strong: "Strong",
      },
    };
  }
}

module.exports = reaction;
