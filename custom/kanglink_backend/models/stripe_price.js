const BaseModel = require("../../../baas/core/BaseModel");

class stripe_price extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "stripe_id",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "product_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "name",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "amount",
        type: "float",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "currency",
        type: "string",
        validation: [],
        defaultValue: "USD",
        mapping: null,
      },
      {
        name: "type",
        type: "mapping",
        validation: "required,enum:one_time,recurring,lifetime",
        defaultValue: null,
        mapping: "one_time:One Time,recurring:Recurring,lifetime:Lifetime",
      },
      {
        name: "interval_type",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "interval_count",
        type: "integer",
        validation: [],
        defaultValue: "1",
        mapping: null,
      },
      {
        name: "is_usage_metered",
        type: "boolean",
        validation: [],
        defaultValue: "0",
        mapping: null,
      },
      {
        name: "usage_limit",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "object",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "status",
        type: "mapping",
        validation: "required",
        defaultValue: "1",
        mapping: "0:Inactive,1:Active",
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformType(value) {
    const mappings = {
      one_time: "One Time",
      recurring: "Recurring",
      lifetime: "Lifetime",
    };
    return mappings[value] || value;
  }

  transformStatus(value) {
    const mappings = {
      0: "Inactive",
      1: "Active",
    };
    return mappings[value] || value;
  }
}

module.exports = stripe_price;
