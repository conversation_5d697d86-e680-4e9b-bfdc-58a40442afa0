const BaseModel = require("../../../baas/core/BaseModel");

class exercise_instance extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "session_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "exercise_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "exercise_name",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "video_url",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "sets",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "reps_or_time",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "reps_time_type",
        type: "mapping",
        validation: [],
        defaultValue: "reps",
        mapping: "reps:Reps,time:Time",
      },
      {
        name: "time_minutes",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "time_seconds",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "exercise_details",
        type: "long text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "rest_duration_minutes",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "rest_duration_seconds",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "label",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "label_number",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "is_linked",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "exercise_order",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformRepsTimeType(value) {
    const mappings = {
      reps: "Reps",
      time: "Time",
    };
    return mappings[value] || value;
  }
}

module.exports = exercise_instance;
