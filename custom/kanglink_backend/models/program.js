const BaseModel = require("../../../baas/core/BaseModel");

class program extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "program_name",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "type_of_program",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "program_description",
        type: "long text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "payment_plan",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "rating",
        type: "integer",
        validation: "min:1,max:5",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "track_progress",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "allow_comments",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "allow_private_messages",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "target_levels",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "split_program",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "currency",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "days_for_preview",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "image",
        type: "text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "status",
        type: "mapping",
        validation: "required",
        defaultValue: "draft",
        mapping:
          "draft:Draft,pending_approval:Pending Approval,live:Live,published:Published,rejected:Rejected,archived:Archived,active:Active",
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "approval_date",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformStatus(value) {
    const mappings = {
      draft: "Draft",
      pending_approval: "Pending Approval",
      live: "Live",
      published: "Published",
      rejected: "Rejected",
      archived: "Archived",
      active: "Active",
    };
    return mappings[value] || value;
  }
}

module.exports = program;
