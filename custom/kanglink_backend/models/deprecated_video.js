const BaseModel = require("../../../baas/core/BaseModel");

class video extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "name",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "type",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "url",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "video_type",
        type: "string",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "foreign key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }
}

module.exports = video;
