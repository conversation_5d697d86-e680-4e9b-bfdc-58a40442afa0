const BaseModel = require("../../../baas/core/BaseModel");

class qualification extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "name",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "type",
        type: "integer",
        validation: "required,enum:1,2",
        defaultValue: 1,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }

  // Relationships
  static relationships() {
    return {
      user: {
        type: "belongsTo",
        model: "user",
        foreignKey: "user_id",
      },
    };
  }
}

module.exports = qualification;
