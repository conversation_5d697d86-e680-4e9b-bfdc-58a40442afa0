const BaseModel = require("../../../baas/core/BaseModel");

class program_discount extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "program_id",
        type: "foreign key",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "affiliate_link",
        type: "text",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "sale_discount_type",
        type: "mapping",
        validation: "enum:fixed,percentage",
        defaultValue: null,
        mapping: "fixed:Fixed Amount,percentage:Percentage",
      },
      {
        name: "sale_discount_value",
        type: "float",
        validation: "positive",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "sale_apply_to_all",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
    ];
  }

  transformSaleDiscountType(value) {
    const mappings = {
      fixed: "Fixed Amount",
      percentage: "Percentage",
    };
    return mappings[value] || value;
  }
}

module.exports = program_discount;
