const BaseModel = require("../../../baas/core/BaseModel");

class notification extends BaseModel {
  static schema() {
    return [
      {
        name: "id",
        type: "primary key",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "user_id",
        type: "integer",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "sender_id",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "related_id",
        type: "integer",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "related_type",
        type: "mapping",
        validation: [],
        defaultValue: null,
        mapping:
          "enrollment:Enrollment,program:Program,split:Split,exercise:Exercise,payment:Payment,user:User,general:General",
      },
      {
        name: "notification_type",
        type: "mapping",
        validation:
          "required,enum:exercise_completed,day_completed,week_completed,program_completed,milestone_reached,new_enrollment,payment_received,program_updated,athlete_message,system_alert,refund_requested,refund_approved,refund_rejected,refund_processed,program_approved,program_rejected",
        defaultValue: null,
        mapping:
          "exercise_completed:Exercise Completed,day_completed:Day Completed,week_completed:Week Completed,program_completed:Program Completed,milestone_reached:Milestone Reached,new_enrollment:New Enrollment,payment_received:Payment Received,program_updated:Program Updated,athlete_message:Athlete Message,system_alert:System Alert,refund_requested:Refund Requested,refund_approved:Refund Approved,refund_rejected:Refund Rejected,refund_processed:Refund Processed,program_approved:Program Approved,program_rejected:Program Rejected",
      },
      {
        name: "category",
        type: "mapping",
        validation: "required",
        defaultValue: "general",
        mapping:
          "progress:Progress Update,enrollment:Enrollment,payment:Payment,communication:Communication,system:System,general:General,refund:Refund",
      },
      {
        name: "title",
        type: "string",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "message",
        type: "text",
        validation: "required",
        defaultValue: null,
        mapping: null,
      },
      {
        name: "data",
        type: "json",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "is_read",
        type: "boolean",
        validation: [],
        defaultValue: false,
        mapping: null,
      },
      {
        name: "read_at",
        type: "datetime",
        validation: [],
        defaultValue: null,
        mapping: null,
      },
      {
        name: "created_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
      {
        name: "updated_at",
        type: "datetime",
        validation: [],
        defaultValue: "CURRENT_TIMESTAMP",
        mapping: null,
      },
    ];
  }

  // Transform methods
  transformIsRead(value) {
    return Boolean(value);
  }

  transformNotificationType(value) {
    const mappings = {
      exercise_completed: "Exercise Completed",
      day_completed: "Day Completed",
      week_completed: "Week Completed",
      program_completed: "Program Completed",
      milestone_reached: "Milestone Reached",
      new_enrollment: "New Enrollment",
      payment_received: "Payment Received",
      program_updated: "Program Updated",
      athlete_message: "Athlete Message",
      system_alert: "System Alert",
      refund_requested: "Refund Requested",
      refund_approved: "Refund Approved",
      refund_rejected: "Refund Rejected",
      refund_processed: "Refund Processed",
      program_approved: "Program Approved",
      program_rejected: "Program Rejected",
    };
    return mappings[value] || value;
  }

  transformCategory(value) {
    const mappings = {
      progress: "Progress Update",
      enrollment: "Enrollment",
      payment: "Payment",
      communication: "Communication",
      system: "System",
      general: "General",
      refund: "Refund",
    };
    return mappings[value] || value;
  }

  transformRelatedType(value) {
    const mappings = {
      enrollment: "Enrollment",
      program: "Program",
      split: "Split",
      exercise: "Exercise",
      payment: "Payment",
      user: "User",
      general: "General",
    };
    return mappings[value] || value;
  }

  transformData(value) {
    if (typeof value === "string") {
      try {
        return JSON.parse(value);
      } catch (e) {
        return value;
      }
    }
    return value;
  }

  // Helper methods
  getTimeAgo() {
    if (!this.created_at) return null;

    const now = new Date();
    const created = new Date(this.created_at);
    const diffMs = now - created;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;

    return created.toLocaleDateString();
  }

  getNotificationIcon() {
    const icons = {
      exercise_completed: "💪",
      day_completed: "✅",
      week_completed: "🎯",
      program_completed: "🏆",
      milestone_reached: "🎉",
      new_enrollment: "👥",
      payment_received: "💰",
      program_updated: "📝",
      athlete_message: "💬",
      system_alert: "⚠️",
      refund_requested: "💸",
      refund_approved: "✅",
      refund_rejected: "❌",
      refund_processed: "💰",
      program_approved: "✅",
      program_rejected: "❌",
    };
    return icons[this.notification_type] || "📢";
  }

  // Mark as read
  markAsRead() {
    this.is_read = true;
    this.read_at = new Date();
  }

  // Relationships
  static relationships() {
    return {
      user: {
        type: "belongsTo",
        model: "user",
        foreignKey: "user_id",
      },
      sender: {
        type: "belongsTo",
        model: "user",
        foreignKey: "sender_id",
      },
    };
  }
}

module.exports = notification;
