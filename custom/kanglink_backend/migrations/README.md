# Database Migrations

This directory contains database migration scripts for the Kanglink Sport Link application.

## Overview

The migration system helps manage database schema changes in a controlled and versioned manner. It includes:

1. **Notification System Restructure**: Converts the trainer-specific notification system to a general notification system
2. **Activity Tracking**: Adds comprehensive activity tracking for user actions and system events

## Migration Files

### 001_rename_trainer_notifications_to_notification.sql
- Renames `kanglink_trainer_notifications` table to `kanglink_notification`
- Updates table structure to support general notifications for all user types
- Adds new fields: `sender_id`, `related_id`, `related_type`, `category`
- Expands notification types to include enrollment, payment, and system notifications
- Migrates existing data from the old structure

### 002_create_activity_table.sql
- Creates new `kanglink_activity` table for tracking system activities
- Supports various activity types: workouts, enrollments, payments, program updates, etc.
- Includes metadata storage and visibility controls
- Optimized with proper indexes for performance

## Usage

### Prerequisites
1. Install required dependencies:
   ```bash
   npm install mysql2
   ```

2. Set up environment variables or update the database configuration in `run_migrations.js`:
   ```bash
   export DB_HOST=localhost
   export DB_USER=your_username
   export DB_PASSWORD=your_password
   export DB_NAME=kanglink
   ```

### Running Migrations

1. **Run all pending migrations**:
   ```bash
   node run_migrations.js migrate
   ```
   or
   ```bash
   node run_migrations.js up
   ```

2. **Rollback last migration** (removes migration record only):
   ```bash
   node run_migrations.js rollback
   ```
   or
   ```bash
   node run_migrations.js down
   ```

### Migration Tracking

The system automatically creates a `kanglink_migrations` table to track which migrations have been executed. This prevents running the same migration multiple times.

## Database Schema Changes

### New Notification Table Structure
```sql
kanglink_notification:
- id (Primary Key)
- user_id (Who receives the notification)
- sender_id (Who triggered the notification, optional)
- related_id (ID of related entity, optional)
- related_type (Type of related entity: enrollment, program, etc.)
- notification_type (Specific type of notification)
- category (General category: progress, enrollment, payment, etc.)
- title (Notification title)
- message (Notification message)
- data (JSON metadata)
- is_read (Read status)
- read_at (When marked as read)
- created_at, updated_at (Timestamps)
```

### New Activity Table Structure
```sql
kanglink_activity:
- id (Primary Key)
- user_id (User the activity belongs to)
- actor_id (User who performed the activity, optional)
- activity_type (Type of activity performed)
- related_id (ID of related entity, optional)
- related_type (Type of related entity)
- title (Activity title)
- description (Activity description, optional)
- metadata (JSON metadata)
- visibility (public, private, trainer_only)
- created_at, updated_at (Timestamps)
```

## Data Migration

The migration script automatically handles data migration from the old `kanglink_trainer_notifications` table:

- `trainer_id` → `user_id` (notification recipient)
- `athlete_id` → `sender_id` (notification sender)
- `enrollment_id` → `related_id` (related entity)
- Sets `related_type` to 'enrollment'
- Maps notification types to appropriate categories
- Preserves all existing notification data

## Safety Notes

1. **Backup your database** before running migrations
2. Test migrations on a development environment first
3. The rollback command only removes migration tracking records - it doesn't undo schema changes
4. Foreign key constraints are commented out by default - enable them if needed

## Troubleshooting

### Common Issues

1. **Connection errors**: Verify database credentials and server availability
2. **Permission errors**: Ensure database user has CREATE, ALTER, DROP, INSERT, UPDATE, DELETE permissions
3. **Table already exists**: The migrations use `IF NOT EXISTS` clauses to handle this gracefully

### Manual Rollback

If you need to manually rollback changes:

1. For notification table:
   ```sql
   -- Backup data first
   CREATE TABLE backup_notification AS SELECT * FROM kanglink_notification;
   
   -- Recreate old structure if needed
   -- (Refer to your backup schema)
   ```

2. For activity table:
   ```sql
   DROP TABLE IF EXISTS kanglink_activity;
   ```

## Next Steps

After running migrations:

1. Update your application code to use the new `notification` model instead of `trainer_notifications`
2. Implement activity tracking in your application logic
3. Update any existing queries or API endpoints that reference the old table structure
4. Test notification and activity functionality thoroughly
