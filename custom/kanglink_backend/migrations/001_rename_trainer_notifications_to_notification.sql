-- Migration: Rename trainer_notifications table to notification and update structure
-- Date: 2025-01-08
-- Description: Restructure trainer_notifications to be a general notification system

-- Step 1: Create new notification table with updated structure
CREATE TABLE IF NOT EXISTS kanglink_notification (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  sender_id INT NULL,
  related_id INT NULL,
  related_type ENUM('enrollment', 'program', 'split', 'exercise', 'payment', 'user', 'general') NULL,
  notification_type ENUM(
    'exercise_completed', 
    'day_completed', 
    'week_completed', 
    'program_completed', 
    'milestone_reached',
    'new_enrollment',
    'payment_received',
    'program_updated',
    'athlete_message',
    'system_alert'
  ) NOT NULL,
  category ENUM('progress', 'enrollment', 'payment', 'communication', 'system', 'general') NOT NULL DEFAULT 'general',
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSON NULL,
  is_read BOOLEAN NOT NULL DEFAULT FALSE,
  read_at DATETIME NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id),
  INDEX idx_sender_id (sender_id),
  INDEX idx_notification_type (notification_type),
  INDEX idx_category (category),
  INDEX idx_is_read (is_read),
  INDEX idx_created_at (created_at),
  INDEX idx_related (related_type, related_id)
);

-- Step 2: Migrate data from trainer_notifications to notification (if table exists)
INSERT INTO kanglink_notification (
  user_id, 
  sender_id, 
  related_id, 
  related_type, 
  notification_type, 
  category,
  title, 
  message, 
  data, 
  is_read, 
  read_at, 
  created_at, 
  updated_at
)
SELECT 
  trainer_id as user_id,
  athlete_id as sender_id,
  enrollment_id as related_id,
  'enrollment' as related_type,
  notification_type,
  CASE 
    WHEN notification_type IN ('exercise_completed', 'day_completed', 'week_completed', 'program_completed', 'milestone_reached') THEN 'progress'
    ELSE 'general'
  END as category,
  title,
  message,
  data,
  is_read,
  read_at,
  created_at,
  updated_at
FROM kanglink_trainer_notifications
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'kanglink_trainer_notifications');

-- Step 3: Drop old trainer_notifications table (if exists)
DROP TABLE IF EXISTS kanglink_trainer_notifications;

-- Step 4: Add foreign key constraints (optional, depending on your setup)
-- ALTER TABLE kanglink_notification ADD CONSTRAINT fk_notification_user FOREIGN KEY (user_id) REFERENCES kanglink_user(id) ON DELETE CASCADE;
-- ALTER TABLE kanglink_notification ADD CONSTRAINT fk_notification_sender FOREIGN KEY (sender_id) REFERENCES kanglink_user(id) ON DELETE SET NULL;
