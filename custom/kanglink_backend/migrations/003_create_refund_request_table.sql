-- Migration: Create refund_request table for tracking refund requests
-- Date: 2025-01-09
-- Description: Create refund_request table to track athlete refund requests with full workflow support

CREATE TABLE IF NOT EXISTS kanglink_refund_request (
  id INT AUTO_INCREMENT PRIMARY KEY,
  enrollment_id INT NOT NULL,
  athlete_id INT NOT NULL,
  trainer_id INT NOT NULL,
  program_id INT NOT NULL,
  split_id INT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) NOT NULL DEFAULT 'USD',
  reason TEXT NOT NULL,
  status ENUM('pending', 'approved', 'rejected', 'processed') NOT NULL DEFAULT 'pending',
  requested_at DATETIME NULL,
  processed_at DATETIME NULL,
  processed_by INT NULL,
  admin_notes TEXT NULL,
  stripe_refund_id VARCHAR(255) NULL,
  refund_amount DECIMAL(10,2) NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Indexes for performance
  INDEX idx_enrollment_id (enrollment_id),
  INDEX idx_athlete_id (athlete_id),
  INDEX idx_trainer_id (trainer_id),
  INDEX idx_program_id (program_id),
  INDEX idx_split_id (split_id),
  INDEX idx_status (status),
  INDEX idx_requested_at (requested_at),
  INDEX idx_processed_at (processed_at),
  INDEX idx_processed_by (processed_by),
  INDEX idx_stripe_refund_id (stripe_refund_id),
  INDEX idx_athlete_status (athlete_id, status),
  INDEX idx_trainer_status (trainer_id, status),
  INDEX idx_status_requested (status, requested_at DESC),
  
  -- Unique constraint to prevent duplicate refund requests for same enrollment
  UNIQUE KEY unique_enrollment_pending (enrollment_id, status)
);

-- Add foreign key constraints (optional, depending on your setup)
-- ALTER TABLE kanglink_refund_request ADD CONSTRAINT fk_refund_enrollment FOREIGN KEY (enrollment_id) REFERENCES kanglink_enrollment(id) ON DELETE CASCADE;
-- ALTER TABLE kanglink_refund_request ADD CONSTRAINT fk_refund_athlete FOREIGN KEY (athlete_id) REFERENCES kanglink_user(id) ON DELETE CASCADE;
-- ALTER TABLE kanglink_refund_request ADD CONSTRAINT fk_refund_trainer FOREIGN KEY (trainer_id) REFERENCES kanglink_user(id) ON DELETE CASCADE;
-- ALTER TABLE kanglink_refund_request ADD CONSTRAINT fk_refund_program FOREIGN KEY (program_id) REFERENCES kanglink_program(id) ON DELETE CASCADE;
-- ALTER TABLE kanglink_refund_request ADD CONSTRAINT fk_refund_split FOREIGN KEY (split_id) REFERENCES kanglink_split(id) ON DELETE CASCADE;
-- ALTER TABLE kanglink_refund_request ADD CONSTRAINT fk_refund_processed_by FOREIGN KEY (processed_by) REFERENCES kanglink_user(id) ON DELETE SET NULL;

-- Update notification table to support refund notification types
ALTER TABLE kanglink_notification 
MODIFY COLUMN notification_type ENUM(
  'exercise_completed',
  'day_completed', 
  'week_completed',
  'program_completed',
  'milestone_reached',
  'new_enrollment',
  'payment_received',
  'program_updated',
  'athlete_message',
  'system_alert',
  'refund_requested',
  'refund_approved',
  'refund_rejected',
  'refund_processed'
) NOT NULL;

ALTER TABLE kanglink_notification 
MODIFY COLUMN category ENUM(
  'progress',
  'enrollment',
  'payment',
  'communication',
  'system',
  'general',
  'refund'
) NOT NULL DEFAULT 'general';

-- Add related_type for refund requests
ALTER TABLE kanglink_notification 
MODIFY COLUMN related_type ENUM(
  'enrollment',
  'program',
  'split',
  'exercise',
  'payment',
  'user',
  'general',
  'refund_request'
) NULL;
