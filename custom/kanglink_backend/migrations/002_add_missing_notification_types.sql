-- Migration: Add missing notification types for refund requests, post feeds, and comments
-- Date: 2025-01-08
-- Description: Add notification types for refund requests, post feeds, and comments

-- Step 1: Add new notification types to the ENUM
ALTER TABLE kanglink_notification 
MODIFY COLUMN notification_type ENUM(
  'exercise_completed', 
  'day_completed', 
  'week_completed', 
  'program_completed', 
  'milestone_reached',
  'new_enrollment',
  'payment_received',
  'program_updated',
  'athlete_message',
  'system_alert',
  'refund_requested',
  'refund_approved',
  'refund_rejected',
  'post_feed_created',
  'post_feed_comment'
) NOT NULL;

-- Step 2: Add new related types to the ENUM
ALTER TABLE kanglink_notification 
MODIFY COLUMN related_type ENUM('enrollment', 'program', 'split', 'exercise', 'payment', 'user', 'general', 'refund_request', 'post_feed') NULL;

-- Step 3: Add new categories to the ENUM
ALTER TABLE kanglink_notification 
MODIFY COLUMN category ENUM('progress', 'enrollment', 'payment', 'communication', 'system', 'general', 'refund', 'social') NOT NULL DEFAULT 'general'; 