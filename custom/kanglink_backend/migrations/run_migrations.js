const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');

// Database configuration - update these values according to your setup
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'kanglink',
  multipleStatements: true
};

// Migration tracking table
const MIGRATION_TABLE = 'kanglink_migrations';

class MigrationRunner {
  constructor() {
    this.connection = null;
  }

  async connect() {
    try {
      this.connection = await mysql.createConnection(dbConfig);
      console.log('✅ Connected to database');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.connection) {
      await this.connection.end();
      console.log('✅ Disconnected from database');
    }
  }

  async createMigrationTable() {
    const sql = `
      CREATE TABLE IF NOT EXISTS ${MIGRATION_TABLE} (
        id INT AUTO_INCREMENT PRIMARY KEY,
        filename VARCHAR(255) NOT NULL UNIQUE,
        executed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_filename (filename)
      )
    `;
    
    await this.connection.execute(sql);
    console.log('✅ Migration tracking table ready');
  }

  async getExecutedMigrations() {
    try {
      const [rows] = await this.connection.execute(
        `SELECT filename FROM ${MIGRATION_TABLE} ORDER BY executed_at`
      );
      return rows.map(row => row.filename);
    } catch (error) {
      // Table doesn't exist yet
      return [];
    }
  }

  async getMigrationFiles() {
    const migrationsDir = __dirname;
    const files = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    return files;
  }

  async executeMigration(filename) {
    const filePath = path.join(__dirname, filename);
    const sql = fs.readFileSync(filePath, 'utf8');
    
    console.log(`🔄 Executing migration: ${filename}`);
    
    try {
      // Split SQL by semicolons and execute each statement
      const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
      
      for (const statement of statements) {
        if (statement.trim()) {
          await this.connection.execute(statement);
        }
      }
      
      // Record migration as executed
      await this.connection.execute(
        `INSERT INTO ${MIGRATION_TABLE} (filename) VALUES (?)`,
        [filename]
      );
      
      console.log(`✅ Migration completed: ${filename}`);
    } catch (error) {
      console.error(`❌ Migration failed: ${filename}`, error.message);
      throw error;
    }
  }

  async runMigrations() {
    try {
      await this.connect();
      await this.createMigrationTable();
      
      const executedMigrations = await this.getExecutedMigrations();
      const migrationFiles = await this.getMigrationFiles();
      
      const pendingMigrations = migrationFiles.filter(
        file => !executedMigrations.includes(file)
      );
      
      if (pendingMigrations.length === 0) {
        console.log('✅ No pending migrations');
        return;
      }
      
      console.log(`📋 Found ${pendingMigrations.length} pending migration(s):`);
      pendingMigrations.forEach(file => console.log(`  - ${file}`));
      
      for (const migration of pendingMigrations) {
        await this.executeMigration(migration);
      }
      
      console.log('🎉 All migrations completed successfully!');
      
    } catch (error) {
      console.error('💥 Migration process failed:', error.message);
      process.exit(1);
    } finally {
      await this.disconnect();
    }
  }

  async rollbackLastMigration() {
    try {
      await this.connect();
      
      const [rows] = await this.connection.execute(
        `SELECT filename FROM ${MIGRATION_TABLE} ORDER BY executed_at DESC LIMIT 1`
      );
      
      if (rows.length === 0) {
        console.log('ℹ️ No migrations to rollback');
        return;
      }
      
      const lastMigration = rows[0].filename;
      console.log(`⚠️ Rolling back migration: ${lastMigration}`);
      console.log('⚠️ Note: This will only remove the migration record. Manual rollback may be required.');
      
      await this.connection.execute(
        `DELETE FROM ${MIGRATION_TABLE} WHERE filename = ?`,
        [lastMigration]
      );
      
      console.log('✅ Migration record removed');
      
    } catch (error) {
      console.error('❌ Rollback failed:', error.message);
      throw error;
    } finally {
      await this.disconnect();
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'migrate';
  
  const runner = new MigrationRunner();
  
  switch (command) {
    case 'migrate':
    case 'up':
      await runner.runMigrations();
      break;
      
    case 'rollback':
    case 'down':
      await runner.rollbackLastMigration();
      break;
      
    default:
      console.log('Usage:');
      console.log('  node run_migrations.js migrate   # Run pending migrations');
      console.log('  node run_migrations.js rollback  # Rollback last migration');
      break;
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = MigrationRunner;
