-- Migration: Create activity table for tracking system activities
-- Date: 2025-01-08
-- Description: Create activity table to track various system activities like workouts, enrollments, payments, etc.

CREATE TABLE IF NOT EXISTS kanglink_activity (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  actor_id INT NULL,
  activity_type ENUM(
    'workout_started',
    'workout_completed', 
    'day_completed',
    'week_completed',
    'program_completed',
    'new_enrollment',
    'payment_made',
    'program_created',
    'program_updated',
    'session_scheduled',
    'milestone_reached',
    'refund_requested',
    'subscription_cancelled',
    'profile_updated'
  ) NOT NULL,
  related_id INT NULL,
  related_type ENUM('enrollment', 'program', 'split', 'exercise', 'payment', 'user', 'session', 'day', 'week') NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT NULL,
  metadata JSON NULL,
  visibility ENUM('public', 'private', 'trainer_only') NOT NULL DEFAULT 'private',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id),
  INDEX idx_actor_id (actor_id),
  INDEX idx_activity_type (activity_type),
  INDEX idx_visibility (visibility),
  INDEX idx_created_at (created_at),
  INDEX idx_related (related_type, related_id),
  INDEX idx_user_activity_type (user_id, activity_type),
  INDEX idx_user_created (user_id, created_at DESC)
);

-- Add foreign key constraints (optional, depending on your setup)
-- ALTER TABLE kanglink_activity ADD CONSTRAINT fk_activity_user FOREIGN KEY (user_id) REFERENCES kanglink_user(id) ON DELETE CASCADE;
-- ALTER TABLE kanglink_activity ADD CONSTRAINT fk_activity_actor FOREIGN KEY (actor_id) REFERENCES kanglink_user(id) ON DELETE SET NULL;
