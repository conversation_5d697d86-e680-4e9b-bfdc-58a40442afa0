-- Migration: Update activity table for admin alerts functionality
-- Date: 2025-01-15
-- Description: Add new activity types and admin-specific fields for admin alerts system

-- Step 1: Add new activity types for admin alerts
ALTER TABLE kanglink_activity 
MODIFY COLUMN activity_type ENUM(
  'workout_started',
  'workout_completed', 
  'day_completed',
  'week_completed',
  'program_completed',
  'new_enrollment',
  'payment_made',
  'program_created',
  'program_updated',
  'session_scheduled',
  'milestone_reached',
  'refund_requested',
  'subscription_cancelled',
  'profile_updated',
  -- Admin alert activity types
  'program_approval_pending',
  'new_athlete_signup',
  'new_trainer_signup',
  'new_transaction',
  'refund_approved',
  'refund_rejected',
  'low_rated_trainer',
  'system_alert'
) NOT NULL;

-- Step 2: Update visibility enum to include admin_only
ALTER TABLE kanglink_activity 
MODIFY COLUMN visibility ENUM('public', 'private', 'trainer_only', 'admin_only') NOT NULL DEFAULT 'private';

-- Step 3: Add index for admin alerts queries
CREATE INDEX IF NOT EXISTS idx_admin_alerts ON kanglink_activity (visibility, user_id, created_at DESC);

-- Step 4: Add index for activity type filtering
CREATE INDEX IF NOT EXISTS idx_activity_type_visibility ON kanglink_activity (activity_type, visibility);

-- Step 5: Add index for date range queries
CREATE INDEX IF NOT EXISTS idx_activity_created_date ON kanglink_activity (created_at DESC, visibility, user_id); 