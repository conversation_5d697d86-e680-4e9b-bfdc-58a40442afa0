import React, { useRef, useEffect } from "react";
import { Modal } from "@/components/Modal";

interface VideoPlayerModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: any | null;
}

const VideoPlayerModal: React.FC<VideoPlayerModalProps> = ({
  isOpen,
  onClose,
  data,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // if (!data) return null;
  console.log("data", data);
  const isYouTube =
    data?.video_url?.includes("youtube.com") ||
    data?.video_url?.includes("youtu.be");
  const isVimeo = data?.video_url?.includes("vimeo.com");

  // Convert YouTube URL to embed format
  const getYouTubeEmbedUrl = (url: string) => {
    const videoId = url.match(
      /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/
    )?.[1];
    return videoId ? `https://www.youtube.com/embed/${videoId}` : url;
  };

  // Convert Vimeo URL to embed format
  const getVimeoEmbedUrl = (url: string) => {
    const videoId = url.match(/vimeo\.com\/(\d+)/)?.[1];
    return videoId ? `https://player.vimeo.com/video/${videoId}` : url;
  };

  const getEmbedUrl = () => {
    if (!data?.video_url) return "";

    if (isYouTube) {
      return getYouTubeEmbedUrl(data?.video_url);
    } else if (isVimeo) {
      return getVimeoEmbedUrl(data?.video_url);
    }

    return data?.video_url;
  };

  const canEmbed = isYouTube || isVimeo;

  // Stop video playback when modal closes
  useEffect(() => {
    if (!isOpen) {
      // Stop HTML5 video
      if (videoRef.current) {
        videoRef.current.pause();
        videoRef.current.currentTime = 0;
      }
      
      // Stop iframe videos by changing src to empty
      if (iframeRef.current) {
        iframeRef.current.src = "";
      }
    }
  }, [isOpen]);

  const handleClose = () => {
    // Stop video before closing
    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.currentTime = 0;
    }
    
    if (iframeRef.current) {
      iframeRef.current.src = "";
    }
    
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      title={data?.name || "Video Player"}
      modalCloseClick={handleClose}
      modalHeader={true}
      classes={{
        modal: "h-full",
        modalDialog: "w-full max-w-4xl h-auto",
        modalContent: "",
      }}
    >
      <div className="space-y-4">
        {/* Video Info */}
        <div className="border-b border-border pb-4">
          {/* <h3 className="text-lg font-semibold text-text">{data?.name}</h3> */}
          {data?.video_type && (
            <p className="text-sm text-text-secondary capitalize">
              Type: {data?.video_type}
            </p>
          )}
        </div>

        {/* Video Player */}
        <div className="relative w-full">
          {canEmbed ? (
            <div
              className="relative w-full"
              style={{ paddingBottom: "56.25%" }}
            >
              <iframe
                ref={iframeRef}
                src={isOpen ? getEmbedUrl() : ""}
                title={data?.name || "Video"}
                className="absolute top-0 left-0 w-full h-full rounded-lg"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
          ) : (
            <div className="w-full">
              <video
                ref={videoRef}
                controls
                className="w-full rounded-lg"
                style={{ maxHeight: "60vh" }}
              >
                <source src={data?.video_url} type="video/mp4" />
                <source src={data?.video_url} type="video/webm" />
                <source src={data?.video_url} type="video/ogg" />
                Your browser does not support the video tag.
              </video>
            </div>
          )}
        </div>

        {/* Fallback link if video doesn't load */}
        <div className="text-center pt-4">
          <p className="text-sm text-text-secondary mb-2">
            Having trouble viewing the video?
          </p>
          <a
            href={data?.video_url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:text-primary-dark underline cursor-pointer"
          >
            Open video in new tab
          </a>
        </div>
      </div>
    </Modal>
  );
};

export default VideoPlayerModal;
