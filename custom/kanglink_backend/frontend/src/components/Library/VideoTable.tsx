import React from "react";
import { ChevronDown, Edit, Trash2 } from "lucide-react";
import { PaginationBar } from "@/components/PaginationBar";
import { InteractiveButton } from "@/components/InteractiveButton";

interface VideoTableProps {
  videos: any[];
  currentPage: number;
  totalPages: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onDeleteVideo: (videoId: number) => void;
  onEditVideo: (videoId: number) => void;
  onVideoAction: (videoId: number) => void;
  onAddVideo: () => void;
}

const VideoTable: React.FC<VideoTableProps> = ({
  videos,
  currentPage,
  totalPages,
  pageSize,
  onPageChange,
  onDeleteVideo,
  onEditVideo,
  onVideoAction,
  onAddVideo,
}) => {
  return (
    <div className="bg-background border border-border rounded-lg shadow-sm overflow-hidden">
      {/* Header with Title and Add Button */}
      <div className="px-4 sm:px-6 py-4 border-b border-border flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-2xl font-bold text-text">Video</h2>
        <InteractiveButton
          onClick={onAddVideo}
          type="button"
          className="!h-11 px-6 !w-[121px] !min-w-[121px] !max-w-[121px]  bg-primary text-white font-semibold hover:bg-primary-dark transition-colors"
        >
          Add Video
        </InteractiveButton>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Table Header */}
          <thead className="bg-background-secondary">
            <tr>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">ID</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">
                    Video Name
                  </span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">Type</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">Date</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-sm font-medium text-text">Add Video</span>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-sm font-medium text-text">Actions</span>
              </th>
            </tr>
          </thead>

          {/* Table Body */}
          <tbody className="divide-y divide-border">
            {videos.map((video) => (
              <tr
                key={video.id}
                className="hover:bg-background-hover transition-colors duration-200"
              >
                <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                  {video.id}
                </td>
                <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                  {video.name}
                </td>
                <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                  {video.video_type}
                </td>
                <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                  {video.created_at}
                </td>
                <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                  <InteractiveButton
                    onClick={() => onVideoAction(video.id as number)}
                    type="button"
                    className={`!h-9 !w-[90.91px] !min-w-[90.91px] !max-w-[90.91px] px-4 text-sm font-medium transition-colors ${
                      video.url
                        ? "bg-transparent border border-primary text-primary hover:bg-primary hover:text-white"
                        : "bg-transparent border border-primary text-primary hover:bg-primary hover:text-white"
                    }`}
                  >
                    {video.url ? "Open" : "Add Video"}
                  </InteractiveButton>
                </td>
                <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onEditVideo(video.id as number)}
                      className="p-2 text-text-secondary hover:text-text transition-colors duration-200"
                      title="Edit Video"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => onDeleteVideo(video.id as number)}
                      className="p-2 text-text-secondary hover:text-red-600 transition-colors duration-200"
                      title="Delete Video"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 sm:px-6 py-4 border-t border-border">
          <PaginationBar
            currentPage={currentPage}
            pageCount={totalPages}
            pageSize={pageSize}
            canPreviousPage={currentPage > 1}
            canNextPage={currentPage < totalPages}
            updatePageSize={() => {}} // Not needed for this implementation
            updateCurrentPage={onPageChange}
            startSize={pageSize}
            multiplier={1}
            canChangeLimit={false}
          />
        </div>
      )}
    </div>
  );
};

export default VideoTable;
