import React from "react";
import { ChevronDown, Edit, Trash2 } from "lucide-react";
import { PaginationBar } from "@/components/PaginationBar";
import { InteractiveButton } from "@/components/InteractiveButton";
import { Exercise } from "@/interfaces";

interface ExerciseTableProps {
  exercises: Exercise[];
  currentPage: number;
  totalPages: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onDeleteExercise: (exerciseId: number) => void;
  onEditExercise: (exerciseId: number) => void;
  onVideoAction: (exerciseId: number) => void;
  onAddExercise: () => void;
}

const ExerciseTable: React.FC<ExerciseTableProps> = ({
  exercises,
  currentPage,
  totalPages,
  pageSize,
  onPageChange,
  onDeleteExercise,
  onEditExercise,
  onVideoAction,
  onAddExercise,
}) => {
  return (
    <div className="bg-background border border-border rounded-lg shadow-sm overflow-hidden">
      {/* Header with Title and Add Button */}
      <div className="px-4 sm:px-6 py-4 border-b border-border flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h2 className="text-2xl font-bold text-text">Exercise</h2>
        <InteractiveButton
          onClick={onAddExercise}
          type="button"
          className="!h-11 px-6 bg-primary text-white font-semibold hover:bg-primary-dark transition-colors"
        >
          Add Exercise
        </InteractiveButton>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Table Header */}
          <thead className="bg-background-secondary">
            <tr>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">ID</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">
                    Exercise
                  </span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              {/* <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">Type</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th> */}
              <th className="px-4 sm:px-6 py-3 text-left">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-text">Date</span>
                  <ChevronDown className="w-4 h-4 text-text-secondary" />
                </div>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-sm font-medium text-text">Add Video</span>
              </th>
              <th className="px-4 sm:px-6 py-3 text-left">
                <span className="text-sm font-medium text-text">Actions</span>
              </th>
            </tr>
          </thead>

          {/* Table Body */}
          <tbody className="divide-y divide-border">
            {exercises.map((exercise) => (
              <tr
                key={exercise.id}
                className="hover:bg-background-hover transition-colors duration-200"
              >
                <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                  {exercise.id}
                </td>
                <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                  {exercise.name}
                </td>
                {/* <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                  {exercise.exercise_type}
                </td> */}
                <td className="px-4 sm:px-6 py-4 text-sm text-text whitespace-nowrap">
                  {exercise.created_at}
                </td>
                <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                  <InteractiveButton
                    onClick={() => onVideoAction(exercise.id as number)}
                    type="button"
                    className={`!h-9 !w-[90.91px] !min-w-[90.91px] !max-w-[90.91px] px-4 text-sm font-medium transition-colors ${
                      exercise.video_url
                        ? "bg-transparent border border-primary text-primary hover:bg-primary hover:text-white"
                        : "bg-transparent border border-primary text-primary hover:bg-primary hover:text-white"
                    }`}
                  >
                    {exercise.video_url ? "Open" : "Add Video"}
                  </InteractiveButton>
                </td>
                <td className="px-4 sm:px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => onEditExercise(exercise.id as number)}
                      className="p-2 text-text-secondary hover:text-text transition-colors duration-200"
                      title="Edit Exercise"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => onDeleteExercise(exercise.id as number)}
                      className="p-2 text-text-secondary hover:text-red-600 transition-colors duration-200"
                      title="Delete Exercise"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 sm:px-6 py-4 border-t border-border">
          <PaginationBar
            currentPage={currentPage}
            pageCount={totalPages}
            pageSize={pageSize}
            canPreviousPage={currentPage > 1}
            canNextPage={currentPage < totalPages}
            updatePageSize={() => {}} // Not needed for this implementation
            updateCurrentPage={onPageChange}
            startSize={pageSize}
            multiplier={1}
            canChangeLimit={false}
          />
        </div>
      )}
    </div>
  );
};

export default ExerciseTable;
