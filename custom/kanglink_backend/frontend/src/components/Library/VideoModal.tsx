import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Modal } from "@/components/Modal";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useCreateModelMutation, useUpdateModelMutation } from "@/query/shared";
import { useContexts } from "@/hooks/useContexts";
import { useProfile } from "@/hooks/useProfile";
import { Models } from "@/utils/baas/models";
import { ToastStatusEnum } from "@/utils/Enums";

interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  video?: any;
  onSuccess?: (video: any) => void;
}

interface VideoFormData {
  name: string;
  video_type: string;
  url: string;
}

const VideoModal: React.FC<VideoModalProps> = ({
  isOpen,
  onClose,
  video,
  onSuccess,
}) => {
  const { showToast } = useContexts();
  const { profile } = useProfile();

  // Validation schema
  const schema = yup.object().shape({
    name: yup.string().required("Video name is required"),
    video_type: yup.string().required("Video type is required"),
    url: yup
      .string()
      .url("Please enter a valid URL")
      .required("Video URL is required"),
  });

  // Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<VideoFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      name: "",
      video_type: "",
      url: "",
    },
  });

  // API mutations
  const { mutateAsync: createVideo, isPending: isCreating } =
    useCreateModelMutation(Models.VIDEO);
  const { mutateAsync: updateVideo, isPending: isUpdating } =
    useUpdateModelMutation(Models.VIDEO);

  const isLoading = isCreating || isUpdating;
  const isEditMode = !!video;

  // Populate form when editing
  useEffect(() => {
    if (isEditMode && video) {
      setValue("name", video.name || "");
      setValue("video_type", video.video_type || "");
      setValue("url", video.url || "");
    } else {
      reset();
    }
  }, [video, isEditMode, setValue, reset]);

  // Handle form submission
  const onSubmit = async (data: VideoFormData) => {
    try {
      const payload = {
        name: data.name,
        video_type: data.video_type,
        url: data.url,
        type: 1,
        user_id: profile?.id,
      };

      let result;
      if (isEditMode && video?.id) {
        result = await updateVideo({
          id: video.id,
          payload,
        });
      } else {
        result = await createVideo(payload);
      }

      showToast(
        `Video ${isEditMode ? "updated" : "created"} successfully`,
        5000,
        ToastStatusEnum.SUCCESS
      );

      onSuccess?.(result);
      handleClose();
    } catch (error) {
      console.error("Error saving video:", error);
      showToast(
        `Failed to ${isEditMode ? "update" : "create"} video`,
        5000,
        ToastStatusEnum.ERROR
      );
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      title={isEditMode ? "Edit Video" : "Add Video"}
      modalCloseClick={handleClose}
      modalHeader={true}
      classes={{
        modal: "h-full",
        modalDialog: "w-full max-w-md h-auto",
        modalContent: "",
      }}
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Video Name */}
        <MkdInputV2
          name="name"
          type="text"
          register={register}
          errors={errors}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Video Name</MkdInputV2.Label>
            <MkdInputV2.Field placeholder="Enter video name" />
            <MkdInputV2.Error />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Video Type */}
        <MkdInputV2
          name="video_type"
          type="text"
          register={register}
          errors={errors}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Video Type</MkdInputV2.Label>
            <MkdInputV2.Field></MkdInputV2.Field>
            <MkdInputV2.Error />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Video URL */}
        <MkdInputV2
          name="url"
          type="url"
          register={register}
          errors={errors}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Video URL</MkdInputV2.Label>
            <MkdInputV2.Field placeholder="https://example.com/video.mp4" />
            <MkdInputV2.Error />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4">
          <InteractiveButton
            type="button"
            onClick={handleClose}
            className="px-4 py-2 border border-border text-text hover:bg-background-hover transition-colors"
            disabled={isLoading}
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            type="submit"
            className="px-4 py-2 bg-primary text-white hover:bg-primary-dark transition-colors"
            disabled={isLoading}
          >
            {isLoading
              ? isEditMode
                ? "Updating..."
                : "Creating..."
              : isEditMode
                ? "Update Video"
                : "Create Video"}
          </InteractiveButton>
        </div>
      </form>
    </Modal>
  );
};

export default VideoModal;
