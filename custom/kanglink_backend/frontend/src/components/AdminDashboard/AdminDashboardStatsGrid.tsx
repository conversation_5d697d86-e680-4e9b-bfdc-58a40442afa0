import React, { Suspense } from "react";
import AdminDashboardStatCard from "./AdminDashboardStatCard";
import { AdminDashboardStats } from "@/hooks/useAdminDashboard";

interface AdminDashboardStatsGridProps {
  stats: AdminDashboardStats | null;
  isLoading?: boolean;
}

const AdminDashboardStatsGrid: React.FC<AdminDashboardStatsGridProps> = ({
  stats,
  isLoading = false,
}) => {
  const statCards = [
    {
      title: "Athletes",
      value: isLoading ? "..." : (stats?.total_athletes ?? 0),
      subtitle: "No. of Athlete",
    },
    {
      title: "Trainers", 
      value: isLoading ? "..." : (stats?.total_trainers ?? 0),
      subtitle: "No. of Trainer",
    },
    {
      title: "Program Approval",
      value: isLoading ? "..." : (stats?.pending_approval_programs_count ?? 0),
      subtitle: "Program Pending Approval",
    },
    {
      title: "Refund Requests",
      value: isLoading ? "..." : (stats?.pending_refund_requests_count ?? 0),
      subtitle: "Refund Requests",
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8 sm:mb-12">
      {statCards.map((card, index) => (
        <Suspense
          key={index}
          fallback={
            <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded-lg h-32"></div>
          }
        >
          <AdminDashboardStatCard
            title={card.title}
            value={card.value}
            subtitle={card.subtitle}
            isLoading={isLoading}
          />
        </Suspense>
      ))}
    </div>
  );
};

export default AdminDashboardStatsGrid;
