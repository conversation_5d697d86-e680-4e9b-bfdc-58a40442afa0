import React from "react";

interface AdminDashboardStatCardProps {
  title: string;
  value: string | number;
  subtitle: string;
  isLoading?: boolean;
}

const AdminDashboardStatCard: React.FC<AdminDashboardStatCardProps> = ({
  title: _title,
  value,
  subtitle,
  isLoading = false,
}) => {
  return (
    <div className="bg-background-secondary rounded-md shadow-md p-6 flex flex-col items-center border border-border hover:shadow-lg transition-all duration-200">
      {isLoading ? (
        <>
          <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded h-8 w-16 mb-2"></div>
          <div className="animate-pulse bg-gray-200 dark:bg-gray-700 rounded h-4 w-24"></div>
        </>
      ) : (
        <>
          <span className="text-3xl font-bold text-text">{value}</span>
          <span className="text-sm text-text-secondary mt-2 text-center">
            {subtitle}
          </span>
        </>
      )}
    </div>
  );
};

export default AdminDashboardStatCard;
