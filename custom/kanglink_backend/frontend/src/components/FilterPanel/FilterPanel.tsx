import { useState, useEffect } from "react";
import { useSearchParams, useNavigate, useLocation } from "react-router-dom";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { Modal } from "../Modal";

interface FilterPanelProps {
  isOpen: boolean;
  onClose: () => void;
  pageView?: "trainers" | "programs";
}

interface FilterState {
  gender: string[];
  experience: string[];
  sortBy: string;
  sortOrder: string;
  minRating: number;
  maxRating: number;
  minExperience: number;
  maxExperience: number;
  hasPreview: boolean;
  hasPrograms: boolean;
}

const FilterPanel = ({ isOpen, onClose, pageView: _pageView }: FilterPanelProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();

  const [filters, setFilters] = useState<FilterState>({
    gender: [],
    experience: [],
    sortBy: "created_at",
    sortOrder: "desc",
    minRating: 0,
    maxRating: 5,
    minExperience: 0,
    maxExperience: 50,
    hasPreview: false,
    hasPrograms: false,
  });

  const genderOptions = [
    { value: "man", label: "Man" },
    { value: "woman", label: "Woman" },
    { value: "non_binary", label: "Non-Binary" },
    { value: "transgender_woman", label: "Transgender Woman" },
    { value: "transgender_man", label: "Transgender Man" },
    { value: "other", label: "Other" },
  ];

  const experienceOptions = [
    { value: "less_than_1_year", label: "Less than 1 year" },
    { value: "1_2_years", label: "1-2 years" },
    { value: "3_5_years", label: "3-5 years" },
    { value: "5_7_years", label: "5-7 years" },
    { value: "6_10_years", label: "6-10 years" },
    { value: "10_plus_years", label: "10+ years" },
  ];

  const sortOptions = [
    { value: "created_at", label: "Recently Added" },
    { value: "price", label: "Price: Low to High" },
    { value: "popularity", label: "Most Popular" },
    { value: "rating", label: "Highest Rated" },
  ];

  // Load filters from URL params on mount
  useEffect(() => {
    const gender = searchParams.get("gender")?.split(",").filter(Boolean) || [];
    const experience = searchParams.get("experience")?.split(",").filter(Boolean) || [];
    const sortBy = searchParams.get("sort_by") || "created_at";
    const sortOrder = searchParams.get("sort_order") || "desc";
    const minRating = parseFloat(searchParams.get("min_rating") || "0");
    const maxRating = parseFloat(searchParams.get("max_rating") || "5");
    const minExperience = parseInt(searchParams.get("min_experience") || "0");
    const maxExperience = parseInt(searchParams.get("max_experience") || "50");
    const hasPreview = searchParams.get("has_preview") === "true";
    const hasPrograms = searchParams.get("has_programs") === "true";

    setFilters({
      gender,
      experience,
      sortBy,
      sortOrder,
      minRating,
      maxRating,
      minExperience,
      maxExperience,
      hasPreview,
      hasPrograms,
    });
  }, [searchParams]);

  const updateFilters = (newFilters: Partial<FilterState>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);

    // Update URL params
    const params = new URLSearchParams(searchParams);

    // Update gender filter
    if (updatedFilters.gender.length > 0) {
      params.set("gender", updatedFilters.gender.join(","));
    } else {
      params.delete("gender");
    }

    // Update experience filter
    if (updatedFilters.experience.length > 0) {
      params.set("experience", updatedFilters.experience.join(","));
    } else {
      params.delete("experience");
    }

    // Update sort options
    params.set("sort_by", updatedFilters.sortBy);
    params.set("sort_order", updatedFilters.sortOrder);

    // Update rating filters
    if (updatedFilters.minRating > 0) {
      params.set("min_rating", updatedFilters.minRating.toString());
    } else {
      params.delete("min_rating");
    }

    if (updatedFilters.maxRating < 5) {
      params.set("max_rating", updatedFilters.maxRating.toString());
    } else {
      params.delete("max_rating");
    }

    // Update experience range filters
    if (updatedFilters.minExperience > 0) {
      params.set("min_experience", updatedFilters.minExperience.toString());
    } else {
      params.delete("min_experience");
    }

    if (updatedFilters.maxExperience < 50) {
      params.set("max_experience", updatedFilters.maxExperience.toString());
    } else {
      params.delete("max_experience");
    }

    // Update boolean filters
    if (updatedFilters.hasPreview) {
      params.set("has_preview", "true");
    } else {
      params.delete("has_preview");
    }

    if (updatedFilters.hasPrograms) {
      params.set("has_programs", "true");
    } else {
      params.delete("has_programs");
    }

    setSearchParams(params);
  };

  const handleGenderChange = (value: string) => {
    const newGender = filters.gender.includes(value)
      ? filters.gender.filter(g => g !== value)
      : [...filters.gender, value];
    updateFilters({ gender: newGender });
  };

  const handleExperienceChange = (value: string) => {
    const newExperience = filters.experience.includes(value)
      ? filters.experience.filter(e => e !== value)
      : [...filters.experience, value];
    updateFilters({ experience: newExperience });
  };

  const handleSortChange = (value: string) => {
    // For price sorting, use ascending order (low to high)
    // For other options, use descending order
    const sortOrder = value === "price" ? "asc" : "desc";
    updateFilters({ sortBy: value, sortOrder });
  };

  const handleSortOrderChange = () => {
    const newOrder = filters.sortOrder === "asc" ? "desc" : "asc";
    updateFilters({ sortOrder: newOrder });
  };

  const clearAllFilters = () => {
    setFilters({
      gender: [],
      experience: [],
      sortBy: "created_at",
      sortOrder: "desc",
      minRating: 0,
      maxRating: 5,
      minExperience: 0,
      maxExperience: 50,
      hasPreview: false,
      hasPrograms: false,
    });

    // Clear all filter params from URL
    const params = new URLSearchParams(searchParams);
    params.delete("gender");
    params.delete("experience");
    params.delete("sort_by");
    params.delete("sort_order");
    params.delete("min_rating");
    params.delete("max_rating");
    params.delete("min_experience");
    params.delete("max_experience");
    params.delete("has_preview");
    params.delete("has_programs");
    setSearchParams(params);
  };

  const hasActiveFilters =
    filters.gender.length > 0 ||
    filters.experience.length > 0 ||
    filters.sortBy !== "created_at" ||
    filters.sortOrder !== "desc" ||
    filters.minRating > 0 ||
    filters.maxRating < 5 ||
    filters.minExperience > 0 ||
    filters.maxExperience < 50 ||
    filters.hasPreview ||
    filters.hasPrograms;

  const handleApplyFilters = () => {
    // If we're not on the home page, navigate to home with current filters
    if (location.pathname !== "/") {
      // Preserve all current search parameters when navigating to home
      navigate(`/?${searchParams.toString()}`);
    }
    // Close the filter panel
    onClose();
  };

  const panelStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  const checkboxStyles = {
    accentColor: THEME_COLORS[mode].PRIMARY,
  };

  const buttonStyles = {
    backgroundColor: THEME_COLORS[mode].PRIMARY,
    color: "white",
  };

  const secondaryButtonStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_HOVER,
    color: THEME_COLORS[mode].TEXT,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  if (!isOpen) return null;

  return (
    <Modal 
    isOpen={isOpen} title="Filters" 
    modalCloseClick={onClose} 
    modalHeader
    classes={{
      modal: "h-full",
      modalDialog: "min-h-fit max-h-[90vh] w-full max-w-xl",
      modalContent: "!p-0 !m-0",
    }}
    >
      {/* <h3 className="text-lg font-medium">Filters</h3>
      <button
        onClick={onClose}
        className="p-1 rounded-md hover:opacity-70 transition-opacity duration-200"
      >
        <XMarkIcon className="h-5 w-5" style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }} />
      </button> */}
    
          <div className="w-full">
            <div
              className="rounded-lg transition-colors duration-200"
              style={panelStyles}
            >

              {/* Content */}
              <div className="p-6 space-y-6">
                {/* Gender Filter */}
                <div>
                  <h4 className="font-medium mb-3">Gender</h4>
                  <div className="space-y-2">
                    {genderOptions.map((option) => (
                      <label key={option.value} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={filters.gender.includes(option.value)}
                          onChange={() => handleGenderChange(option.value)}
                          className="rounded"
                          style={checkboxStyles}
                        />
                        <span className="text-sm">{option.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Experience Filter */}
                <div>
                  <h4 className="font-medium mb-3">Experience</h4>
                  <div className="space-y-2">
                    {experienceOptions.map((option) => (
                      <label key={option.value} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={filters.experience.includes(option.value)}
                          onChange={() => handleExperienceChange(option.value)}
                          className="rounded"
                          style={checkboxStyles}
                        />
                        <span className="text-sm">{option.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Sort Options */}
                <div>
                  <h4 className="font-medium mb-3">Sort By</h4>
                  <div className="space-y-2">
                    {sortOptions.map((option) => (
                      <label key={option.value} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="radio"
                          name="sortBy"
                          checked={filters.sortBy === option.value}
                          onChange={() => handleSortChange(option.value)}
                          className="rounded"
                          style={checkboxStyles}
                        />
                        <span className="text-sm">{option.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Sort Order Toggle */}
                {/* <div>
                  <h4 className="font-medium mb-3">Sort Order</h4>
                  <button
                    onClick={handleSortOrderChange}
                    className="px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                    style={secondaryButtonStyles}
                  >
                    {filters.sortOrder === "asc" ? "Ascending" : "Descending"}
                  </button>
                </div> */}

                {/* Rating Range */}
                {/* <div>
                <h4 className="font-medium mb-3">Rating Range</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="0"
                      max="5"
                      step="0.5"
                      value={filters.minRating}
                      onChange={(e) => updateFilters({ minRating: parseFloat(e.target.value) })}
                      className="flex-1"
                      style={checkboxStyles}
                    />
                    <span className="text-sm w-12">{filters.minRating}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="0"
                      max="5"
                      step="0.5"
                      value={filters.maxRating}
                      onChange={(e) => updateFilters({ maxRating: parseFloat(e.target.value) })}
                      className="flex-1"
                      style={checkboxStyles}
                    />
                    <span className="text-sm w-12">{filters.maxRating}</span>
                  </div>
                </div>
              </div> */}

                {/* Experience Range */}
                {/* <div>
                <h4 className="font-medium mb-3">Experience Range (Years)</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="0"
                      max="50"
                      step="1"
                      value={filters.minExperience}
                      onChange={(e) => updateFilters({ minExperience: parseInt(e.target.value) })}
                      className="flex-1"
                      style={checkboxStyles}
                    />
                    <span className="text-sm w-12">{filters.minExperience}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="0"
                      max="50"
                      step="1"
                      value={filters.maxExperience}
                      onChange={(e) => updateFilters({ maxExperience: parseInt(e.target.value) })}
                      className="flex-1"
                      style={checkboxStyles}
                    />
                    <span className="text-sm w-12">{filters.maxExperience}</span>
                  </div>
                </div>
              </div> */}

                {/* Boolean Filters */}
                {/* <div className="space-y-3">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.hasPreview}
                    onChange={(e) => updateFilters({ hasPreview: e.target.checked })}
                    className="rounded"
                    style={checkboxStyles}
                  />
                  <span className="text-sm">Has Preview</span>
                </label>

                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.hasPrograms}
                    onChange={(e) => updateFilters({ hasPrograms: e.target.checked })}
                    className="rounded"
                    style={checkboxStyles}
                  />
                  <span className="text-sm">Has Programs</span>
                </label>
              </div> */}
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between p-6 border-t" style={{ borderTopColor: THEME_COLORS[mode].BORDER }}>
                <button
                  onClick={clearAllFilters}
                  className="text-sm text-red-500 hover:text-red-700 transition-colors duration-200"
                >
                  Clear All
                </button>
                <div className="flex space-x-3">
                  <button
                    onClick={onClose}
                    className="px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                    style={secondaryButtonStyles}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleApplyFilters}
                    className="px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                    style={buttonStyles}
                  >
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          </div>

    </Modal>
  );
};

export default FilterPanel; 