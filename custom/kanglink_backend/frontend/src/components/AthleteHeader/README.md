# AthleteHeader Component

A responsive, theme-aware header component designed for the athlete interface of the Kanga Sportlink application.

## Features

- **Responsive Design**: Adapts to different screen sizes with mobile-first approach
- **Theme Support**: Full dark and light theme support with smooth transitions
- **Search Functionality**: Integrated search bar with filter options
- **Navigation Icons**: Quick access to calendar, notifications, messages, and profile
- **Accessibility**: Proper semantic HTML and keyboard navigation support
- **Sticky Header**: Remains at the top of the page during scroll

## Design Specifications

The component follows the Figma design specifications and matches the UI exactly:
- Height: 80px (5rem)
- Responsive breakpoints: mobile (< 768px), tablet (768px+), desktop (1024px+)
- Theme-aware colors using the application's theme system
- Proper spacing and typography using Inter font family

## Responsive Behavior

### Mobile (< 768px)
- Logo on the left
- Search bar hidden, replaced with search icon
- Essential icons: search, notifications, profile
- Compact spacing

### Tablet (768px+)
- Full search bar visible in center
- Additional icons: calendar, messages
- Expanded spacing

### Desktop (1024px+)
- Maximum width container (7xl)
- Full feature set visible
- Optimal spacing and layout

## Theme Integration

The component uses the application's theme system:

```typescript
// Theme colors are automatically applied based on current theme
const headerStyles = {
  backgroundColor: THEME_COLORS[mode].BACKGROUND,
  borderBottomColor: THEME_COLORS[mode].BORDER,
  color: THEME_COLORS[mode].TEXT,
};
```

### Light Theme
- Background: White
- Text: Dark colors
- Primary: Green (#4CBF6D)
- Borders: Light gray

### Dark Theme
- Background: Dark (#1A1A1A)
- Text: Light colors (#F6F7F8)
- Primary: Green (#4CBF6D)
- Borders: Dark gray

## Usage

```tsx
import { AthleteHeader } from '@/components/AthleteHeader';

// Basic usage
<AthleteHeader />

// Used within AthleteWrapper
<AthleteWrapper>
  <YourPageContent />
</AthleteWrapper>
```

## Component Structure

```
AthleteHeader/
├── AthleteHeader.tsx    # Main component
├── index.ts            # Lazy-loaded export
└── README.md          # This documentation
```

## Dependencies

- React Router DOM (for navigation)
- Heroicons (for icons)
- Theme context (for theme support)
- Tailwind CSS (for styling)

## Icons Used

- `MagnifyingGlassIcon`: Search functionality
- `AdjustmentsHorizontalIcon`: Search filters
- `CalendarIcon`: Calendar access
- `BellIcon`: Notifications
- `ChatBubbleLeftIcon`: Messages
- `UserCircleIcon`: User profile

## Accessibility

- Semantic HTML structure with proper `<header>` element
- Keyboard navigation support
- Proper focus states
- Screen reader friendly
- High contrast ratios in both themes

## Testing

Unit tests are available in `src/test/unit/AthleteHeader.test.tsx` covering:
- Component rendering
- Search functionality
- Responsive behavior
- Theme integration
- Navigation links

## Performance

- Lazy-loaded export for code splitting
- Optimized re-renders with proper state management
- Smooth transitions without layout shifts
- Minimal bundle impact

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Responsive design tested across devices

## Future Enhancements

- Search autocomplete functionality
- Notification dropdown
- User profile dropdown
- Advanced search filters
- Keyboard shortcuts
- Voice search integration
