import { useState, useEffect } from "react";
import { Link, useLocation, useNavigate, useSearchParams } from "react-router-dom";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { useProfile } from "@/hooks/useProfile";
import { useContexts } from "@/hooks/useContexts";
import { Menu } from "@headlessui/react";
import {
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  Squares2X2Icon,
  // CalendarIcon,
  BellIcon,
  ChatBubbleLeftIcon,
  UserCircleIcon,
  ChevronDownIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { RoleMap } from "@/utils";
import { NotificationPopover } from "./NotificationPopover";
import { useNotifications } from "@/hooks/useNotifications";
import FilterPanel from "@/components/FilterPanel";
// import { LazyLoad } from "@/components/LazyLoad";
// import { ThemeToggle } from "@/components/ThemeToggle";
const ProfilePortals = {
  member: "/athlete/profile",
  trainer: "/trainer/profile",
  super_admin: "/admin/profile/view",
}
const AthleteHeader = () => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [searchValue, setSearchValue] = useState("");
  const [showMobileSearch, setShowMobileSearch] = useState(false);
  const [notificationPopoverOpen, setNotificationPopoverOpen] = useState(false);
  const [filterPanelOpen, setFilterPanelOpen] = useState(false);
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const location = useLocation();

  const { profile } = useProfile({isPublic: true});
  const { authDispatch, authState } = useContexts();
  const { isAuthenticated } = authState;
  const pageView = searchParams.get("page-view") || "trainers"

  // Notifications hook
  const { unreadCount, refetchUnreadCount } = useNotifications();

  const togglePageView = () => {
    const currentPath = location.pathname;
    if (currentPath !== "/") {
      const currentPageView =  "programs";
      navigate(`/?page-view=${currentPageView}`);
    } else {
      if (pageView === "programs") {
        setSearchParams({ "page-view": "trainers" });
      } else {
        setSearchParams({ "page-view": "programs" });
      }
    }
  };
  // Handle search functionality
  const handleSearch = (searchTerm: string) => {
    if (searchTerm.trim()) {
      // Navigate to home page with search params
      console.log("searchValue", searchValue);
      // Don't manually encode - setSearchParams will handle encoding
      const search = searchTerm.trim();
      console.log("search", search);
      setSearchParams({ search, "page-view": pageView });
    } else {
      // Clear search by navigating to home without params
      setSearchParams({ "page-view": pageView, });
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch(searchValue);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {

    const value = (e.target.value);
    console.log(value);
    
    setSearchValue(value);

    // Optional: Implement debounced search for real-time results
    // For now, we'll only search on Enter or form submit
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch(searchValue);
    }
  };

  const handleClearSearch = () => {
    setSearchValue("");
    navigate("/");
  };

  // Handle notification popover
  const handleNotificationToggle = () => {
    setNotificationPopoverOpen(!notificationPopoverOpen);
  };

  const handleNotificationClose = () => {
    setNotificationPopoverOpen(false);
  };

  const handleFilterToggle = () => {
    setFilterPanelOpen(!filterPanelOpen);
  };

  const handleFilterClose = () => {
    setFilterPanelOpen(false);
  };


  const headerStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND,
    borderBottomColor: THEME_COLORS[mode].BORDER,
    color: THEME_COLORS[mode].TEXT,
  };

  const searchInputStyles = {
    backgroundColor: THEME_COLORS[mode].INPUT,
    color: THEME_COLORS[mode].TEXT,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const logoStyles = {
    color: THEME_COLORS[mode].PRIMARY,
  };

  const iconStyles = {
    color: THEME_COLORS[mode].TEXT_SECONDARY,
  };

  // Sync search input with URL params on mount and when URL changes
  useEffect(() => {
    const urlSearchTerm = searchParams.get("search") || "";
    // Decode the URL parameter for display in the input
    setSearchValue(decodeURIComponent(urlSearchTerm));
  }, [searchParams]);

  return (
    <header
      className="w-full h-20 border-b transition-colors duration-200 sticky top-0 z-50"
      style={headerStyles}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
        <div className="flex items-center justify-between h-full">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link
              to="/"
              className="flex items-center hover:opacity-80 transition-opacity duration-200"
            >
              <h1
                className="text-xl md:text-2xl font-bold font-inter transition-colors duration-200"
                style={logoStyles}
              >
                TrainerIQ
              </h1>
            </Link>
          </div>

          {/* Search Bar - Hidden on mobile, visible on tablet and up */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8">
            <form onSubmit={handleSearchSubmit} className="relative w-full">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon
                  className="h-4 w-4 transition-colors duration-200"
                  style={iconStyles}
                />
              </div>
              <input
                type="text"
                value={searchValue}
                onChange={(e: any) => {
                  if(e.key !== "Enter") {
                    handleSearchChange(e)
                  }
                }}
                onKeyDown={handleKeyDown}
                placeholder="Search programs, trainers, or categories..."
                className="block w-full pl-10 pr-12 py-3 border rounded-lg text-sm placeholder-text-disabled focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
                style={searchInputStyles}
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center space-x-2">
                {searchValue && (
                  <XMarkIcon
                    className="h-4 w-4 cursor-pointer hover:opacity-70 transition-opacity duration-200"
                    style={iconStyles}
                    onClick={handleClearSearch}
                  />
                )}
                <AdjustmentsHorizontalIcon
                  className="h-4 w-4 cursor-pointer hover:opacity-70 transition-opacity duration-200"
                  style={iconStyles}
                  onClick={handleFilterToggle}
                />
              </div>
            </form>
          </div>

          {/* Right Side Icons */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {/* Mobile Search Icon */}
            <button
              className="md:hidden p-2 rounded-lg transition-colors duration-200"
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor =
                  THEME_COLORS[mode].BACKGROUND_HOVER;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "transparent";
              }}
              onClick={() => setShowMobileSearch(!showMobileSearch)}
            >
              <MagnifyingGlassIcon className="h-5 w-5" style={iconStyles} />
            </button>

            {/* Calendar Icon */}
            {/* <button
              className="hidden sm:block p-2 rounded-lg transition-colors duration-200"
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor =
                  THEME_COLORS[mode].BACKGROUND_HOVER;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "transparent";
              }}
            >
              <CalendarIcon className="h-5 w-5" style={iconStyles} />
            </button> */}

            {/* icon to toggle page search query in the url, when clicked, simply update the url ?page-view=programs, and update the page view state */}
            <button
              className="p-2 rounded-lg transition-colors duration-200"
              onClick={() => togglePageView()}
            >
              {/* Grid view icon for switching between trainers and programs */}
              <Squares2X2Icon className="h-5 w-5" style={iconStyles} />
            </button>
            {/* Notifications Icon */}
            {isAuthenticated ? (
              <NotificationPopover
                isOpen={notificationPopoverOpen}
                onClose={handleNotificationClose}
                onToggle={handleNotificationToggle}
                unreadCount={unreadCount}
                refetchUnreadCount={refetchUnreadCount}
              />
            ) : (
              <button
                className="p-2 rounded-lg transition-colors duration-200 relative"
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor =
                    THEME_COLORS[mode].BACKGROUND_HOVER;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "transparent";
                }}
                onClick={() => navigate("/login")}
              >
                <BellIcon className="h-5 w-5" style={iconStyles} />
              </button>
            )}

            {/* Messages Icon - Hidden on mobile, visible on larger screens */}
            {["member", "trainer"].includes(profile?.role) && <button
              className="hidden sm:block p-2 rounded-lg transition-colors duration-200"
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor =
                  THEME_COLORS[mode].BACKGROUND_HOVER;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "transparent";
              }}
              onClick={() => {
                if (!isAuthenticated) {
                  navigate("/login");
                } else {
                  navigate(`/${RoleMap[profile?.role]}/feed`);
                }
              }}
            >
              <ChatBubbleLeftIcon className="h-5 w-5" style={iconStyles} />
            </button>}

            {/* Theme Toggle */}
            {/* <LazyLoad>
              <ThemeToggle className="transition-all duration-200 hover:scale-105" />
            </LazyLoad> */}

            {/* Authentication Menu */}
            <Menu as="div" className="relative">
              <Menu.Button className="flex items-center p-2 rounded-lg transition-colors duration-200 hover:opacity-80">
                {isAuthenticated ? (
                  /* Show profile image/icon for authenticated users */
                  <>
                    {profile?.photo ? (
                      <img
                        className="h-8 w-8 rounded-full object-cover border-2"
                        src={profile.photo}
                        alt={`${profile.first_name} ${profile.last_name}`}
                        style={{ borderColor: THEME_COLORS[mode].BORDER }}
                      />
                    ) : (
                      <UserCircleIcon className="h-6 w-6" style={iconStyles} />
                    )}
                  </>
                ) : (
                  /* Show generic user icon for non-authenticated users */
                  <UserCircleIcon className="h-6 w-6" style={iconStyles} />
                )}
                <ChevronDownIcon className="ml-1 h-4 w-4" style={iconStyles} />
              </Menu.Button>

              <Menu.Items
                className="absolute right-0 mt-2 w-48 origin-top-right divide-y rounded-md border shadow-lg ring-1 ring-opacity-5 focus:outline-none transition-colors duration-200"
                style={{
                  backgroundColor: THEME_COLORS[mode].BACKGROUND,
                  borderColor: THEME_COLORS[mode].BORDER,
                  color: THEME_COLORS[mode].TEXT,
                }}
              >
                <div className="px-1 py-1">
                  {isAuthenticated ? (
                    /* Menu items for authenticated users */
                    <>
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            type="button"
                            className="group flex w-full items-center px-3 py-3 text-sm border-b transition-all duration-200 hover:opacity-80"
                            style={{
                              borderBottomColor: active
                                ? `${THEME_COLORS[mode].BORDER}80`
                                : "transparent",
                              color: THEME_COLORS[mode].TEXT,
                            }}
                            onClick={() =>
                              navigate(`${ProfilePortals[profile?.role as keyof typeof ProfilePortals]}`)
                            }
                          >
                            Account
                          </button>
                        )}
                      </Menu.Item>
                      {/* Feed Menu Item - Visible on mobile */}
                     {["member", "trainer"].includes(profile?.role) && <Menu.Item>
                        {({ active }) => (
                          <button
                            type="button"
                            className="group flex w-full items-center px-3 py-3 text-sm border-b transition-all duration-200 hover:opacity-80 sm:hidden"
                            style={{
                              borderBottomColor: active
                                ? `${THEME_COLORS[mode].BORDER}80`
                                : "transparent",
                              color: THEME_COLORS[mode].TEXT,
                            }}
                            onClick={() => navigate(`/${RoleMap[profile?.role]}/feed`)}
                          >
                            <ChatBubbleLeftIcon
                              className="mr-2 h-4 w-4"
                              style={iconStyles}
                            />
                            Feed
                          </button>
                        )}
                      </Menu.Item>}

                      {["member"].includes(profile?.role) ? (
                        <>
                          <Menu.Item>
                            {({ active }) => (
                              <button
                                type="button"
                                className="group flex w-full items-center px-3 py-3 text-sm border-b transition-all duration-200 hover:opacity-80"
                                style={{
                                  borderBottomColor: active
                                    ? `${THEME_COLORS[mode].BORDER}80`
                                    : "transparent",
                                  color: THEME_COLORS[mode].TEXT,
                                }}
                                onClick={() =>
                                  navigate(`/${RoleMap[profile?.role]}/library`)
                                }
                              >
                                Library
                              </button>
                            )}
                          </Menu.Item>
                        </>
                      ) : null}

                      <Menu.Item>
                        {({ active }) => (
                          <button
                            type="button"
                            className="group flex w-full items-center px-3 py-3 text-sm transition-all duration-200 hover:opacity-80"
                            style={{
                              borderBottomColor: active
                                ? `${THEME_COLORS[mode].BORDER}80`
                                : "transparent",
                              color: "#CE0000", // Keep logout button red
                            }}
                            onClick={() => {
                              authDispatch({ type: "LOGOUT" });
                              navigate("/login");
                            }}
                          >
                            Logout
                          </button>
                        )}
                      </Menu.Item>
                    </>
                  ) : (
                    /* Menu items for non-authenticated users */
                    <>
                      <Menu.Item>
                        {({ active }) => (
                          <button
                            type="button"
                            className="group flex w-full items-center px-3 py-3 text-sm border-b transition-all duration-200 hover:opacity-80"
                            style={{
                              borderBottomColor: active
                                ? `${THEME_COLORS[mode].BORDER}80`
                                : "transparent",
                              color: THEME_COLORS[mode].TEXT,
                            }}
                            onClick={() => navigate("/login")}
                          >
                            Sign In
                          </button>
                        )}
                      </Menu.Item>

                      <Menu.Item>
                        {({ active }) => (
                          <button
                            type="button"
                            className="group flex w-full items-center px-3 py-3 text-sm transition-all duration-200 hover:opacity-80"
                            style={{
                              borderBottomColor: active
                                ? `${THEME_COLORS[mode].BORDER}80`
                                : "transparent",
                              color: THEME_COLORS[mode].TEXT,
                            }}
                            onClick={() => navigate("/athlete/signup")}
                          >
                            Sign Up
                          </button>
                        )}
                      </Menu.Item>
                    </>
                  )}
                </div>
              </Menu.Items>
            </Menu>
          </div>
        </div>
      </div>

      {/* Mobile Search Bar - Shows when mobile search icon is clicked */}
      {showMobileSearch && (
        <div
          className="md:hidden px-4 pb-4 border-b transition-colors duration-200"
          style={{
            backgroundColor: THEME_COLORS[mode].BACKGROUND,
            borderBottomColor: THEME_COLORS[mode].BORDER,
          }}
        >
          <form onSubmit={handleSearchSubmit} className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon
                className="h-4 w-4 transition-colors duration-200"
                style={iconStyles}
              />
            </div>
            <input
              type="text"
              value={searchValue}
              onChange={handleSearchChange}
              onKeyDown={handleKeyDown}
              placeholder="Search programs, trainers, or categories..."
              className="block w-full pl-10 pr-12 py-3 border rounded-lg text-sm placeholder-text-disabled focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200"
              style={searchInputStyles}
              autoFocus
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center space-x-2">
              {searchValue && (
                <XMarkIcon
                  className="h-4 w-4 cursor-pointer hover:opacity-70 transition-opacity duration-200"
                  style={iconStyles}
                  onClick={handleClearSearch}
                />
              )}
              <AdjustmentsHorizontalIcon
                className="h-4 w-4 cursor-pointer hover:opacity-70 transition-opacity duration-200"
                style={iconStyles}
                onClick={handleFilterToggle}
              />
            </div>
          </form>
        </div>
      )}

      {/* Filter Panel */}
      <FilterPanel
        isOpen={filterPanelOpen}
        onClose={handleFilterClose}
        pageView={pageView as "trainers" | "programs"}
      />
    </header>
  );
};

export default AthleteHeader;
