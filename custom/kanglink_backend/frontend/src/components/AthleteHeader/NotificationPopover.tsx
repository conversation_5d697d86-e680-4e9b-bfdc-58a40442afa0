import React, { useState, useEffect } from "react";
import { BellI<PERSON>, CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { useNotifications } from "@/hooks/useNotifications";
import { useNavigate } from "react-router-dom";

interface Notification {
  id: number;
  notification_type: string;
  category: string;
  title: string;
  message: string;
  data: any;
  is_read: boolean;
  read_at: string | null;
  created_at: string;
  sender_id: number | null;
  sender_name: string | null;
  sender_email: string | null;
  related_id: number | null;
  related_type: string | null;
}

interface NotificationPopoverProps {
  isOpen: boolean;
  onClose: () => void;
  onToggle: () => void;
  unreadCount: number;
  refetchUnreadCount: () => void;
}

export const NotificationPopover: React.FC<NotificationPopoverProps> = ({
  isOpen,
  onClose,
  onToggle,
  unreadCount,
  refetchUnreadCount,
}) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const navigate = useNavigate();
  const { notifications, loading, markAsRead: markAsReadHook, markAllAsRead: markAllAsReadHook, fetchNotifications } = useNotifications();
  const [markingAsRead, setMarkingAsRead] = useState<number | null>(null);

  // Mark notification as read with loading state
  const handleMarkAsRead = async (notificationId: number) => {
    try {
      setMarkingAsRead(notificationId);
      await markAsReadHook(notificationId);
      refetchUnreadCount();
    } catch (error) {
      console.error("Error marking notification as read:", error);
    } finally {
      setMarkingAsRead(null);
    }
  };

  // Mark all notifications as read
  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsReadHook();
      refetchUnreadCount();
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "new_enrollment":
        return "🎯";
      case "payment_received":
        return "💰";
      case "exercise_completed":
      case "day_completed":
      case "week_completed":
      case "program_completed":
      case "milestone_reached":
        return "🏆";
      case "program_updated":
        return "📝";
      case "athlete_message":
        return "💬";
      case "system_alert":
        return "⚠️";
      case "refund_requested":
      case "refund_approved":
      case "refund_rejected":
        return "🔄";
      case "post_feed_created":
        return "📢";
      case "post_feed_comment":
        return "💭";
      default:
        return "🔔";
    }
  };

  // Format notification time
  const formatNotificationTime = (createdAt: string) => {
    const date = new Date(createdAt);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  // Fetch notifications when popover opens
  useEffect(() => {
    if (isOpen) {
      // Notifications are fetched automatically by the hook
    }
  }, [isOpen]);

  const iconStyles = {
    color: THEME_COLORS[mode].TEXT_SECONDARY,
  };

  return (
    <div className="relative">
      {/* Notification Bell Button */}
      <button
        className="p-2 rounded-lg transition-colors duration-200 relative"
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = THEME_COLORS[mode].BACKGROUND_HOVER;
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = "transparent";
        }}
        onClick={onToggle}
      >
        <BellIcon className="h-5 w-5" style={iconStyles} />
        {/* Notification badge */}
        {unreadCount > 0 && (
          <span className={`absolute -top-1 -right-1 bg-red-500 p-2 rounded-full flex items-center justify-center ${unreadCount > 99 ? "h-fit w-fit" : "h-3 w-3"}`}>
            <span className="text-xs w-fit text-white font-bold">
              {unreadCount > 99 ? "99+" : unreadCount}
            </span>
          </span>
        )}
      </button>

      {/* Notification Popover */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40"
            onClick={onClose}
          />
          
          {/* Popover Content */}
          <div className="absolute right-0 top-full mt-2 w-80 bg-card-bg border border-border rounded-lg shadow-lg z-50 max-h-96 overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-border">
              <h3 className="text-text font-semibold">Notifications</h3>
              <div className="flex items-center gap-2">
                                 {notifications.some(n => !n.is_read) && (
                   <button
                     onClick={handleMarkAllAsRead}
                     className="text-xs text-primary hover:text-primary/80 transition-colors"
                   >
                     Mark all read
                   </button>
                 )}
                <button
                  onClick={onClose}
                  className="p-1 rounded hover:bg-background-hover transition-colors"
                >
                  <XMarkIcon className="h-4 w-4 text-text" />
                </button>
              </div>
            </div>

            {/* Notifications List */}
            <div className="max-h-80 overflow-y-auto">
              {loading ? (
                <div className="p-4 text-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                  <p className="text-text-disabled text-sm mt-2">Loading notifications...</p>
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-4 text-center">
                  <BellIcon className="h-8 w-8 text-text-disabled mx-auto mb-2" />
                  <p className="text-text-disabled text-sm">No notifications yet</p>
                </div>
              ) : (
                <div className="divide-y divide-border">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 hover:bg-background-hover transition-colors ${
                        !notification.is_read ? "bg-background-hover/50" : ""
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        {/* Notification Icon */}
                        <div className="flex-shrink-0">
                          <span className="text-lg">
                            {getNotificationIcon(notification.notification_type)}
                          </span>
                        </div>

                        {/* Notification Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2">
                            <div className="flex-1">
                              <h4 className="text-text font-medium text-sm">
                                {notification.title}
                              </h4>
                              <p className="text-text-disabled text-xs mt-1">
                                {notification.message}
                              </p>
                              <p className="text-text-disabled text-xs mt-2">
                                {formatNotificationTime(notification.created_at)}
                              </p>
                            </div>

                                                         {/* Mark as Read Button */}
                             {!notification.is_read && (
                               <button
                                 onClick={() => handleMarkAsRead(notification.id)}
                                 disabled={markingAsRead === notification.id}
                                 className="flex-shrink-0 p-1 rounded hover:bg-background-hover transition-colors disabled:opacity-50"
                                 title="Mark as read"
                               >
                                {markingAsRead === notification.id ? (
                                  <div className="animate-spin rounded-full h-3 w-3 border-b border-primary"></div>
                                ) : (
                                  <CheckIcon className="h-3 w-3 text-text-disabled" />
                                )}
                              </button>
                            )}
                          </div>

                          {/* Unread Indicator */}
                          {!notification.is_read && (
                            <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {notifications.length > 0 && (
              <div className="p-3 border-t border-border bg-background-hover/30">
                <button
                  onClick={() => {
                    // Navigate to full notifications page
                    navigate("/athlete/notifications");
                    onClose(); // Close the popover
                  }}
                  className="w-full text-center text-xs text-primary hover:text-primary/80 transition-colors"
                >
                  View all notifications
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}; 