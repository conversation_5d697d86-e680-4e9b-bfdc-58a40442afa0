/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from "react";
import { Container } from "@/components/Container";
import {
  CourseDetailsSection,
  ProgramStructureSection,
  ActionButtons,
} from "./components";

interface CreateProgramPreviewProps {
  componentId?: number;
  onSubmit?: () => void;
  onCancel?: () => void;
  stepOneData?: any;
  stepTwoData?: any;
  refetch?: (id: string) => void;
}

const CreateProgramPreview: React.FC<CreateProgramPreviewProps> = ({
  componentId: _componentId,
  onSubmit,
  onCancel,
  stepOneData,
  stepTwoData,
  refetch,
}) => {
  const [weeks, setWeeks] = useState<any[]>([]);
  const [selectedSplit, setSelectedSplit] = useState<string>("");
  const [availableSplits, setAvailableSplits] = useState<
    Array<{
      value: string;
      label: string;
      split_id: string;
    }>
  >([]);

  // Get available splits from stepOneData
  const getAvailableSplits = () => {
    if (!stepOneData?.splits || !stepTwoData?.splitConfigurations) return [];
    return stepOneData.splits
      .filter((split: any) => stepTwoData.splitConfigurations[split.split_id])
      .map((split: any) => ({
        value: split.split_id,
        label: split.title || `Split ${split.split_id}`,
        split_id: split.split_id,
      }));
  };

  // Initialize selected split with the first available split
  useEffect(() => {
    const availableSplits = getAvailableSplits();
    setAvailableSplits(availableSplits); // Set availableSplits state to trigger re-render
    if (availableSplits.length > 0 && !selectedSplit) {
      setSelectedSplit(availableSplits[0].value);
      setWeeks(stepTwoData?.splitConfigurations?.[availableSplits[0].value]);
    }
  }, [stepOneData, stepTwoData]);

  const handleSplitChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const splitId = event.target.value;
    setSelectedSplit(splitId);

    const weeks = stepTwoData?.splitConfigurations?.[splitId];
    setWeeks(weeks);
  };
return (
    <Container className="max-w-none">
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-4 sm:mb-6">
          <h1 className="text-xl sm:text-2xl font-bold text-text break-words">
            {stepOneData?.program_name || "Course Title"}
          </h1>
        </div>

        {/* Main Content Card */}
        <div className="bg-background rounded-lg border border-border shadow-sm overflow-hidden">
          <div className="p-4 sm:p-6 space-y-6 sm:space-y-8">
            {/* Course Details Section */}
            <CourseDetailsSection
              stepOneData={stepOneData}
              stepTwoData={stepTwoData}
              selectedSplit={selectedSplit}
              onSplitChange={handleSplitChange}
              availableSplits={availableSplits}
              refetch={refetch}
            />

            {/* Program Structure Section */}
            <ProgramStructureSection
              stepTwoData={stepTwoData}
              stepOneData={stepOneData}
              selectedSplit={selectedSplit}
              onSplitChange={handleSplitChange}
              availableSplits={availableSplits}
              weeks={weeks}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <ActionButtons onCancel={onCancel} onSubmit={onSubmit} />
      </div>
    </Container>
  );
};

export default CreateProgramPreview;
