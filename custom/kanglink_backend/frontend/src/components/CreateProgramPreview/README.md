# CreateProgramPreview Component

A comprehensive preview component for displaying program creation data from both CreateProgramStepOne and CreateProgramStepTwo components.

## Features

- **Responsive Design**: Fully responsive layout that works on all screen sizes
- **Theme Support**: Full light/dark mode support using theme tokens
- **Collapsible Sections**: Interactive collapsible program structure (weeks, days, sessions, exercises)
- **Data Integration**: Seamlessly displays data from both wizard steps
- **Action Buttons**: Back and Done buttons for navigation

## Usage

```tsx
import { CreateProgramPreview } from "@/components/CreateProgramPreview";

<CreateProgramPreview
  componentId={3}
  onSubmit={handleFinalSubmit}
  onCancel={handleCancel}
  stepOneData={stepOneData}
  stepTwoData={stepTwoData}
/>;
```

## Props

| Prop          | Type         | Description                              |
| ------------- | ------------ | ---------------------------------------- |
| `componentId` | `number`     | Optional component identifier for wizard |
| `onSubmit`    | `() => void` | Callback for final form submission       |
| `onCancel`    | `() => void` | Callback for canceling/going back        |
| `stepOneData` | `any`        | Data from CreateProgramStepOne           |
| `stepTwoData` | `any`        | Data from CreateProgramStepTwo           |

## Data Structure

**Note**: All field names follow snake_case convention as per database schema.

### stepOneData

- `program_name`: Program title
- `program_description`: Program description
- `type_of_program`: Program category
- `target_levels`: Array of target levels
- `track_progress`: Boolean for progress tracking
- `allow_comments`: Boolean for comments
- `allow_private_messages`: Boolean for private messages
- `splits`: Array of split configurations
- `days_for_preview`: Number of preview days

### stepTwoData

- `splitConfigurations`: Object containing weeks data for each split
- Each week contains days, sessions, and exercises

### Exercise Data Structure

- `reps_or_time`: Repetitions or time value
- `reps_time_type`: "reps" | "time"
- `video_url`: Exercise video URL
- `rest_duration_minutes`: Rest duration in minutes
- `rest_duration_seconds`: Rest duration in seconds
- `is_linked`: Boolean for linked exercises
- `exercise_order`: Sequential ordering number
- `label`: Exercise group label (A, B, C)
- `label_number`: Exercise number within group (1, 2, 3)

### Day Data Structure

- `is_rest_day`: Boolean indicating if day is a rest day

## Components

### CourseDetailsSection

Displays program metadata in a two-column responsive layout.

### ProgramStructureSection

Shows the hierarchical program structure with collapsible sections.

### ExerciseCard

Individual exercise display with thumbnail, details, and collapse functionality.

### ActionButtons

Back and Done buttons with proper styling and theme support.

## Responsive Breakpoints

- **Mobile**: < 640px - Single column layout, stacked elements
- **Tablet**: 640px - 1024px - Improved spacing, some two-column layouts
- **Desktop**: > 1024px - Full two-column layout, optimal spacing

## Theme Support

Uses Tailwind theme tokens:

- `text-text` / `text-text-secondary` for text colors
- `bg-background` / `bg-input` for backgrounds
- `border-border` for borders
- `text-primary` for accent colors

## Testing

A test component `CreateProgramPreviewTest` is available to verify the component works correctly with snake_case data:

```tsx
import { CreateProgramPreviewTest } from "@/components/CreateProgramPreview";

// Use this component to test the preview with properly formatted data
<CreateProgramPreviewTest />;
```

## Accessibility

- Proper semantic HTML structure
- Keyboard navigation support
- Screen reader friendly
- Focus management for interactive elements

## Recent Updates

- **Snake_case Convention**: Updated all data field access to use snake_case naming convention
- **Exercise Labeling**: Implemented proper exercise labeling using `label` and `label_number` fields
- **Data Consistency**: Ensured all components consistently use snake_case field names
- **Test Data**: Added comprehensive test data demonstrating correct usage
