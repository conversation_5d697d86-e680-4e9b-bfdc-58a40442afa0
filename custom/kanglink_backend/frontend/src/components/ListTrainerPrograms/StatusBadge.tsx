import React from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { StatusBadgeProps } from "./types";
import { Program } from "@/interfaces";

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const getStatusStyles = (status: Program["status"]) => {
    switch (status?.toLowerCase()) {
      case "published":
        return {
          backgroundColor: "#10B981",
          color: "#FFFFFF",
          borderColor: "#10B981",
        };
      case "draft":
        return {
          backgroundColor: "#6B7280",
          color: "#FFFFFF",
          borderColor: "#6B7280",
        };
      case "pending_approval":
        return {
          backgroundColor: "#F59E0B",
          color: "#FFFFFF",
          borderColor: "#F59E0B",
        };
      case "rejected":
        return {
          backgroundColor: "#EF4444",
          color: "#FFFFFF",
          borderColor: "#EF4444",
        };
      case "archived":
        return {
          backgroundColor: "#A1A1A9",
          color: "#FFFFFF",
          borderColor: "#A1A1A9",
        };
      default:
        return {
          backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
          color: THEME_COLORS[mode].TEXT,
          borderColor: THEME_COLORS[mode].BORDER,
        };
    }
  };

  const styles = getStatusStyles(status);

  return (
    <span
      style={styles}
      className="inline-flex capitalize items-center px-2.5 py-0.5 rounded-full text-xs font-medium border"
    >
      {status?.replaceAll("_", " ")}
    </span>
  );
};

export default StatusBadge;
