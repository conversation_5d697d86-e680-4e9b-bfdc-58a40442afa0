import { Link } from "react-router-dom";
import moment from "moment";
import { ProgramsTableProps } from "./types";
import { StatusBadge, ActionMenu } from "./index";
import { LazyLoad } from "@/components/LazyLoad";
import { useContexts } from "@/hooks/useContexts";
import { ToastStatusEnum } from "@/utils/Enums";
import { Program } from "@/interfaces";
import { disableActionForDeactivatedTrainer, useTrainerStatus } from "@/hooks/useTrainerStatus";

const ProgramsTable = ({
  programs,
  isLoading,
  isError,
  error,
}: ProgramsTableProps) => {
  const { showToast } = useContexts();
  const { isDeactivated, canPerformActions, isVerified, isActive, statusMessage } = useTrainerStatus();
  // Function to copy affiliate link to clipboard
  const copyAffiliateLink = async (
    programDiscount: Program["program_discount"]
  ) => {
    // get code from affiliate_link
    if (!programDiscount) return;

    let discount = null;
    if (Array.isArray(programDiscount)) {
      if (programDiscount.length === 0) return;
      discount = programDiscount[0];
    } else {
      discount = programDiscount;
    }

    
    const affiliateLink = discount.affiliate_link
      ? `${window.location.origin}/athlete/program/${discount?.program_id}?ref=${discount.affiliate_link}`
      : "N/A";

    if (!affiliateLink || affiliateLink === "N/A") {
      showToast(
        "No affiliate link available to copy",
        3000,
        ToastStatusEnum.WARNING
      );
      return;
    }

    try {
      await navigator.clipboard.writeText(affiliateLink);
      showToast(
        "Affiliate link copied to clipboard!",
        2000,
        ToastStatusEnum.SUCCESS
      );
    } catch (error) {
      console.error("Failed to copy affiliate link:", error);
      showToast("Failed to copy affiliate link", 3000, ToastStatusEnum.ERROR);
    }
  };

  const isAffiliateLink = (programDiscount: Program["program_discount"]) => {
    if (!programDiscount) return false;
    if (Array.isArray(programDiscount)) {
      if (programDiscount.length === 0) return false;
      return !!programDiscount[0].affiliate_link;
    }
    return !!programDiscount.affiliate_link;
  };

  // Loading state component
  const LoadingRow = () => (
    <tr>
      <td
        colSpan={6}
        className="px-3 sm:px-6 py-8 text-center text-sm text-text-secondary"
      >
        <div className="flex flex-col items-center justify-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <div>Loading programs...</div>
        </div>
      </td>
    </tr>
  );

  // Error state component
  const ErrorRow = () => (
    <tr>
      <td
        colSpan={6}
        className="px-3 sm:px-6 py-8 text-center text-sm text-red-600"
      >
        <div className="flex flex-col items-center justify-center space-y-2">
          <div className="text-lg">⚠️</div>
          <div>Error loading programs</div>
          <div className="text-xs text-text-secondary">
            {error?.message || "Please try again later"}
          </div>
        </div>
      </td>
    </tr>
  );

  return (
    <section className="flex flex-col gap-4">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
        <h2 className="text-xl font-bold text-text">Programs</h2>
       {!isDeactivated && <Link
          to={"/trainer/add-program"}
          className="bg-primary hover:bg-primary-hover text-white font-medium px-4 py-2 rounded-md border border-primary transition-colors duration-200 flex items-center gap-2"
          type="button"
        >
          <span className="text-lg leading-none">+</span>
          New Program
        </Link>}
      </div>

      <div className="overflow-x-auto rounded-lg shadow-sm border border-border bg-background">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-table-header">
            <tr>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                Program ID #
                <span className="ml-1 text-gray-400 hidden sm:inline">↕</span>
              </th>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider hidden md:table-cell">
                Date Created
                <span className="ml-1 text-gray-400">↕</span>
              </th>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                Program Name
              </th>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider hidden lg:table-cell">
                Affiliate Link
              </th>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-text uppercase tracking-wider">
                Status
              </th>
              {!isDeactivated && <th className="px-3 sm:px-6 py-3 relative">
                <span className="sr-only">Actions</span>
              </th>}  
            </tr>
          </thead>
          <tbody className="bg-background divide-y divide-border">
            {isLoading ? (
              <LoadingRow />
            ) : isError ? (
              <ErrorRow />
            ) : programs.length > 0 ? (
              programs.map((program) => (
                <tr
                  key={program.id}
                  className="hover:bg-table-row-hover transition-colors duration-200"
                >
                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-text">
                    {program.id}
                  </td>
                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-text hidden md:table-cell">
                    {moment(program.created_at).format("MM/DD/YYYY")}
                  </td>
                  <td className="px-3 sm:px-6 py-4 text-sm text-text">
                    <div className="font-medium">{program.program_name}</div>
                    <div className="text-text-secondary text-xs md:hidden mt-1">
                      Created: {moment(program.created_at).format("MM/DD/YYYY")}
                    </div>
                  </td>
                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm hidden lg:table-cell">
                    <span
                      className="text-primary hover:text-primary-hover cursor-pointer transition-colors duration-200"
                      onClick={() =>
                        disableActionForDeactivatedTrainer(()=> {copyAffiliateLink(program.program_discount)}, showToast, { isDeactivated, canPerformActions, isVerified, isActive, statusMessage })
                      }
                      title="Click to copy affiliate link"
                    >
                      {isAffiliateLink(program.program_discount)
                        ? "Affiliate Link"
                        : "N/A"}{" "}
                      🔗
                    </span>
                  </td>
                  <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm">
                    <LazyLoad>
                      <StatusBadge status={program.status} />
                    </LazyLoad>
                    <div
                      className="text-xs text-text-secondary lg:hidden mt-1 cursor-pointer hover:text-primary transition-colors duration-200"
                      onClick={() =>
                        copyAffiliateLink(program.program_discount)
                      }
                      title="Click to copy affiliate link"
                    >
                      {isAffiliateLink(program.program_discount)
                        ? "Affiliate Link"
                        : "N/A"}{" "}
                      🔗
                    </div>
                  </td>
                  {!isDeactivated && !["deleted"].includes(program?.status || "") && <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <LazyLoad>
                      <ActionMenu programId={program.id} program={program} />
                    </LazyLoad>
                  </td>}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={6}
                  className="px-3 sm:px-6 py-8 text-center text-sm text-text-secondary"
                >
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <div className="text-lg">📋</div>
                    <div>No programs found</div>
                    <div className="text-xs">
                      Try adjusting your search criteria
                    </div>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </section>
  );
};

export default ProgramsTable;
