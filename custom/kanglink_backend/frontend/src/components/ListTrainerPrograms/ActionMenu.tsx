import React from "react";
import { useNavigate } from "react-router-dom";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { KebabIcon } from "@/assets/svgs";
import { MkdPopover } from "@/components/MkdPopover";
import { LazyLoad } from "@/components/LazyLoad";
import { ActionMenuProps } from "./types";

const ActionMenu: React.FC<ActionMenuProps> = ({ programId, program }) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const navigate = useNavigate();

  const handleEdit = () => {
    // TODO: Implement edit functionality
    navigate(`/trainer/edit-program/${programId}`);
  };

  const handleDelete = () => {
    // TODO: Implement delete functionality
    console.log("program >>", program.id);
  };

  const handleView = () => {
    // TODO: Implement view functionality
    navigate(`/trainer/view-program/${programId}`);
  };

  return (
    <LazyLoad>
      <MkdPopover
        display={
          <button
            className="p-1 rounded hover:bg-background-secondary transition-colors duration-200"
            aria-label="More actions"
          >
            <KebabIcon className="w-4 h-4" stroke={THEME_COLORS[mode].TEXT} />
          </button>
        }
        place="left"
        backgroundColor={THEME_COLORS[mode].BACKGROUND}
        tooltipClasses="rounded-lg shadow-lg !duration-[25ms] !transition-all"
        className="!w-fit !p-0"
      >
        <div className="py-1 flex flex-col min-w-[120px]">
         {["draft", "pending_approval", "published"].includes(program?.status || "") && <button
            onClick={() => {
              // take us to discount page with program id
              navigate(`/trainer/discount/${programId}`);
            }}
            className="w-full text-left px-4 py-2 text-sm hover:bg-background-secondary transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            Discount
          </button>}
          <button
            onClick={handleView}
            className="w-full text-left px-4 py-2 text-sm hover:bg-background-secondary transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            View
          </button>
          <button
            onClick={handleEdit}
            className="w-full text-left px-4 py-2 text-sm hover:bg-background-secondary transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            Edit
          </button>
          {/* <button
            onClick={handleDelete}
            className="w-full text-left px-4 py-2 text-sm hover:bg-background-secondary transition-colors duration-200 text-red-600"
          >
            Delete
          </button> */}
        </div>
      </MkdPopover>
    </LazyLoad>
  );
};

export default ActionMenu;
