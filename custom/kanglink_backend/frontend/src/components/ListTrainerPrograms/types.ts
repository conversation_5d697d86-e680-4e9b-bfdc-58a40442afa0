import { Program } from "@/interfaces";

// Program interface
export interface TrainerProgram extends Program {
  affiliate_link: string;
  code: string;
}

// Status Badge Component Props
export interface StatusBadgeProps {
  status: Program["status"];
}

// Custom Pagination Component Props
export interface CustomPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

// Action Menu Component Props
export interface ActionMenuProps {
  programId: number | string | undefined;
  program: TrainerProgram;
}

// Program Search Section Props
export interface ProgramSearchSectionProps {
  searchTerm: string;
  statusFilter: Program["status"] | "" | "All";
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onStatusChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onApplyFilter: () => void;
}

// Programs Table Props
export interface ProgramsTableProps {
  programs: TrainerProgram[];
  isLoading: boolean;
  isError: boolean;
  error: any;
}
