// @ts-nocheck
// Test file to verify equipment functionality
// This file demonstrates how the equipment system should work

import { stepOneData, stepTwoData } from "./mock";

export const mapEquipmentToSplits = (stepOneData: any, stepTwoData: any) => {
  const equipmentBySplitId = stepTwoData.reduce((acc: any, split: any) => {
    acc[split.split_id] = split.equipment_required;
    return acc;
  }, {});

  return {
    ...stepOneData,
    splits: stepOneData.splits.map((split: any) => ({
      ...split,
      equipment_required: equipmentBySplitId[split.split_id] || "",
    })),
  };
};

export const checkEquipmentMapping = (
  enhancedStepOneData: any,
  stepTwoData: any
) => {
  const equipmentBySplitId = stepTwoData.reduce((acc: any, split: any) => {
    acc[split.split_id] = split.equipment_required;
    return acc;
  }, {});

  return enhancedStepOneData.splits.every(
    (split: any) =>
      split.equipment_required === equipmentBySplitId[split.split_id]
  );
};

// Test function to verify equipment handling
export function testEquipmentFunctionality() {
  const enhancedStepOneData = mapEquipmentToSplits(stepOneData, stepTwoData);
  const equipmentMapped = checkEquipmentMapping(
    enhancedStepOneData,
    stepTwoData
  );

  return {
    enhancedStepOneData,
    equipmentMapped,
  };
}
