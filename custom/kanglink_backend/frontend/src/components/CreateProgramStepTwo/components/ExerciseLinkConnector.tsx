import React from "react";
import { Week, Exercise } from "../types";
import { updateExerciseOrdersAndLabels } from "../utils/exerciseUtils";

interface ExerciseLinkConnectorProps {
  currentExercise: Exercise;
  nextExercise?: Exercise;
  exerciseIndex: number;
  sessionIndex: number;
  dayIndex: number;
  weekIndex: number;
  onUpdateWeeks: (weeks: Week[]) => void;
  weeks: Week[];
  isLastExercise: boolean;
  onAddExercise: (insertIndex?: number) => void;
}

const ExerciseLinkConnector: React.FC<ExerciseLinkConnectorProps> = ({
  currentExercise,
  nextExercise,
  exerciseIndex,
  sessionIndex,
  dayIndex,
  weekIndex,
  onUpdateWeeks,
  weeks,
  isLastExercise,
  onAddExercise,
}) => {
  const toggleExerciseLink = () => {
    const updatedWeeks = [...weeks];
    const exercises =
      updatedWeeks[weekIndex].days[dayIndex].sessions[sessionIndex].exercises;
    const currentExerciseRef = exercises[exerciseIndex];
    const nextExerciseRef = nextExercise ? exercises[exerciseIndex + 1] : null;

    if (!nextExerciseRef) return;

    if (
      currentExerciseRef.is_linked &&
      nextExerciseRef.is_linked &&
      currentExerciseRef.label === nextExerciseRef.label
    ) {
      // Unlink the exercises - they are currently linked with the same label

      // Find all exercises with the same label
      const linkedExercises = exercises.filter(
        (ex) => ex.is_linked && ex.label === currentExerciseRef.label
      );

      if (linkedExercises.length <= 2) {
        // If only 2 exercises in the group, unlink both completely
        currentExerciseRef.is_linked = false;
        currentExerciseRef.label = null;
        currentExerciseRef.label_number = "1";
        nextExerciseRef.is_linked = false;
        nextExerciseRef.label = null;
        nextExerciseRef.label_number = "1";
      } else {
        // More than 2 exercises in the group - split the group
        const nextIndex = exercises.indexOf(nextExerciseRef);

        // Find the next available label for the new group
        const usedLabels = exercises
          .filter(
            (ex) =>
              ex.is_linked && ex.label && ex.label !== currentExerciseRef.label
          )
          .map((ex) => ex.label);

        let newLabel = "A";
        for (let i = 0; i < 26; i++) {
          const testLabel = String.fromCharCode(65 + i);
          if (!usedLabels.includes(testLabel)) {
            newLabel = testLabel;
            break;
          }
        }

        // Split the group: exercises from nextExercise onwards get new label
        linkedExercises.forEach((ex) => {
          const exIndex = exercises.indexOf(ex);
          if (exIndex >= nextIndex) {
            ex.label = newLabel;
          }
        });

        // Renumber both groups
        const originalGroup = linkedExercises.filter(
          (ex) => exercises.indexOf(ex) < nextIndex
        );
        const newGroup = linkedExercises.filter(
          (ex) => exercises.indexOf(ex) >= nextIndex
        );

        originalGroup.forEach((ex, index) => {
          ex.label_number = (index + 1).toString();
        });

        newGroup.forEach((ex, index) => {
          ex.label_number = (index + 1).toString();
        });

        // Check if any group becomes isolated (single exercise)
        if (originalGroup.length === 1) {
          originalGroup[0].is_linked = false;
          originalGroup[0].label = null;
          originalGroup[0].label_number = "1";
        }

        if (newGroup.length === 1) {
          newGroup[0].is_linked = false;
          newGroup[0].label = null;
          newGroup[0].label_number = "1";
        }
      }
    } else {
      // Link the exercises

      // If current exercise is already linked to others, extend the group
      if (currentExerciseRef.is_linked && currentExerciseRef.label) {
        nextExerciseRef.is_linked = true;
        nextExerciseRef.label = currentExerciseRef.label;

        // Find the next sequential label_number for this group
        const sameGroupExercises = exercises.filter(
          (ex) => ex.is_linked && ex.label === currentExerciseRef.label
        );

        // Sort by exercise_order to maintain proper sequence
        sameGroupExercises.sort((a, b) => a.exercise_order - b.exercise_order);

        // Renumber the entire group sequentially
        sameGroupExercises.forEach((ex, index) => {
          ex.label_number = (index + 1).toString();
        });

        // Add the next exercise to the group
        nextExerciseRef.label_number = (
          sameGroupExercises.length + 1
        ).toString();
      } else {
        // Create new link group with a new label
        const usedLabels = exercises
          .filter((ex) => ex.is_linked && ex.label)
          .map((ex) => ex.label);

        let newLabel = "A";
        for (let i = 0; i < 26; i++) {
          const testLabel = String.fromCharCode(65 + i);
          if (!usedLabels.includes(testLabel)) {
            newLabel = testLabel;
            break;
          }
        }

        currentExerciseRef.is_linked = true;
        currentExerciseRef.label = newLabel;
        currentExerciseRef.label_number = "1";
        nextExerciseRef.is_linked = true;
        nextExerciseRef.label = newLabel;
        nextExerciseRef.label_number = "2";
      }
    }

    // Update exercise orders and reassign labels for the entire session
    // This will also update rest durations for linked exercises
    updateExerciseOrdersAndLabels(exercises);

    onUpdateWeeks(updatedWeeks);
  };

  // Don't show connector after the last exercise
  if (isLastExercise) {
    return null;
  }

  const isLinked =
    currentExercise.is_linked &&
    nextExercise?.is_linked &&
    currentExercise?.label == nextExercise?.label;

  const handleAddExercise = () => {
    onAddExercise(exerciseIndex + 1);
  };

  return (
    <div className="w-full h-[4.6rem] relative flex justify-between items-center my-3">
      {/* Connecting Line and Link Button Container */}
      <div className="relative flex items-center justify-start ">
        {/* Vertical Connecting Lines */}
        {isLinked && (
          <>
            {/* Top vertical line from current exercise */}
            {/* <div className="absolute left-[-48px] top-[-12px] w-0.5 h-3 bg-primary" /> */}
            {/* Horizontal connecting line */}
            {/* <div className="absolute left-[-48px] top-0 w-6 h-0.5 bg-primary" /> */}
            {/* Bottom vertical line to next exercise */}

            <div className="absolute -top-[80%] m-auto inset-x-0 w-[0.0625rem] h-[2rem] bg-border border border-border" />
          </>
        )}

        {/* Link Icon */}
        <button
          type="button"
          onClick={toggleExerciseLink}
          className={`w-7 h-7 rounded-full border-2 flex items-center justify-center text-primary transition-all duration-200 relative z-10 shadow-sm ${
            isLinked
              ? "bg-primary text-white border-primary hover:bg-primary-hover "
              : "bg-background border-border hover:border-primary text-primary hover:shadow-md"
          }`}
          // style={{ marginLeft: "-51px" }}
          title={isLinked ? "Unlink exercises" : "Link exercises"}
        >
          <svg
            width="14"
            height="14"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            {isLinked ? (
              // Unlink icon
              <>
                <path d="M18.84 12.25l1.72-1.71a5.5 5.5 0 0 0-7.78-7.78l-1.72 1.71" />
                <path d="M5.16 11.75l-1.72 1.71a5.5 5.5 0 0 0 7.78 7.78l1.72-1.71" />
                <line x1="8" y1="2" x2="8" y2="5" />
                <line x1="2" y1="8" x2="5" y2="8" />
                <line x1="16" y1="19" x2="16" y2="22" />
                <line x1="19" y1="16" x2="22" y2="16" />
              </>
            ) : (
              // Link icon
              <>
                <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
                <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
              </>
            )}
          </svg>
        </button>
        <div className="absolute top-[80%] m-auto inset-x-0 w-[0.0625rem] h-[2rem] bg-border border border-border" />
      </div>

      {/* Add Exercise Button - Only show between unlinked exercises */}
      {!isLinked && nextExercise && (
        <div className="">
          <button
            type="button"
            onClick={handleAddExercise}
            className="flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-primary border border-primary rounded-md bg-background hover:bg-primary hover:text-white transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <svg
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="12" y1="5" x2="12" y2="19" />
              <line x1="5" y1="12" x2="19" y2="12" />
            </svg>
            Add Exercise
          </button>
        </div>
      )}
    </div>
  );
};

export default ExerciseLinkConnector;
