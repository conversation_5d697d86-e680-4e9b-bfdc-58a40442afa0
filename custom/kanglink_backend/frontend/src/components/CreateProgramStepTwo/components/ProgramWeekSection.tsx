import React, { useState } from "react";
import { ProgramDaySection } from "./index";
import { InteractiveButton } from "@/components/InteractiveButton";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { Week } from "../types";

interface ProgramWeekSectionProps {
  week: Week;
  weekIndex: number;
  onUpdateWeeks: (weeks: Week[]) => void;
  weeks: Week[];
  onAddWeek?: () => void;
  onAddDay?: (weekIndex: number) => void;
  onAddSession?: (weekIndex: number, dayIndex: number) => void;
  onAddExercise?: (
    weekIndex: number,
    dayIndex: number,
    sessionIndex: number
  ) => void;
  onDeleteWeek?: (weekIndex: number) => void;
  onDeleteDay?: (weekIndex: number, dayIndex: number) => void;
  onDeleteSession?: (
    weekIndex: number,
    dayIndex: number,
    sessionIndex: number
  ) => void;
}

const ProgramWeekSection: React.FC<ProgramWeekSectionProps> = ({
  week,
  weekIndex,
  onUpdateWeeks,
  weeks,
  onAddWeek,
  onAddDay,
  onAddSession,
  onAddExercise,
  onDeleteWeek,
  onDeleteDay,
  onDeleteSession,
}) => {
  const { state } = useTheme();
  const mode = state?.theme;

  // Local state for collapse management
  const [isCollapsed, setIsCollapsed] = useState(false);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div className="space-y-4 relative">
      {/* Week Header */}
      <div className="flex items-center justify-between">
        <button
          type="button"
          onClick={toggleCollapse}
          className="w-fit gap-[.2rem] h-4 px-0 flex items-center justify-center"
        >
          <svg
            className={`w-14 h-15 transform transition-transform ${
              isCollapsed ? "-rotate-90" : ""
            }`}
            xmlns="http://www.w3.org/2000/svg"
            width="10"
            height="16"
            viewBox="0 0 10 16"
            fill="none"
          >
            <g clipPath="url(#clip0_89_2164)">
              <path
                d="M4.29354 11.7062C4.68416 12.0968 5.31854 12.0968 5.70916 11.7062L9.70916 7.7062C9.99666 7.4187 10.081 6.99058 9.92479 6.61558C9.76854 6.24058 9.40604 5.99683 8.99979 5.99683L0.999786 5.99995C0.596662 5.99995 0.231037 6.2437 0.0747866 6.6187C-0.0814634 6.9937 0.00603655 7.42183 0.290412 7.70933L4.29041 11.7093L4.29354 11.7062Z"
                fill={THEME_COLORS[mode]?.TEXT}
              />
            </g>
            <defs>
              <clipPath id="clip0_89_2164">
                <path d="M0 0H10V16H0V0Z" fill="white" />
              </clipPath>
            </defs>
          </svg>
          <h3 className="text-base font-medium text-text font-['Inter']">
            {week.title}
          </h3>
        </button>

        {/* Delete Week Button */}
        <button
          type="button"
          className="w-3.5 h-4 rounded"
          onClick={() => onDeleteWeek?.(weekIndex)}
          title="Delete Week"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="14"
            height="16"
            viewBox="0 0 14 16"
            fill="none"
          >
            <g clipPath="url(#clip0_89_2339)">
              <path
                d="M4.225 0.553125L4 1H1C0.446875 1 0 1.44687 0 2C0 2.55312 0.446875 3 1 3H13C13.5531 3 14 2.55312 14 2C14 1.44687 13.5531 1 13 1H10L9.775 0.553125C9.60625 0.2125 9.25938 0 8.88125 0H5.11875C4.74062 0 4.39375 0.2125 4.225 0.553125ZM13 4H1L1.6625 14.5938C1.7125 15.3844 2.36875 16 3.15937 16H10.8406C11.6312 16 12.2875 15.3844 12.3375 14.5938L13 4Z"
                fill="#EF4444"
              />
            </g>
            <defs>
              <clipPath id="clip0_89_2339">
                <path d="M0 0H14V16H0V0Z" fill="white" />
              </clipPath>
            </defs>
          </svg>
        </button>
      </div>

      {/* Days */}
      {!isCollapsed && (
        <div className="ml-4 space-y-4">
          {week.days.map((day, dayIndex) => (
            <ProgramDaySection
              key={day.id}
              day={day}
              dayIndex={dayIndex}
              weekIndex={weekIndex}
              onUpdateWeeks={onUpdateWeeks}
              weeks={weeks}
              onAddSession={onAddSession || (() => {})}
              onAddExercise={onAddExercise || (() => {})}
              onDeleteDay={onDeleteDay}
              onDeleteSession={onDeleteSession}
            />
          ))}

          {/* Action Buttons after Week */}
          <div className="flex justify-end flex-wrap w-full gap-2 mt-6 pt-4 border-t border-border">
            <InteractiveButton
              type="button"
              onClick={() => onAddWeek?.()}
              className="bg-transparent text-primary border border-primary hover:bg-primary-hover hover:text-white text-sm px-3 py-1 rounded"
            >
              + Add Week
            </InteractiveButton>
            <InteractiveButton
              type="button"
              onClick={() => onAddDay?.(weekIndex)}
              disabled={week.days.length >= 7}
              className={`text-sm px-3 py-1 rounded ${
                week.days.length >= 7
                  ? "bg-gray-300 text-gray-500 border-gray-300 cursor-not-allowed"
                  : "bg-transparent text-primary border border-primary hover:bg-primary-hover hover:text-white"
              }`}
            >
              + Add Day
            </InteractiveButton>
            <InteractiveButton
              type="button"
              onClick={() => {
                // Add session to the last day, or create a day if none exist
                const lastDayIndex =
                  week.days.length > 0 ? week.days.length - 1 : 0;
                if (week.days.length === 0) {
                  onAddDay?.(weekIndex);
                }
                onAddSession?.(weekIndex, lastDayIndex);
              }}
              disabled={week.days.length === 0}
              className={`text-sm px-3 py-1 rounded ${
                week.days.length === 0
                  ? "bg-gray-300 text-gray-500 border-gray-300 cursor-not-allowed"
                  : "bg-transparent text-primary border border-primary hover:bg-primary-hover hover:text-white"
              }`}
            >
              + Add Session
            </InteractiveButton>
            <InteractiveButton
              type="button"
              onClick={() => {
                // Add exercise to the last session of the last day
                const lastDayIndex =
                  week.days.length > 0 ? week.days.length - 1 : 0;
                const lastDay = week.days[lastDayIndex];
                const lastSessionIndex =
                  lastDay?.sessions.length > 0
                    ? lastDay.sessions.length - 1
                    : 0;

                if (week.days.length === 0) {
                  onAddDay?.(weekIndex);
                }
                if (!lastDay || lastDay.sessions.length === 0) {
                  onAddSession?.(weekIndex, lastDayIndex);
                }
                onAddExercise?.(weekIndex, lastDayIndex, lastSessionIndex);
              }}
              disabled={week.days.length === 0}
              className={`text-sm px-3 py-1 rounded ${
                week.days.length === 0
                  ? "bg-gray-300 text-gray-500 border-gray-300 cursor-not-allowed"
                  : "bg-primary text-white border-primary hover:bg-primary-hover"
              }`}
            >
              + Add Exercise
            </InteractiveButton>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProgramWeekSection;
