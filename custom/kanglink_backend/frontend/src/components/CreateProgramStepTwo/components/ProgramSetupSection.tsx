import React, { useMemo, useRef, useState } from "react";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { InteractiveButton } from "@/components/InteractiveButton";
import { PhotoIcon, XMarkIcon } from "@heroicons/react/24/outline";

interface ProgramSetupSectionProps {
  register: UseFormRegister<any>;
  errors: FieldErrors<any>;
  stepOneData?: any;
  onSplitChange?: (splitValue: string) => void;
  currentSplit?: string;
  onAddWeek?: () => void;
  onImageChange?: (file: File | null, previewUrl: string | null) => void;
  currentImage?: string | null;
  splitEquipment?: { [splitId: string]: string };
  onEquipmentChange?: (splitId: string, equipment: string) => void;
}

const ProgramSetupSection: React.FC<ProgramSetupSectionProps> = ({
  register,
  errors,
  stepOneData,
  onSplitChange,
  currentSplit,
  onAddWeek,
  onImageChange,
  currentImage,
  splitEquipment,
  onEquipmentChange,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);

  const image = useMemo(() => {
    if (currentImage) {
      if (currentImage.startsWith("http") || currentImage.startsWith("blob")) {
        return currentImage;
      }
      return `http://127.0.0.1:5172${currentImage}`;
    }
    return null;
  }, [currentImage]);

  // Generate split options from Step One data
  const programSplitOptions = stepOneData?.splits?.map(
    (split: any, index: number) => ({
      ...split,
      label: split.title || `Split ${index + 1}`,
    })
  ) || [{ value: "split-0", label: "Split 1" }];

  const handleSplitChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedSplit = event.target.value;
    onSplitChange?.(selectedSplit);
  };

  const handleEquipmentChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    if (currentSplit && onEquipmentChange) {
      const value = event.target.value;
      // Limit to 500 characters
      if (value.length <= 500) {
        onEquipmentChange(currentSplit, value);
      }
    }
  };

  const handleImageUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        alert("Please select an image file");
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert("File size must be less than 5MB");
        return;
      }

      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      onImageChange?.(file, previewUrl);
    }
  };

  const handleRemoveImage = () => {
    onImageChange?.(null, null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.type.startsWith("image/")) {
        if (file.size <= 5 * 1024 * 1024) {
          const previewUrl = URL.createObjectURL(file);
          onImageChange?.(file, previewUrl);
        } else {
          alert("File size must be less than 5MB");
        }
      } else {
        alert("Please select an image file");
      }
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-normal text-text font-['Inter']">
        Make routine for {stepOneData?.programName || "Program Name"}
      </h2>

      {/* Program Split and Add Program Image */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
        <div className="flex-1 max-w-64">
          <MkdInputV2
            name="program_split"
            type="dropdown"
            uniqueKey="split_id"
            display="label"
            options={programSplitOptions}
            value={currentSplit}
            onChange={handleSplitChange}
            required
          >
            <MkdInputV2.Container>
              <MkdInputV2.Label className="text-base font-normal text-text">
                Choose Program Split
              </MkdInputV2.Label>
              <MkdInputV2.Field
                placeholder="Select program split..."
                className="mt-2"
              />
              <MkdInputV2.Error />
            </MkdInputV2.Container>
          </MkdInputV2>
        </div>

        {/* Image Upload Section */}
        <div className="flex flex-col gap-3">
          {image ? (
            <div className="w-full relative">
              <img
                src={image}
                alt="Program preview"
                className="w-32 h-24 object-cover rounded-lg border border-border"
              />
              <button
                type="button"
                onClick={handleRemoveImage}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            </div>
          ) : (
            <div
              className={`w-full h-24 border-2 border-dashed rounded-lg flex flex-col items-center justify-center cursor-pointer transition-colors ${
                dragOver
                  ? "border-primary bg-primary/10"
                  : "border-border hover:border-primary hover:bg-background-hover"
              }`}
              onClick={handleImageUpload}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <PhotoIcon className="w-6 h-6 text-text-secondary mb-1" />
              <span className="text-xs text-text-secondary text-center">
                Drop image or click
              </span>
            </div>
          )}

          <InteractiveButton
            type="button"
            onClick={handleImageUpload}
            className="bg-transparent w-full text-primary border border-primary hover:bg-primary-hover hover:text-white text-xs font-medium px-4 py-2 rounded"
          >
            {currentImage ? "Change Image" : "+ Add Program Image"}
          </InteractiveButton>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />
        </div>
      </div>

      {/* Description */}
      <MkdInputV2
        name="description"
        type="textarea"
        register={register}
        errors={errors}
        required
        disabled
        value={stepOneData?.program_description || ""}
      >
        <MkdInputV2.Container>
          <MkdInputV2.Label className="text-base font-normal text-text">
            Description
          </MkdInputV2.Label>
          <MkdInputV2.Field
            placeholder="Enter program description..."
            rows="6"
            className="mt-2"
          />
          <MkdInputV2.Error />
        </MkdInputV2.Container>
      </MkdInputV2>

      {/* Equipment Required */}
      <div className="space-y-4">
        <MkdInputV2
          name={`equipment_${currentSplit}`}
          type="textarea"
          value={splitEquipment?.[currentSplit || ""] || ""}
          onChange={handleEquipmentChange}
          // required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label className="text-base font-normal text-text">
              Equipment Required for{" "}
              {stepOneData?.splits?.find(
                (s: any) => s.split_id === currentSplit
              )?.title || "Current Split"}
            </MkdInputV2.Label>
            <MkdInputV2.Field
              placeholder="List required equipment for this split..."
              rows="3"
              className="mt-2"
              maxLength={500}
            />
            <MkdInputV2.Error />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Character counter */}
        <div className="flex justify-between items-center text-xs text-text-secondary">
          <span>
            {(splitEquipment?.[currentSplit || ""] || "").length}/500 characters
          </span>
        </div>

        <div className="flex justify-end">
          <InteractiveButton
            type="button"
            onClick={onAddWeek}
            className="bg-transparent text-primary border border-primary hover:bg-primary-hover hover:text-white text-xs font-medium px-4 py-2 rounded"
          >
            + Add Week
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
};

export default ProgramSetupSection;
