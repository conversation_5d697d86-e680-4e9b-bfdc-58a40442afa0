import React, { useState } from "react";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import { PaginationBar } from "@/components/PaginationBar";
import { useLibrary } from "@/hooks";
import { Exercise } from "@/interfaces";
import { useProfile } from "@/hooks/useProfile";

interface SelectExerciseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectExercise: (exercise: Exercise) => void;
}

const SelectExerciseModal: React.FC<SelectExerciseModalProps> = ({
  isOpen,
  onClose,
  onSelectExercise,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const { profile } = useProfile();
  const userId = profile?.id;

  // Use the library hook to fetch exercises
  const {
    libraryData: exercises,
    isLoading,
    pagination,
  } = useLibrary({
    libraryType: "exercise",
    initialPagination: { limit: 10000, page: currentPage },
    autoFetch: true,
    disable: {
      all: false,
      type_one: true,
      type_two: true,
    },
  });

  const handleSelectExercise = (exercise: Exercise) => {
    onSelectExercise(exercise);
    onClose();
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const filteredExercises = exercises?.filter((exercise: Exercise) =>
    exercise.name?.toLowerCase().includes(searchTerm.toLowerCase()) && (exercise.user_id === userId || exercise.type === 1)
  ) || [];

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title="Select Exercise"
      modalHeader={true}
      classes={{
        modalDialog: "!w-full md:!w-[50rem] !h-fit max-h-[80vh]",
        modalContent: "!px-5 !pt-5",
        modal: "h-full",
      }}
    >
      <div className="space-y-4">
        {/* Search Input */}
        <div className="mb-4">
          <input
            type="text"
            placeholder="Search exercises..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-border rounded-md bg-input text-text placeholder-text-secondary"
          />
        </div>

        {/* Exercise List */}
        <div className="max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="text-center py-8 text-text-secondary">
              Loading exercises...
            </div>
          ) : filteredExercises.length === 0 ? (
            <div className="text-center py-8 text-text-secondary">
              {searchTerm ? "No exercises found matching your search." : "No exercises available."}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredExercises.map((exercise: Exercise) => (
                <div
                  key={exercise.id}
                  onClick={() => handleSelectExercise(exercise)}
                  className="flex items-center justify-between p-3 border border-border rounded-md hover:bg-background-hover cursor-pointer transition-colors"
                >
                  <div className="flex-1">
                    <div className="font-medium text-text">{exercise.name}</div>
                    <div className="text-sm text-text-secondary">
                      {exercise.exercise_type || "Exercise"}
                    </div>
                  </div>
                  <div className="text-sm text-text-secondary">
                    {exercise.video_url ? "Has Video" : "No Video"}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {pagination.num_pages > 1 && (
          <div className="border-t border-border pt-4">
            <PaginationBar
              currentPage={currentPage}
              pageCount={pagination.num_pages}
              pageSize={pagination.limit}
              canPreviousPage={currentPage > 1}
              canNextPage={currentPage < pagination.num_pages}
              updatePageSize={() => {}} // Not needed for this implementation
              updateCurrentPage={handlePageChange}
              startSize={pagination.limit}
              multiplier={1}
              canChangeLimit={false}
            />
          </div>
        )}

        {/* Footer Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t border-border">
          <InteractiveButton
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-border rounded text-text hover:bg-input"
          >
            Cancel
          </InteractiveButton>
        </div>
      </div>
    </Modal>
  );
};

export default SelectExerciseModal; 