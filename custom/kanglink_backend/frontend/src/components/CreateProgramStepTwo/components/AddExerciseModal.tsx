import React, { useState } from "react";
import { Modal } from "@/components/Modal";
import { InteractiveButton } from "@/components/InteractiveButton";
import { MkdInputV2 } from "@/components/MkdInputV2";

interface AddExerciseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddExercise: (exerciseName: string, saveToDatabase: boolean) => void;
}

const AddExerciseModal: React.FC<AddExerciseModalProps> = ({
  isOpen,
  onClose,
  onAddExercise,
}) => {
  const [exerciseName, setExerciseName] = useState("");
  const [saveToMyExercises, setSaveToMyExercises] = useState(false);

  const handleSubmit = () => {
    if (exerciseName.trim()) {
      onAddExercise(exerciseName.trim(), saveToMyExercises);
      // Reset form
      setExerciseName("");
      setSaveToMyExercises(false);
      onClose();
    }
  };

  const handleClose = () => {
    // Reset form on close
    setExerciseName("");
    setSaveToMyExercises(false);
    onClose();
  };

  const isValid = exerciseName.trim();

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={handleClose}
      title="Add Exercise"
      modalHeader={true}
      classes={{
        modalDialog: "!w-full md:!w-[25rem] !h-fit",
        modalContent: "!px-5 !pt-5",
        modal: "h-full",
      }}
    >
      <div className="space-y-6">
        {/* Exercise Name Input */}
        <MkdInputV2
          name="exerciseName"
          type="text"
          value={exerciseName}
          onChange={(e) => setExerciseName(e.target.value)}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Label>Name</MkdInputV2.Label>
            <MkdInputV2.Field placeholder="Enter exercise name" />
          </MkdInputV2.Container>
        </MkdInputV2>

        {/* Save to My Exercises Checkbox */}
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="saveToMyExercises"
            checked={saveToMyExercises}
            onChange={(e) => setSaveToMyExercises(e.target.checked)}
            className="text-primary"
          />
          <label
            htmlFor="saveToMyExercises"
            className="text-sm text-text cursor-pointer"
          >
            Save to My Exercises
          </label>
        </div>

        {/* Footer Buttons */}
        <div className="flex justify-end gap-3 pt-4">
          <InteractiveButton
            type="button"
            onClick={handleClose}
            className="px-4 py-2 border border-border rounded text-text hover:bg-input"
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            type="button"
            onClick={handleSubmit}
            disabled={!isValid}
            className={`px-4 py-2 rounded text-white ${
              isValid
                ? "bg-primary hover:bg-primary-hover"
                : "bg-gray-400 cursor-not-allowed"
            }`}
          >
            Save
          </InteractiveButton>
        </div>
      </div>
    </Modal>
  );
};

export default AddExerciseModal;
