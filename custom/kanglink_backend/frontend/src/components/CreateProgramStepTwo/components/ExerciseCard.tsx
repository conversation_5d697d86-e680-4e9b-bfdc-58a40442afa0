import React, { useState } from "react";
import { InteractiveButton } from "@/components/InteractiveButton";

import SelectExerciseModal from "./SelectExerciseModal";
import { Week, Exercise as ExerciseInstance } from "../types";
import { Exercise } from "@/interfaces";
import { updateExerciseOrdersAndLabels } from "../utils/exerciseUtils";
import { useLibrary } from "@/hooks";
import MkdInputV2 from "@/components/MkdInputV2";

interface ExerciseCardProps {
  exercise: ExerciseInstance;
  exerciseIndex: number;
  sessionIndex: number;
  dayIndex: number;

  weekIndex: number;
  onUpdateWeeks: (weeks: Week[]) => void;
  weeks: Week[];
}

const ExerciseCard: React.FC<ExerciseCardProps> = ({
  exercise,
  exerciseIndex,
  sessionIndex,
  dayIndex,
  weekIndex,
  onUpdateWeeks,
  weeks,
}) => {
  const [showSelectExerciseModal, setShowSelectExerciseModal] = useState(false);






  // Handle selecting an existing exercise
  const handleSelectExercise = (selectedExercise: Exercise) => {
    // Update the exercise instance with the selected exercise data
    updateExercise("exercise_id", selectedExercise.id);
    updateExercise("exercise_name", selectedExercise.name);
    updateExercise("video_url", selectedExercise.video_url || "");
    setShowSelectExerciseModal(false);
  };

  // Generate exercise label based on linking
  const generateExerciseLabel = () => {
    // Now that labels are stored in the data, we can simply use them
    if (exercise.is_linked && exercise.label) {
      // Linked exercise: show label + number (e.g., "A1", "A2")
      return `${exercise.label}${exercise.label_number}`;
    } else if (exercise.label) {
      // Unlinked exercise: show just the label (e.g., "A", "B")
      return exercise.label;
    } else {
      // Fallback for exercises that haven't been processed yet
      return String.fromCharCode(64 + exercise.exercise_order);
    }
  };

  // Check if this exercise is linked and not the last in its group
  // If so, rest duration should be disabled (automatically set to 0)
  const isLinkedAndNotLast = () => {
    if (!exercise.is_linked || !exercise.label) return false;

    // Get all exercises in the same session
    const session = weeks[weekIndex].days[dayIndex].sessions[sessionIndex];
    const exercises = session.exercises;

    // Find all exercises in the same linked group
    const linkedGroup = exercises.filter(
      (ex) => ex.is_linked && ex.label === exercise.label
    );

    if (linkedGroup.length <= 1) return false;

    // Sort by exercise_order to find the last exercise
    linkedGroup.sort((a, b) => a.exercise_order - b.exercise_order);
    const lastExercise = linkedGroup[linkedGroup.length - 1];

    // Return true if this exercise is not the last in the group
    return exercise.id !== lastExercise.id;
  };

  const restDurationDisabled = isLinkedAndNotLast();

  const deleteExercise = () => {
    const updatedWeeks = [...weeks];
    const exercises =
      updatedWeeks[weekIndex].days[dayIndex].sessions[sessionIndex].exercises;

    // Remove the exercise
    exercises.splice(exerciseIndex, 1);

    // Update exercise orders and reassign labels for the entire session
    updateExerciseOrdersAndLabels(exercises);

    onUpdateWeeks(updatedWeeks);
  };

  const updateExercise = (field: string, value: any) => {
    const updatedWeeks = [...weeks];
    const exerciseRef =
      updatedWeeks[weekIndex].days[dayIndex].sessions[sessionIndex].exercises[
      exerciseIndex
      ];

    if (field.includes(".")) {
      const [parent, child] = field.split(".");
      (exerciseRef as any)[parent][child] = value;
    } else {
      (exerciseRef as any)[field] = value;
    }

    // Clear values when switching between reps and time
    // if (field === "reps_time_type") {
    //   if (value === "reps") {
    //     // Clear time values when switching to reps
    //     (exerciseRef as any).time_minutes = "";
    //     (exerciseRef as any).time_seconds = "";
    //   } else if (value === "time") {
    //     // Clear reps value when switching to time
    //     (exerciseRef as any).reps_or_time = "";
    //   }
    // }

    // If updating fields that might affect exercise structure, update labels
    // Note: Most field updates don't affect labeling, but we could add specific checks here if needed
    // For now, we'll only update labels for structural changes (handled in other functions)

    onUpdateWeeks(updatedWeeks);
  };

  return (
    <>
      <div className="bg-background border border-border rounded-md shadow-sm p-4 relative w-full">
        {/* Exercise Label */}
        <div className="absolute -left-12 top-8 w-9 h-9 bg-primary rounded flex items-center justify-center">
          <span className="text-white text-base font-bold font-['Inter']">
            {generateExerciseLabel()}
          </span>
        </div>

        {/* Delete Button */}
        <div className="absolute top-2 right-2">
          <button
            type="button"
            className="w-3.5 h-4 rounded"
            onClick={deleteExercise}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="16"
              viewBox="0 0 14 16"
              fill="none"
            >
              <g clipPath="url(#clip0_89_2339)">
                <path
                  d="M4.225 0.553125L4 1H1C0.446875 1 0 1.44687 0 2C0 2.55312 0.446875 3 1 3H13C13.5531 3 14 2.55312 14 2C14 1.44687 13.5531 1 13 1H10L9.775 0.553125C9.60625 0.2125 9.25938 0 8.88125 0H5.11875C4.74062 0 4.39375 0.2125 4.225 0.553125ZM13 4H1L1.6625 14.5938C1.7125 15.3844 2.36875 16 3.15937 16H10.8406C11.6312 16 12.2875 15.3844 12.3375 14.5938L13 4Z"
                  fill="#EF4444"
                />
              </g>
              <defs>
                <clipPath id="clip0_89_2339">
                  <path d="M0 0H14V16H0V0Z" fill="white" />
                </clipPath>
              </defs>
            </svg>
          </button>
        </div>

        <div className="space-y-4">
          {/* Exercise Selection */}
          <div className="space-y-2">
            {/* <label className="text-sm font-normal text-text font-['Inter']">
              Choose Exercise
            </label> */}
            <div className="flex gap-2 items-end">
              {/* <select
                className="flex-1 h-10 px-2 bg-input border border-border rounded text-text"
                value={exercise.exercise_id}
                onChange={(e) => updateExercise("exercise_id", e.target.value)}
                disabled={loadingExercises}
              >
                <option value="">
                  {loadingExercises
                    ? "Loading exercises..."
                    : "Choose exercise..."}
                </option>
                {availableExercises.map((ex) => (
                  <option key={ex.id} value={ex.id}>
                    {ex.name}{" "}
                    {ex.createdBy === "admin" ? "(Admin)" : "(My Exercise)"}
                  </option>
                ))}
              </select> */}
              <MkdInputV2
                name="exercise_name"
                type="text"
                value={exercise.exercise_name || ""}
                onChange={(e) => {
                  updateExercise("exercise_name", e.target.value);
                }}
                className="[&_input::-webkit-outer-spin-button]:appearance-none [&_input::-webkit-inner-spin-button]:appearance-none [&_input:-moz-appearance:textfield] z-[1] grow"
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Label className="text-sm font-normal text-text font-['Inter']">Choose Exercise</MkdInputV2.Label>
                  <MkdInputV2.Field
                    placeholder="Enter exercise name..."
                    maxLength={100}
                  />
                  <MkdInputV2.Error />
                </MkdInputV2.Container>
              </MkdInputV2>

              <InteractiveButton
                type="button"
                onClick={() => setShowSelectExerciseModal(true)}
                className="flex w-[1.875rem] !h-[42px] py-[0.625rem] px-[0.5rem] bg-primary rounded items-center justify-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="16"
                  viewBox="0 0 14 16"
                  fill="none"
                >
                  <path d="M14 16H0V0H14V16Z" stroke="none" />
                  <path
                    d="M8 2.5C8 1.94687 7.55312 1.5 7 1.5C6.44688 1.5 6 1.94687 6 2.5V7H1.5C0.946875 7 0.5 7.44688 0.5 8C0.5 8.55312 0.946875 9 1.5 9H6V13.5C6 14.0531 6.44688 14.5 7 14.5C7.55312 14.5 8 14.0531 8 13.5V9H12.5C13.0531 9 13.5 8.55312 13.5 8C13.5 7.44688 13.0531 7 12.5 7H8V2.5Z"
                    fill="white"
                  />
                </svg>
              </InteractiveButton>
            </div>
          </div>

          {/* Sets and Reps/Time */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
            {/* Sets */}

            <MkdInputV2
              name="sets"
              type="number"
              value={exercise.sets}
              onChange={(e) => {
                const value = parseInt(e.target.value) || 0;
                // Limit sets to 0-2147483647 (max 32-bit integer)
                if (value >= 0 && value <= 2147483647) {
                  updateExercise("sets", value.toString());
                }
              }}
              className="[&_input::-webkit-outer-spin-button]:appearance-none [&_input::-webkit-inner-spin-button]:appearance-none [&_input:-moz-appearance:textfield]"
            >
              <MkdInputV2.Container>
                <MkdInputV2.Label className="text-sm font-normal text-text font-['Inter']">Sets</MkdInputV2.Label>
                                    <MkdInputV2.Field
                      placeholder="Enter sets..."
                      min="0"
                      max="2147483647"
                    />
                <MkdInputV2.Error />
              </MkdInputV2.Container>
            </MkdInputV2>


            {/* Reps/Time */}
            <div className="relative">

              <div className="flex bg-input border border-border rounded overflow-hidden absolute top-0 right-0 z-[10]">
                <button
                  type="button"
                  className={`px-3 py-1 text-xs font-normal font-['Inter'] ${exercise.reps_time_type === "reps"
                    ? "bg-primary text-white"
                    : "bg-transparent text-text-secondary"
                    }`}
                  onClick={() => updateExercise("reps_time_type", "reps")}
                >
                  Reps
                </button>
                <button
                  type="button"
                  className={`px-3 py-1 text-xs font-normal font-['Inter'] ${exercise.reps_time_type === "time"
                    ? "bg-primary text-white"
                    : "bg-transparent text-text-secondary"
                    }`}
                  onClick={() => updateExercise("reps_time_type", "time")}
                >
                  Time
                </button>
              </div>

              {exercise.reps_time_type === "reps" ? (
                <MkdInputV2
                  name="reps_or_time"
                  type="number"
                  value={exercise.reps_or_time}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 0;
                    // Limit reps to 0-2147483647 (max 32-bit integer)
                    if (value >= 0 && value <= 2147483647) {
                      updateExercise("reps_or_time", value.toString());
                    }
                  }}
                  className="[&_input::-webkit-outer-spin-button]:appearance-none [&_input::-webkit-inner-spin-button]:appearance-none [&_input:-moz-appearance:textfield] z-[1]"
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className="text-sm font-normal text-text font-['Inter']">Reps</MkdInputV2.Label>
                    <MkdInputV2.Field
                      placeholder="Enter reps..."
                      min="0"
                      max="2147483647"
                    />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              ) : null}
              {exercise.reps_time_type === "time" ? (
                <div className="flex gap-2 w-full">
                  <MkdInputV2
                    name="time_minutes"
                    type="number"
                    value={exercise.time_minutes || ""}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 0;
                      // Allow 0-999 for minutes (up to 16+ hours)
                      if (value >= 0 && value <= 60) {
                        updateExercise("time_minutes", value.toString());
                      }
                    }}
                    className="[&_input::-webkit-outer-spin-button]:appearance-none [&_input::-webkit-inner-spin-button]:appearance-none [&_input:-moz-appearance:textfield] z-[1] grow"
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-normal text-text font-['Inter']">MM</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="0"
                        min="0"
                        max="60"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>

                  <MkdInputV2
                    name="time_seconds"
                    type="number"
                    value={exercise.time_seconds || ""}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 0;
                      // Allow 0-59 for seconds
                      if (value >= 0 && value <= 59) {
                        updateExercise("time_seconds", value.toString());
                      }
                    }}
                    className="[&_input::-webkit-outer-spin-button]:appearance-none [&_input::-webkit-inner-spin-button]:appearance-none [&_input:-moz-appearance:textfield] z-[1] grow"
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label className="text-sm font-normal text-text font-['Inter']">SS</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="0"
                        min="0"
                        max="59"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                </div>
              ) : null}

            </div>
          </div>

          {/* Add Video */}
          {/* <div className="space-y-2">
            <label className="text-sm font-normal text-text font-['Inter']">
              Add Video
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                className="flex-1 h-10 px-3 bg-input border border-border rounded text-text"
                placeholder="Enter video URL..."
                value={exercise.video_url || ""}
                onChange={(e) => updateExercise("video_url", e.target.value)}
              />

            </div> */}

          <MkdInputV2
            name="video_url"
            type="text"
            value={exercise.video_url || ""}
            onChange={(e) => {
              updateExercise("video_url", e.target.value);
            }}
            className="[&_input::-webkit-outer-spin-button]:appearance-none [&_input::-webkit-inner-spin-button]:appearance-none [&_input:-moz-appearance:textfield] z-[1] grow"
          >
            <MkdInputV2.Container>
              <MkdInputV2.Label className="text-sm font-normal text-text font-['Inter']">Add Video</MkdInputV2.Label>
              <MkdInputV2.Field
                placeholder="Enter video URL..."
              />
              <MkdInputV2.Error />
            </MkdInputV2.Container>
          </MkdInputV2>
          {/* </div> */}

          {/* Exercise Details and Rest Duration */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Exercise Details */}
            {/* <div className="space-y-2"> */}
            {/* <label className="text-sm font-normal text-text font-['Inter']">
                Exercise Details
              </label>
              <textarea
                className="w-full h-24 px-3 py-2 bg-input border border-border rounded text-text resize-none"
                placeholder="Enter exercise details..."
                value={exercise.exercise_details}
                onChange={(e) =>
                  updateExercise("exercise_details", e.target.value)
                }
              /> */}
              <div>

            <MkdInputV2
              name="exercise_details"
              type="textarea"
              value={exercise.exercise_details || ""}
              onChange={(e) => {
                const value = e.target.value;
                // Limit to 500 characters
                if (value.length <= 500) {
                  updateExercise("exercise_details", value)
                }
              }}

              className="[&_input::-webkit-outer-spin-button]:appearance-none [&_input::-webkit-inner-spin-button]:appearance-none [&_input:-moz-appearance:textfield] z-[1] grow"
            >
              <MkdInputV2.Container>
                <MkdInputV2.Label className="text-sm font-normal text-text font-['Inter']">Exercise Details</MkdInputV2.Label>
                <MkdInputV2.Field
                  rows={4}
                  className="h-24 resize-none px-3 py-2"
                  placeholder="Enter exercise details..."
                />
                <MkdInputV2.Error />
              </MkdInputV2.Container>
            </MkdInputV2>

            {/* Character counter for exercise details */}
            <div className="flex justify-end items-center text-xs text-text-secondary">
              <span>
                {(exercise.exercise_details || "").length}/500 characters
              </span>
            </div>
            </div>
            {/* </div> */}

            {/* Rest Duration */}
            <div className="space-y-2">
              <label className="text-sm font-normal text-text font-['Inter']">
                Rest Duration
                {restDurationDisabled && (
                  <span className="text-xs text-text-secondary ml-2">
                    (Disabled - linked exercise)
                  </span>
                )}
              </label>
              <div className="flex items-center gap-2 mt-2 w-full">
                {/* <div className="space-y-1">
                  <input
                    type="number"
                    min="0"
                    max="59"
                    className={`w-16 h-10 px-2 border rounded text-center ${restDurationDisabled
                        ? "bg-background-secondary border-border text-gray-500 cursor-not-allowed"
                        : "bg-input border-border text-text"
                      }`}
                    placeholder="00"
                    value={exercise.rest_duration_minutes || 0}
                    disabled={restDurationDisabled}
                    onChange={(e) =>
                      updateExercise(
                        "rest_duration_minutes",
                        parseInt(e.target.value) || 0
                      )
                    }
                  />
                  <div className="text-xs text-text-secondary text-center">
                    MM
                  </div>
                </div> */}

                <MkdInputV2
                  name="rest_duration_minutes"
                  type="number"
                  value={exercise.rest_duration_minutes || ""}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 0;
                    // Allow 0-999 for minutes (up to 16+ hours)
                    if (value >= 0 && value <= 60) {
                      updateExercise("rest_duration_minutes", value.toString());
                    }
                  }}
                  className="[&_input::-webkit-outer-spin-button]:appearance-none [&_input::-webkit-inner-spin-button]:appearance-none [&_input:-moz-appearance:textfield] z-[1] grow"
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Field
                      placeholder="0"
                      min="0"
                      max="60"
                    />
                    <MkdInputV2.Error />
                    <MkdInputV2.Label className="text-sm font-normal text-text font-['Inter']">MM</MkdInputV2.Label>
                  </MkdInputV2.Container>
                </MkdInputV2>
                {/* <span className="text-lg text-text">:</span> */}
                {/* <div className="space-y-1">
                  <input
                    type="number"
                    min="0"
                    max="59"
                    className={`w-16 h-10 px-2 border rounded text-center ${restDurationDisabled
                        ? "bg-background-secondary border-border text-gray-500 cursor-not-allowed"
                        : "bg-input border-border text-text"
                      }`}
                    placeholder="00"
                    value={exercise.rest_duration_seconds || 0}
                    disabled={restDurationDisabled}
                    onChange={(e) =>
                      updateExercise(
                        "rest_duration_seconds",
                        parseInt(e.target.value) || 0
                      )
                    }
                  />
                  <div className="text-xs text-text-secondary text-center">
                    SS
                  </div>
                </div> */}

                <MkdInputV2
                  name="rest_duration_seconds"
                  type="number"
                  value={exercise.rest_duration_seconds || ""}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 0;
                    // Allow 0-999 for minutes (up to 16+ hours)
                    if (value >= 0 && value <= 60) {
                      updateExercise("rest_duration_seconds", value.toString());
                    }
                  }}
                  className="[&_input::-webkit-outer-spin-button]:appearance-none [&_input::-webkit-inner-spin-button]:appearance-none [&_input:-moz-appearance:textfield] z-[1] grow"
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Field
                      placeholder="0"
                      min="0"
                      max="60"
                    />
                    <MkdInputV2.Error />
                    <MkdInputV2.Label className="text-sm font-normal text-text font-['Inter']">SS</MkdInputV2.Label>
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Select Exercise Modal */}
      <SelectExerciseModal
        isOpen={showSelectExerciseModal}
        onClose={() => setShowSelectExerciseModal(false)}
        onSelectExercise={handleSelectExercise}
      />


    </>
  );
};

export default ExerciseCard;
