import React from "react";
import { InteractiveButton } from "@/components/InteractiveButton";

interface ActionButtonsSectionProps {
  onAddDay?: () => void;
  onAddWeek?: () => void;
  onAddSession?: () => void;
  onAddExercise?: () => void;
}

const ActionButtonsSection: React.FC<ActionButtonsSectionProps> = ({
  onAddDay,
  onAddWeek,
  onAddSession,
  onAddExercise,
}) => {
  return (
    <div className="flex justify-center gap-4 pt-6">
      <InteractiveButton
        type="button"
        onClick={onAddDay}
        className="bg-transparent text-primary border border-primary hover:bg-primary-hover hover:text-white text-xs font-medium px-4 py-2 rounded"
      >
        + Add Day
      </InteractiveButton>
      <InteractiveButton
        type="button"
        onClick={onAddWeek}
        className="bg-transparent text-primary border border-primary hover:bg-primary-hover hover:text-white text-xs font-medium px-4 py-2 rounded"
      >
        + Add Week
      </InteractiveButton>
      <InteractiveButton
        type="button"
        onClick={onAddSession}
        className="bg-transparent text-primary border border-primary hover:bg-primary-hover hover:text-white text-xs font-medium px-4 py-2 rounded"
      >
        + Add Session
      </InteractiveButton>
      <InteractiveButton
        type="button"
        onClick={onAddExercise}
        className="bg-transparent text-primary border border-primary hover:bg-primary-hover hover:text-white text-xs font-medium px-4 py-2 rounded"
      >
        + Add Exercise
      </InteractiveButton>
    </div>
  );
};

export default ActionButtonsSection;
