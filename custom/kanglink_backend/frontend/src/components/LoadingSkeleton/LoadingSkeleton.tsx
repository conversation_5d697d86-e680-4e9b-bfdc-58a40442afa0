import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";

interface LoadingSkeletonProps {
  title?: string;
  titleWidth?: string;
  itemCount?: number;
  gridCols?: string;
  itemHeight?: string;
}

const LoadingSkeleton = ({
  title,
  titleWidth = "w-48",
  itemCount = 6,
  gridCols = "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
  itemHeight = "h-64",
}: LoadingSkeletonProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const skeletonColor = mode === "dark" ? "bg-gray-700" : "bg-gray-300";

  return (
    <div className="py-8">
      <div className="animate-pulse">
        {title && (
          <div
            className={`h-8 ${skeletonColor} rounded ${titleWidth} mb-6`}
          ></div>
        )}
        <div className={`grid ${gridCols} gap-6`}>
          {[...Array(itemCount)].map((_, i) => (
            <div
              key={i}
              className={`${itemHeight} ${skeletonColor} rounded-lg`}
            ></div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LoadingSkeleton;
