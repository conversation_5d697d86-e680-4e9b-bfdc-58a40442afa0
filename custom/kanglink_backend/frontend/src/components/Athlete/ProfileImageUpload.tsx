import { useRef, useState } from 'react';
import { UserIcon } from '@heroicons/react/24/outline';
import { XMarkIcon } from '@heroicons/react/24/solid';

interface ProfileImageUploadProps {
  currentImage?: string;
  onImageChange: (image: File | undefined) => void;
}

export const ProfileImageUpload = ({ currentImage, onImageChange }: ProfileImageUploadProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [image, setImage] = useState<string>("");

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    onImageChange(file);
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setImage(result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleClearImage = () => {
    setImage("");
    onImageChange(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const displayImage = image || currentImage;

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Profile Image Circle */}
      <div className="relative">
        <div className="w-40 h-40 bg-background-secondary border border-border rounded-full flex items-center justify-center overflow-hidden cursor-pointer hover:bg-background-hover transition-colors">
          {displayImage ? (
            <img 
              src={displayImage} 
              alt="Profile" 
              className="w-full h-full object-cover"
              onClick={handleImageClick}
            />
          ) : (
            <div onClick={handleImageClick} className="flex items-center justify-center w-full h-full">
              <UserIcon className="w-12 h-12 text-text-secondary" />
            </div>
          )}
          
          {/* Clear button - only show when there's a selected image */}
          {image && (
            <button
              type="button"
              onClick={handleClearImage}
              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              title="Remove selected image"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col space-y-2">
        <button
          type="button"
          onClick={handleImageClick}
          className="px-6 py-2 bg-transparent border border-primary text-primary text-sm font-semibold rounded hover:bg-primary hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
        >
          {displayImage ? 'Change Profile Picture' : 'Add Profile Picture'}
        </button>
        
        {image && (
          <button
            type="button"
            onClick={handleClearImage}
            className="px-6 py-2 bg-transparent border border-red-500 text-red-500 text-sm font-semibold rounded hover:bg-red-500 hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            Remove Selected Image
          </button>
        )}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};
