import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import ReactionButtons from "./ReactionButtons";
import PrivacyBadge from "./PrivacyBadge";

// API Response interface matching the backend structure
export interface CommentApiResponse {
  id: number;
  user_id: number;
  post_id: number;
  parent_comment_id: number | null;
  content: string;
  attachments: any | null;
  is_private: boolean;
  is_edited: boolean;
  is_flagged: boolean;
  flag_reason: string | null;
  mentioned_users: any | null;
  reaction_count: number;
  created_at: string;
  updated_at: string;
  user: {
    id: number;
    email: string;
    data: {
      full_name: string;
      date_of_birth?: string;
      level?: string;
      fitness_goals?: string[];
      terms?: boolean;
      photo?: string;
      role?: string;
      [key: string]: any;
    };
  };
  replies?: CommentApiResponse[];
}

// UI interface for rendered comments
export interface Comment {
  id: string;
  author: {
    id: string;
    name: string;
    avatar: string;
    isTrainer?: boolean;
  };
  content: string;
  timestamp: string;
  isPrivate?: boolean;
  reactions: Array<{
    type: "like" | "love" | "comment" | "share";
    count: number;
    isActive?: boolean;
  }>;
  replies?: Comment[];
  reactionCount?: number;
  isEdited?: boolean;
  isFlagged?: boolean;
  flagReason?: string | null;
}

// Utility function to transform API response to UI format
export const transformCommentFromApi = (
  apiComment: CommentApiResponse,
  defaultAvatar: string = "https://placehold.co/48x48"
): Comment => {
  const transformComment = (comment: CommentApiResponse): Comment => ({
    id: comment.id.toString(),
    author: {
      id: comment.user.id.toString(),
      name: comment.user.data?.full_name || "Anonymous",
      avatar: comment.user.data?.photo || defaultAvatar,
      isTrainer: comment.user.data?.role === "trainer",
    },
    content: comment.content,
    timestamp: comment.created_at,
    isPrivate: comment.is_private,
    reactions: [], // Can be populated if needed
    replies: comment.replies ? comment.replies.map(transformComment) : [],
    reactionCount: comment.reaction_count || 0,
    isEdited: comment.is_edited || false,
    isFlagged: comment.is_flagged || false,
    flagReason: comment.flag_reason,
  });

  return transformComment(apiComment);
};

interface CommentSectionProps {
  comments: Comment[];
  currentUserAvatar?: string;
  showReplyInput?: boolean;
  isAnonymous?: boolean;
  onAddComment?: (content: string, isAnonymous: boolean) => void;
  onReplyToComment?: (
    commentId: string,
    content: string,
    isAnonymous: boolean
  ) => void;
}

const CommentSection = ({
  comments,
  currentUserAvatar: _currentUserAvatar = "https://placehold.co/48x48",
  showReplyInput = true,
  isAnonymous = false,
  onAddComment,
  onReplyToComment,
}: CommentSectionProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [replyContent, setReplyContent] = useState("");
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [localIsAnonymous, setLocalIsAnonymous] = useState(isAnonymous);

  const handleAddComment = () => {
    if (replyContent.trim()) {
      if (replyingTo) {
        onReplyToComment?.(replyingTo, replyContent, localIsAnonymous);
        setReplyingTo(null);
      } else {
        onAddComment?.(replyContent, localIsAnonymous);
      }
      setReplyContent("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      handleAddComment();
    }
  };

  const renderComment = (comment: Comment, isReply = false) => (
    <div
      key={comment.id}
      className={`flex gap-3 ${isReply ? "ml-8 sm:ml-12 mt-4" : "mb-6"}`}
    >
      {/* Avatar */}
      <div className="flex-shrink-0">
        <img
          src={comment.author.avatar}
          alt={comment.author.name}
          className="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover border-2"
          style={{ borderColor: THEME_COLORS[mode].BORDER }}
        />
      </div>

      {/* Comment Content */}
      <div className="flex-1 min-w-0">
        {/* Author Info */}
        <div className="flex items-center gap-2 mb-2">
          <h4
            className="text-sm sm:text-base font-medium transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            {comment.author.name}
          </h4>
          {comment.author.isTrainer && (
            <span
              className="text-xs px-2 py-0.5 rounded-full"
              style={{
                backgroundColor: THEME_COLORS[mode].PRIMARY + "20",
                color: THEME_COLORS[mode].PRIMARY,
              }}
            >
              Trainer
            </span>
          )}
          {comment.isPrivate && <PrivacyBadge isPrivate={true} size="sm" />}
        </div>

        {/* Comment Text */}
        <div
          className="p-3 sm:p-4 rounded-lg border mb-3 transition-all duration-200"
          style={{
            backgroundColor: THEME_COLORS[mode].INPUT,
            borderColor: THEME_COLORS[mode].BORDER,
          }}
        >
          <p
            className="text-sm sm:text-base transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            {comment.content}
          </p>
        </div>

        {/* Reactions */}
        <div className="flex items-center justify-between">
          <ReactionButtons
            reactions={comment.reactions}
            size="sm"
            onReactionClick={(_type, _isActive) => {
              // console.log(
              //   `Reaction ${type} ${isActive ? "added" : "removed"} for comment ${comment.id}`
              // );
            }}
          />

          {/* {!isReply && (
            <button
              onClick={() => setReplyingTo(comment.id)}
              className="text-xs sm:text-sm font-medium transition-colors duration-200 hover:opacity-80"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              Reply
            </button>
          )} */}
        </div>

        {/* Replies */}
        {/* {comment.replies && comment.replies.length > 0 && (
          <div className="mt-4">
            {comment.replies.map((reply) => renderComment(reply, true))}
          </div>
        )} */}
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Comments List */}
      {comments.map((comment) => renderComment(comment))}

      {/* Reply Input */}
      {showReplyInput && (
        <div
          className="border-t pt-4"
          style={{ borderColor: THEME_COLORS[mode].BORDER }}
        >
          <div className="flex items-start gap-2 mb-3">
            <span
              className="text-sm sm:text-base font-medium transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              {replyingTo ? "Reply" : "Add Comment"}
            </span>

            {/* Anonymous Toggle */}
            <label className="hidden flex items-center gap-2 cursor-pointer ml-auto">
              <input
                type="checkbox"
                checked={localIsAnonymous}
                onChange={(e) => setLocalIsAnonymous(e.target.checked)}
                className="w-3 h-3 rounded border transition-colors duration-200"
                style={{
                  backgroundColor: localIsAnonymous
                    ? THEME_COLORS[mode].PRIMARY
                    : THEME_COLORS[mode].INPUT,
                  borderColor: THEME_COLORS[mode].BORDER,
                }}
              />
              <span
                className="text-xs sm:text-sm transition-colors duration-200"
                style={{ color: THEME_COLORS[mode].TEXT }}
              >
                Anonymous
              </span>
            </label>
          </div>

          <div className="flex gap-2">
            <input
              type="text"
              value={replyContent}
              onChange={(e) => setReplyContent(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Write a comment..."
              className="flex-1 px-3 py-2 rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
              style={{
                backgroundColor: THEME_COLORS[mode].INPUT,
                borderColor: THEME_COLORS[mode].BORDER,
                color: THEME_COLORS[mode].TEXT,
              }}
            />
            <button
              onClick={handleAddComment}
              disabled={!replyContent.trim()}
              className="px-4 py-2 rounded-lg text-sm font-semibold text-white transition-all duration-200 hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                backgroundColor: replyContent.trim()
                  ? THEME_COLORS[mode].PRIMARY
                  : THEME_COLORS[mode].PRIMARY + "50",
              }}
            >
              Post
            </button>
          </div>

          {replyingTo && (
            <button
              onClick={() => setReplyingTo(null)}
              className="mt-2 text-xs text-gray-500 hover:opacity-80"
            >
              Cancel Reply
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default CommentSection;
