import { useState, useEffect, useCallback, useRef } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import ReactionButtons from "./ReactionButtons";
import CommentSection, { transformCommentFromApi } from "./CommentSection";
import PrivacyBadge from "./PrivacyBadge";
import { usePostFeedActions } from "@/hooks/usePostFeed";
import Attachments from "./Attachments";

interface Author {
  id: string;
  name: string;
  avatar: string;
  isTrainer?: boolean;
}

interface Post {
  id: string;
  author: Author;
  content: string;
  timestamp: string;
  isPrivate?: boolean;
  attachments?: string[] | null;
  reactions: Array<{
    type: "like" | "love" | "comment" | "share";
    count: number;
    isActive?: boolean;
  }>;
  comments: Array<{
    id: string;
    author: Author;
    content: string;
    timestamp: string;
    isPrivate?: boolean;
    reactions: Array<{
      type: "like" | "love" | "comment" | "share";
      count: number;
      isActive?: boolean;
    }>;
    replies?: any[];
  }>;
}

interface FeedPostProps {
  post: Post;
  currentUserAvatar?: string;
  onReactionClick?: (
    postId: string,
    reactionType: string,
    isActive: boolean
  ) => void;
  onAddComment?: (
    postId: string,
    content: string,
    isAnonymous: boolean
  ) => void;
  onReplyToComment?: (
    postId: string,
    commentId: string,
    content: string,
    isAnonymous: boolean
  ) => void;
}

const FeedPost = ({
  post,
  currentUserAvatar = "https://placehold.co/48x48",
  onReactionClick,
  onAddComment,
  onReplyToComment,
}: FeedPostProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [showComments, setShowComments] = useState(true);
  const [localComments, setLocalComments] = useState(post.comments);
  const hasFetchedComments = useRef(false);

  // Store refs to latest values to avoid dependency issues
  const currentUserAvatarRef = useRef(currentUserAvatar);
  const onAddCommentRef = useRef(onAddComment);
  const onReactionClickRef = useRef(onReactionClick);

  // Update refs when values change
  currentUserAvatarRef.current = currentUserAvatar;
  onAddCommentRef.current = onAddComment;
  onReactionClickRef.current = onReactionClick;

  // Stable callback functions using refs
  const handleCommentAdded = useCallback((newComment: any) => {
    // Transform API comment to UI format using utility function
    const transformedComment = transformCommentFromApi(newComment, currentUserAvatarRef.current);
    setLocalComments((prev) => [...prev, transformedComment]);
    onAddCommentRef.current?.(post.id, newComment.content, newComment.is_private);
  }, [post.id]);

  const handleReactionToggled = useCallback((reactionData: any) => {
    onReactionClickRef.current?.(post.id, reactionData.reaction_type, reactionData.action === "added");
  }, [post.id]);

  // Use the post feed actions hook
  const {
    addComment,
    toggleReaction,
    refetchComments,
    comments: apiComments,
    userReaction,
    loading,
  } = usePostFeedActions({
    postId: post.id,
    onCommentAdded: handleCommentAdded,
    onReactionToggled: handleReactionToggled,
  });

  // Sync API comments with local state when they're fetched
  useEffect(() => {
    if (apiComments && apiComments.length > 0) {
      const transformedComments = apiComments.map((comment: any) =>
        transformCommentFromApi(comment, currentUserAvatarRef.current)
      );
      setLocalComments(transformedComments);
    }
  }, [apiComments]);

  const formatTimestamp = (timestamp: string) => {
    // Simple timestamp formatting - you can enhance this
    return new Date(timestamp).toLocaleDateString();
  };

  const handleReactionClick = async (reactionType: string, isActive: boolean) => {
    try {
      // Map UI reaction types to API reaction types
      const apiReactionType = reactionType === "like" ? "like" :
        reactionType === "love" ? "love" :
          reactionType === "comment" ? "like" : // comments don't have reactions, default to like
            reactionType === "share" ? "like" : "like"; // shares don't have reactions, default to like

      if (reactionType === "comment" || reactionType === "share") {
        // Handle comment/share actions differently if needed
        onReactionClick?.(post.id, reactionType, isActive);
        return;
      }

      await toggleReaction("post", post.id, apiReactionType as any);
    } catch (error) {
      // Error is already handled in the hook with toast
      console.error("Failed to toggle reaction:", error);
    }
  };

  const handleAddComment = async (content: string, isAnonymous: boolean) => {
    try {
      await addComment(content, isAnonymous);
    } catch (error) {
      // Error is already handled in the hook with toast
      console.error("Failed to add comment:", error);
    }
  };

  const handleReplyToComment = async (
    commentId: string,
    content: string,
    isAnonymous: boolean
  ) => {
    try {
      await addComment(content, isAnonymous, commentId);
      onReplyToComment?.(post.id, commentId, content, isAnonymous);
    } catch (error) {
      // Error is already handled in the hook with toast
      console.error("Failed to add reply:", error);
    }
  };

  // Fetch comments when component mounts or when showComments changes
  useEffect(() => {
    if (showComments && post.id && !hasFetchedComments.current) {
      refetchComments();
      hasFetchedComments.current = true;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showComments, post.id]);

  console.log(post);
  return (
    <div
      className="rounded-lg shadow-sm border p-4 sm:p-6 mb-6 transition-all duration-200"
      style={{
        backgroundColor: THEME_COLORS[mode].CARD_BG,
        borderColor: THEME_COLORS[mode].BORDER,
      }}
    >
      {/* Post Header */}
      <div className="flex items-start gap-3 sm:gap-4 mb-4">
        {/* Avatar */}
        <div className="flex-shrink-0">
          <img
            src={post.author.avatar}
            alt={post.author.name}
            className="w-10 h-10 sm:w-12 sm:h-12 rounded-full object-cover border-2"
            style={{ borderColor: THEME_COLORS[mode].BORDER }}
          />
        </div>

        {/* Author Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h3
              className="text-sm sm:text-base font-medium transition-colors duration-200"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              {post.author.name}
            </h3>
            {post.author.isTrainer && (
              <span
                className="text-xs px-2 py-0.5 rounded-full"
                style={{
                  backgroundColor: THEME_COLORS[mode].PRIMARY + "20",
                  color: THEME_COLORS[mode].PRIMARY,
                }}
              >
                Trainer
              </span>
            )}
          </div>
          <p
            className="text-xs sm:text-sm transition-colors duration-200"
            style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
          >
            {formatTimestamp(post.timestamp)}
          </p>
        </div>

        {/* Privacy Badge */}
        {post.isPrivate && (
          <div className="flex-shrink-0">
            <PrivacyBadge isPrivate={true} size="sm" />
          </div>
        )}
      </div>

      {/* Post Content */}
      <div
        className="p-3 sm:p-4 rounded-lg border mb-4 transition-all duration-200"
        style={{
          backgroundColor: THEME_COLORS[mode].INPUT,
          borderColor: THEME_COLORS[mode].BORDER,
        }}
      >
        <p
          className="text-sm sm:text-base transition-colors duration-200 whitespace-pre-wrap"
          style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
        >
          {post.content}
        </p>
      </div>

      {/* Reactions */}
      <div
        className="flex items-center justify-between mb-4 pb-4 border-b"
        style={{ borderColor: THEME_COLORS[mode].BORDER }}
      >
        <ReactionButtons
          reactions={post.reactions.map(reaction => ({
            ...reaction,
            isActive: reaction.type === userReaction || reaction.isActive,
          }))}
          onReactionClick={handleReactionClick}
          size="md"
        />
        <div className="flex items-center gap-2">

        {post.attachments && post.attachments.length > 0 && (
          <Attachments attachments={post.attachments} />
        )}
        <button
          onClick={() => setShowComments(!showComments)}
          className="text-xs sm:text-sm font-medium transition-colors duration-200 hover:opacity-80"
          style={{ color: THEME_COLORS[mode].TEXT }}
          disabled={loading.isFetchingComments}
          >
          {loading.isFetchingComments
            ? "Loading..."
            : `${showComments ? "Hide" : "Show"} Comments (${localComments.length})`}
        </button>
            
            </div>
      </div>

      {/* Comments Section */}
      {showComments && (
        <CommentSection
          comments={localComments}
          currentUserAvatar={currentUserAvatar}
          onAddComment={handleAddComment}
          onReplyToComment={handleReplyToComment}
        />
      )}
    </div>
  );
};

export default FeedPost;
