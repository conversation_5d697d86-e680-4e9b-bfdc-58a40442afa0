import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { LockClosedIcon, GlobeAltIcon } from "@heroicons/react/24/outline";

interface PrivacyBadgeProps {
  isPrivate: boolean;
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
}

const PrivacyBadge = ({ 
  isPrivate, 
  size = "md", 
  showIcon = true 
}: PrivacyBadgeProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const sizeClasses = {
    sm: "px-2 py-1 text-xs",
    md: "px-3 py-1.5 text-sm",
    lg: "px-4 py-2 text-base",
  };

  const iconSizes = {
    sm: "w-3 h-3",
    md: "w-3.5 h-3.5",
    lg: "w-4 h-4",
  };

  const getBadgeStyles = () => {
    if (isPrivate) {
      return {
        backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
        borderColor: THEME_COLORS[mode].BORDER,
        color: THEME_COLORS[mode].PRIMARY,
      };
    } else {
      return {
        backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
        borderColor: THEME_COLORS[mode].BORDER,
        color: THEME_COLORS[mode].TEXT_SECONDARY,
      };
    }
  };

  const styles = getBadgeStyles();

  return (
    <div
      className={`inline-flex items-center gap-1.5 rounded-lg border font-medium transition-all duration-200 ${sizeClasses[size]}`}
      style={styles}
    >
      {showIcon && (
        <>
          {isPrivate ? (
            <LockClosedIcon className={iconSizes[size]} />
          ) : (
            <GlobeAltIcon className={iconSizes[size]} />
          )}
        </>
      )}
      <span>{isPrivate ? "Private" : "Public"}</span>
    </div>
  );
};

export default PrivacyBadge;
