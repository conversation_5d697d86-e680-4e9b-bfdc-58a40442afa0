import React, { useState } from "react";
import { Modal } from "@/components/Modal";
import { LoadingSkeleton } from "@/components/LoadingSkeleton";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { useRefundHistory } from "@/hooks/useAthleteRefunds";
import { RefundRequest } from "@/interfaces/model.interface";

interface RefundHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const RefundHistoryModal: React.FC<RefundHistoryModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>("");
  const itemsPerPage = 10;

  const { data, isLoading, error, refetch } = useRefundHistory(
    currentPage,
    itemsPerPage,
    statusFilter
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900";
      case "approved":
        return "text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900";
      case "rejected":
        return "text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900";
      case "processed":
        return "text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900";
      default:
        return "text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
      case "approved":
        return (
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
      case "rejected":
        return (
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
      case "processed":
        return (
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        );
      default:
        return null;
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status);
    setCurrentPage(1); // Reset to first page when filter changes
  };

  if (error) {
    return (
      <Modal
        isOpen={isOpen}
        modalCloseClick={onClose}
        title="Refund History"
        modalHeader
        classes={{
          modalDialog: "max-w-2xl",
          modal: "h-full",
        }}
      >
        <div className="p-6 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-red-600 dark:text-red-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h3
            className="text-lg font-semibold mb-2"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            Error Loading Refund History
          </h3>
          <p
            className="text-sm opacity-70 mb-4"
            style={{ color: THEME_COLORS[mode].TEXT }}
          >
            {error.message || "Failed to load refund history"}
          </p>
          <div className="flex space-x-3">
            <button
              onClick={() => refetch()}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Try Again
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 rounded-lg text-white transition-colors"
              style={{
                backgroundColor: THEME_COLORS[mode].PRIMARY,
              }}
            >
              Close
            </button>
          </div>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title="Refund History"
      modalHeader
      classes={{
        modalDialog: "max-w-4xl w-full max-h-[90vh]",
        modal: "h-full",
        modalContent: "overflow-y-auto",
      }}
    >
      <div className="p-6">
        {/* Filter Controls */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <label
              className="block text-sm font-medium"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              Filter by Status
            </label>
            <button
              onClick={() => refetch()}
              disabled={isLoading}
              className="flex items-center space-x-2 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
              title="Refresh refund history"
            >
              <svg
                className={`w-4 h-4 ${isLoading ? "animate-spin" : ""}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              <span>Refresh</span>
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleStatusFilterChange("")}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                statusFilter === ""
                  ? "bg-blue-600 text-white"
                  : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
              }`}
            >
              All
            </button>
            {["pending", "approved", "rejected", "processed"].map((status) => (
              <button
                key={status}
                onClick={() => handleStatusFilterChange(status)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors capitalize ${
                  statusFilter === status
                    ? "bg-blue-600 text-white"
                    : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"
                }`}
              >
                {status}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        {isLoading ? (
          <>
            <div className="md:block hidden">
              <LoadingSkeleton itemCount={3} />
            </div>

            <div className="block md:hidden">
              <LoadingSkeleton itemCount={1} itemHeight="h-16" />
            </div>
          </>
        ) : data?.refund_requests?.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h3
              className="text-lg font-semibold mb-2"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              No Refund Requests
            </h3>
            <p
              className="text-sm opacity-70"
              style={{ color: THEME_COLORS[mode].TEXT }}
            >
              {statusFilter
                ? `No ${statusFilter} refund requests found.`
                : "You haven't submitted any refund requests yet."}
            </p>
          </div>
        ) : (
          <>
            {/* Refund Requests List */}
            <div className="space-y-4 mb-6">
              {data?.refund_requests?.map((request: RefundRequest) => (
                <div
                  key={request.id}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3
                          className="font-semibold"
                          style={{ color: THEME_COLORS[mode].TEXT }}
                        >
                          {request.program.name}
                        </h3>
                        <span
                          className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}
                        >
                          {getStatusIcon(request.status)}
                          <span className="capitalize">{request.status}</span>
                        </span>
                      </div>
                      {request.program.split_name && (
                        <p
                          className="text-sm opacity-70 mb-1"
                          style={{ color: THEME_COLORS[mode].TEXT }}
                        >
                          Split: {request.program.split_name}
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      <p
                        className="font-semibold"
                        style={{ color: THEME_COLORS[mode].TEXT }}
                      >
                        {request.currency} {request.amount}
                      </p>
                      <p
                        className="text-xs opacity-70"
                        style={{ color: THEME_COLORS[mode].TEXT }}
                      >
                        {formatDate(request.requested_at)}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div>
                      <span
                        className="text-sm font-medium"
                        style={{ color: THEME_COLORS[mode].TEXT }}
                      >
                        Reason:
                      </span>
                      <p
                        className="text-sm opacity-70 mt-1"
                        style={{ color: THEME_COLORS[mode].TEXT }}
                      >
                        {request.reason}
                      </p>
                    </div>

                    {request.admin_notes && (
                      <div>
                        <span
                          className="text-sm font-medium"
                          style={{ color: THEME_COLORS[mode].TEXT }}
                        >
                          Admin Notes:
                        </span>
                        <p
                          className="text-sm opacity-70 mt-1"
                          style={{ color: THEME_COLORS[mode].TEXT }}
                        >
                          {request.admin_notes}
                        </p>
                      </div>
                    )}

                    {request.processed_at && (
                      <div className="flex justify-between text-sm">
                        <span
                          className="opacity-70"
                          style={{ color: THEME_COLORS[mode].TEXT }}
                        >
                          Processed:
                        </span>
                        <span style={{ color: THEME_COLORS[mode].TEXT }}>
                          {formatDate(request.processed_at)}
                        </span>
                      </div>
                    )}

                    {request.refund_amount &&
                      request.refund_amount !== request.amount && (
                        <div className="flex justify-between text-sm">
                          <span
                            className="opacity-70"
                            style={{ color: THEME_COLORS[mode].TEXT }}
                          >
                            Refund Amount:
                          </span>
                          <span style={{ color: THEME_COLORS[mode].TEXT }}>
                            {request.currency} {request.refund_amount}
                          </span>
                        </div>
                      )}
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {data?.pagination && data.pagination.total_pages > 1 && (
              <div className="flex items-center justify-between">
                <p
                  className="text-sm opacity-70"
                  style={{ color: THEME_COLORS[mode].TEXT }}
                >
                  Showing{" "}
                  {(data.pagination.current_page - 1) *
                    data.pagination.per_page +
                    1}{" "}
                  to{" "}
                  {Math.min(
                    data.pagination.current_page * data.pagination.per_page,
                    data.pagination.total
                  )}{" "}
                  of {data.pagination.total} results
                </p>

                <div className="flex space-x-2">
                  <button
                    onClick={() =>
                      handlePageChange(data.pagination.current_page - 1)
                    }
                    disabled={data.pagination.current_page === 1}
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
                  >
                    Previous
                  </button>

                  <span
                    className="flex items-center px-3 py-1 text-sm"
                    style={{ color: THEME_COLORS[mode].TEXT }}
                  >
                    Page {data.pagination.current_page} of{" "}
                    {data.pagination.total_pages}
                  </span>

                  <button
                    onClick={() =>
                      handlePageChange(data.pagination.current_page + 1)
                    }
                    disabled={
                      data.pagination.current_page ===
                      data.pagination.total_pages
                    }
                    className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        )}

        {/* Close Button */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="w-full px-4 py-2 rounded-lg text-white transition-colors"
            style={{
              backgroundColor: THEME_COLORS[mode].PRIMARY,
            }}
          >
            Close
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default RefundHistoryModal;
