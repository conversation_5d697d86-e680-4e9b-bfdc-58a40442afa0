import { useState, useRef, useEffect } from "react";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import SearchableDropdown, {
  SearchableDropdownRef,
} from "@/components/SearchableDropdown/SearchableDropdown";
import { useCustomModelQuery } from "@/query/shared/customModel";

interface Enrollment {
  id: string;
  program_id: string;
  split_id: string;
  program_name: string;
  split_title: string;
  program: {
    id: string;
    program_name: string;
    type_of_program: string;
    allow_comments: boolean;
  };
  split: {
    id: string;
    title: string;
  };
  trainer: {
    id: string;
    email: string;
    data: any;
  };
}

interface FeedHeaderProps {
  title?: string;
  subtitle?: string;
  searchPlaceholder?: string;
  onSearch?: (query: string) => void;
  onProgramSelect?: (enrollment: Enrollment | null) => void;
  selectedEnrollment?: Enrollment | null;
}

const FeedHeader = ({
  title = "Feed",
  subtitle = 'Welcome to "Selected Program"',
  searchPlaceholder: _searchPlaceholder = "Search Program",
  onSearch,
  onProgramSelect,
  selectedEnrollment,
}: FeedHeaderProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [_searchQuery, setSearchQuery] = useState("");
  const [enrollments, setEnrollments] = useState<Enrollment[]>([]);
  const dropdownRef = useRef<SearchableDropdownRef>(null);

  // Use custom model query for fetching enrollments
  const enrollmentQuery = useCustomModelQuery({
    role: "member",
    showToast: false,
  });

  // Fetch enrollments where trainer has allowed comments
  const fetchEnrollments = async (search?: string) => {
    try {
      const params = new URLSearchParams({
        page: "1",
        limit: "100",
        ...(search && { search }),
      });

      const response = await enrollmentQuery.mutateAsync({
        endpoint: `/v2/api/kanglink/custom/athlete/enrollments/with-comments?${params.toString()}`,
        method: "GET",
      });

      if (!response.error && response.data) {
        setEnrollments(response.data);
      }
    } catch (error) {
      console.error("Error fetching enrollments:", error);
    }
  };

  useEffect(() => {
    fetchEnrollments();
  }, []);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearch?.(query);
  };

  const handleEnrollmentSelect = (enrollment: any) => {
    if (enrollment) {
      onProgramSelect?.(enrollment);
    } else {
      onProgramSelect?.(null);
    }
  };

  const getDisplayValue = (enrollment: Enrollment) => {
    return `${enrollment.program_name} - ${enrollment.split_title}`;
  };

  const getSubtitle = () => {
    if (selectedEnrollment) {
      return `Welcome to "${selectedEnrollment.program_name}"`;
    }
    return subtitle;
  };

  // console.log(enrollments);

  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
      {/* Title Section */}
      <div className="flex-1">
        <h1
          className="text-xl sm:text-2xl lg:text-3xl font-bold mb-1 transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT }}
        >
          {title}
        </h1>
        <p
          className="text-sm sm:text-base transition-colors duration-200"
          style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
        >
          {getSubtitle()}
        </p>
      </div>

      {/* Program Selection Section */}
      <div className="flex-shrink-0 w-full sm:w-auto">
        <SearchableDropdown
          ref={dropdownRef}
          label="Select Program"
          placeholder="Choose a program..."
          uniqueKey="id"
          display={["program_name", "split_title"]}
          displaySeparator=" - "
          value={selectedEnrollment?.id || null}
          onSelect={handleEnrollmentSelect}
          useExternalData={true}
          externalDataLoading={enrollmentQuery.isPending}
          externalDataOptions={enrollments}
          className="w-full sm:w-64 lg:w-72"
          showSearchIcon={true}
        />
      </div>

      {/* Search Section - Only show if a program is selected */}
      {/* {selectedEnrollment && (
        <div className="flex-shrink-0 w-full sm:w-auto">
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchChange}
              placeholder={searchPlaceholder}
              className="w-full sm:w-48 lg:w-56 xl:w-64 h-10 pl-4 pr-10 rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
              style={{
                backgroundColor: THEME_COLORS[mode].INPUT,
                borderColor: THEME_COLORS[mode].BORDER,
                color: THEME_COLORS[mode].TEXT,
              }}
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <MagnifyingGlassIcon
                className="h-4 w-4 transition-colors duration-200"
                style={{ color: THEME_COLORS[mode].TEXT_SECONDARY }}
              />
            </div>
          </div>
        </div>
      )} */}
    </div>
  );
};

export default FeedHeader;
