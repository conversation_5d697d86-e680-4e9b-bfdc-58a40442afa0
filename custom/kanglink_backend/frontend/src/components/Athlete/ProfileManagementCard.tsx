
import { useState, useEffect } from "react";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCalendarDays,
  faChevronDown,
} from "@fortawesome/free-solid-svg-icons";
import { ProfileImageUpload } from ".";
import { UseFormRegister, FieldErrors, UseFormSetValue, UseFormWatch } from "react-hook-form";

const FITNESS_GOALS = [
  "Weight Loss",
  "Muscle Gain",
  "Endurance",
  "Flexibility",
  "General Fitness",
  "Sports Performance",
];

const levelOptions = ["Beginner", "Intermediate", "Advanced"];

interface ProfileManagementCardProps {
  register?: UseFormRegister<any>;
  errors?: FieldErrors<any>;
  setValue?: UseFormSetValue<any>;
  watch?: UseFormWatch<any>;
  profileImage?: string;
  setProfileImage?: (image: File | undefined) => void;
}

export const ProfileManagementCard = ({
  register,
  errors,
  setValue,
  watch,
  profileImage,
  setProfileImage
}: ProfileManagementCardProps) => {
  const [selectedGoals, setSelectedGoals] = useState<string[]>(watch?.("fitnessGoals") || []);

  // Update form value when selectedGoals changes
  useEffect(() => {
    if (setValue) {
      (setValue as any)("fitnessGoals", selectedGoals);
    }
  }, [selectedGoals, setValue]);


  return (
    <div className="bg-card-bg rounded-lg shadow-sm border border-border p-6">
      {/* Card Header */}
      <div className="mb-6">
        <h2 className="text-lg font-medium text-text">Profile Management</h2>
      </div>

      {/* Profile Form */}

      {/* Profile Image Section */}
      <div className="flex flex-col lg:flex-row lg:items-start gap-6">
        <div className="flex-1 space-y-6">
          {/* Full Name Field */}
          <MkdInputV2
            name="fullName"
            register={register}
            errors={errors}
            required
          >
            <MkdInputV2.Container>
              <MkdInputV2.Label className="text-text">
                Full Name
              </MkdInputV2.Label>
              <MkdInputV2.Field
                placeholder="Enter Full Name"
                className="bg-input border-border text-text placeholder:text-text-disabled"
              />
              <MkdInputV2.Error />
            </MkdInputV2.Container>
          </MkdInputV2>

          {/* Email Field */}
          <MkdInputV2
            name="email"
            type="email"
            register={register}
            errors={errors}
            required
          >
            <MkdInputV2.Container>
              <MkdInputV2.Label className="text-text">Email</MkdInputV2.Label>
              <MkdInputV2.Field
                placeholder="Enter Email Address"
                className="bg-input border-border text-text placeholder:text-text-disabled"
              />
              <MkdInputV2.Error />
            </MkdInputV2.Container>
          </MkdInputV2>

          {/* Fitness Goals - Multi-select with chips */}
          <div>
            <label className="block text-text font-bold mb-2">
              Fitness Goals
            </label>
            <div className="hidden flex-wrap gap-2 mb-2">
              {selectedGoals.map((goal) => (
                <span
                  key={goal}
                  className="flex items-center bg-primary text-secondary rounded-full px-3 py-1 text-xs font-medium shadow-sm"
                >
                  {goal}
                  <button
                    type="button"
                    aria-label={`Remove ${goal}`}
                    className="ml-2 text-secondary hover:text-secondary/80 focus:outline-none"
                    onClick={() => setSelectedGoals((prev) => prev.filter((g) => g !== goal))}
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
            <div className="flex flex-wrap gap-2">
              {FITNESS_GOALS.map((goal) => (
                <button
                  key={goal}
                  type="button"
                  className={`px-3 py-1 rounded-full border text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary/50 ${selectedGoals.includes(goal)
                      ? "bg-primary text-secondary border-primary"
                      : "bg-input text-text hover:bg-input/80 border-border"
                    }`}
                  aria-pressed={selectedGoals.includes(goal)}
                  onClick={() => setSelectedGoals((prev: string[]) =>
                    prev.includes(goal) ? prev.filter((g: string) => g !== goal) : [...prev, goal]
                  )}
                >
                  {goal}
                </button>
              ))}
            </div>
            {/* Hidden input for react-hook-form */}
            <input
              type="hidden"
              {...(register as any)("fitnessGoals", {
                validate: () =>
                  selectedGoals.length > 0 ||
                  "Select at least one fitness goal",
              })}
              value={selectedGoals.join(",")}
            />
            {errors?.fitnessGoals && (
              <p className="mt-2 text-xs text-red-500">
                {errors?.fitnessGoals?.message as string}
              </p>
            )}
            <p className="mt-2 text-xs text-text-disabled">
              Select all fitness goals that apply to your training preferences.
            </p>
          </div>

          {/* Date of Birth Field */}
          <MkdInputV2
            name="dateOfBirth"
            type="date"
            register={register}
            errors={errors}
            required
          >
            <MkdInputV2.Container>
              <MkdInputV2.Label className="text-text">
                Date of Birth
              </MkdInputV2.Label>
              <div
                className="relative cursor-pointer"
                onClick={() => {
                  const container = document
                    .querySelector('[name="dateOfBirth"]')
                    ?.closest(".relative");
                  const input = container?.querySelector(
                    'input[type="date"]'
                  ) as HTMLInputElement;
                  if (input) {
                    input.focus();
                    input.click();
                    if (input.showPicker) {
                      try {
                        input.showPicker();
                      } catch (e) {
                        // Ignore errors if showPicker is not supported
                      }
                    }
                  }
                }}
              >
                <MkdInputV2.Field
                  placeholder="mm/dd/yyyy"
                  className="bg-input border-border text-text placeholder:text-text-disabled pr-10 [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden cursor-pointer"
                />
                <FontAwesomeIcon
                  icon={faCalendarDays}
                  className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 pointer-events-none text-icon"
                />
              </div>
              <MkdInputV2.Error />
            </MkdInputV2.Container>
          </MkdInputV2>

          {/* Level Dropdown */}
          <MkdInputV2
            name="level"
            type="select"
            register={register}
            errors={errors}
            required
            options={levelOptions}
          >
            <MkdInputV2.Container>
              <MkdInputV2.Label className="text-text">
                Select Level
              </MkdInputV2.Label>
              <div className="relative">
                <MkdInputV2.Field
                  placeholder="Choose your level"
                  className="bg-input border-border text-text placeholder:text-text-disabled pr-10"
                />
                <FontAwesomeIcon
                  icon={faChevronDown}
                  className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 pointer-events-none text-icon"
                />
              </div>
              <MkdInputV2.Error />
            </MkdInputV2.Container>
          </MkdInputV2>
        </div>

        {/* Profile Image Upload */}
        <div className="lg:w-56 flex flex-col items-center">
          <ProfileImageUpload
            currentImage={profileImage}
            onImageChange={setProfileImage as (image: File | undefined) => void}
          />
        </div>
      </div>

    </div>
  );
};
