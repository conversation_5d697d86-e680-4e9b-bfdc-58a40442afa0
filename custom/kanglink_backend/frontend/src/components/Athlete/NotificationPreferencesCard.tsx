import { useState } from 'react';
import { UseFormRegister, FieldErrors, UseFormSetValue, UseFormWatch } from "react-hook-form";

interface NotificationSettings {
  emailNotifications: boolean;
  inAppNotifications: boolean;
}

interface NotificationPreferencesCardProps {
  register?: UseFormRegister<any>;
  errors?: FieldErrors<any>;
  setValue?: UseFormSetValue<any>;
  watch?: UseFormWatch<any>;
}

export const NotificationPreferencesCard = ({ 
  setValue
}: NotificationPreferencesCardProps) => {
  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: false,
    inAppNotifications: true
  });

  const handleNotificationChange = (type: keyof NotificationSettings) => {
    const newValue = !notifications[type];
    setNotifications(prev => ({
      ...prev,
      [type]: newValue
    }));
    
    // Update form value if register is provided
    if (setValue) {
      setValue(type === 'emailNotifications' ? 'emailNotifications' : 'inAppNotifications', newValue);
    }
  };

  return (
    <div className="bg-card-bg rounded-lg shadow-sm border border-border p-6">
      {/* Card Header */}
      <div className="mb-6">
        <h2 className="text-lg font-medium text-text">Notifications Preferences</h2>
      </div>

      {/* Notification Options */}
      <div className="space-y-4">
        {/* Email Notifications */}
        <div className="flex items-center space-x-3">
          <div className="relative">
            <input
              type="checkbox"
              id="emailNotifications"
              checked={notifications.emailNotifications}
              onChange={() => handleNotificationChange('emailNotifications')}
              className="sr-only"
            />
            <label
              htmlFor="emailNotifications"
              className={`flex items-center justify-center w-4 h-4 border rounded cursor-pointer transition-colors ${
                notifications.emailNotifications
                  ? 'bg-primary border-primary'
                  : 'bg-background border-border hover:border-primary'
              }`}
            >
              {notifications.emailNotifications && (
                <svg
                  className="w-3 h-3 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </label>
          </div>
          <label htmlFor="emailNotifications" className="text-sm text-text cursor-pointer">
            Email Notification
          </label>
        </div>

        {/* In-app Notifications */}
        <div className="flex items-center space-x-3">
          <div className="relative">
            <input
              type="checkbox"
              id="inAppNotifications"
              checked={notifications.inAppNotifications}
              onChange={() => handleNotificationChange('inAppNotifications')}
              className="sr-only"
            />
            <label
              htmlFor="inAppNotifications"
              className={`flex items-center justify-center w-4 h-4 border rounded cursor-pointer transition-colors ${
                notifications.inAppNotifications
                  ? 'bg-primary border-primary'
                  : 'bg-background border-border hover:border-primary'
              }`}
            >
              {notifications.inAppNotifications && (
                <svg
                  className="w-3 h-3 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </label>
          </div>
          <label htmlFor="inAppNotifications" className="text-sm text-text cursor-pointer">
            In-app Notifications
          </label>
        </div>
      </div>
    </div>
  );
};
