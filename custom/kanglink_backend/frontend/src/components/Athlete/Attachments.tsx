import { useTheme } from "@/hooks/useTheme";
import { Modal } from "@/components/Modal";
import { useState } from "react";

interface FeedPostProps {
  attachments: string[];
}

const Attachments = ({
  attachments,
}: FeedPostProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [isOpen, setIsOpen] = useState(false);
  const [selectedAttachment, setSelectedAttachment] = useState<string | null>(null);
  return (
    <>
      {
        attachments.map((attachment, index) => (
          <button key={index} onClick={() => {
            setIsOpen(true);
            setSelectedAttachment(attachment);
          }}>
            <img className="w-5 rounded-md h-5" src={attachment} alt="Attachment" />
          </button>
        ))
      }


      <Modal isOpen={isOpen} title="Attachment" modalCloseClick={() => setIsOpen(false)} modalHeader classes={{
        modalContent: "p-0",
        modalDialog: "w-full max-w-2xl",
      }}>

        <div className="flex flex-col gap-4">
          <img className="w-full h-full object-cover" src={selectedAttachment || "https://placehold.co/400x400"} alt="Attachment" />
        </div>
      </Modal>
    </>
  );
};

export default Attachments;
