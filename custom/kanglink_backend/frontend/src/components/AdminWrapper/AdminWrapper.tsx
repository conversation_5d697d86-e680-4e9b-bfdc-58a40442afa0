import React, { Suspense, memo } from "react";

import { AdminHeader } from "@/components/AdminHeader";
import { TopHeader } from "@/components/TopHeader";
import { Spinner } from "@/assets/svgs";
import { LazyLoad } from "@/components/LazyLoad";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { ThemeToggle } from "@/components/ThemeToggle";

interface AdminWrapperProps {
  children: React.ReactNode;
}

const AdminWrapper = ({ children }: AdminWrapperProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  return (
    <>
      <div></div>
      <LazyLoad>
        <div
          className={`relative flex h-full max-h-full min-h-full w-full max-w-full overflow-hidden`}
        >
          <AdminHeader />
          <div
            className={`grid h-full max-h-full min-h-full w-full grow grid-rows-[auto_1fr] overflow-x-hidden`}
          >
            <TopHeader />
            <Suspense
              fallback={
                <div
                  className={`flex h-full max-h-full min-h-full w-full items-center justify-center`}
                >
                  <Spinner size={40} color={THEME_COLORS[mode].PRIMARY} />
                </div>
              }
            >
              <div className="h-full max-h-full min-h-full w-full overflow-y-auto overflow-x-hidden">
                {children}
              </div>
              {/* Floating Theme Toggle */}
              <div className="fixed z-50 bottom-6 right-6 md:bottom-8 md:right-8">
                <LazyLoad>
                  <ThemeToggle className="shadow-lg border bg-background-secondary hover:scale-110 transition-all" />
                </LazyLoad>
              </div>
            </Suspense>
          </div>
        </div>
      </LazyLoad>
    </>
  );
};

export default memo(AdminWrapper);
