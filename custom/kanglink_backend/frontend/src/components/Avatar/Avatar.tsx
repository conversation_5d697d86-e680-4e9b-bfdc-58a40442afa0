import React from "react";

interface AvatarProps {
  src?: string;
  alt: string;
  className?: string;
  size?: "sm" | "md" | "lg" | "xl";
}

const Avatar: React.FC<AvatarProps> = ({ 
  src, 
  alt, 
  className = "", 
  size = "md" 
}) => {
  // Default avatar SVG as a data URL
  const defaultAvatar = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236B7280'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E";

  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-12 h-12", 
    lg: "w-16 h-16",
    xl: "w-20 h-20"
  };

  return (
    <img
      src={src || defaultAvatar}
      alt={alt}
      className={`${sizeClasses[size]} object-cover ${className}`}
    />
  );
};

export default Avatar; 