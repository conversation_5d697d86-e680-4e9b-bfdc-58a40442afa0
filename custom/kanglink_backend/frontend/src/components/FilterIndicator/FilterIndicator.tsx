import { useSearchParams } from "react-router-dom";
import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { XMarkIcon } from "@heroicons/react/24/outline";

interface FilterIndicatorProps {
  onClearFilter: (key: string) => void;
}

const FilterIndicator = ({ onClearFilter }: FilterIndicatorProps) => {
  const { state } = useTheme();
  const mode = state?.theme;
  const [searchParams] = useSearchParams();

  const getActiveFilters = () => {
    const filters: Array<{ key: string; label: string; value: string }> = [];

    // Gender filters
    const gender = searchParams.get("gender");
    if (gender) {
      const genderLabels: Record<string, string> = {
        man: "Man",
        woman: "Woman",
        non_binary: "Non-Binary",
        transgender_woman: "Transgender Woman",
        transgender_man: "Transgender Man",
        other: "Other",
      };
      gender.split(",").forEach(g => {
        if (genderLabels[g]) {
          filters.push({ key: "gender", label: "Gender", value: genderLabels[g] });
        }
      });
    }

    // Experience filters
    const experience = searchParams.get("experience");
    if (experience) {
      const experienceLabels: Record<string, string> = {
        "less_than_1_year": "Less than 1 year",
        "1_2_years": "1-2 years",
        "3_5_years": "3-5 years",
        "5_7_years": "5-7 years",
        "6_10_years": "6-10 years",
        "10_plus_years": "10+ years",
      };

      experience.split(",").forEach(exp => {
        if (experienceLabels[exp]) {
          filters.push({ key: "experience", label: "Experience", value: experienceLabels[exp] });
        }
      });
    }

    // Rating filters
    const minRating = searchParams.get("min_rating");
    const maxRating = searchParams.get("max_rating");
    if (minRating && parseFloat(minRating) > 0) {
      filters.push({ key: "min_rating", label: "Min Rating", value: `${minRating}+` });
    }
    if (maxRating && parseFloat(maxRating) < 5) {
      filters.push({ key: "max_rating", label: "Max Rating", value: `≤${maxRating}` });
    }

    // Experience range filters
    const minExperience = searchParams.get("min_experience");
    const maxExperience = searchParams.get("max_experience");
    if (minExperience && parseInt(minExperience) > 0) {
      filters.push({ key: "min_experience", label: "Min Experience", value: `${minExperience}+ years` });
    }
    if (maxExperience && parseInt(maxExperience) < 50) {
      filters.push({ key: "max_experience", label: "Max Experience", value: `≤${maxExperience} years` });
    }

    // Sort filters
    const sortBy = searchParams.get("sort_by");
    const sortOrder = searchParams.get("sort_order");
    if (sortBy && sortBy !== "created_at") {
      const sortLabels: Record<string, string> = {
        rating: "Highest Rated",
        name: "Name",
        popularity: "Most Popular",
        price: "Price",
        experience: "Experience",
      };
      const orderLabel = sortOrder === "asc" ? " (A-Z)" : " (Z-A)";
      filters.push({ key: "sort_by", label: "Sort", value: sortLabels[sortBy] + orderLabel });
    }

    // Boolean filters
    const hasPreview = searchParams.get("has_preview");
    if (hasPreview === "true") {
      filters.push({ key: "has_preview", label: "Has Preview", value: "Yes" });
    }

    const hasPrograms = searchParams.get("has_programs");
    if (hasPrograms === "true") {
      filters.push({ key: "has_programs", label: "Has Programs", value: "Yes" });
    }

    return filters;
  };

  const activeFilters = getActiveFilters();

  if (activeFilters.length === 0) return null;

  const chipStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_HOVER,
    color: THEME_COLORS[mode].TEXT,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const removeButtonStyles = {
    color: THEME_COLORS[mode].TEXT_SECONDARY,
  };

  return (
    <div className="flex flex-wrap gap-2 mb-4">
      {activeFilters.map((filter, index) => (
        <div
          key={`${filter.key}-${index}`}
          className="flex items-center space-x-2 px-3 py-1 rounded-full border text-sm"
          style={chipStyles}
        >
          <span className="font-medium">{filter.label}:</span>
          <span>{filter.value}</span>
          <button
            onClick={() => onClearFilter(filter.key)}
            className="ml-1 hover:opacity-70 transition-opacity duration-200"
          >
            <XMarkIcon className="h-3 w-3" style={removeButtonStyles} />
          </button>
        </div>
      ))}
    </div>
  );
};

export default FilterIndicator; 