import React from "react";
import { UseFormRegister } from "react-hook-form";
import { FormData } from "../types";

interface ProgramSettingsSectionProps {
  register: UseFormRegister<FormData>;
}

const ProgramSettingsSection: React.FC<ProgramSettingsSectionProps> = ({
  register,
}) => {
  return (
    <div className="space-y-6">
      {/* Track Progress */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-text">Track progress</label>
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            {...register("track_progress")}
            className="w-3 h-3 text-primary border-border rounded focus:ring-primary focus:ring-2"
          />
          <span className="text-sm text-text">Track Weekly Performance</span>
        </label>
      </div>

      {/* Contact */}
      <div className="space-y-3">
        <label className="text-sm font-medium text-text">Contact</label>
        <div className="space-y-2">
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              {...register("allow_comments")}
              className="w-3 h-3 text-primary border-border rounded focus:ring-primary focus:ring-2"
            />
            <span className="text-sm text-text">
              Allow athletes to comment in Feed
            </span>
          </label>
          <label className="flex items-center space-x-3">
            <input
              type="checkbox"
              {...register("allow_private_messages")}
              className="w-3 h-3 text-primary border-border rounded focus:ring-primary focus:ring-2"
            />
            <span className="text-sm text-text">
              Allow athletes to message privately in Feed
            </span>
          </label>
        </div>
      </div>
    </div>
  );
};

export default ProgramSettingsSection;
