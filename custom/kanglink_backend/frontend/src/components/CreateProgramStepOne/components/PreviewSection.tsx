import React from "react";
import { UseFormRegister } from "react-hook-form";
import { FormData } from "../types";

interface PreviewSectionProps {
  register: UseFormRegister<FormData>;
  daysForPreviewValue: number | undefined;
  onIncrementDaysForPreview: () => void;
  onDecrementDaysForPreview: () => void;
}

const PreviewSection: React.FC<PreviewSectionProps> = ({
  register,
  daysForPreviewValue,
  onIncrementDaysForPreview,
  onDecrementDaysForPreview,
}) => {
  return (
    <div className="space-y-4">
      <label className="text-sm font-medium text-text">Preview</label>
      <div className="flex items-center justify-between">
        <span className="text-sm text-text">Days for Preview</span>
        <div className="flex items-center border border-border rounded-md bg-input">
          <button
            type="button"
            onClick={onDecrementDaysForPreview}
            className="px-3 py-2 text-text hover:bg-background-hover rounded-l-md"
          >
            −
          </button>
          <input
            type="number"
            {...register("days_for_preview")}
            value={daysForPreviewValue || 1}
            disabled  
            className={`focus:shadow-none w-16 font-inter h-[3rem] text-center appearance-none rounded-[.625rem] border-none p-[.625rem] px-3 py-2 leading-tight text-text bg-input focus:outline-none focus:ring-0 focus:border-primary transition-colors duration-200 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [&::-moz-number-spin-button]:appearance-none`}
            min="1"
            readOnly
          />
          <button
            type="button"
            onClick={onIncrementDaysForPreview}
            className="px-3 py-2 text-text hover:bg-background-hover rounded-r-md"
          >
            +
          </button>
        </div>
      </div>
    </div>
  );
};

export default PreviewSection;
