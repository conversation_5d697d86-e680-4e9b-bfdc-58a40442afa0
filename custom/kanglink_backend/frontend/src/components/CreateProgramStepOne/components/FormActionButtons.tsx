import React from "react";
import { InteractiveButton } from "@/components/InteractiveButton";

interface FormActionButtonsProps {
  isValid?: boolean;
  onCancel: () => void;
}

const FormActionButtons: React.FC<FormActionButtonsProps> = ({
  isValid,
  onCancel,
}) => {
  return (
    <div className="flex justify-end gap-4 pt-6">
      <InteractiveButton
        type="button"
        onClick={onCancel}
        className="bg-transparent text-primary border-primary hover:bg-primary-hover hover:text-white"
        style={{
          width: "7.49613rem",
          height: "2.625rem",
          padding: "0.75rem 1.5rem",
        }}
      >
        Cancel
      </InteractiveButton>
      <InteractiveButton
        type="submit"
        disabled={!isValid}
        className={`border ${
          isValid
            ? "bg-primary text-white border-primary hover:bg-primary-hover"
            : "bg-gray-300 text-gray-500 border-gray-300 cursor-not-allowed"
        }`}
        style={{
          width: "7.49613rem",
          height: "2.625rem",
          padding: "0.75rem 1.5rem",
        }}
      >
        Continue
      </InteractiveButton>
    </div>
  );
};

export default FormActionButtons;
