import React, { useMemo } from "react";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { FormData } from "../types";
import { useQuery } from "@tanstack/react-query";
import { useCustomModelQuery } from "@/query/shared";

interface ProgramBasicInfoSectionProps {
  register: UseFormRegister<FormData>;
  errors: FieldErrors<FormData>;
  programDescriptionValue: string | undefined;
}

const ProgramBasicInfoSection: React.FC<ProgramBasicInfoSectionProps> = ({
  register,
  errors,
  programDescriptionValue,
}) => {
  // const programTypeOptions = [
  //   { value: "body building", label: "Body building" },
  //   { value: "endurance", label: "Endurance" },
  //   { value: "hiit", label: "HIIT" },
  //   { value: "strength", label: "Strength" },
  //   { value: "cross fit", label: "Cross Fit" },
  //   { value: "flexibility", label: "Flexibility" },
  //   { value: "calisthenics", label: "Calisthenics" },
  //   { value: "yoga", label: "Yoga" },
  //   { value: "high jump", label: "High Jump" },
  // ];

  const { mutateAsync: customModelQuery } = useCustomModelQuery()

  const { data: specializations } = useQuery({
    queryKey: ["specializations"],
    queryFn: () => customModelQuery({ endpoint: "/v2/api/kanglink/custom/public/specializations", method: "GET" }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  const programTypeOptions = useMemo(() => specializations?.data?.map((specialization: any) => ({ value: (specialization.name)?.toLowerCase(), label: specialization.name })), [specializations]).concat([
    {value: "other", label: "Other"}
  ]);

  return (
    <div className="space-y-6">
      {/* Program Name */}
      <MkdInputV2
        name="program_name"
        type="text"
        register={register}
        errors={errors}
        required
      >
        <MkdInputV2.Container>
          <MkdInputV2.Label className="text-sm font-medium text-text">
            Program Name
          </MkdInputV2.Label>
          <MkdInputV2.Field placeholder="" className="mt-1" />
          <MkdInputV2.Error />
        </MkdInputV2.Container>
      </MkdInputV2>

      {/* Type of Program */}
      <MkdInputV2
        name="type_of_program"
        type="select"
        register={register}
        errors={errors}
        options={programTypeOptions}
        required
      >
        <MkdInputV2.Container>
          <MkdInputV2.Label className="text-sm font-medium text-text">
            Type of Program
          </MkdInputV2.Label>
          <MkdInputV2.Field className="mt-1 capitalize" />
          <MkdInputV2.Error />
        </MkdInputV2.Container>
      </MkdInputV2>

      {/* Program Description */}
      <MkdInputV2
        name="program_description"
        type="textarea"
        register={register}
        errors={errors}
        required
      >
        <MkdInputV2.Container>
          <MkdInputV2.Label className="text-sm font-medium text-text">
            Program Description
          </MkdInputV2.Label>
          <MkdInputV2.Field
            placeholder="Enter program description (max 500 characters)"
            rows={"4"}
            className="mt-1 resize-none"
          />
          <div className="flex justify-between items-center mt-1">
            <MkdInputV2.Error />
            <span className="text-xs text-text-secondary">
              {programDescriptionValue?.length || 0}/500
            </span>
          </div>
        </MkdInputV2.Container>
      </MkdInputV2>
    </div>
  );
};

export default ProgramBasicInfoSection;
