import React from "react";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { FormData } from "../types";

interface PaymentPlansSectionProps {
  register: UseFormRegister<FormData>;
  errors: FieldErrors<FormData>;
}

const PaymentPlansSection: React.FC<PaymentPlansSectionProps> = ({
  register,
  errors,
}) => {
  return (
    <div className="space-y-3">
      <label className="text-sm font-medium text-text">Payment Plans</label>
      <div className="space-y-2">
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            value="one_time"
            {...register("payment_plan")}
            className="w-3 h-3 text-primary border-border rounded focus:ring-primary focus:ring-2"
          />
          <span className="text-sm text-text">One Time Payment</span>
        </label>
        <label className="flex items-center space-x-3">
          <input
            type="checkbox"
            value="monthly"
            {...register("payment_plan")}
            className="w-3 h-3 text-primary border-border rounded focus:ring-primary focus:ring-2"
          />
          <span className="text-sm text-text">Monthly Subscription</span>
        </label>
      </div>
      {errors.payment_plan && (
        <p className="text-sm text-red-500">{errors.payment_plan.message}</p>
      )}
    </div>
  );
};

export default PaymentPlansSection;
