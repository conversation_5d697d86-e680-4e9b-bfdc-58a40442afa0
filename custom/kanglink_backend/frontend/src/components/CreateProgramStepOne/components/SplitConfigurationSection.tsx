import React from "react";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { FormData } from "../types";

interface SplitConfigurationSectionProps {
  register: UseFormRegister<FormData>;
  errors: FieldErrors<FormData>;
  splitProgramValue: number | undefined;
  onIncrementSplitProgram: () => void;
  onDecrementSplitProgram: () => void;
}

const SplitConfigurationSection: React.FC<SplitConfigurationSectionProps> = ({
  register,
  errors,
  splitProgramValue,
  onIncrementSplitProgram,
  onDecrementSplitProgram,
}) => {
  const currencyOptions = [
    { value: "USD", label: "USD" },
    { value: "EUR", label: "EUR" },
    { value: "GBP", label: "GBP" },
    { value: "CAD", label: "CAD" },
  ];

  return (
    <div className="space-y-6">
      {/* Split Program */}
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-text">Split Program</label>
        <div className="flex items-center border border-border rounded-md bg-input">
          <button
            type="button"
            disabled={splitProgramValue === 1}
            onClick={onDecrementSplitProgram}
            className="px-3 py-2 text-text hover:bg-background-hover rounded-l-md"
          >
            −
          </button>

          <input
            type={"number"}
            step="1"
            min="1"
            max="3"
            {...register("split_program")}
            disabled
            className={`focus:shadow-none font-inter h-[3rem] w-full text-center appearance-none rounded-[.625rem] border-none p-[.625rem] px-3 py-2 leading-tight text-text bg-input focus:outline-none focus:ring-0 focus:border-primary transition-colors duration-200 [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [&::-moz-number-spin-button]:appearance-none`}
            autoComplete="new-password"
            aria-autocomplete="none"
          />
          <button
            type="button"
            disabled={splitProgramValue === 3}
            onClick={onIncrementSplitProgram}
            className="px-3 py-2 text-text hover:bg-background-hover rounded-r-md"
          >
            +
          </button>
        </div>
      </div>

      {/* Currency Selection */}
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium text-text">Currency</label>
        <MkdInputV2
          name="currency"
          type="select"
          register={register}
          errors={errors}
          options={currencyOptions}
          required
        >
          <MkdInputV2.Container>
            <MkdInputV2.Field className="w-20 bg-none" />
          </MkdInputV2.Container>
        </MkdInputV2>
      </div>
    </div>
  );
};

export default SplitConfigurationSection;
