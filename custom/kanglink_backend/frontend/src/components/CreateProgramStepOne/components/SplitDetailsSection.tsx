import React from "react";
import { UseFormRegister, FieldErrors } from "react-hook-form";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { FormData } from "../types";

interface SplitDetailsSectionProps {
  register: UseFormRegister<FormData>;
  errors: FieldErrors<FormData>;
  splitsValue:
  | Array<{
    title: string;
    full_price?: number | null;
    subscription?: number | null;
  }>
  | undefined;
  paymentPlanValue: string[] | undefined;
}

const SplitDetailsSection: React.FC<SplitDetailsSectionProps> = ({
  register,
  errors,
  splitsValue,
  paymentPlanValue,
}) => {

  return (
    <div className="space-y-4">
      <label className="text-sm font-medium text-text">Split Details</label>
      {splitsValue?.map((_, index) => {
        
        return <div
          key={index}
          className={`border border-border rounded-md p-4 space-y-4 ${errors?.splits?.length ? "pb-[30px]" : ""}`}
        >
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-text">Split {index + 1}</h4>
          </div>

          {/* Split Title */}
          <MkdInputV2
            name={`splits.${index}.title`}
            type="text"
            register={register}
            errors={{ [`splits.${index}.title`]: errors?.splits?.[index]?.title }}
            required
          >
            <MkdInputV2.Container>
              <MkdInputV2.Label className="text-sm font-medium text-text">
                Title
              </MkdInputV2.Label>
              <MkdInputV2.Field
                placeholder="Enter split title"
                className="mt-1"
                max={100}
              />
              <MkdInputV2.Error />
            </MkdInputV2.Container>
          </MkdInputV2>

          {/* Price Inputs - Conditional based on payment plan selection */}
          {paymentPlanValue && paymentPlanValue.length > 0 ? (
            <div
              className={`${paymentPlanValue?.includes("one_time") &&
                  paymentPlanValue?.includes("monthly")
                  ? "grid grid-cols-2 grid-rows-1 gap-4"
                  : "grid grid-cols-1 grid-rows-1"
                }`}
            >
              {/* Full Price - Show if one_time is selected */}
              {paymentPlanValue?.includes("one_time") && (
                <MkdInputV2
                  name={`splits.${index}.full_price`}
                  type="decimal"
                  register={register}
                  errors={{ [`splits.${index}.full_price`]: errors?.splits?.[index]?.full_price }}
                  required={paymentPlanValue?.includes("one_time")}
                  className="[&_input::-webkit-outer-spin-button]:appearance-none [&_input::-webkit-inner-spin-button]:appearance-none [&_input:-moz-appearance:textfield]"
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className="text-xs text-text-secondary">Full Price</MkdInputV2.Label>
                    <MkdInputV2.Field
                      placeholder="0"
                      // className="mt-1"
                      min="0"
                      step="0.01"
                    />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              )}

              {/* Subscription Price - Show if monthly is selected */}
              {paymentPlanValue?.includes("monthly") && (
                <MkdInputV2
                  name={`splits.${index}.subscription`}
                  type="decimal"
                  register={register}
                  errors={{ [`splits.${index}.subscription`]: errors?.splits?.[index]?.subscription }}
                  required={paymentPlanValue?.includes("monthly")}
                  className="[&_input::-webkit-outer-spin-button]:appearance-none [&_input::-webkit-inner-spin-button]:appearance-none [&_input:-moz-appearance:textfield]"
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Label className="text-xs text-text-secondary">
                      Subscription
                    </MkdInputV2.Label>
                    <MkdInputV2.Field
                      placeholder="0"
                      // className="mt-1"
                      min="0"
                      step="0.01"
                    />
                    <MkdInputV2.Error />
                  </MkdInputV2.Container>
                </MkdInputV2>
              )}
            </div>
          ) : (
            <div className="text-center py-4 text-text-secondary text-sm border border-border rounded-md bg-background-secondary">
              Please select at least one payment plan to configure pricing
            </div>
          )}
        </div>
      })}
    </div>
  );
};

export default SplitDetailsSection;
