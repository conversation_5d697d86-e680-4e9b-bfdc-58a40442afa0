import React from "react";
import { SplitCard } from "@/components/SplitCard";
import { TransformedProgramData } from "@/interfaces";
// import { TransformedProgramData } from "@/interfaces";

interface Split {
  id: string;
  name: string;
  description: string;
  subscriptionPrice: number;
  buyPrice: number;
}

interface BillingSectionProps {
  programId: string;
  splits: Split[];
  paymentPlan?: string[];
  program?: TransformedProgramData | null;
  onSubscribe?: (splitId: string) => void;
  onBuy?: (splitId: string) => void;
  onInvalidateQueries?: () => void;
}

const BillingSection: React.FC<BillingSectionProps> = ({
  programId,
  splits,
  paymentPlan,
  program,
  onSubscribe: _handleSubscribe,
  onBuy: _handleBuy,
  onInvalidateQueries,
}) => {
  return (
    <div className="w-full bg-background">
      <div className="p-4 lg:p-6">
        <h2 className="text-lg lg:text-xl font-bold text-text mb-6">Billing</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {splits.map((split) => (
            <SplitCard
              splitId={split.id}
              programId={programId}
              key={split.id}
              splitName={split.name}
              description={split.description}
              subscriptionPrice={split.subscriptionPrice}
              buyPrice={split.buyPrice}
              paymentPlan={paymentPlan}
              programData={program}
              // onSubscribe={() => onSubscribe?.(split.id)}
              // onBuy={() => onBuy?.(split.id)}
              onInvalidateQueries={onInvalidateQueries}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default BillingSection;
