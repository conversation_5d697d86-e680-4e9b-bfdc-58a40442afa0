import { useTheme } from "@/hooks/useTheme";
import { THEME_COLORS } from "@/context/Theme";
import { PlayIcon } from "@heroicons/react/24/solid";

interface ExerciseStatsGridProps {
  sets: string;
  reps: string;
  rest: string;
  weight: string;
  // Video preview props
  showVideoPreview?: boolean;
  videoUrl?: string;
  thumbnailUrl?: string;
  onVideoClick?: () => void;
}

const ExerciseStatsGrid = ({
  sets,
  reps,
  rest,
  weight,
  showVideoPreview = false,
  videoUrl,
  thumbnailUrl = "https://placehold.co/300x200/e2e8f0/64748b?text=Video",
  onVideoClick,
}: ExerciseStatsGridProps) => {
  const { state } = useTheme();
  const mode = state?.theme;

  const cardStyles = {
    backgroundColor: THEME_COLORS[mode].BACKGROUND_SECONDARY,
    borderColor: THEME_COLORS[mode].BORDER,
  };

  const StatCard = ({ label, value }: { label: string; value: string }) => (
    <div
      className="bg-background-secondary border border-border rounded-md p-3 transition-colors duration-200"
      style={cardStyles}
    >
      <div className="text-center">
        <div className="text-sm font-normal text-text-secondary mb-1">
          {label}
        </div>
        <div className="text-xl font-bold text-text">{value}</div>
      </div>
    </div>
  );

  // Helper functions for video URL handling (copied from VideoPlayer)
  const isYouTube =
    videoUrl?.includes("youtube.com") || videoUrl?.includes("youtu.be");
  const isVimeo = videoUrl?.includes("vimeo.com");
  const isExternalVideo = isYouTube || isVimeo;

  const getYouTubeVideoId = (url: string) => {
    return url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/)?.[1];
  };

  const getVimeoVideoId = (url: string) => {
    return url.match(/vimeo\.com\/(\d+)/)?.[1];
  };

  const getYouTubeThumbnail = (videoId: string) => {
    return `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`;
  };

  const getVimeoThumbnail = (videoId: string) => {
    // Use Vimeo's thumbnail API - this will work for most public videos
    return `https://vumbnail.com/${videoId}.jpg`;
  };

  const getVideoThumbnail = () => {
    if (!videoUrl) return thumbnailUrl;

    if (isYouTube) {
      const videoId = getYouTubeVideoId(videoUrl);
      return videoId ? getYouTubeThumbnail(videoId) : thumbnailUrl;
    } else if (isVimeo) {
      const videoId = getVimeoVideoId(videoUrl);
      return videoId ? getVimeoThumbnail(videoId) : thumbnailUrl;
    }

    return thumbnailUrl || videoUrl;
  };

  const VideoPreviewCard = () => (
    <div
      className="bg-background-secondary border border-border rounded-md p-3 transition-colors duration-200 cursor-pointer hover:bg-background-hover group"
      style={cardStyles}
      onClick={onVideoClick}
    >
      <div className="text-center">
        <div className="text-sm font-normal text-text-secondary mb-1">
          Video
        </div>
        <div className="relative w-full h-12 bg-gray-200 dark:bg-gray-700 rounded overflow-hidden">
          {videoUrl ? (
            <>
              {isExternalVideo ? (
                /* External video thumbnail */
                <img
                  src={getVideoThumbnail()}
                  alt="Exercise video preview"
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Fallback to placeholder if thumbnail fails to load
                    e.currentTarget.src =
                      "https://placehold.co/320x180/e2e8f0/64748b?text=Video";
                  }}
                />
              ) : (
                /* Direct video file */
                <video
                  src={videoUrl}
                  className="w-full h-full object-cover"
                  preload="metadata"
                  muted
                  onError={() => {
                    // Video will show first frame or poster
                  }}
                />
              )}
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 group-hover:bg-opacity-50 transition-colors">
                <PlayIcon className="w-4 h-4 text-white" />
              </div>
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <PlayIcon className="w-4 h-4 text-text-secondary" />
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div
      className={`grid gap-3 sm:gap-4 mb-4 ${
        showVideoPreview
          ? "grid-cols-2 lg:grid-cols-5"
          : "grid-cols-2 lg:grid-cols-4"
      }`}
    >
      <StatCard label="Sets" value={sets} />
      <StatCard label="Reps" value={reps} />
      <StatCard label="Rest" value={rest} />
      <StatCard label="Weight" value={weight} />
      {showVideoPreview && <VideoPreviewCard />}
    </div>
  );
};

export default ExerciseStatsGrid;
