// Transaction data types and dummy data

export type TransactionStatus = "Completed" | "Pending" | "Refunded";

export interface Transaction {
  id: number;
  trainer: string;
  date: string;
  status: TransactionStatus;
  totalAmount: string;
  coachRevenue: string;
  yourEarning: string;
}

// Refund data types
export type RefundStatus = "Pending" | "Refunded";

export interface Refund {
  id: number;
  trainer: string;
  purchaseDate: string;
  refundDate: string;
  status: RefundStatus;
  reason: string;
  totalAmount: string;
  coachRevenue: string;
  yourEarning: string;
}

// Filter options for transactions
export const transactionStatusFilterOptions = [
  { value: "Show All", label: "Show All" },
  { value: "paid", label: "Completed" },
  { value: "pending", label: "Pending" },
  { value: "refunded", label: "Refunded" },
];

export const transactionDateFilterOptions = [
  { value: "Today", label: "Today" },
  { value: "Yesterday", label: "Yesterday" },
  { value: "This Week", label: "This Week" },
  { value: "Last Week", label: "Last Week" },
  { value: "This Month", label: "This Month" },
  { value: "Last Month", label: "Last Month" },
  { value: "This Year", label: "This Year" },
  { value: "Custom Range", label: "Custom Range" },
];

// Filter options for refunds
export const refundStatusFilterOptions = [
  { value: "Active", label: "All Active" },
  { value: "pending", label: "Pending" },
  { value: "approved", label: "Approved" },
  { value: "rejected", label: "Rejected" },
  { value: "processed", label: "Processed" },
];

export const refundDateFilterOptions = [
  { value: "Select date", label: "Select date" },
  { value: "Today", label: "Today" },
  { value: "Yesterday", label: "Yesterday" },
  { value: "This Week", label: "This Week" },
  { value: "Last Week", label: "Last Week" },
  { value: "This Month", label: "This Month" },
  { value: "Last Month", label: "Last Month" },
];

// Dummy transaction data matching the design
export const dummyTransactions: Transaction[] = [
  {
    id: 1,
    trainer: "Joy",
    date: "2024-06-02",
    status: "Completed",
    totalAmount: "$10",
    coachRevenue: "$7",
    yourEarning: "$3",
  },
  {
    id: 2,
    trainer: "Josh",
    date: "2024-06-02",
    status: "Pending",
    totalAmount: "$15",
    coachRevenue: "$10",
    yourEarning: "$5",
  },
  {
    id: 3,
    trainer: "Frank",
    date: "2024-06-02",
    status: "Refunded",
    totalAmount: "$100",
    coachRevenue: "$70",
    yourEarning: "$30",
  },
  {
    id: 4,
    trainer: "Josh",
    date: "2024-06-02",
    status: "Completed",
    totalAmount: "$125",
    coachRevenue: "$90",
    yourEarning: "$35",
  },
  {
    id: 5,
    trainer: "Sarah",
    date: "2024-06-01",
    status: "Completed",
    totalAmount: "$75",
    coachRevenue: "$52",
    yourEarning: "$23",
  },
  {
    id: 6,
    trainer: "Mike",
    date: "2024-06-01",
    status: "Pending",
    totalAmount: "$200",
    coachRevenue: "$140",
    yourEarning: "$60",
  },
  {
    id: 7,
    trainer: "Emma",
    date: "2024-05-31",
    status: "Completed",
    totalAmount: "$50",
    coachRevenue: "$35",
    yourEarning: "$15",
  },
  {
    id: 8,
    trainer: "David",
    date: "2024-05-31",
    status: "Refunded",
    totalAmount: "$180",
    coachRevenue: "$126",
    yourEarning: "$54",
  },
  {
    id: 9,
    trainer: "Lisa",
    date: "2024-05-30",
    status: "Completed",
    totalAmount: "$95",
    coachRevenue: "$66",
    yourEarning: "$29",
  },
  {
    id: 10,
    trainer: "Alex",
    date: "2024-05-30",
    status: "Pending",
    totalAmount: "$160",
    coachRevenue: "$112",
    yourEarning: "$48",
  },
];

// Dummy refund data matching the design
export const dummyRefunds: Refund[] = [
  {
    id: 3,
    trainer: "Frank",
    purchaseDate: "06/02/24",
    refundDate: "06/03/24",
    status: "Refunded",
    reason: "Not satisfied with program",
    totalAmount: "$100",
    coachRevenue: "$70",
    yourEarning: "$30",
  },
  {
    id: 4,
    trainer: "Sarah",
    purchaseDate: "06/01/24",
    refundDate: "06/02/24",
    status: "Pending",
    reason: "Technical issues",
    totalAmount: "$150",
    coachRevenue: "$105",
    yourEarning: "$45",
  },
  {
    id: 5,
    trainer: "Mike",
    purchaseDate: "05/30/24",
    refundDate: "06/01/24",
    status: "Refunded",
    reason: "Program not as expected",
    totalAmount: "$200",
    coachRevenue: "$140",
    yourEarning: "$60",
  },
  {
    id: 6,
    trainer: "Emma",
    purchaseDate: "05/29/24",
    refundDate: "05/31/24",
    status: "Pending",
    reason: "Schedule conflicts",
    totalAmount: "$75",
    coachRevenue: "$52",
    yourEarning: "$23",
  },
  {
    id: 7,
    trainer: "Alex",
    purchaseDate: "05/28/24",
    refundDate: "05/30/24",
    status: "Refunded",
    reason: "Changed mind",
    totalAmount: "$125",
    coachRevenue: "$87",
    yourEarning: "$38",
  },
  {
    id: 8,
    trainer: "Lisa",
    purchaseDate: "05/27/24",
    refundDate: "05/29/24",
    status: "Pending",
    reason: "Medical reasons",
    totalAmount: "$180",
    coachRevenue: "$126",
    yourEarning: "$54",
  },
];

// Library data types
export interface LibraryExercise {
  id: number;
  name: string;
  type: string;
  date: string;
  hasVideo: boolean;
  videoStatus: "Open" | "Add Video";
}

export interface LibraryVideo {
  id: number;
  name: string;
  type: string;
  date: string;
  status: "Open" | "Add Video";
}

// Dummy library data matching the design
export const dummyExercises: LibraryExercise[] = [
  {
    id: 1,
    name: "Exercise1",
    type: "Bodybuilding",
    date: "2024-06-02",
    hasVideo: true,
    videoStatus: "Open",
  },
  {
    id: 2,
    name: "Exercise2",
    type: "Callisthenics",
    date: "2024-06-02",
    hasVideo: false,
    videoStatus: "Add Video",
  },
  {
    id: 3,
    name: "Exercise3",
    type: "Bodybuilding",
    date: "2024-06-02",
    hasVideo: false,
    videoStatus: "Add Video",
  },
  {
    id: 4,
    name: "Exercise4",
    type: "Yoga",
    date: "2024-06-02",
    hasVideo: false,
    videoStatus: "Add Video",
  },
];

export const dummyVideos: LibraryVideo[] = [
  {
    id: 1,
    name: "Video1",
    type: "Bodybuilding",
    date: "2024-06-02",
    status: "Open",
  },
  {
    id: 2,
    name: "Video2",
    type: "Callisthenics",
    date: "2024-06-02",
    status: "Add Video",
  },
  {
    id: 3,
    name: "Video3",
    type: "Bodybuilding",
    date: "2024-06-02",
    status: "Add Video",
  },
  {
    id: 4,
    name: "Video4",
    type: "Yoga",
    date: "2024-06-02",
    status: "Add Video",
  },
];

// Library filter options
export const libraryDateFilterOptions = [
  { value: "Select date", label: "Select date" },
  { value: "Today", label: "Today" },
  { value: "Yesterday", label: "Yesterday" },
  { value: "Last 7 days", label: "Last 7 days" },
  { value: "Last 30 days", label: "Last 30 days" },
  { value: "All time", label: "All time" },
];

export const libraryTypeFilterOptions = [
  { value: "Search video", label: "Search video" },
  { value: "Bodybuilding", label: "Bodybuilding" },
  { value: "Callisthenics", label: "Callisthenics" },
  { value: "Yoga", label: "Yoga" },
  { value: "All", label: "All Types" },
];
