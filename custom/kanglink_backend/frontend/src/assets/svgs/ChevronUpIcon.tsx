import React from "react";

const ChevronUpIcon = ({ className = "", stroke = "black", onClick = () => { } }) => {
  return (
    <svg
      className={`${className}`}
      onClick={onClick}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="chevron-up">
        <path
          id="Icon"
          d="M18 15L12 9L6 15"
          stroke={stroke}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};

export default ChevronUpIcon;
