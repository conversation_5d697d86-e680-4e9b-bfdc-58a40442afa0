import React from 'react';

const RobotIcon = ({ className = "", fill = "#A8A8A8", onClick = () => { } }) => {
    return (
        <svg onClick={onClick} className={`${className}`} xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path fillRule="evenodd" clipRule="evenodd" d="M10 0.833252C10.3452 0.833252 10.625 1.11307 10.625 1.45825V2.49992H15.1823C15.9877 2.49992 16.6406 3.15284 16.6406 3.95825V10.2083C16.6406 10.7797 16.312 11.2744 15.8333 11.5136V11.616L17.3169 13.0996C17.561 13.3437 17.561 13.7394 17.3169 13.9835C17.0729 14.2276 16.6771 14.2276 16.4331 13.9835L15.7753 13.3258C15.3742 16.1563 12.9413 18.3333 10 18.3333C7.05869 18.3333 4.62582 16.1563 4.22468 13.3258L3.56694 13.9835C3.32286 14.2276 2.92714 14.2276 2.68306 13.9835C2.43898 13.7394 2.43898 13.3437 2.68306 13.0996L4.16667 11.616V11.5136C3.68805 11.2744 3.35938 10.7797 3.35938 10.2083V3.95825C3.35938 3.15284 4.01229 2.49992 4.81771 2.49992H9.375V1.45825C9.375 1.11307 9.65482 0.833252 10 0.833252ZM4.81771 3.74992C4.70265 3.74992 4.60938 3.84319 4.60938 3.95825V10.2083C4.60938 10.3233 4.70265 10.4166 4.81771 10.4166H15.1823C15.2974 10.4166 15.3906 10.3233 15.3906 10.2083V3.95825C15.3906 3.84319 15.2974 3.74992 15.1823 3.74992H4.81771ZM7.29167 5.83325C7.63684 5.83325 7.91667 6.11307 7.91667 6.45825V7.70825C7.91667 8.05343 7.63684 8.33325 7.29167 8.33325C6.94649 8.33325 6.66667 8.05343 6.66667 7.70825V6.45825C6.66667 6.11307 6.94649 5.83325 7.29167 5.83325ZM12.7083 5.83325C13.0535 5.83325 13.3333 6.11307 13.3333 6.45825V7.70825C13.3333 8.05343 13.0535 8.33325 12.7083 8.33325C12.3632 8.33325 12.0833 8.05343 12.0833 7.70825V6.45825C12.0833 6.11307 12.3632 5.83325 12.7083 5.83325Z" fill={fill} />
        </svg>
    );
};

export default RobotIcon;