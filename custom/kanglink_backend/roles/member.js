const BaseRole = require("../../../baas/core/BaseRole");

class member extends BaseRole {
  static id = "role_member_1750440288179";
  static name = "Member";
  static slug = "member";
  static permissions = {
    routes: [],
    canCreateUsers: true,
    canEditUsers: true,
    canDeleteUsers: false,
    canManageRoles: false,
    canLogin: true,
    canRegister: true,
    canForgot: true,
    canReset: true,
    canGoogleLogin: true,
    canFacebookLogin: true,
    canInstagramLogin: true,
    canLinkedinLogin: true,
    canAppleLogin: true,
    canMicrosoftLogin: true,
    canMagicLinkLogin: false,
    canTwitterLogin: false,
    needs2FA: true,
    canSetPermissions: false,
    canPreference: true,
    canVerifyEmail: true,
    canUpload: true,
    canStripe: true,
    canStripeWebhook: true,
    canRealTime: true,
    canAI: true,
    canUpdateEmail: true,
    canUpdatePassword: true,
    canUpdateOtherUsers: false,
    treeql: {
      enabled: true,
      models: {
        user: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        job: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        cms: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        uploads: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        tokens: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        preference: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        day: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        enrollment: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        exercise: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        exercise_instance: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        exercise_link: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        program: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        qualification: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        session: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        specialization: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        split: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        video: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        week: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        coupon: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: false,
            put: false,
            delete: false,
            paginate: true,
            join: true,
          },
        },
        coupon_usage: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: false,
            paginate: true,
            join: true,
          },
        },
        discount: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: false,
            put: false,
            delete: false,
            paginate: true,
            join: true,
          },
        },
        program_discount: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: false,
            put: false,
            delete: false,
            paginate: true,
            join: true,
          },
        },
        post_feed: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        comment: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        reaction: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
        favorite: {
          allowed: true,
          blacklistedFields: [],
          operations: {
            get: true,
            getOne: true,
            getAll: true,
            post: true,
            put: true,
            delete: true,
            paginate: true,
            join: true,
          },
        },
      },
    },
    companyScoped: false,
  };
  static index = 1;

  // Helper method to check route permission
  static hasRoutePermission(routeId) {
    return this.permissions?.routes?.includes(routeId) || false;
  }

  // List of models this role can access
  static allowedModels = [
    "user",
    "job",
    "cms",
    "uploads",
    "tokens",
    "preference",
  ];

  /**
   * Check if role can access a specific model
   * @param {string} modelName - Name of the model to check
   * @returns {boolean} Whether model access is allowed
   */
  static canAccessModel(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.allowed || false;
  }

  /**
   * Get blacklisted fields for a model
   * @param {string} modelName - Name of the model
   * @returns {string[]} Array of blacklisted field names
   */
  static getBlacklistedFields(modelName) {
    return (
      this.permissions?.treeql?.models?.[modelName]?.blacklistedFields || []
    );
  }

  /**
   * Check if role can perform an operation on a model
   * @param {string} modelName - Name of the model
   * @param {string} operation - Operation to check (get, getOne, getAll, post, put, delete, paginate, join)
   * @returns {boolean} Whether operation is allowed
   */
  static canPerformOperation(modelName, operation) {
    const modelConfig = this.permissions?.treeql?.models?.[modelName];
    if (!modelConfig?.allowed) {
      return false;
    }
    return modelConfig.operations?.[operation] || false;
  }

  /**
   * Get all allowed operations for a model
   * @param {string} modelName - Name of the model
   * @returns {Object<string, boolean>} Object mapping operations to permission status
   */
  static getAllowedOperations(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.operations || {};
  }

  /**
   * Check if TreeQL is enabled for this role
   * @returns {boolean} Whether TreeQL is enabled
   */
  static isTreeQLEnabled() {
    return this.permissions?.treeql?.enabled || false;
  }
}

module.exports = member;
