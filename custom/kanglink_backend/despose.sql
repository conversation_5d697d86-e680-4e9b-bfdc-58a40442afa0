CREATE TABLE
  IF NOT EXISTS kanglink_user (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `email` VARCHAR(512) NOT NULL,
    `password` VARCHAR(100) NOT NULL,
    `login_type` INT DEFAULT 0 NOT NULL,
    `role_id` VARCHAR(512),
    `data` TEXT,
    `status` INT DEFAULT 0 NOT NULL,
    `verify` BOOLEAN DEFAULT '0' NOT NULL,
    `two_factor_authentication` BOOLEAN DEFAULT '0',
    `company_id` INT DEFAULT '0',
    `stripe_uid` VARCHAR(512),
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );

CREATE TABLE
  IF NOT EXISTS kanglink_preference (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `first_name` VA<PERSON><PERSON><PERSON>(512),
    `last_name` VARCHA<PERSON>(512),
    `phone` VARCHAR(512),
    `photo` VARCHAR(512),
    `user_id` INT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );

CREATE TABLE
  IF NOT EXISTS kanglink_tokens (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT NOT NULL,
    `token` VARCHAR(512) NOT NULL,
    `code` VARCHAR(512) NOT NULL,
    `type` INT DEFAULT 0 NOT NULL,
    `data` TEXT,
    `status` INT DEFAULT 1 NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expired_at` TIMESTAMP
  );

CREATE TABLE
  IF NOT EXISTS kanglink_uploads (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `url` VARCHAR(512) NOT NULL,
    `caption` VARCHAR(512),
    `user_id` INT,
    `width` INT,
    `height` INT,
    `type` INT DEFAULT 0 NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );

CREATE TABLE
  IF NOT EXISTS kanglink_cms (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `label` VARCHAR(512) NOT NULL,
    `type` INT DEFAULT 0 NOT NULL,
    `value` LONGTEXT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );

CREATE TABLE
  IF NOT EXISTS kanglink_job (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `task` VARCHAR(512) NOT NULL,
    `arguments` TEXT,
    `time_interval` VARCHAR(512) DEFAULT 'once',
    `retries` INT DEFAULT '1',
    `status` INT DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  );

CREATE TABLE
  IF NOT EXISTS kanglink_program (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT NOT NULL,
    `program_name` VARCHAR(512),
    `type_of_program` VARCHAR(512),
    `program_description` LONGTEXT,
    `payment_plan` JSON,
    `track_progress` BOOLEAN DEFAULT FALSE,
    `allow_comments` BOOLEAN DEFAULT FALSE,
    `allow_private_messages` BOOLEAN DEFAULT FALSE,
    `target_levels` JSON,
    `split_program` INT,
    `currency` VARCHAR(10),
    `days_for_preview` INT,
    `image` TEXT,
    `status` VARCHAR(50) DEFAULT 'draft',
    `created_at` DATETIME,
    `updated_at` DATETIME
  );

CREATE TABLE
  IF NOT EXISTS kanglink_split (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `program_id` INT NOT NULL,
    `equipment_required` VARCHAR(512),
    `title` VARCHAR(512),
    `full_price` DECIMAL(10, 2),
    `subscription` DECIMAL(10, 2),
    `created_at` DATETIME,
    `updated_at` DATETIME,
    FOREIGN KEY (`program_id`) REFERENCES kanglink_program (`id`) ON DELETE CASCADE
  );

CREATE TABLE
  IF NOT EXISTS kanglink_week (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `split_id` INT NOT NULL,
    `title` VARCHAR(512),
    `user_id` INT NOT NULL,
    `week_order` INT,
    `created_at` DATETIME,
    `updated_at` DATETIME,
    FOREIGN KEY (`split_id`) REFERENCES kanglink_split (`id`) ON DELETE CASCADE
  );

CREATE TABLE
  IF NOT EXISTS kanglink_day (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `week_id` INT NOT NULL,
    `title` VARCHAR(512),
    `user_id` INT NOT NULL,
    `day_order` INT,
    `is_rest_day` BOOLEAN DEFAULT FALSE,
    `created_at` DATETIME,
    `updated_at` DATETIME,
    FOREIGN KEY (`week_id`) REFERENCES kanglink_week (`id`) ON DELETE CASCADE
  );

CREATE TABLE
  IF NOT EXISTS kanglink_session (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `day_id` INT NOT NULL,
    `title` VARCHAR(512),
    `user_id` INT NOT NULL,
    `session_order` INT,
    `created_at` DATETIME,
    `updated_at` DATETIME,
    FOREIGN KEY (`day_id`) REFERENCES kanglink_day (`id`) ON DELETE CASCADE
  );

CREATE TABLE
  IF NOT EXISTS kanglink_exercise (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT NOT NULL,
    `name` VARCHAR(512),
    `type` INT DEFAULT 0,
    `created_at` DATETIME,
    `updated_at` DATETIME
  );

CREATE TABLE
  IF NOT EXISTS kanglink_video (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(512),
    `type` INT DEFAULT 0,
    `url` VARCHAR(512),
    `user_id` INT,
    `created_at` DATETIME,
    `updated_at` DATETIME
  );

CREATE TABLE
  IF NOT EXISTS kanglink_exercise_instance (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT NOT NULL,
    `session_id` INT NOT NULL,
    `exercise_id` INT,
    `video_id` INT,
    `sets` INT,
    `reps_or_time` VARCHAR(512),
    `reps_time_type` VARCHAR(50),
    `exercise_details` TEXT,
    `rest_duration_minutes` INT,
    `rest_duration_seconds` INT,
    `label` VARCHAR(512),
    `label_number` INT,
    `is_linked` BOOLEAN DEFAULT FALSE,
    `exercise_order` INT,
    `created_at` DATETIME,
    `updated_at` DATETIME,
    FOREIGN KEY (`session_id`) REFERENCES kanglink_session (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`exercise_id`) REFERENCES kanglink_exercise (`id`) ON DELETE SET NULL,
    FOREIGN KEY (`video_id`) REFERENCES kanglink_video (`id`) ON DELETE SET NULL
  );

-- Discount System Tables
CREATE TABLE
  IF NOT EXISTS kanglink_program_discount (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `program_id` INT NOT NULL,
    `affiliate_link` TEXT,
    `sale_discount_type` ENUM ('fixed', 'percentage') NULL,
    `sale_discount_value` DECIMAL(10, 2) NULL,
    `sale_apply_to_all` BOOLEAN DEFAULT FALSE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`program_id`) REFERENCES kanglink_program (`id`) ON DELETE CASCADE,
    UNIQUE KEY `unique_program_discount` (`program_id`)
  );

CREATE TABLE
  IF NOT EXISTS kanglink_discount (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `program_id` INT NOT NULL,
    `split_id` INT NULL,
    `discount_type` ENUM ('fixed', 'percentage') NOT NULL,
    `discount_value` DECIMAL(10, 2) NOT NULL,
    `applies_to` ENUM ('subscription', 'full_payment', 'both') NOT NULL,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`program_id`) REFERENCES kanglink_program (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`split_id`) REFERENCES kanglink_split (`id`) ON DELETE CASCADE,
    INDEX `idx_program_split` (`program_id`, `split_id`),
    INDEX `idx_applies_to` (`applies_to`)
  );

CREATE TABLE
  IF NOT EXISTS kanglink_coupon (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `program_id` INT NOT NULL,
    `code` VARCHAR(50) NOT NULL,
    `discount_type` ENUM ('fixed', 'percentage') NOT NULL,
    `discount_value` DECIMAL(10, 2) NOT NULL,
    `applies_to` ENUM ('subscription', 'full_payment', 'both') NOT NULL,
    `is_active` BOOLEAN DEFAULT TRUE,
    `expiry_date` DATETIME NULL,
    `usage_limit` INT NULL,
    `used_count` INT DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`program_id`) REFERENCES kanglink_program (`id`) ON DELETE CASCADE,
    UNIQUE KEY `unique_coupon_code` (`code`),
    INDEX `idx_program_coupon` (`program_id`),
    INDEX `idx_coupon_active` (`code`, `is_active`, `expiry_date`)
  );

CREATE TABLE
  IF NOT EXISTS kanglink_coupon_usage (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `coupon_id` INT NOT NULL,
    `user_id` INT NOT NULL,
    `program_id` INT NOT NULL,
    `split_id` INT NULL,
    `discount_amount` DECIMAL(10, 2) NOT NULL,
    `used_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  );

CREATE TABLE
  `kanglink_stripe_invoice` (
    `id` int (11) NOT NULL AUTO_INCREMENT,
    `user_id` int (11) DEFAULT NULL,
    `stripe_id` varchar(512) DEFAULT NULL,
    `object` text DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE
  `kanglink_stripe_price` (
    `id` int (11) NOT NULL AUTO_INCREMENT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `name` varchar(512) DEFAULT NULL,
    `product_id` varchar(512) DEFAULT NULL,
    `stripe_id` varchar(512) DEFAULT NULL,
    `is_usage_metered` int (11) DEFAULT NULL,
    `usage_limit` int (11) DEFAULT NULL,
    `object` mediumtext DEFAULT NULL,
    `amount` float DEFAULT NULL,
    `trial_days` int (11) DEFAULT NULL,
    `type` varchar(512) DEFAULT NULL,
    `status` int (11) DEFAULT NULL,
    PRIMARY KEY (`id`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE
  `kanglink_stripe_product` (
    `id` int (11) NOT NULL AUTO_INCREMENT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `name` varchar(512) DEFAULT NULL,
    `product_id` varchar(512) DEFAULT NULL,
    `stripe_id` varchar(512) DEFAULT NULL,
    `object` longtext DEFAULT NULL,
    `status` int (11) DEFAULT NULL,
    PRIMARY KEY (`id`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE
  `kanglink_stripe_subscription` (
    `id` int (11) NOT NULL AUTO_INCREMENT,
    `stripe_id` varchar(512) DEFAULT NULL,
    `price_id` varchar(512) DEFAULT NULL,
    `user_id` int (11) DEFAULT NULL,
    `object` text DEFAULT NULL,
    `status` varchar(512) DEFAULT NULL,
    `is_lifetime` tinyint (1) DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE
  `kanglink_stripe_webhook` (
    `id` int (11) NOT NULL AUTO_INCREMENT,
    `stripe_id` varchar(512) DEFAULT NULL,
    `idempotency_key` varchar(512) DEFAULT NULL,
    `description` varchar(512) DEFAULT NULL,
    `event_type` varchar(512) DEFAULT NULL,
    `resource_type` varchar(512) DEFAULT NULL,
    `object` text DEFAULT NULL,
    `is_handled` tinyint (1) DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
  ) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci;

CREATE TABLE
  kanglink_enrollment (
    `id` int (11) NOT NULL AUTO_INCREMENT,
    `trainer_id` int (11) NOT NULL,
    `athlete_id` int (11) NOT NULL,
    `program_id` int (11) NOT NULL,
    `split_id` int (11) NOT NULL,
    `payment_type` VARCHAR(20) NOT NULL, -- enum: subscription | one_time
    `amount` FLOAT NOT NULL,
    `currency` VARCHAR(10) NOT NULL DEFAULT 'USD',
    `enrollment_date` DATETIME,
    `expiry_date` DATETIME,
    `status` VARCHAR(20) NOT NULL DEFAULT 'pending', -- enum: active | expired | cancelled | pending
    `payment_status` VARCHAR(20) NOT NULL DEFAULT 'pending', -- enum: paid | pending | failed | refunded
    `stripe_subscription_id` VARCHAR(255),
    `stripe_payment_intent_id` VARCHAR(255),
    `stripe_customer_id` VARCHAR(255),
    `created_at` DATETIME,
    `updated_at` DATETIME
  );