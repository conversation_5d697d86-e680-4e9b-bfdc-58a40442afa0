-- Performance Optimization Indexes for Landing Page Endpoints
-- Execute these indexes to optimize the performance of landing page queries

-- ============================================================================
-- PROGRAMS TABLE INDEXES
-- ============================================================================

-- Index for filtering by program status (published, draft, etc.)
CREATE INDEX IF NOT EXISTS idx_kanglink_program_status 
ON kanglink_program(status);

-- Index for filtering by program type/category
CREATE INDEX IF NOT EXISTS idx_kanglink_program_type 
ON kanglink_program(type_of_program);

-- Index for trainer-specific queries
CREATE INDEX IF NOT EXISTS idx_kanglink_program_user_id 
ON kanglink_program(user_id);

-- Index for sorting by creation date
CREATE INDEX IF NOT EXISTS idx_kanglink_program_created_at 
ON kanglink_program(created_at);

-- Composite index for published programs by trainer
CREATE INDEX IF NOT EXISTS idx_kanglink_program_status_user_id 
ON kanglink_program(status, user_id);

-- Index for target levels JSON field (MySQL 5.7+)
CREATE INDEX IF NOT EXISTS idx_kanglink_program_target_levels 
ON kanglink_program((CAST(target_levels AS JSON)));

-- ============================================================================
-- POST FEED TABLE INDEXES (for ratings and reviews)
-- ============================================================================

-- Index for filtering reviews with ratings
CREATE INDEX IF NOT EXISTS idx_kanglink_post_feed_type_rating 
ON kanglink_post_feed(post_type, rating);

-- Index for program-specific reviews
CREATE INDEX IF NOT EXISTS idx_kanglink_post_feed_program_id 
ON kanglink_post_feed(program_id);

-- Composite index for efficient rating calculations
CREATE INDEX IF NOT EXISTS idx_kanglink_post_feed_program_type_rating 
ON kanglink_post_feed(program_id, post_type, rating);

-- Index for sorting by creation date
CREATE INDEX IF NOT EXISTS idx_kanglink_post_feed_created_at 
ON kanglink_post_feed(created_at);

-- ============================================================================
-- USER TABLE INDEXES
-- ============================================================================

-- Index for filtering by role and status
CREATE INDEX IF NOT EXISTS idx_kanglink_user_role_status 
ON kanglink_user(role_id, status);

-- Index for sorting by creation date
CREATE INDEX IF NOT EXISTS idx_kanglink_user_created_at 
ON kanglink_user(created_at);

-- Index for email lookups (if not already primary)
CREATE INDEX IF NOT EXISTS idx_kanglink_user_email 
ON kanglink_user(email);

-- Index for user data JSON field (MySQL 5.7+)
CREATE INDEX IF NOT EXISTS idx_kanglink_user_data 
ON kanglink_user((CAST(data AS JSON)));

-- ============================================================================
-- PREFERENCE TABLE INDEXES
-- ============================================================================

-- Index for user preference lookups
CREATE INDEX IF NOT EXISTS idx_kanglink_preference_user_id 
ON kanglink_preference(user_id);

-- Index for name-based searches
CREATE INDEX IF NOT EXISTS idx_kanglink_preference_names 
ON kanglink_preference(first_name, last_name);

-- ============================================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- ============================================================================

-- For top-rated programs query optimization
CREATE INDEX IF NOT EXISTS idx_kanglink_program_status_type_created 
ON kanglink_program(status, type_of_program, created_at);

-- For trainer rating calculations
CREATE INDEX IF NOT EXISTS idx_kanglink_post_feed_rating_calc 
ON kanglink_post_feed(post_type, rating, program_id, created_at);

-- ============================================================================
-- FULL-TEXT SEARCH INDEXES (for search functionality)
-- ============================================================================

-- Full-text search on program names and descriptions
CREATE FULLTEXT INDEX IF NOT EXISTS idx_kanglink_program_search 
ON kanglink_program(program_name, program_description);

-- Full-text search on trainer names
CREATE FULLTEXT INDEX IF NOT EXISTS idx_kanglink_preference_search 
ON kanglink_preference(first_name, last_name);

-- ============================================================================
-- PERFORMANCE MONITORING QUERIES
-- ============================================================================

-- Query to check index usage
-- Run this periodically to ensure indexes are being used:
/*
SHOW INDEX FROM kanglink_program;
SHOW INDEX FROM kanglink_post_feed;
SHOW INDEX FROM kanglink_user;
SHOW INDEX FROM kanglink_preference;
*/

-- Query to analyze slow queries
-- Enable slow query log and analyze:
/*
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;
SET GLOBAL slow_query_log_file = '/var/log/mysql/slow.log';
*/

-- ============================================================================
-- CACHE OPTIMIZATION SETTINGS
-- ============================================================================

-- Recommended MySQL settings for better performance:
/*
-- Increase query cache size
SET GLOBAL query_cache_size = 268435456; -- 256MB

-- Increase buffer pool size (adjust based on available RAM)
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB

-- Optimize for read-heavy workloads
SET GLOBAL innodb_read_ahead_threshold = 0;
*/

-- ============================================================================
-- MAINTENANCE QUERIES
-- ============================================================================

-- Analyze tables to update statistics (run weekly)
/*
ANALYZE TABLE kanglink_program;
ANALYZE TABLE kanglink_post_feed;
ANALYZE TABLE kanglink_user;
ANALYZE TABLE kanglink_preference;
*/

-- Optimize tables to defragment (run monthly)
/*
OPTIMIZE TABLE kanglink_program;
OPTIMIZE TABLE kanglink_post_feed;
OPTIMIZE TABLE kanglink_user;
OPTIMIZE TABLE kanglink_preference;
*/

-- ============================================================================
-- NOTES
-- ============================================================================

/*
1. These indexes are designed specifically for the landing page endpoints
2. Monitor query performance using EXPLAIN before and after index creation
3. Some indexes may already exist - IF NOT EXISTS prevents errors
4. JSON indexes require MySQL 5.7+ and may need adjustment for older versions
5. Full-text indexes improve search performance but use additional storage
6. Regular maintenance (ANALYZE/OPTIMIZE) keeps indexes efficient
7. Monitor index usage with SHOW INDEX and query performance logs
8. Consider partitioning for very large tables (millions of records)
*/
