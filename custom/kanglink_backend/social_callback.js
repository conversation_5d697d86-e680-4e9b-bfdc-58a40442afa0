module.exports = {
  google_callback: async ({
    req,
    res,
    project,
    state,
    projectId,
    role,
    needRefreshToken,
    refreshToken,
    database,
    sdk,
    manaknightSDK,
    originalUrl,
    config,
    googleConfig,
  }) => {
    console.log("Google callback function called for role:", role);
    const hostname =
      config.hostnames[projectId] ?? `${projectId}.manaknightdigital.com`;

    try {
      const NodeGoogleLogin = require("node-google-login");
      const AuthService = require("../../baas/services/AuthService");
      const JwtService = require("../../baas/services/JwtService");

      // Get user profile from Google
      const googleLogin = new NodeGoogleLogin(googleConfig);
      const userProfile = await googleLogin.getUserProfile(req.query.code);

      // Initialize AuthService
      let service = new AuthService();

      // Login with get_all_data=true to get full user data
      let userData = await service.googleLogin(
        sdk,
        projectId,
        userProfile.user,
        userProfile.tokens,
        role,
        null,
        true // get_all_data=true
      );

      // Check if login returned an error string
      if (typeof userData == "string") {
        const response = {
          error: true,
          message: userData,
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      // always create refresh token

      const newRefreshToken = JwtService.createAccessToken(
        {
          user_id: userData.id,
          role: role,
        },
        config.refresh_jwt_expire,
        config.jwt_key
      );
      let expireDate = new Date();
      expireDate.setSeconds(
        expireDate.getSeconds() + config.refresh_jwt_expire
      );
      await service.saveRefreshToken(
        sdk,
        projectId,
        userData.id,
        newRefreshToken,
        expireDate
      );

      // Prepare response with full user data
      const responseData = {
        error: false,
        ...userData,
        role: userData.role_id,
        token: JwtService.createAccessToken(
          {
            user_id: userData.id,
            role: userData.role_id,
          },
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: userData.id,
        refresh_token: newRefreshToken,
        profile_update: userData?.profile_update || false,
        data: undefined,
      };

      const data = JSON.stringify(responseData);
      const encodedURI = encodeURI(data);
      console.log(
        "redirecting to",
        `http://localhost:3001/login/oauth?data=${encodedURI}`
      );
      // Redirect to frontend with full data
      res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
    } catch (error) {
      console.error("Error in google_callback:", error);
      const response = {
        error: true,
        message: error.message || "Google login failed",
      };
      const data = JSON.stringify(response);
      const encodedURI = encodeURI(data);
      return res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
    }
  },
  apple_callback: async ({
    req,
    res,
    project,
    parts,
    projectId,
    role,
    needRefreshToken,
    refreshToken,
    database,
    sdk,
    manaknightSDK,
    originalUrl,
    config,
    googleConfig,
  }) => {
    console.log("Apple callback function called for role:", role);

    // Add custom login logic here
    // You can access the role parameter to handle different roles

    return;
  },
  microsoft_callback: async ({
    req,
    res,
    project,
    state,
    projectId,
    role,
    needRefreshToken,
    refreshToken,
    sdk,
    manaknightSDK,
    config,
    code,
  }) => {
    console.log("Microsoft callback function called for role:", role);

    // Add custom login logic here
    // You can access the role parameter to handle different roles

    return;
  },
  instagram_callback: async ({
    req,
    res,
    project,
    state,
    projectId,
    role,
    needRefreshToken,
    refreshToken,
    sdk,
    manaknightSDK,
    config,
    code,
  }) => {
    console.log("Instagram callback function called for role:", role);

    // Add custom login logic here
    // You can access the role parameter to handle different roles

    return;
  },
  linkedin_callback: async ({
    req,
    res,
    project,
    state,
    projectId,
    role,
    needRefreshToken,
    refreshToken,
    sdk,
    manaknightSDK,
    config,
    code,
  }) => {
    console.log("LinkedIn callback function called for role:", role);
    const hostname =
      config.hostnames[projectId] ?? `${projectId}.manaknightdigital.com`;
    try {
      const AuthService = require("../../baas/services/AuthService");
      const JwtService = require("../../baas/services/JwtService");
      const axios = require("axios");

      // LinkedIn OAuth configuration
      const linkedinConfig = {
        clientID: config.linkedin.client_id,
        clientSecret: config.linkedin.client_secret,
        redirectURI:
          config.linkedin.redirect_url ||
          "http://localhost:5172/v2/api/lambda/linkedin/code",
        scopes: ["openid", "profile", "email"],
      };

      // console.log("LinkedIn config:", linkedinConfig);

      // Exchange the authorization code for an access token
      let tokenResponse;
      try {
        tokenResponse = await axios.post(
          "https://www.linkedin.com/oauth/v2/accessToken",
          null,
          {
            params: {
              grant_type: "authorization_code",
              code: req.query.code,
              redirect_uri: linkedinConfig.redirectURI,
              client_id: linkedinConfig.clientID,
              client_secret: linkedinConfig.clientSecret,
            },
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );
      } catch (error) {
        console.error(
          "LinkedIn token exchange error:",
          error.response ? error.response.data : error.message
        );
        const response = {
          error: true,
          message: "Failed to exchange code for token",
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      const tokens = tokenResponse.data;

      // Get user profile information
      let profileResponse;
      try {
        profileResponse = await axios.get(
          "https://api.linkedin.com/v2/userinfo",
          {
            headers: {
              Authorization: `Bearer ${tokens.access_token}`,
            },
          }
        );
      } catch (error) {
        console.error(
          "LinkedIn profile request error:",
          error.response ? error.response.data : error.message
        );
        const response = {
          error: true,
          message: "Failed to get LinkedIn profile",
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      // With the new scopes, email is included in the userinfo response
      const profile = profileResponse.data;
      const email = profile.email;

      // Format user data
      const user = {
        id: profile.sub,
        firstName: profile.given_name,
        lastName: profile.family_name,
        email: email,
      };

      // Initialize AuthService
      let service = new AuthService();

      // Login with get_all_data=true to get full user data
      let userResult = await service.linkedinLogin(
        sdk,
        projectId,
        user,
        tokens,
        role,
        null,
        true // get_all_data=true
      );

      // Check if login returned an error string
      if (typeof userResult == "string") {
        const response = {
          error: true,
          message: userResult,
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      // Always create refresh token
      const newRefreshToken = JwtService.createAccessToken(
        {
          user_id: userResult.id,
          role: userResult.role_id,
        },
        config.refresh_jwt_expire,
        config.jwt_key
      );
      let expireDate = new Date();
      expireDate.setSeconds(
        expireDate.getSeconds() + config.refresh_jwt_expire
      );
      await service.saveRefreshToken(
        sdk,
        projectId,
        userResult.id,
        newRefreshToken,
        expireDate
      );

      // Prepare response with full user data
      const responseData = {
        error: false,
        ...userResult,
        role: userResult.role_id,
        token: JwtService.createAccessToken(
          {
            user_id: userResult.id,
            role: userResult.role_id,
          },
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: userResult.id,
        refresh_token: newRefreshToken,
        profile_update: userResult?.profile_update || false,
        data: undefined,
      };

      const data = JSON.stringify(responseData);
      const encodedURI = encodeURI(data);
      console.log(
        "redirecting to",
        `http://localhost:3001/login/oauth?data=${encodedURI}`
      );
      // Redirect to frontend with full data
      res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
    } catch (error) {
      console.error("Error in linkedin_callback:", error);
      const response = {
        error: true,
        message: error.message || "LinkedIn login failed",
      };
      const data = JSON.stringify(response);
      const encodedURI = encodeURI(data);
      return res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
    }
  },
  facebook_callback: async ({
    req,
    res,
    project,
    state,
    projectId,
    role,
    needRefreshToken,
    refreshToken,
    sdk,
    manaknightSDK,
    config,
    code,
  }) => {
    console.log("Facebook callback function called for role:", role);
    const hostname =
      config.hostnames[projectId] ?? `${projectId}.manaknightdigital.com`;
    try {
      const AuthService = require("../../baas/services/AuthService");
      const JwtService = require("../../baas/services/JwtService");

      // Get access token from Facebook
      const facebookAccessTokenCall = await fetch(
        `https://graph.facebook.com/v4.0/oauth/access_token?client_id=${config.facebook.client_id}&client_secret=${config.facebook.client_secret}&code=${req.query.code}&redirect_uri=${config.facebook.callback_uri}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const tokenData = await facebookAccessTokenCall.json();
      console.log("Facebook token data:", tokenData);

      if (facebookAccessTokenCall.status !== 200) {
        const response = {
          error: true,
          message: "Failed to get Facebook access token",
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      // Get user profile from Facebook
      const facebookMeCall = await fetch(
        `https://graph.facebook.com/v4.0/me?fields=id,email,first_name,last_name&access_token=${tokenData.access_token}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const userData = await facebookMeCall.json();

      if (facebookMeCall.status !== 200) {
        const response = {
          error: true,
          message: "Failed to get Facebook user profile",
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      // Initialize AuthService
      let service = new AuthService();

      // Login with get_all_data=true to get full user data
      let userResult = await service.facebookLogin(
        sdk,
        projectId,
        userData,
        tokenData,
        role,
        null,
        true // get_all_data=true
      );

      // Check if login returned an error string
      if (typeof userResult == "string") {
        const response = {
          error: true,
          message: userResult,
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      // Always create refresh token
      const newRefreshToken = JwtService.createAccessToken(
        {
          user_id: userResult.id,
          role: userResult.role_id,
        },
        config.refresh_jwt_expire,
        config.jwt_key
      );
      let expireDate = new Date();
      expireDate.setSeconds(
        expireDate.getSeconds() + config.refresh_jwt_expire
      );
      await service.saveRefreshToken(
        sdk,
        projectId,
        userResult.id,
        newRefreshToken,
        expireDate
      );

      // Prepare response with full user data
      const responseData = {
        error: false,
        ...userResult,
        role: userResult.role_id,
        token: JwtService.createAccessToken(
          {
            user_id: userResult.id,
            role: userResult.role_id,
          },
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: userResult.id,
        refresh_token: newRefreshToken,
        profile_update: userResult?.profile_update || false,
        data: undefined,
      };

      const data = JSON.stringify(responseData);
      const encodedURI = encodeURI(data);
      console.log(
        "redirecting to",
        `http://localhost:3001/login/oauth?data=${encodedURI}`
      );
      // Redirect to frontend with full data
      res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
    } catch (error) {
      console.error("Error in facebook_callback:", error);
      const response = {
        error: true,
        message: error.message || "Facebook login failed",
      };
      const data = JSON.stringify(response);
      const encodedURI = encodeURI(data);
      return res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
    }
  },
};
