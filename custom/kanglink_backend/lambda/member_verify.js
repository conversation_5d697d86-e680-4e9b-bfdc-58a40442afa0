const JwtService = require("../../../baas/services/JwtService");

module.exports = function (app) {
  const config = app.get("configuration");

  app.post("/v1/api/kanglink/member/lambda/verify", async function (req, res) {
    try {
      const { token } = req.body;

      if (!token) {
        return res.status(400).json({
          error: true,
          message: "Verification token is required"
        });
      }

      const RoleClass = require(`../roles/member`);
      if (!RoleClass.canVerifyEmail()) {
        return res.status(403).json({
          error: true,
          message: `${RoleClass.name} cannot verify email`
        });
      }

      // Verify the token
      let decoded;
      try {
        decoded = JwtService.verifyToken(token, config.jwt_key);
      } catch (err) {
        return res.status(400).json({
          error: true,
          message: "Invalid or expired verification token"
        });
      }
      console.log("decoded", decoded);

      if(!decoded) {
        return res.status(400).json({
          error: true,
          message: "Invalid or expired verification token"
        });
      }

      const sdk = app.get("sdk");

      // Get the verification token from database
      sdk.setProjectId("kanglink");
      const verificationToken = await sdk.findOne('tokens', {
        token: token,
        user_id: decoded.user_id
      });

     

      if (!verificationToken) {
        return res.status(400).json({
          error: true,
          message: "Verification token not found"
        });
      }

      // Check if token is expired
      if (new Date() > new Date(verificationToken.expired_at)) {
        return res.status(400).json({
          error: true,
          message: "Verification token has expired"
        });
      }

      // Update user verify status
      await sdk.update('user',
        { id: decoded.user_id, status: 0 },
        { verify: 1, status: 1 }
      );

      // Delete the used verification token
      await sdk.delete('tokens', {
        token: token
      });

      return res.status(200).json({
        error: false,
        message: "Email verified successfully"
      });

    } catch (err) {
      res.status(500).json({
        error: true,
        message: err.message
      });
    }
  });

  return [
    {
      method: "POST",
      name: "Verify Email API",
      url: "/v1/api/kanglink/member/lambda/verify",
      successBody: '{ "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxOCwidG9rZW4iOiJiMmpzdHptdGk0IiwiaWF0IjoxNzM3NTAwMzA4LCJleHAiOjE3Mzc1ODY3MDh9.cNBhlA7UtF3Mil3IasevFR5NeYJTBOipoMQEvkzo3rE"}',
      successPayload: '{"error":false, "message": "Your email is verified!"}',
      errors: [
        {
          name: "403",
          response: '{"error": true,"message": "Something went wrong!"}'
        },
        {
          name: "401",
          response: '{"error": true,"message": "Failed to verify email"}'
        }
      ],
      needToken: false
    }
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "Email Verification API",
      url: "/v1/api/kanglink/member/lambda/verify",
      successBody: '{ "token": "verification_token" }',
      successPayload: '{"error":false, "message": "Email verified successfully"}',
      errors: [
        {
          name: "403",
          body: '{ "token": "invalid_token" }',
          response: '{"error":true,"message":"Invalid verification token"}',
        },
        {
          name: "403",
          body: '{}',
          response: '{"error":true,"message":"Token is required","validation":[{"field": "token", "message": "Token is required"}]}',
        }
      ],
      needToken: false,
    }
  ];
}; 