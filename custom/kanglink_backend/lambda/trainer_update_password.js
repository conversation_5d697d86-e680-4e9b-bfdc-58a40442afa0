const PasswordService = require("../../../baas/services/PasswordService");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");

const handlePasswordUpdate = async (req, res, sdk, userId) => {
  try {
    // Validate password
    if (!req.body.password) {
      return res.status(403).json({
        error: true,
        message: "Password missing",
        validation: [{ field: "password", message: "Password missing" }]
      });
    }

    // Check role permissions
    const Role = require(`../roles/trainer`);
    if (!Role.canUpdatePassword()) {
      return res.status(403).json({
        error: true,
        message: "Forbidden access"
      });
    }

    sdk.setProjectId("kanglink");

    // Find and validate user
    const result = await sdk.findOne("user", {
      id: userId,
      status: 1
    });

    if (!result) {
      return res.status(401).json({
        error: true,
        message: "Invalid Credentials"
      });
    }

    const currentUserRole = req.role;
    const userRole = result.role_id;

    if (currentUserRole !== userRole && !Role.canUpdateOtherUsers()) {
      return res.status(403).json({
        error: true,
        message: "Forbidden: You do not have permission to update this password"
      });
    }

    // Update password
    const hashPassword = await PasswordService.hash(req.body.password);
    await sdk.updateById(
      "user",
      userId,
      {
        password: hashPassword
      }
    );

    return res.status(200).json({
      error: false,
      message: "Password updated successfully"
    });

  } catch (err) {
    console.error('Password update error:', err);
    return res.status(403).json({
      error: true,
      message: err.message || "Error updating password"
    });
  }
};

const middlewares = [
  TokenMiddleware()
];

module.exports = function (app) {
  // User password update endpoint
  app.post("/v1/api/kanglink/trainer/lambda/update/password", middlewares, async function (req, res) {
    await handlePasswordUpdate(req, res, app.get("sdk"), req.user_id);
  });

  return [
    {
      method: "POST",
      name: "Password Update API",
      url: "/v1/api/kanglink/trainer/lambda/update/password",
      successBody: '{ "password": "a12345"}',
      successPayload: '{"error":false,message: "Password updated successfully"}',
      errors: [
        {
          name: "403",
          body: '{ "password": "a12345"}',
          response: '{"error":true,"message":"Invalid Credentials"}'
        },
        {
          name: "403",
          body: "{}",
          response: '{"error":true,"message":"Invalid Credentials","validation":[{"field": "password", "message": "Password missing"}]}'
        },
        {
          name: "403",
          body: '{ "password": "a12345"}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: true
    },
  ]
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "Password Update API",
      url: "/v1/api/kanglink/trainer/lambda/update/password",
      successBody: '{ "password": "a12345"}',
      successPayload: '{"error":false,message: "Password updated successfully"}',
      errors: [
        {
          name: "403",
          body: '{ "password": "a12345"}',
          response: '{"error":true,"message":"Invalid Credentials"}'
        },
        {
          name: "403",
          body: "{}",
          response: '{"error":true,"message":"Invalid Credentials","validation":[{"field": "password", "message": "Password missing"}]}'
        },
        {
          name: "403",
          body: '{ "password": "a12345"}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: true
    },
  ]
};