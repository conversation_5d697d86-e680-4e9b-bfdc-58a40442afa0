const BackendSDK = require("../../../baas/core/BackendSDK");

module.exports = function (app) {
  app.get("/v2/api/lambda/member/instagram/login", async function (req, res) {
    try {
      const config = app.get("configuration");
      const projectId = req.projectId;
      const role = "member";
      // Instagram Basic Display API only supports these scopes
      const scope = "user_profile,user_media";

      // Validate Instagram configuration
      if (
        !config.instagram ||
        !config.instagram.client_id ||
        !config.instagram.redirect_url
      ) {
        console.error("Instagram configuration missing:", {
          hasInstagramConfig: !!config.instagram,
          hasClientId: !!(config.instagram && config.instagram.client_id),
          hasRedirectUrl: !!(config.instagram && config.instagram.redirect_url),
        });
        return res.status(500).json({
          error: true,
          message: "Instagram OAuth is not properly configured",
        });
      }

      // Check if there's an existing state for this project
      let authURL;
      const sdk = new BackendSDK();
      sdk.setProjectId(projectId);
      const existingState = await sdk.findOne("oauth_state", {
        project_id: projectId,
        role: role,
      });

      if (existingState) {
        const newState = JSON.stringify({
          project_id: Buffer.from(
            `${projectId}:${config.secret}:${config.databaseName}`
          ).toString("base64"),
          role: role,
          hostname: req.hostname,
          is_refresh: false,
        });

        authURL = `https://api.instagram.com/oauth/authorize?client_id=${
          config.instagram.client_id
        }&redirect_uri=${encodeURIComponent(
          config.instagram.redirect_url
        )}&scope=${encodeURIComponent(
          scope
        )}&response_type=code&state=${encodeURIComponent(newState)}`;

        await sdk.updateOne(
          "oauth_state",
          { project_id: projectId, role: role },
          { state: newState }
        );
      } else {
        const currentState = JSON.stringify({
          project_id: Buffer.from(
            `${projectId}:${config.secret}:${config.databaseName}`
          ).toString("base64"),
          role: role,
          hostname: req.hostname,
          is_refresh: false,
        });

        authURL = `https://api.instagram.com/oauth/authorize?client_id=${
          config.instagram.client_id
        }&redirect_uri=${encodeURIComponent(
          config.instagram.redirect_url
        )}&scope=${encodeURIComponent(
          scope
        )}&response_type=code&state=${encodeURIComponent(currentState)}`;

        await sdk.insertOne("oauth_state", {
          project_id: projectId,
          role: role,
          state: currentState,
        });
      }

      res.redirect(authURL);
    } catch (error) {
      console.error("Instagram login error:", error);
      res.status(500).json({
        error: true,
        message: "Failed to initiate Instagram login",
      });
    }
  });

  return [
    {
      method: "GET",
      name: "Member Instagram Login API",
      url: "/v2/api/lambda/member/instagram/login",
      successPayload: "Redirects to Instagram OAuth",
      queryBody: [],
      needToken: false,
      errors: [
        {
          name: "500",
          response:
            '{"error": true, "message": "Failed to initiate Instagram login"}',
        },
      ],
    },
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",
      name: "Member Instagram Login API",
      url: "/v2/api/lambda/member/instagram/login",
      successPayload: "Redirects to Instagram OAuth",
      queryBody: [],
      needToken: false,
      errors: [
        {
          name: "500",
          response:
            '{"error": true, "message": "Failed to initiate Instagram login"}',
        },
      ],
    },
  ];
};
