const JwtService = require("../../../baas/services/JwtService");

const middlewares = [
];

module.exports = function (app) {
  const config = app.get("configuration");
  app.post("/v1/api/kanglink/trainer/lambda/refresh_token", middlewares, async function (req, res) {

    const Role = require(`../roles/trainer`);
    if (!Role.canLogin()) {
      return res.status(403).json({
        error: true,
        message: "Forbidden access"
      });
    }

    try {
      const sdk = app.get("sdk");
      sdk.setProjectId("kanglink");

      const token = req.body.refresh_token;
      if (!token) {
        // user did not pass refreshToken
        return res.status(403).json({
          error: true,
          message: "Refresh token missing",
          validation: [{ field: "refresh_token", message: "Refresh token missing" }],
        });
      } 
      
      const verify = JwtService.verifyRefreshToken(token, config.jwt_key);
     
      if (!verify) {
        // Delete invalid token from database
        
        
        sdk.setTable("tokens");

        await sdk.delete("tokens", {
          token: req.body.refresh_token,
          user_id: req.body.user_id
        });

        return res.status(401).json({
          error: true,
          message: "Token Invalid",
        });
      } 
        
      // Check if token exists in database
      sdk.setTable("tokens");

      const tokenRecord = await sdk.findOne("tokens", {
        token: req.body.refresh_token,
        user_id: req.body.user_id
      });

      if (!tokenRecord) {
        return res.status(401).json({
          error: true,
          message: "Token Invalid",
        });
      }

      
      return res.status(200).json({
        error: false,
        access_token: JwtService.createAccessToken(
          {
            user_id: req.body.user_id,
            role: req.body.role,
          },
          config.access_jwt_expire,
          config.jwt_key
        ),
        refresh_token: token,
      });
    
    } catch (error) {
      return res.status(403).json({
        error: true,
        message: error.message,
      });
    }
  });
  
  return [
    {
      method: "GET",
      name: "Refresh Token API",
      url: "/v1/api/kanglink/trainer/lambda/refresh_token",
      successBody: "{'refresh_token': 'JWT refresh token', 'user_id': 1}",
      successPayload: '{"error":false,"access_token":"New JWT Token","refresh_token":"New JWT Token"}',
      errors: [
        {
          name: "401",
          body: "{'refresh_token': 'JWT refresh token', 'user_id': 1}",
          response: '{"error": true,"message": "Token Invalid"}',
        },
        {
          name: "403",
          body: "{'user_id': 1}",
          response: '{"error": true,"message": "Refresh token missing",validation: [{ field: "refresh_token", message: "Refresh token missing" }] }',
        },
        {
          name: "403",
          body: "{'refresh_token': 'JWT refresh token', 'user_id': 1}",
          response: '{"error": true,"message": "Some error message"}',
        },
      ],
      needToken: true,
    },
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",
      name: "Refresh Token API",
      url: "/v1/api/kanglink/trainer/lambda/refresh_token",
      successBody: "{'refresh_token': 'JWT refresh token', 'user_id': 1}",
      successPayload: '{"error":false,"access_token":"New JWT Token","refresh_token":"New JWT Token"}',
      errors: [
        {
          name: "401",
          body: "{'refresh_token': 'JWT refresh token', 'user_id': 1}",
          response: '{"error": true,"message": "Token Invalid"}',
        },
        {
          name: "403",
          body: "{'user_id': 1}",
          response: '{"error": true,"message": "Refresh token missing",validation: [{ field: "refresh_token", message: "Refresh token missing" }] }',
        },
        {
          name: "403",
          body: "{'refresh_token': 'JWT refresh token', 'user_id': 1}",
          response: '{"error": true,"message": "Some error message"}',
        },
      ],
      needToken: true,
    },
  ];
};

