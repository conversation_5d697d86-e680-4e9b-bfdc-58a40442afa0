const JwtService = require("../../../baas/services/JwtService");
const PasswordService = require("../../../baas/services/PasswordService");
const User = require("../models/user");
const { sqlDateFormat, sqlDateTimeFormat } = require("../../../baas/services/UtilService");

module.exports = function (app) {
  app.post("/v1/api/kanglink/super_admin/lambda/login", async function (req, res) {
    try {
      const config = app.get('configuration');
      const sdk = app.get("sdk");
      sdk.setProjectId("kanglink");

      // Check role permissions
      const Role = require(`../roles/super_admin`);
      if (!Role.canLogin()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Validate input using User model
      const userInput = new User({
        email: req.body.email,
        password: req.body.password,
        role_id: Role.slug,
        login_type: 0,
        status: 0,
        verify: 0,
        company_id: 0
      });

      if (!userInput.isValid()) {
        return res.status(400).json({
          error: true,
          message: "Validation failed",
          validation: userInput.getErrors()
        });
      }

      // Query user from database
      const user = await sdk.findOne('user', {
        email: req.body.email,
        role_id: Role.slug
      });

      if (!user) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      // Verify password using PasswordService
      const validPassword = await PasswordService.compareHash(req.body.password, user.password);
      if (!validPassword) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      // Check account status
      if (user.status == 2) {
        return res.status(403).json({
          error: true,
          message: "Your account is disabled"
        });
      }

      // Check email verification
      if (!user.verify) {
        return res.status(403).json({
          error: true,
          message: "Your email is not verified"
        });
      }

      // Handle refresh token if needed
      let refreshToken;
      if (req.body.is_refresh) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: user.id,
            role: Role.slug
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );

        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.auth.cookie.maxAge);

        // Save refresh token to tokens table instead of user table
        await sdk.create('tokens', {
          user_id: user.id,
          token: refreshToken,
          expired_at: expireDate,
          type: 1,
          created_at: sqlDateFormat(new Date()),
          updated_at: sqlDateTimeFormat(new Date()),
        });
      }

      return res.status(200).json({
        error: false,
        role: Role.slug,
        token: JwtService.createAccessToken(
          {
            user_id: user.id,
            role: Role.slug
          },
          // config.access_jwt_expire,
          60 * 60 * 24,
          config.jwt_key
        ),
        refresh_token: refreshToken,
        expire_at: config.refresh_jwt_expire,
        verify: user.verify,
        status: user.status,
        two_factor_authentication: user.two_factor_authentication,
        two_fa_type: user.two_fa_type,
        user_id: user.id,
        first_name: user.first_name ?? "",
        last_name: user.last_name ?? "",
        photo: user.photo ?? ""
      });

    } catch (err) {
      console.error(err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  // API definition for Postman collection
  module.exports.getPostmanDefinition = function () {
    return [
      {
        method: "POST",
        name: "User Login API",
        url: "/v1/api/kanglink/super_admin/lambda/login",
        successBody: '{ "email": "<EMAIL>", "password": "password123" }',
        successPayload: '{"error":false, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "refresh": "...", "user": {"id": 1, "email": "<EMAIL>", "name": "Test User"}}',
        errors: [
          {
            name: "401",
            body: '{ "email": "<EMAIL>", "password": "wrongpassword" }',
            response: '{"error":true,"message":"Invalid Credentials"}',
          },
          {
            name: "400",
            body: '{}',
            response: '{"error":true,"message":"Email and password are required","validation":[{"field": "email", "message": "Email is required"}, {"field": "password", "message": "Password is required"}]}',
          }
        ],
        needToken: false,
      },
    ];
  };
};
