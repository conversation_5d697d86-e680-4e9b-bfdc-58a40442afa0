// LinkedIn OAuth login

// app.get(
//   "/v1/api/kanglink/trainer/lambda/linkedin/login",
//   async (req, res) => {
//     const config = app.get("configuration");
//     const role = "trainer";
//     const projectId = "kanglink";
//     // const hostname = projectId + ".manaknightdigital.com";
//     const hostname = "baas.mytechpassport.com";

//     const Role = require(`../roles/trainer`);
//     if (!Role.permissions.canLinkedinLogin) {
//       return res.status(403).json({
//         error: true,
//         message: "Forbidden access.",
//       });
//     }

//     const scope = "r_liteprofile r_emailaddress"; // LinkedIn scopes
//     const state = `${projectId}~${hostname}~${role}`;

//     const authorizeUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${
//       config.linkedin.client_id
//     }&redirect_uri=${encodeURIComponent(
//       config.linkedin.redirect_url
//     )}&state=${encodeURIComponent(state)}&scope=${encodeURIComponent(scope)}`;

//     let authURL = authorizeUrl;

//     if (req.query.company_id != undefined) {
//       const newState = `${state}~${req.query.company_id}`;
//       authURL = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${
//         config.linkedin.client_id
//       }&redirect_uri=${encodeURIComponent(
//         config.linkedin.redirect_url
//       )}&state=${encodeURIComponent(newState)}&scope=${encodeURIComponent(
//         scope
//       )}`;
//     }

//     if (req.query.is_refresh != undefined) {
//       const currentState = authURL.includes("company_id")
//         ? `${state}~${req.query.company_id}~with_refresh`
//         : `${state}~with_refresh`;
//       authURL = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${
//         config.linkedin.client_id
//       }&redirect_uri=${encodeURIComponent(
//         config.linkedin.redirect_url
//       )}&state=${encodeURIComponent(currentState)}&scope=${encodeURIComponent(
//         scope
//       )}`;
//     }

//     return res.send(authURL);
//   }
// );
module.exports = function (app) {
  app.get(
    "/v1/api/kanglink/trainer/lambda/linkedin/login",
    async function (req, res) {
      try {
        const config = app.get("configuration");
        const role = "trainer";
        const projectId = "kanglink";
        const hostname =
          config.hostnames[projectId] ?? `${projectId}.manaknightdigital.com`;
        const is_refresh = req.query.is_refresh || false;

        // Convert project ID to base64
        const base64 = Buffer.from(projectId).toString("base64");

        // Create state to pass through the OAuth flow
        const state = JSON.stringify({
          project_id: base64,
          role: role,
          hostname: hostname,
          is_refresh: is_refresh,
        });

        // "http://localhost:5172/v2/api/kanaknight/lambda/linkedin/code" ||
        // "https://baas.mytechpassport.com/v2/api/kanaknight/lambda/linkedin/code"
        // Generate LinkedIn authorization URL
        const linkedinConfig = {
          clientID: config.linkedin.client_id,
          redirectURI:
            config.linkedin.redirect_url ||
            "http://localhost:5172/v2/api/kanaknight/lambda/linkedin/code",
          scopes: ["openid", "profile", "email"],
        };

        // console.log("\n\nlinkedinConfig",linkedinConfig);

        const scope = linkedinConfig.scopes.join("%20");
        const authUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${
          linkedinConfig.clientID
        }&redirect_uri=${encodeURIComponent(
          linkedinConfig.redirectURI
        )}&state=${encodeURIComponent(state)}&scope=${scope}`;

        res.send(authUrl);
      } catch (error) {
        console.error(error);
        return res.status(500).json({
          error: true,
          message: "Failed to generate LinkedIn authorization URL",
        });
      }
    }
  );

  return [
    {
      method: "GET",
      name: "LinkedIn Code API",
      url: "/v2/api/lambda/linkedin/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      queryBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "LinkedIn Login API",
      url: "/v2/api/lambda/linkedin/login",
      successPayload: "['Will redirect to linkedin login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",
      name: "LinkedIn Code API",
      url: "/v2/api/lambda/linkedin/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      queryBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "LinkedIn Login API",
      url: "/v2/api/lambda/linkedin/login",
      successPayload: "['Will redirect to linkedin login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};
