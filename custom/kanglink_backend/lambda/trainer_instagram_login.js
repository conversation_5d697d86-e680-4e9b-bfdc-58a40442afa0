// Instagram login using Facebook's API (Instagram Basic Display API)

module.exports = function (app) {
  app.get(
    "/v1/api/kanglink/trainer/lambda/instagram/login",
    async function (req, res) {
      const config = app.get("configuration");
      const role = "trainer";

      const projectId = "kanglink";
      const hostname = projectId + ".manaknightdigital.com";

      const Role = require(`../roles/trainer`);
      if (!Role.permissions.canInstagramLogin) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access.",
        });
      }

      // Instagram Basic Display API uses Facebook's OAuth
      const stringifiedParams = Object.entries({
        client_id: config.instagram.client_id || config.facebook.client_id,
        redirect_uri:
          config.instagram.callback_uri || config.facebook.callback_uri,
        scope: ["user_profile", "user_media"].join(","),
        response_type: "code",
        auth_type: "rerequest",
        display: "popup",
      })
        .map(
          ([key, value]) =>
            `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
        )
        .join("&");

      // Use Instagram Basic Display API endpoint
      let instagramLoginUrl = `https://api.instagram.com/oauth/authorize?${stringifiedParams}&state=${projectId}~${hostname}~${role}`;
      if (req.query.company_id != undefined) {
        instagramLoginUrl += `~${req.query.company_id}`;
      }

      if (req.query.is_refresh != undefined) {
        instagramLoginUrl += "~" + "with_refresh";
      }

      return res.send(instagramLoginUrl);
    }
  );

  return [
    {
      method: "GET",
      name: "Instagram Login API",
      url: "/v2/api/lambda/instagram/login",
      successPayload: "['Will redirect to instagram login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [],
    },
    {
      method: "GET",
      name: "Instagram Code Webhook",
      url: "/v2/api/lambda/instagram/code",
      successPayload:
        '{"error": false,"role": "admin","qr_code": "qrCode","one_time_token": "token","expire_at": 60,"user_id": 1}',
      queryBody: [{ key: "state", value: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",
      name: "Instagram Login API",
      url: "/v2/api/lambda/instagram/login",
      successPayload: "['Will redirect to instagram login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [],
    },
    {
      method: "GET",
      name: "Instagram Code Webhook",
      url: "/v2/api/lambda/instagram/code",
      successPayload:
        '{"error": false,"role": "admin","qr_code": "qrCode","one_time_token": "token","expire_at": 60,"user_id": 1}',
      queryBody: [{ key: "state", value: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};
