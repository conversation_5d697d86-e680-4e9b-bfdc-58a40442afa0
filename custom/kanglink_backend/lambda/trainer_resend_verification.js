const JwtService = require("../../../baas/services/JwtService");
const User = require("../models/user");
const MailService = require("../../../baas/services/MailService");

module.exports = function (app) {
  const config = app.get("configuration");

  app.post("/v1/api/kanglink/trainer/lambda/resend_verification", async function (req, res) {
    try {
      const { email, role } = req.body;

      if (!email) {
        return res.status(400).json({
          error: true,
          message: "Email is required"
        });
      }

      const RoleClass = require(`../roles/trainer`);
      if (!RoleClass.canVerifyEmail()) {
        return res.status(403).json({
          error: true,
          message: `${RoleClass.name} cannot verify email`
        });
      }

      const sdk = app.get("sdk");
      sdk.setProjectId("kanglink");

      // Find user by email
      const user = await sdk.findOne('user', {
        email: email,
        role_id: RoleClass.slug
      });

      if (!user) {
        return res.status(404).json({
          error: true,
          message: "User not found"
        });
      }

      // Check if user is already verified
      if (user.verify) {
        return res.status(400).json({
          error: true,
          message: "User is already verified"
        });
      }

      // Delete any existing verification tokens for this user
      await sdk.delete('tokens', {
        user_id: user.id,
        type: 3 // Verification type
      });

      // Generate new verification token
      const verificationToken = JwtService.createAccessToken(
        { 
          user_id: user.id, 
          email: user.email,
          role: "trainer",
          type: "email_verification"
        },
        // 7 days
        config.verification_token_expire || 604800,
        config.jwt_key
      );

      // Calculate expiry date
      let expireDate = new Date();
      expireDate.setSeconds(expireDate.getSeconds() + (config.verification_token_expire || 604800));

      // Save new verification token to database
      const tokenPayload = {
        user_id: user.id,
        token: verificationToken,
        type: 3, // Verification type
        data: JSON.stringify({
          email: user.email,
          role: "trainer",
          user_id: user.id
        }),
        status: 1,
        expired_at: expireDate,
        created_at: new Date(),
        updated_at: new Date()
      };

      await sdk.create('tokens', tokenPayload);

      // Send verification email
      const mailService = new MailService(config);
      const verificationUrl = `${req.headers.origin}/verify-email`
      const token = `${verificationToken}&role=trainer`;
      
      try {
        await mailService.sendVerificationEmail(
          user.email,
          token,
          verificationUrl,
        );
        
        return res.status(200).json({
          error: false,
          message: "Verification email sent successfully"
        });
      } catch (err) {
        console.error("Failed to send verification email:", err);
        return res.status(500).json({
          error: true,
          message: "Failed to send verification email"
        });
      }

    } catch (err) {
      console.error("Resend verification error:", err);
      res.status(500).json({
        error: true,
        message: err.message
      });
    }
  });

  return [
    {
      method: "POST",
      name: "Resend Verification Email API",
      url: "/v1/api/kanglink/trainer/lambda/resend_verification",
      successBody: '{ "email": "<EMAIL>", "role": "trainer" }',
      successPayload: '{"error":false, "message": "Verification email sent successfully"}',
      errors: [
        {
          name: "400",
          body: '{}',
          response: '{"error":true,"message":"Email is required"}'
        },
        {
          name: "404",
          body: '{ "email": "<EMAIL>", "role": "trainer" }',
          response: '{"error":true,"message":"User not found"}'
        },
        {
          name: "400",
          body: '{ "email": "<EMAIL>", "role": "trainer" }',
          response: '{"error":true,"message":"User is already verified"}'
        }
      ],
      needToken: false
    }
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "Resend Verification Email API",
      url: "/v1/api/kanglink/trainer/lambda/resend_verification",
      successBody: '{ "email": "<EMAIL>", "role": "trainer" }',
      successPayload: '{"error":false, "message": "Verification email sent successfully"}',
      errors: [
        {
          name: "400",
          body: '{}',
          response: '{"error":true,"message":"Email is required"}',
        },
        {
          name: "404",
          body: '{ "email": "<EMAIL>", "role": "trainer" }',
          response: '{"error":true,"message":"User not found"}',
        },
        {
          name: "400",
          body: '{ "email": "<EMAIL>", "role": "trainer" }',
          response: '{"error":true,"message":"User is already verified"}',
        }
      ],
      needToken: false,
    },
  ];
}; 