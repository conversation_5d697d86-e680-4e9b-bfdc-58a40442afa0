const JwtService = require("../../../baas/services/JwtService");
const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");
const TwoFactorService = require("../../../baas/services/TwoFactorService");
const TwilloSmsService = require("../../../baas/services/TwilloSmsService");
const AuthService = require("../../../baas/services/AuthService")


module.exports = function (app) {
  app.post("/v1/api/kanglink/super_admin/lambda/2fa/login", async function (req, res) {
    try {
      let sdk = app.get("sdk");
      const config = app.get("configuration");
      // sdk.setDatabase("kanglink");
      sdk.setProjectId("kanglink");

      // Check if 2FA is enabled for this role
      // const Role = require(`../custom/${req.params.kanglink}/roles/${req.params.super_admin}`);
      const Role = require(`../roles/super_admin`);
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      // Validate required fields
      if (!req.body.email || !req.body.password || !req.body.role) {
        return res.status(403).json({
          error: true,
          message: "Invalid Credentials",
          validation: [
            { field: "email", message: "Email required" },
            { field: "password", message: "Password required" },
            { field: "role", message: "Role required" }
          ]
        });
      }

      // Find user
      let service = new AuthService();

      const result = await service.login(app, "kanglink", req.body.email, req.body.password, req.body.role);

      if (!result.status)
        return res.status(403).json({
          error: true,
          message: "Failed to login"
        });

      if (typeof result == "string") {
        return res.status(403).json({
          error: true,  
          message: result
        });
      }
      // Generate 2FA code
      const twoFaPayload = await TwoFactorService.getTwoFactorAuthenticationCode(`kanglink-admin-${result.id}`);
      const qrCode = await TwoFactorService.getDataURL(twoFaPayload.otpauthUrl);

      return res.status(200).json({
        error: false,
        role: req.body.role,
        qr_code: qrCode,
        one_time_token: JwtService.createAccessToken(
          {
            user_id: result.id,
            role: req.body.role,
            nonce: (Math.random() + 1).toString(36).substring(7)
          },
          60,
          config.jwt_key
        ),
        expire_at: 60,
        user_id: result.id
      });

    } catch (err) {
      console.log(err)
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.get("/v1/api/lambda/2fa/test", async function (req, res) {
    try {
      let sdk = app.get("sdk");
      sdk.setProjectId("kanglink");
      const user =await sdk.findOne("user", {
        id:1})
      
      console.log(user)
      return res.status(200).json({
        error: false,
        role: "super_admin",
        user,
        qr_code: "test",
        one_time_token: "test",
        expire_at: 60
      });

    } catch (err) {
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v2/api/kanglink/super_admin/lambda/2fa/signin", [TokenMiddleware()], async function (req, res) {
    try {
      // Check if 2FA is enabled for this role
      const Role = require(`../roles/super_admin`);
      const config = app.get("configuration");
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      let sdk = app.get("sdk");
      // sdk.setDatabase("kanglink");
      sdk.setProjectId("kanglink");

      if (!req.body.email || !req.body.password || !req.body.role) {
        return res.status(403).json({
          error: true,
          message: "Invalid Credentials",
          validation: [
            { field: "email", message: "Email required" },
            { field: "password", message: "Password required" },
            { field: "role", message: "Role required" }
          ]
        });
      }

      // Find user
      const result = await sdk.findOne("user", {
        email: req.body.email,
        password: req.body.password,
        role_id: req.body.role,
        status: 1
      });

      if (!result) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      const twoFaPayload = await TwoFactorService.setUpTwoFactorAuth("kanglink", result.id, sdk);
      const qrCode = await TwoFactorService.getDataURL(twoFaPayload.otpauthUrl);

      if (twoFaPayload.type === "sms") {
        await TwilloSmsService.send(
          twoFaPayload.phone,
          `Your 2FA code is ${twoFaPayload.token}. It expires in ${twoFaPayload.expire_at}`
        );
      }

      return res.status(200).json({
        error: false,
        qr_code: qrCode,
        access_token: JwtService.createAccessToken(
          {
            user_id: result.id,
            nonce: (Math.random() + 1).toString(36).substring(7)
          },
          60,
          config.jwt_key
        ),
        expire_at: twoFaPayload.expire_at,
        user_id: result.id
      });
    } catch (err) {
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v2/api/kanglink/super_admin/lambda/2fa/authorize", [TokenMiddleware()], async function (req, res) {
    try {

      // Check if 2FA is enabled for this role
      const Role = require(`../roles/super_admin`);
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      let sdk = app.get("sdk");
      const config = app.get("configuration");
      // sdk.setDatabase("kanglink");
      const user_id = req.user_id;
      sdk.setProjectId("kanglink");

      // Find user
      const result = await sdk.findOne("user", { id: user_id });
      if (!result) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      // Set up 2FA
      const twoFaPayload = await TwoFactorService.setUpTwoFactorAuth("kanglink", result.id, sdk);
      const qrCode = await TwoFactorService.getDataURL(twoFaPayload.otpauthUrl);

      // Send SMS if needed
      if (twoFaPayload.type === "sms" && twoFaPayload.phone) {
        await TwilloSmsService.send(
          twoFaPayload.phone,
          `Your 2FA code is ${twoFaPayload.token}. It expires in ${twoFaPayload.expire_at}`
        );
      }

      return res.status(200).json({
        error: false,
        qr_code: qrCode,
        type: twoFaPayload.type ?? "qr",
        access_token: JwtService.createAccessToken(
          {
            user_id: result.id,
            nonce: (Math.random() + 1).toString(36).substring(7)
          },
          60,
          config.jwt_key
        ),
        expire_at: twoFaPayload.expire_at,
        user_id: result.id
      });

    } catch (err) {
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v2/api/kanglink/super_admin/lambda/2fa/enable", [TokenMiddleware()], async function (req, res) {
    try {
      // Check if 2FA is enabled for this role
      const Role = require(`../roles/super_admin`);
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }
      let sdk = app.get("sdk");
      const config = app.get("configuration");
      // sdk.setDatabase("kanglink");
      sdk.setProjectId("kanglink");

      const user_id = req.user_id;

      // Find user
      const result = await sdk.findOne("user", { id: user_id, status: 1 });

      if (!result) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }
      // Enable 2FA
      const type = req.body.type ?? 'qr';
      const phone = req.body.phone;
      const token = req.body.token;

      let twoFaPayload;
      if (type === "sms") {
        if (!phone) {
          return res.status(400).json({
            error: true,
            message: "Phone number required for SMS 2FA"
          });
        }
        twoFaPayload = await TwoFactorService.enable2FA(sdk, user_id, `kanglink-admin-${user_id}`, type, token, phone);
      } else {  
        twoFaPayload = await TwoFactorService.enable2FA(sdk, user_id, `kanglink-admin-${user_id}`, type, token);
      }

      // If already verified, return success
      if (twoFaPayload.verified) {
        return res.status(200).json({
          error: false,
          message: "2FA enabled successfully"
        });
      }

      // Otherwise return setup info
      const qrCode = await TwoFactorService.getDataURL(twoFaPayload.otpauthUrl);

      if (type === "sms") {
        await TwilloSmsService.send(
          phone,
          `Your 2FA code is ${twoFaPayload.token}. It expires in ${twoFaPayload.expire_at}`
        );
      }

      return res.status(200).json({
        error: false,
        qr_code: qrCode,
        access_token: JwtService.createAccessToken(
          {
            user_id: result.id,
            nonce: (Math.random() + 1).toString(36).substring(7)
          },
          60,
          config.jwt_key
        ),
        expire_at: twoFaPayload.expire_at,
        user_id: result.id
      });

    } catch (err) {
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v2/api/kanglink/super_admin/lambda/2fa/disable", [TokenMiddleware()], async function (req, res) {
    try {
      // Check if 2FA is enabled for this role
      const Role = require(`../roles/super_admin`);
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      let sdk = app.get("sdk")
      const config = app.get("configuration");
      // sdk.setDatabase("kanglink");
      sdk.setProjectId("kanglink");
      const user_id = req.user_id;
      const   data = await TwoFactorService.disable2FA(sdk, user_id, `kanglink-admin-${user_id}`);
      await sdk.updateById("user", user_id, { two_factor_authentication: false });
      return res.status(200).json({
        error: false,
        message: data.message
      })
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });

  app.get("/v2/api/kanglink/super_admin/lambda/2fa/status", [TokenMiddleware()], async function (req, res) {
    try {
      // Check if 2FA is enabled for this role
      const Role = require(`../roles/super_admin`);
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      let sdk = app.get("sdk");
      sdk.setProjectId("kanglink");
      const user_id = req.user_id;

      // Find user
      const result = await sdk.findOne("user", { id: user_id, status: 1 });

      if (!result) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      return res.status(200).json({
        error: false,
        enabled: !!result.two_factor_authentication,
        user_id: result.id
      });

    } catch (err) {
      console.error("2FA Status Error:", err);
      return res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });

  app.post("/v2/api/kanglink/super_admin/lambda/2fa/verify", [TokenMiddleware()], async function (req, res) {
    try {
      // Check if 2FA is enabled for this role
      const Role = require(`../roles/super_admin`);
      if(!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }
      let sdk = app.get("sdk")
      const config = app.get("configuration");
      // sdk.setDatabase("kanglink");
      sdk.setProjectId("kanglink");

      if (!req.body.verification_code) {
        return res.status(403).json({
          error: true,
          message: "Verification code required",
          validation: [{ field: "verification_code", message: "Verification code missing" }]
        });
      }

      const user_id = req.user_id;

      // Find user
      const result = await sdk.findOne("user", { id: user_id });
      if (!result) {
        return res.status(403).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      // Verify the TOTP code
      const speakEasyToken = await TwoFactorService.verifyTotp(req.body.verification_code, user_id, sdk);

      if (speakEasyToken) {
        // update the user 2fa_enabled to true
        await sdk.updateById("user", user_id, {
          two_factor_authentication: true,
          two_fa_type: req.body.type ?? "qr"
        });
        
        return res.status(200).json({
          error: false,
          valid: true,
          message: "Verified Successfully",
        });
      } else {
        return res.status(403).json({
          error: true,
          valid: false,
          message: "Invalid verification code",
          validation: [{ field: "verification_code", message: "Invalid verification code" }]
        });
      }

    } catch (err) {
      console.error("2FA Verify Error:", err);
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });
  app.post("/v2/api/kanglink/super_admin/lambda/2fa/auth", async function (req, res) {
    try {
      // Check if 2FA is enabled for this role
      const Role = require(`../roles/super_admin`);
      const config = app.get("configuration");
      if (!Role.needs2FA()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      if (!req.body.code) {
        return res.status(403).json({
          error: true,
          message: "Invalid Code",
          validation: [{ field: "code", message: "Code missing" }]
        });
      }

      if (!req.body.token) {
        return res.status(403).json({
          error: true,
          message: "Token Invalid",
          validation: [{ field: "token", message: "Token missing" }]
        });
      }

      // Verify the one-time token
      const verifyToken = JwtService.verifyAccessToken(req.body.token, config.jwt_key);

      if (!verifyToken || !verifyToken.nonce) {
        return res.status(403).json({
          error: true,
          message: "Invalid Token",
          validation: [{ field: "token", message: "Invalid Token" }]
        });
      }

      let sdk = app.get("sdk");
      sdk.setProjectId("kanglink");

      // Verify the 2FA code
      const isCodeValid = await TwoFactorService.verifyTotp(req.body.code, verifyToken.user_id, sdk);

      if (!isCodeValid) {
        return res.status(403).json({
          error: true,
          message: "Invalid 2FA Code",
          validation: [{ field: "code", message: "Invalid 2FA Code" }]
        });
      }

      // If both token and 2FA code are valid, return success
      return res.status(200).json({
        error: false,
        role: verifyToken.role,
        token: JwtService.createAccessToken(
          {
            user_id: verifyToken.user_id,
            role: verifyToken.role
          },
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: verifyToken.user_id
      });

    } catch (err) {
      console.error("2FA Auth Error:", err);
      res.status(403);
      res.json({
        error: true,
        message: err.message
      });
    }
  });
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "2FA Login API",
      url: "/v1/api/kanglink/super_admin/lambda/2fa/login",
      successBody: '{ "email": "<EMAIL>", "password": "password123", "role": "user" }',
      successPayload: '{"error":false, "role": "user", "qr_code": "base64_qr_code", "one_time_token": "token", "expire_at": 60, "user_id": 1}',
      errors: [
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "password": "wrongpassword", "role": "user" }',
          response: '{"error":true,"message":"Failed to login"}',
        },
        {
          name: "403",
          body: '{}',
          response: '{"error":true,"message":"Invalid Credentials","validation":[{"field": "email", "message": "Email required"},{"field": "password", "message": "Password required"},{"field": "role", "message": "Role required"}]}',
        }
      ],
      needToken: false,
    },
    {
      method: "POST",
      name: "2FA Verify API",
      url: "/v2/api/kanglink/super_admin/lambda/2fa/verify",
      successBody: '{ "token": "verification_code" }',
      successPayload: '{"error":false, "valid": true, "message": "Verified Successfully"}',
      errors: [
        {
          name: "403",
          body: '{ "token": "invalid_code" }',
          response: '{"error":true,"valid":false,"message":"Invalid Token","validation":[{"field": "token", "message": "Invalid Token"}]}',
        }
      ],
      needToken: true,
    },
    {
      method: "POST",
      name: "2FA Authorization API",
      url: "/v2/api/kanglink/super_admin/lambda/2fa/auth",
      successBody: '{ "code": "verification_code", "token": "one_time_token" }',
      successPayload: '{"error":false, "role": "user", "token": "access_token", "expire_at": 3600, "user_id": 1}',
      errors: [
        {
          name: "403",
          body: '{ "code": "invalid_code", "token": "one_time_token" }',
          response: '{"error":true,"message":"Invalid Credential","validation":[{"field": "code", "message": "Invalid Credentials"}]}',
        }
      ],
      needToken: true,
    }
  ];
};
