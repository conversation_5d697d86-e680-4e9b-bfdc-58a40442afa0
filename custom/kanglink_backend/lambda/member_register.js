const JwtService = require("../../../baas/services/JwtService");
const PasswordService = require("../../../baas/services/PasswordService");
const User = require("../models/user");
const MailService = require("../../../baas/services/MailService");

module.exports = function (app) {
  const config = app.get("configuration");
  app.post("/v1/api/kanglink/member/lambda/register", async function (req, res) {
    try {
      // Get the role class and check registration permission
      const RoleClass = require(`../roles/member`);
      if (!RoleClass.canRegister()) {
        return res.status(403).json({
          error: true,
          message: `${RoleClass.name} cannot register`,
        });
      }
      // secure: true, // Use SSL/TLS
      // Enable email verification for all registrations
      let verify = true;
      const needRefreshToken = req.body.is_refresh ? true : false;
      let refreshToken = undefined;
      const hostname = req.headers.origin;

      // Create user object and validate
      const userObj = new User({
        email: req.body.email,
        role_id: RoleClass.slug,
        password: req.body.password,
        login_type: 0,
        status: 0,
        verify: verify ? 0 : 1,
        company_id: req.body.company_id || 0,
        data: req.body.data ? JSON.stringify(req.body.data) : "{}",
      });

      if (!userObj.isValid()) {
        return res.status(403).json({
          error: true,
          message: userObj.getErrors()[0],
          validation: userObj.getErrors().map(error => {
            const [field, message] = error.split(' must ');
            return {
              field: field.replace('Field ', ''),
              message: 'must ' + message
            };
          })
        });
      }

      const sdk = app.get("sdk");

      // Check if user already exists

      sdk.setProjectId("kanglink");
      sdk.setTable("user");
      const existingUser = await sdk.findOne('user', {
        email: userObj.fields.find(f => f.name === 'email').value
      });
      const rolemap = {
        "member": "An Athlete",
        "trainer": "A Trainer"
      }
      if (existingUser) {
        return res.status(403).json({
          error: true,
          message: `${rolemap[existingUser.role_id]} already exists with this email `
        });
      }

      // Hash password
      // const passwordService = new PasswordService();
      const hashedPassword = await PasswordService.hash(req.body.password);

      // Create user record
      const userData = {
        email: req.body.email,
        password: hashedPassword,
        role_id: RoleClass.slug,
        login_type: 0,
        status: verify ? 0 : 1,
        verify: verify ? 0 : 1,
        company_id: req.body.company_id || 0,
        data: req.body.data ? JSON.stringify(req.body.data) : "{}",
        profile_update: true,
        created_at: new Date(),
        updated_at: new Date()
      };

      // Insert user

      sdk.setProjectId("kanglink");
      sdk.setTable("user");
      const result = await sdk.create('user', userData);

      if (!result) {
        throw new Error('Failed to create user');
      }

      const userId = result.id;

      // Generate verification token and send email
      const verificationToken = JwtService.createAccessToken(
        {
          user_id: userId,
          email: userData.email,
          role: "member",
          type: "email_verification"
        },
        config.verification_token_expire || 604800,
        config.jwt_key
      );

      // Calculate expiry date
      let expireDate = new Date();
      expireDate.setSeconds(expireDate.getSeconds() + (config.verification_token_expire || 604800));

      // Save verification token to database
      const tokenPayload = {
        user_id: userId,
        token: verificationToken,
        type: 3, // Verification type
        data: JSON.stringify({
          email: userData.email,
          role: "member",
          user_id: userId
        }),
        status: 1,
        expired_at: expireDate,
        created_at: new Date(),
        updated_at: new Date()
      };

      await sdk.create('tokens', tokenPayload);

      const mailService = new MailService(config);
      const verificationUrl = `${hostname}/verify-email`;
      const token = `${verificationToken}&role=member`;

      // Test email configuration
      console.log("Testing email configuration...");
      console.log("Config:", {
        app_url: config.app_url,
        mail_host: config.mail.mail_host,
        mail_port: config.mail.mail_port,
        mail_user: config.mail.mail_user,
        from_mail: config.mail.from_mail
      });

      try {
        console.log("Sending verification email to:", userData.email);
        console.log("Verification URL:", verificationUrl);
        console.log("Verification token:", verificationToken);

        await mailService.sendVerificationEmail(
          userData.email,
          token,
          verificationUrl
        );

        console.log("Verification email sent successfully");
      } catch (err) {
        console.error("Failed to send verification email:", err);
        console.error("Email configuration:", {
          host: config.mail.mail_host,
          port: config.mail.mail_port,
          user: config.mail.mail_user,
          from: config.mail.from_mail
        });
        // Don't fail registration if email fails, but log it
      }

      // Return success response without token - user needs to verify email first
      return res.status(200).json({
        error: false,
        message: "Registration successful. Please check your email to verify your account.",
        user_id: userId,
        requires_verification: true
      });

    } catch (err) {
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });


  return [
    {
      method: "POST",
      name: "Register API",
      url: "/v1/api/kanglink/member/lambda/register",
      successBody: '{ "email": "<EMAIL>", "first_name": "member", "last_name": "User", "is_refresh": true, "password": "a123456"}',
      successPayload: '{"error":false,"message":"Registration successful. Please check your email to verify your account.","user_id":20,"requires_verification":true}',
      errors: [
        {
          name: "403",
          body: '{"role": "member", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Email Missing","validation": [{ "field": "email", "message": "Email missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "is_refresh": false}',
          response: '{"error": true,"message": "Password","validation": [{ "field": "password", "message": "Password missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: true
    }
  ];

};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "User Registration API",
      url: "/v1/api/kanglink/member/lambda/register",
      successBody: '{ "email": "<EMAIL>", "password": "password123", "name": "New User" }',
      successPayload: '{"error":false, "message": "Registration successful. Please check your email to verify your account.", "user_id": 1, "requires_verification": true}',
      errors: [
        {
          name: "409",
          body: '{ "email": "<EMAIL>", "password": "password123", "name": "Existing User" }',
          response: '{"error":true,"message":"Email already exists"}',
        },
        {
          name: "400",
          body: '{}',
          response: '{"error":true,"message":"Invalid registration data","validation":[{"field": "email", "message": "Email is required"}, {"field": "password", "message": "Password is required"}]}',
        }
      ],
      needToken: false,
    },
  ];
};
