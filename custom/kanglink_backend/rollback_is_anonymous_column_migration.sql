-- Rollback Migration: Remove is_anonymous column from post_feed table
-- Date: Rollback for anonymous posts feature
-- Description: Removes the is_anonymous column and restores user_id NOT NULL constraint

-- WARNING: This rollback will delete all anonymous posts (where user_id is NULL)
-- Make sure to backup data before running this rollback!

-- First, delete all anonymous posts to avoid constraint violations
DELETE FROM kanglink_post_feed WHERE user_id IS NULL;

-- Drop the indexes we created
DROP INDEX IF EXISTS idx_post_feed_is_anonymous ON kanglink_post_feed;
DROP INDEX IF EXISTS idx_post_feed_program_anonymous ON kanglink_post_feed;

-- Restore user_id column to NOT NULL constraint
ALTER TABLE kanglink_post_feed 
MODIFY COLUMN user_id INT NOT NULL;

-- Remove the is_anonymous column
ALTER TABLE kanglink_post_feed 
DROP COLUMN is_anonymous; 