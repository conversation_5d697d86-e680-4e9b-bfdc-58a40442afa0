const UtilService = require("../../../baas/services/UtilService");

class CommissionService {
  constructor(sdk) {
    this.sdk = sdk;
  }

  /**
   * Calculate commission for an enrollment
   * @param {Object} enrollmentData - Enrollment data including amount, affiliate info
   * @param {Object} payoutSettings - Current payout settings
   * @returns {Object} Commission breakdown
   */
  calculateCommission(enrollmentData, payoutSettings) {
    const {
      amount,
      affiliate_code,
      affiliate_user_id,
      trainer_id,
      athlete_id,
      program_id,
      split_id,
      currency = "USD",
    } = enrollmentData;

    const totalAmount = parseFloat(amount);
    const isAffiliate = affiliate_code && affiliate_user_id;

    let commissionBreakdown;

    if (isAffiliate) {
      // Affiliate commission calculation (higher trainer percentage)
      const companyPercentage = payoutSettings.affiliate_company_percentage;
      const trainerPercentage = payoutSettings.affiliate_trainer_percentage;

      commissionBreakdown = {
        commission_type: "affiliate",
        total_amount: totalAmount,
        company_amount: (totalAmount * companyPercentage) / 100,
        trainer_amount: (totalAmount * trainerPercentage) / 100,
        affiliate_code,
        affiliate_user_id,
      };
    } else {
      // Regular split commission calculation
      const companyPercentage = payoutSettings.split_company_percentage;
      const trainerPercentage = payoutSettings.split_trainer_percentage;

      commissionBreakdown = {
        commission_type: "regular",
        total_amount: totalAmount,
        company_amount: (totalAmount * companyPercentage) / 100,
        trainer_amount: (totalAmount * trainerPercentage) / 100,
        affiliate_code: null,
        affiliate_user_id: null,
      };
    }

    // Add common fields
    commissionBreakdown.trainer_id = trainer_id;
    commissionBreakdown.athlete_id = athlete_id;
    commissionBreakdown.program_id = program_id;
    commissionBreakdown.split_id = split_id;
    commissionBreakdown.currency = currency;
    commissionBreakdown.payout_status = "pending";

    // Calculate payout schedule based on settings
    const payoutScheduledAt = new Date();
    payoutScheduledAt.setHours(
      payoutScheduledAt.getHours() + payoutSettings.trainer_payout_time_hours
    );
    commissionBreakdown.payout_scheduled_at =
      UtilService.sqlDateTimeFormat(payoutScheduledAt);

    return commissionBreakdown;
  }

  /**
   * Create commission record for an enrollment
   * @param {number} enrollmentId - Enrollment ID
   * @param {Object} enrollmentData - Enrollment data
   * @returns {Object} Created commission record
   */
  async createCommissionRecord(enrollmentId, enrollmentData) {
    try {
      // Get active payout settings
      this.sdk.setTable("payout_settings");
      const payoutSettings = await this.sdk.findOne("payout_settings", {
        is_active: true,
      });

      if (!payoutSettings) {
        throw new Error("No active payout settings found");
      }

      // Calculate commission
      const commissionData = this.calculateCommission(
        enrollmentData,
        payoutSettings
      );
      commissionData.enrollment_id = enrollmentId;
      commissionData.created_at = UtilService.sqlDateTimeFormat(new Date());
      commissionData.updated_at = UtilService.sqlDateTimeFormat(new Date());

      // Create commission record
      this.sdk.setTable("commission");
      const commission = await this.sdk.create("commission", commissionData);

      return commission;
    } catch (error) {
      console.error("Error creating commission record:", error);
      throw error;
    }
  }

  /**
   * Get pending payouts that are ready to be processed
   * @returns {Array} Array of commission records ready for payout
   */
  async getPendingPayouts() {
    try {
      this.sdk.setTable("commission");
      const now = UtilService.sqlDateTimeFormat(new Date());

      const pendingPayouts = await this.sdk.rawQuery(`
        SELECT * FROM kanglink_commission 
        WHERE payout_status = 'pending' 
        AND payout_scheduled_at <= '${now}'
        ORDER BY payout_scheduled_at ASC
      `);

      return pendingPayouts;
    } catch (error) {
      console.error("Error getting pending payouts:", error);
      throw error;
    }
  }

  /**
   * Mark commission as processed
   * @param {number} commissionId - Commission ID
   * @returns {Object} Updated commission record
   */
  async markCommissionProcessed(commissionId) {
    try {
      this.sdk.setTable("commission");
      const updateData = {
        payout_status: "processed",
        payout_processed_at: UtilService.sqlDateTimeFormat(new Date()),
        updated_at: UtilService.sqlDateTimeFormat(new Date()),
      };

      const updatedCommission = await this.sdk.updateById(
        "commission",
        commissionId,
        updateData
      );
      return updatedCommission;
    } catch (error) {
      console.error("Error marking commission as processed:", error);
      throw error;
    }
  }

  /**
   * Get commission summary for a trainer
   * @param {number} trainerId - Trainer ID
   * @param {string} status - Optional status filter
   * @returns {Object} Commission summary
   */
  async getTrainerCommissionSummary(trainerId, status = null) {
    try {
      this.sdk.setTable("commission");
      let whereClause = `trainer_id = ${trainerId}`;

      if (status) {
        whereClause += ` AND payout_status = '${status}'`;
      }

      const commissions = await this.sdk.rawQuery(`
        SELECT 
          payout_status,
          COUNT(*) as count,
          SUM(trainer_amount) as total_amount,
          currency
        FROM kanglink_commission 
        WHERE ${whereClause}
        GROUP BY payout_status, currency
      `);

      return commissions;
    } catch (error) {
      console.error("Error getting trainer commission summary:", error);
      throw error;
    }
  }

  /**
   * Get affiliate commission summary (for trainers who referred others)
   * @param {number} affiliateUserId - Affiliate user ID (trainer who referred)
   * @param {string} status - Optional status filter
   * @returns {Object} Commission summary
   */
  async getAffiliateCommissionSummary(affiliateUserId, status = null) {
    try {
      this.sdk.setTable("commission");
      let whereClause = `affiliate_user_id = ${affiliateUserId} AND commission_type = 'affiliate'`;

      if (status) {
        whereClause += ` AND payout_status = '${status}'`;
      }

      const commissions = await this.sdk.rawQuery(`
        SELECT
          payout_status,
          COUNT(*) as count,
          SUM(trainer_amount) as total_amount,
          currency
        FROM kanglink_commission
        WHERE ${whereClause}
        GROUP BY payout_status, currency
      `);

      return commissions;
    } catch (error) {
      console.error("Error getting affiliate commission summary:", error);
      throw error;
    }
  }

  /**
   * Parse affiliate code to extract program and trainer information
   * @param {string} affiliateCode - Affiliate code
   * @returns {Object} Parsed affiliate information
   */
  static parseAffiliateCode(affiliateCode) {
    try {
      // Affiliate code format: {programCode}{trainerCode}{timestamp}
      // All in base36 and uppercase
      const code = affiliateCode.toLowerCase();

      // This is a simplified parser - in production you might want more robust parsing
      // For now, we'll need to look up the code in the database
      return {
        code: affiliateCode,
        valid: true,
      };
    } catch (error) {
      return {
        code: affiliateCode,
        valid: false,
        error: error.message,
      };
    }
  }

  /**
   * Validate affiliate code and get associated program/trainer
   * @param {string} affiliateCode - Affiliate code to validate
   * @returns {Object} Validation result with program and trainer info
   */
  async validateAffiliateCode(affiliateCode) {
    try {
      this.sdk.setTable("program_discount");

      // Find program with matching affiliate link containing this code
      const programDiscount = await this.sdk.rawQuery(`
        SELECT pd.*, p.user_id as trainer_id, p.program_name
        FROM kanglink_program_discount pd
        JOIN kanglink_program p ON pd.program_id = p.id
        WHERE pd.affiliate_link = '${affiliateCode}'
        AND p.status = 'published'
      `);

      if (programDiscount.length === 0) {
        return {
          valid: false,
          message: "Invalid or expired affiliate code",
        };
      }

      const discount = programDiscount[0];
      return {
        valid: true,
        program_id: discount.program_id,
        trainer_id: discount.trainer_id,
        program_name: discount.program_name,
        affiliate_code: affiliateCode,
      };
    } catch (error) {
      console.error("Error validating affiliate code:", error);
      return {
        valid: false,
        message: "Error validating affiliate code",
        error: error.message,
      };
    }
  }
}

module.exports = CommissionService;
