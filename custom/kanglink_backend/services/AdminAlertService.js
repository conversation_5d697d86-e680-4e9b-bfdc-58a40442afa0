const UtilService = require("../../../baas/services/UtilService");

class AdminAlertService {
  constructor(sdk) {
    this.sdk = sdk;
  }

  // Create admin alert for program approval pending
  async createProgramApprovalAlert(programData) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());
      
      const alertData = {
        user_id: 1, // Admin user ID
        actor_id: programData.user_id,
        activity_type: "program_approval_pending",
        related_id: programData.id,
        related_type: "program",
        title: "Program Approval Pending",
        description: `Program "${programData.program_name}" by ${programData.trainer_name} is pending approval`,
        metadata: JSON.stringify({
          program_id: programData.id,
          program_name: programData.program_name,
          trainer_id: programData.user_id,
          trainer_name: programData.trainer_name,
          trainer_email: programData.trainer_email,
          created_at: programData.created_at,
        }),
        visibility: "admin_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", alertData);
      return true;
    } catch (error) {
      console.error("Error creating program approval alert:", error);
      return false;
    }
  }

  // Create admin alert for new athlete signup
  async createNewAthleteSignupAlert(athleteData) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());
      
      const alertData = {
        user_id: 1, // Admin user ID
        actor_id: athleteData.id,
        activity_type: "new_athlete_signup",
        related_id: athleteData.id,
        related_type: "user",
        title: "New Athlete Signup",
        description: `New athlete ${athleteData.full_name} (${athleteData.email}) has signed up`,
        metadata: JSON.stringify({
          athlete_id: athleteData.id,
          athlete_name: athleteData.full_name,
          athlete_email: athleteData.email,
          signup_date: athleteData.created_at,
          level: athleteData.level,
          fitness_goals: athleteData.fitness_goals,
        }),
        visibility: "admin_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", alertData);
      return true;
    } catch (error) {
      console.error("Error creating new athlete signup alert:", error);
      return false;
    }
  }

  // Create admin alert for new trainer signup
  async createNewTrainerSignupAlert(trainerData) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());
      
      const alertData = {
        user_id: 1, // Admin user ID
        actor_id: trainerData.id,
        activity_type: "new_trainer_signup",
        related_id: trainerData.id,
        related_type: "user",
        title: "New Trainer Signup",
        description: `New trainer ${trainerData.full_name} (${trainerData.email}) has signed up`,
        metadata: JSON.stringify({
          trainer_id: trainerData.id,
          trainer_name: trainerData.full_name,
          trainer_email: trainerData.email,
          signup_date: trainerData.created_at,
          years_of_experience: trainerData.years_of_experience,
          qualifications: trainerData.qualifications,
          specializations: trainerData.specializations,
        }),
        visibility: "admin_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", alertData);
      return true;
    } catch (error) {
      console.error("Error creating new trainer signup alert:", error);
      return false;
    }
  }

  // Create admin alert for new transaction
  async createNewTransactionAlert(transactionData) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());
      
      const alertData = {
        user_id: 1, // Admin user ID
        actor_id: transactionData.athlete_id,
        activity_type: "new_transaction",
        related_id: transactionData.id,
        related_type: "enrollment",
        title: "New Transaction",
        description: `New transaction of ${transactionData.currency} ${transactionData.amount} for ${transactionData.program_name}`,
        metadata: JSON.stringify({
          transaction_id: transactionData.id,
          athlete_id: transactionData.athlete_id,
          athlete_name: transactionData.athlete_name,
          trainer_id: transactionData.trainer_id,
          trainer_name: transactionData.trainer_name,
          program_id: transactionData.program_id,
          program_name: transactionData.program_name,
          amount: transactionData.amount,
          currency: transactionData.currency,
          payment_type: transactionData.payment_type,
          payment_status: transactionData.payment_status,
          transaction_date: transactionData.created_at,
        }),
        visibility: "admin_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", alertData);
      return true;
    } catch (error) {
      console.error("Error creating new transaction alert:", error);
      return false;
    }
  }

  // Create admin alert for refund request
  async createRefundRequestAlert(refundData) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());
      
      const alertData = {
        user_id: 1, // Admin user ID
        actor_id: refundData.athlete_id,
        activity_type: "refund_requested",
        related_id: refundData.id,
        related_type: "refund_request",
        title: "Refund Requested",
        description: `Refund request of ${refundData.currency} ${refundData.amount} for ${refundData.program_name}`,
        metadata: JSON.stringify({
          refund_id: refundData.id,
          athlete_id: refundData.athlete_id,
          athlete_name: refundData.athlete_name,
          trainer_id: refundData.trainer_id,
          trainer_name: refundData.trainer_name,
          program_id: refundData.program_id,
          program_name: refundData.program_name,
          amount: refundData.amount,
          currency: refundData.currency,
          reason: refundData.reason,
          status: refundData.status,
          requested_at: refundData.requested_at,
        }),
        visibility: "admin_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", alertData);
      return true;
    } catch (error) {
      console.error("Error creating refund request alert:", error);
      return false;
    }
  }

  // Create admin alert for refund approval/rejection
  async createRefundDecisionAlert(refundData, decision) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());
      const activityType = decision === "approve" ? "refund_approved" : "refund_rejected";
      const title = decision === "approve" ? "Refund Approved" : "Refund Rejected";
      
      const alertData = {
        user_id: 1, // Admin user ID
        actor_id: refundData.athlete_id,
        activity_type: activityType,
        related_id: refundData.id,
        related_type: "refund_request",
        title: title,
        description: `Refund ${decision} for ${refundData.currency} ${refundData.amount} for ${refundData.program_name}`,
        metadata: JSON.stringify({
          refund_id: refundData.id,
          athlete_id: refundData.athlete_id,
          athlete_name: refundData.athlete_name,
          trainer_id: refundData.trainer_id,
          trainer_name: refundData.trainer_name,
          program_id: refundData.program_id,
          program_name: refundData.program_name,
          amount: refundData.amount,
          currency: refundData.currency,
          decision: decision,
          admin_notes: refundData.admin_notes,
          processed_at: refundData.processed_at,
        }),
        visibility: "admin_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", alertData);
      return true;
    } catch (error) {
      console.error("Error creating refund decision alert:", error);
      return false;
    }
  }

  // Create admin alert for low rated trainer
  async createLowRatedTrainerAlert(trainerData) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());
      
      const alertData = {
        user_id: 1, // Admin user ID
        actor_id: trainerData.id,
        activity_type: "low_rated_trainer",
        related_id: trainerData.id,
        related_type: "user",
        title: "Low Rated Trainer",
        description: `Trainer ${trainerData.full_name} has a low rating of ${trainerData.rating}/5`,
        metadata: JSON.stringify({
          trainer_id: trainerData.id,
          trainer_name: trainerData.full_name,
          trainer_email: trainerData.email,
          rating: trainerData.rating,
          review_count: trainerData.review_count,
          program_count: trainerData.program_count,
        }),
        visibility: "admin_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", alertData);
      return true;
    } catch (error) {
      console.error("Error creating low rated trainer alert:", error);
      return false;
    }
  }

  // Create system alert
  async createSystemAlert(title, description, metadata = {}) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());
      
      const alertData = {
        user_id: 1, // Admin user ID
        actor_id: 1, // System actor
        activity_type: "system_alert",
        related_id: null,
        related_type: "system",
        title: title,
        description: description,
        metadata: JSON.stringify(metadata),
        visibility: "admin_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", alertData);
      return true;
    } catch (error) {
      console.error("Error creating system alert:", error);
      return false;
    }
  }

  // Get admin alerts with filtering
  async getAdminAlerts(filters = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        activity_type,
        date_from,
        date_to,
      } = filters;

      const offset = (page - 1) * limit;

      // Build WHERE conditions
      let whereConditions = [
        "a.visibility = 'admin_only'",
        "a.user_id = 1", // Admin user ID
      ];
      let queryParams = [];

      if (activity_type) {
        whereConditions.push("a.activity_type = ?");
        queryParams.push(activity_type);
      }

      if (date_from) {
        whereConditions.push("a.created_at >= ?");
        queryParams.push(date_from);
      }

      if (date_to) {
        whereConditions.push("a.created_at <= ?");
        queryParams.push(date_to);
      }

      const whereClause = whereConditions.join(" AND ");

      // Get alerts with user information
      const alertsQuery = `
        SELECT
          a.*,
          u.data as user_data,
          u.email as user_email
        FROM kanglink_activity a
        LEFT JOIN kanglink_user u ON a.actor_id = u.id
        WHERE ${whereClause}
        ORDER BY a.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

      const alerts = await this.sdk.rawQuery(alertsQuery, queryParams);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM kanglink_activity a
        WHERE ${whereClause}
      `;

      const countResult = await this.sdk.rawQuery(countQuery, queryParams);
      const total = countResult[0]?.total || 0;

      // Format alerts
      const formattedAlerts = alerts.map((alert) => {
        let userData = null;
        if (alert.user_data) {
          try {
            userData = JSON.parse(alert.user_data);
          } catch (e) {
            console.warn("Failed to parse user data:", e);
          }
        }

        return {
          id: alert.id,
          activity_type: alert.activity_type,
          title: alert.title,
          description: alert.description,
          metadata: alert.metadata ? JSON.parse(alert.metadata) : null,
          actor_name: userData ? `${userData.first_name || ""} ${userData.last_name || ""}`.trim() : "Unknown User",
          actor_email: alert.user_email,
          created_at: alert.created_at,
          updated_at: alert.updated_at,
        };
      });

      return {
        success: true,
        alerts: formattedAlerts,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error("Error getting admin alerts:", error);
      return {
        success: false,
        error: "Failed to fetch admin alerts",
      };
    }
  }

  // Get pending approval programs
  async getPendingApprovalPrograms() {
    try {
      this.sdk.setTable("program");
      
      // Get programs with pending_approval status
      const programs = await this.sdk.rawQuery(`
        SELECT 
          p.*,
          u.data as trainer_data,
          u.email as trainer_email
        FROM kanglink_program p
        LEFT JOIN kanglink_user u ON p.user_id = u.id
        WHERE p.status = 'pending_approval'
        ORDER BY p.created_at DESC
      `);

      // Format the programs with trainer information
      const formattedPrograms = programs.map(program => {
        let trainerData = null;
        if (program.trainer_data) {
          try {
            trainerData = JSON.parse(program.trainer_data);
          } catch (e) {
            console.warn("Failed to parse trainer data:", e);
          }
        }

        return {
          id: program.id,
          program_name: program.program_name,
          program_description: program.program_description,
          type_of_program: program.type_of_program,
          payment_plan: program.payment_plan ? JSON.parse(program.payment_plan) : null,
          currency: program.currency,
          days_for_preview: program.days_for_preview,
          image: program.image,
          status: program.status,
          trainer_id: program.user_id,
          trainer_name: trainerData ? `${trainerData.first_name || ""} ${trainerData.last_name || ""}`.trim() : "Unknown Trainer",
          trainer_email: program.trainer_email,
          created_at: program.created_at,
          updated_at: program.updated_at,
        };
      });

      return {
        success: true,
        programs: formattedPrograms,
        count: formattedPrograms.length,
      };
    } catch (error) {
      console.error("Error getting pending approval programs:", error);
      return {
        success: false,
        error: "Failed to fetch pending approval programs",
      };
    }
  }

  // Get refund requests
  async getRefundRequests(filters = {}) {
    try {
      this.sdk.setTable("refund_request");
      
      // Build WHERE conditions
      let whereConditions = ["1=1"]; // Always true condition to start
      let queryParams = [];

      if (filters.status) {
        whereConditions.push("rr.status = ?");
        queryParams.push(filters.status);
      }

      if (filters.athlete_id) {
        whereConditions.push("rr.athlete_id = ?");
        queryParams.push(filters.athlete_id);
      }

      if (filters.trainer_id) {
        whereConditions.push("rr.trainer_id = ?");
        queryParams.push(filters.trainer_id);
      }

      const whereClause = whereConditions.join(" AND ");

      // Get refund requests with related data
      const refundRequests = await this.sdk.rawQuery(`
        SELECT 
          rr.*,
          p.program_name,
          p.currency as program_currency,
          u_athlete.data as athlete_data,
          u_athlete.email as athlete_email,
          u_trainer.data as trainer_data,
          u_trainer.email as trainer_email
        FROM kanglink_refund_request rr
        LEFT JOIN kanglink_program p ON rr.program_id = p.id
        LEFT JOIN kanglink_user u_athlete ON rr.athlete_id = u_athlete.id
        LEFT JOIN kanglink_user u_trainer ON rr.trainer_id = u_trainer.id
        WHERE ${whereClause}
        ORDER BY rr.created_at DESC
      `, queryParams);

      // Format the refund requests
      const formattedRefundRequests = refundRequests.map(refund => {
        let athleteData = null;
        let trainerData = null;

        if (refund.athlete_data) {
          try {
            athleteData = JSON.parse(refund.athlete_data);
          } catch (e) {
            console.warn("Failed to parse athlete data:", e);
          }
        }

        if (refund.trainer_data) {
          try {
            trainerData = JSON.parse(refund.trainer_data);
          } catch (e) {
            console.warn("Failed to parse trainer data:", e);
          }
        }

        return {
          id: refund.id,
          enrollment_id: refund.enrollment_id,
          athlete_id: refund.athlete_id,
          trainer_id: refund.trainer_id,
          program_id: refund.program_id,
          split_id: refund.split_id,
          amount: refund.amount,
          currency: refund.currency,
          reason: refund.reason,
          status: refund.status,
          requested_at: refund.requested_at,
          processed_at: refund.processed_at,
          processed_by: refund.processed_by,
          admin_notes: refund.admin_notes,
          stripe_refund_id: refund.stripe_refund_id,
          refund_amount: refund.refund_amount,
          created_at: refund.created_at,
          updated_at: refund.updated_at,
          // Related data
          program_name: refund.program_name,
          athlete_name: athleteData ? `${athleteData.first_name || ""} ${athleteData.last_name || ""}`.trim() : "Unknown Athlete",
          athlete_email: refund.athlete_email,
          trainer_name: trainerData ? `${trainerData.first_name || ""} ${trainerData.last_name || ""}`.trim() : "Unknown Trainer",
          trainer_email: refund.trainer_email,
        };
      });

      return {
        success: true,
        refund_requests: formattedRefundRequests,
        count: formattedRefundRequests.length,
      };
    } catch (error) {
      console.error("Error getting refund requests:", error);
      return {
        success: false,
        error: "Failed to fetch refund requests",
      };
    }
  }

  // Get admin alert statistics
  async getAdminAlertStats() {
    try {
      // Get alert type statistics
      const alertTypeStats = await this.sdk.rawQuery(`
        SELECT
          a.activity_type,
          COUNT(*) as count
        FROM kanglink_activity a
        WHERE a.visibility = 'admin_only' AND a.user_id = 1
        GROUP BY a.activity_type
        ORDER BY count DESC
      `);

      // Get recent alerts count (last 7 days)
      const recentAlertsCount = await this.sdk.rawQuery(`
        SELECT COUNT(*) as count
        FROM kanglink_activity a
        WHERE a.visibility = 'admin_only' 
          AND a.user_id = 1 
          AND a.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      `);

      // Get unread alerts count
      const unreadAlertsCount = await this.sdk.rawQuery(`
        SELECT COUNT(*) as count
        FROM kanglink_activity a
        WHERE a.visibility = 'admin_only' 
          AND a.user_id = 1 
          AND a.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      `);

      // Get pending approval programs count
      const pendingProgramsCount = await this.sdk.rawQuery(`
        SELECT COUNT(*) as count
        FROM kanglink_program p
        WHERE p.status = 'pending_approval'
      `);

      // Get pending refund requests count
      const pendingRefundsCount = await this.sdk.rawQuery(`
        SELECT COUNT(*) as count
        FROM kanglink_refund_request rr
        WHERE rr.status = 'pending'
      `);

      return {
        success: true,
        data: {
          alert_type_stats: alertTypeStats,
          recent_alerts_count: recentAlertsCount[0]?.count || 0,
          unread_alerts_count: unreadAlertsCount[0]?.count || 0,
          pending_approval_programs_count: pendingProgramsCount[0]?.count || 0,
          pending_refund_requests_count: pendingRefundsCount[0]?.count || 0,
        },
      };
    } catch (error) {
      console.error("Error getting admin alert stats:", error);
      return {
        success: false,
        error: "Failed to fetch admin alert statistics",
      };
    }
  }
}

module.exports = AdminAlertService; 