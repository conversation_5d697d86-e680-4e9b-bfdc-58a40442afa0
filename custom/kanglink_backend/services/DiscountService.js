const UtilService = require("../../../baas/services/UtilService");

class DiscountService {
  constructor(sdk) {
    this.sdk = sdk;
  }

  /**
   * Calculate final amount after applying all applicable discounts
   */
  async calculateDiscountedAmount(params) {
    const {
      program_id,
      split_id,
      payment_type,
      original_amount,
      coupon_code,
      user_id,
    } = params;

    let finalAmount = parseFloat(original_amount);
    let totalDiscountAmount = 0;
    const appliedDiscounts = [];

    try {
      // 1. Check for program sale discount
      const programSaleDiscount = await this.getProgramSaleDiscount(program_id, payment_type);
      if (programSaleDiscount) {
        const saleDiscountAmount = this.calculateDiscountAmount(
          finalAmount,
          programSaleDiscount.sale_discount_type,
          programSaleDiscount.sale_discount_value
        );
        
        if (saleDiscountAmount > 0) {
          finalAmount -= saleDiscountAmount;
          totalDiscountAmount += saleDiscountAmount;
          appliedDiscounts.push({
            type: 'sale',
            discount_type: programSaleDiscount.sale_discount_type,
            discount_value: programSaleDiscount.sale_discount_value,
            discount_amount: saleDiscountAmount,
            source: 'program_sale'
          });
        }
      }

      // 2. Check for general discount
      const generalDiscount = await this.getGeneralDiscount(program_id, split_id, payment_type);
      if (generalDiscount) {
        const generalDiscountAmount = this.calculateDiscountAmount(
          finalAmount,
          generalDiscount.discount_type,
          generalDiscount.discount_value
        );
        
        if (generalDiscountAmount > 0) {
          finalAmount -= generalDiscountAmount;
          totalDiscountAmount += generalDiscountAmount;
          appliedDiscounts.push({
            type: 'general',
            discount_type: generalDiscount.discount_type,
            discount_value: generalDiscount.discount_value,
            discount_amount: generalDiscountAmount,
            source: 'general_discount'
          });
        }
      }

      // 3. Check for coupon discount
      if (coupon_code) {
        const couponResult = await this.validateAndApplyCoupon(
          coupon_code,
          program_id,
          split_id,
          payment_type,
          finalAmount,
          user_id
        );
        
        if (couponResult.valid && couponResult.discount_amount > 0) {
          finalAmount -= couponResult.discount_amount;
          totalDiscountAmount += couponResult.discount_amount;
          appliedDiscounts.push({
            type: 'coupon',
            coupon_id: couponResult.coupon.id,
            coupon_code: coupon_code,
            discount_type: couponResult.coupon.discount_type,
            discount_value: couponResult.coupon.discount_value,
            discount_amount: couponResult.discount_amount,
            source: 'coupon'
          });
        }
      }

      finalAmount = Math.max(finalAmount, 0);

      return {
        success: true,
        original_amount: parseFloat(original_amount),
        final_amount: finalAmount,
        total_discount_amount: totalDiscountAmount,
        applied_discounts: appliedDiscounts,
        has_discounts: appliedDiscounts.length > 0
      };

    } catch (error) {
      console.error('Error calculating discounted amount:', error);
      return {
        success: false,
        error: error.message,
        original_amount: parseFloat(original_amount),
        final_amount: parseFloat(original_amount),
        total_discount_amount: 0,
        applied_discounts: [],
        has_discounts: false
      };
    }
  }

  async getProgramSaleDiscount(program_id, payment_type) {
    try {
      this.sdk.setTable("program_discount");
      const programDiscount = await this.sdk.findOne("program_discount", {
        program_id: program_id
      });

      if (programDiscount && 
          programDiscount.sale_discount_value && 
          programDiscount.sale_discount_type) {
        return programDiscount;
      }
      return null;
    } catch (error) {
      console.error('Error getting program sale discount:', error);
      return null;
    }
  }

  async getGeneralDiscount(program_id, split_id, payment_type) {
    try {
      this.sdk.setTable("discount");
      
      let discount = await this.sdk.findOne("discount", {
        program_id: program_id,
        split_id: split_id,
        is_active: true
      });

      if (!discount) {
        discount = await this.sdk.findOne("discount", {
          program_id: program_id,
          split_id: null,
          is_active: true
        });
      }

      if (discount && this.discountAppliesTo(discount.applies_to, payment_type)) {
        return discount;
      }
      return null;
    } catch (error) {
      console.error('Error getting general discount:', error);
      return null;
    }
  }

  async validateAndApplyCoupon(coupon_code, program_id, split_id, payment_type, current_amount, user_id) {
    try {
      this.sdk.setTable("coupon");
      const coupon = await this.sdk.findOne("coupon", {
        code: coupon_code,
        program_id: program_id,
        is_active: true
      });

      if (!coupon) {
        return { valid: false, message: "Invalid coupon code" };
      }

      if (coupon.expiry_date && new Date(coupon.expiry_date) < new Date()) {
        return { valid: false, message: "Coupon has expired" };
      }

      if (!this.discountAppliesTo(coupon.applies_to, payment_type)) {
        return { valid: false, message: "Coupon does not apply to this payment type" };
      }

      if (coupon.usage_limit && coupon.used_count >= coupon.usage_limit) {
        return { valid: false, message: "Coupon usage limit exceeded" };
      }

      this.sdk.setTable("coupon_usage");
      const existingUsage = await this.sdk.findOne("coupon_usage", {
        coupon_id: coupon.id,
        user_id: user_id,
        program_id: program_id
      });

      if (existingUsage) {
        return { valid: false, message: "You have already used this coupon" };
      }

      const discount_amount = this.calculateDiscountAmount(
        current_amount,
        coupon.discount_type,
        coupon.discount_value
      );

      return {
        valid: true,
        coupon: coupon,
        discount_amount: discount_amount
      };

    } catch (error) {
      console.error('Error validating coupon:', error);
      return { valid: false, message: "Error validating coupon" };
    }
  }

  async recordCouponUsage(coupon_id, user_id, program_id, split_id, discount_amount) {
    try {
      this.sdk.setTable("coupon_usage");
      await this.sdk.create("coupon_usage", {
        coupon_id: coupon_id,
        user_id: user_id,
        program_id: program_id,
        split_id: split_id,
        discount_amount: discount_amount,
        used_at: UtilService.sqlDateTimeFormat(new Date()),
        created_at: UtilService.sqlDateTimeFormat(new Date()),
        updated_at: UtilService.sqlDateTimeFormat(new Date())
      });

      this.sdk.setTable("coupon");
      const coupon = await this.sdk.findOne("coupon", { id: coupon_id });
      if (coupon) {
        await this.sdk.updateById("coupon", coupon_id, {
          used_count: (coupon.used_count || 0) + 1,
          updated_at: UtilService.sqlDateTimeFormat(new Date())
        });
      }

    } catch (error) {
      console.error('Error recording coupon usage:', error);
      throw error;
    }
  }

  calculateDiscountAmount(amount, discount_type, discount_value) {
    if (discount_type === 'percentage') {
      return (amount * parseFloat(discount_value)) / 100;
    } else if (discount_type === 'fixed') {
      return Math.min(parseFloat(discount_value), amount);
    }
    return 0;
  }

  discountAppliesTo(applies_to, payment_type) {
    if (applies_to === 'both') return true;
    if (applies_to === 'subscription' && payment_type === 'subscription') return true;
    if (applies_to === 'full_payment' && payment_type === 'one_time') return true;
    return false;
  }
}

module.exports = DiscountService;
