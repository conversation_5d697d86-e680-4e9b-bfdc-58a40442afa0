const UtilService = require("../../../baas/services/UtilService");

class RefundNotificationService {
  constructor(sdk) {
    this.sdk = sdk;
  }

  async createRefundRequestNotification(refundRequestData, enrollmentData) {
    try {
      // Notify trainer about refund request
      const notificationData = {
        user_id: refundRequestData.trainer_id,
        sender_id: refundRequestData.athlete_id,
        related_id: refundRequestData.id,
        related_type: "enrollment",
        notification_type: "refund_requested",
        category: "refund",
        title: "Refund Request Received",
        message: `An athlete has requested a refund for "${enrollmentData.program_name} - ${enrollmentData.split_name}". Amount: ${refundRequestData.currency} ${refundRequestData.amount}`,
        data: JSON.stringify({
          refund_request_id: refundRequestData.id,
          enrollment_id: refundRequestData.enrollment_id,
          program_name: enrollmentData.program_name,
          split_name: enrollmentData.split_name,
          amount: refundRequestData.amount,
          currency: refundRequestData.currency,
          reason: refundRequestData.reason,
          athlete_name: enrollmentData.athlete_name,
        }),
        is_read: false,
        created_at: UtilService.sqlDateTimeFormat(new Date()),
        updated_at: UtilService.sqlDateTimeFormat(new Date()),
      };

      this.sdk.setTable("notification");
      await this.sdk.create("notification", notificationData);

      return true;
    } catch (error) {
      console.error("Error creating refund request notification:", error);
      return false;
    }
  }

  async createRefundDecisionNotification(refundRequest, decision, adminNotes = null) {
    try {
      const isApproved = decision === "approve";
      
      // Notify athlete about decision
      const athleteNotificationData = {
        user_id: refundRequest.athlete_id,
        sender_id: refundRequest.processed_by,
        related_id: refundRequest.id,
        related_type: "enrollment",
        notification_type: isApproved ? "refund_approved" : "refund_rejected",
        category: "refund",
        title: `Refund Request ${isApproved ? "Approved" : "Rejected"}`,
        message: `Your refund request for ${refundRequest.currency} ${refundRequest.amount} has been ${isApproved ? "approved" : "rejected"}.${adminNotes ? ` Admin notes: ${adminNotes}` : ""}`,
        data: JSON.stringify({
          refund_request_id: refundRequest.id,
          enrollment_id: refundRequest.enrollment_id,
          amount: refundRequest.amount,
          currency: refundRequest.currency,
          decision: decision,
          admin_notes: adminNotes,
        }),
        is_read: false,
        created_at: UtilService.sqlDateTimeFormat(new Date()),
        updated_at: UtilService.sqlDateTimeFormat(new Date()),
      };

      this.sdk.setTable("notification");
      await this.sdk.create("notification", athleteNotificationData);

      // If approved, also notify trainer
      if (isApproved) {
        const trainerNotificationData = {
          user_id: refundRequest.trainer_id,
          sender_id: refundRequest.processed_by,
          related_id: refundRequest.id,
          related_type: "enrollment",
          notification_type: "refund_approved",
          category: "refund",
          title: "Refund Request Approved",
          message: `A refund request for ${refundRequest.currency} ${refundRequest.amount} has been approved and will be processed soon.`,
          data: JSON.stringify({
            refund_request_id: refundRequest.id,
            enrollment_id: refundRequest.enrollment_id,
            amount: refundRequest.amount,
            currency: refundRequest.currency,
            admin_notes: adminNotes,
          }),
          is_read: false,
          created_at: UtilService.sqlDateTimeFormat(new Date()),
          updated_at: UtilService.sqlDateTimeFormat(new Date()),
        };

        await this.sdk.create("notification", trainerNotificationData);
      }

      return true;
    } catch (error) {
      console.error("Error creating refund decision notification:", error);
      return false;
    }
  }

  async createRefundProcessedNotification(refundRequest, refundAmount) {
    try {
      // Notify athlete about processed refund
      const athleteNotificationData = {
        user_id: refundRequest.athlete_id,
        sender_id: refundRequest.processed_by,
        related_id: refundRequest.id,
        related_type: "enrollment",
        notification_type: "refund_processed",
        category: "refund",
        title: "Refund Processed",
        message: `Your refund of ${refundRequest.currency} ${refundAmount} has been processed successfully. The amount will be credited to your original payment method within 5-10 business days.`,
        data: JSON.stringify({
          refund_request_id: refundRequest.id,
          enrollment_id: refundRequest.enrollment_id,
          original_amount: refundRequest.amount,
          refund_amount: refundAmount,
          currency: refundRequest.currency,
          stripe_refund_id: refundRequest.stripe_refund_id,
        }),
        is_read: false,
        created_at: UtilService.sqlDateTimeFormat(new Date()),
        updated_at: UtilService.sqlDateTimeFormat(new Date()),
      };

      this.sdk.setTable("notification");
      await this.sdk.create("notification", athleteNotificationData);

      // Notify trainer about processed refund
      const trainerNotificationData = {
        user_id: refundRequest.trainer_id,
        sender_id: refundRequest.processed_by,
        related_id: refundRequest.id,
        related_type: "enrollment",
        notification_type: "refund_processed",
        category: "refund",
        title: "Refund Processed",
        message: `A refund of ${refundRequest.currency} ${refundAmount} has been processed for one of your programs. This may affect your earnings.`,
        data: JSON.stringify({
          refund_request_id: refundRequest.id,
          enrollment_id: refundRequest.enrollment_id,
          original_amount: refundRequest.amount,
          refund_amount: refundAmount,
          currency: refundRequest.currency,
        }),
        is_read: false,
        created_at: UtilService.sqlDateTimeFormat(new Date()),
        updated_at: UtilService.sqlDateTimeFormat(new Date()),
      };

      await this.sdk.create("notification", trainerNotificationData);

      return true;
    } catch (error) {
      console.error("Error creating refund processed notification:", error);
      return false;
    }
  }
}

module.exports = RefundNotificationService;
