const UtilService = require("../../../baas/services/UtilService");

class NotificationService {
  constructor(sdk) {
    this.sdk = sdk;
  }

  // Create notification for new enrollment
  async createEnrollmentNotification(enrollmentData, athleteData, trainerData) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      // Notify trainer about new enrollment
      const trainerNotification = {
        user_id: enrollmentData.trainer_id,
        sender_id: enrollmentData.athlete_id,
        related_id: enrollmentData.id,
        related_type: "enrollment",
        notification_type: "new_enrollment",
        category: "enrollment",
        title: "New Enrollment",
        message: `${athleteData.full_name} enrolled in "${enrollmentData.program_name} - ${enrollmentData.split_name}"`,
        data: JSON.stringify({
          enrollment_id: enrollmentData.id,
          program_name: enrollmentData.program_name,
          split_name: enrollmentData.split_name,
          athlete_name: athleteData.full_name,
          payment_amount: enrollmentData.payment_amount,
          payment_currency: enrollmentData.payment_currency,
          payment_type: enrollmentData.payment_type,
        }),
        is_read: false,
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("notification");
      await this.sdk.create("notification", trainerNotification);

      // Notify athlete about successful enrollment
      const athleteNotification = {
        user_id: enrollmentData.athlete_id,
        sender_id: enrollmentData.trainer_id,
        related_id: enrollmentData.id,
        related_type: "enrollment",
        notification_type: "new_enrollment",
        category: "enrollment",
        title: "Enrollment Successful",
        message: `You have successfully enrolled in "${enrollmentData.program_name} - ${enrollmentData.split_name}"`,
        data: JSON.stringify({
          enrollment_id: enrollmentData.id,
          program_name: enrollmentData.program_name,
          split_name: enrollmentData.split_name,
          trainer_name: trainerData.full_name,
          payment_amount: enrollmentData.payment_amount,
          payment_currency: enrollmentData.payment_currency,
          payment_type: enrollmentData.payment_type,
        }),
        is_read: false,
        created_at: createdAt,
        updated_at: createdAt,
      };

      await this.sdk.create("notification", athleteNotification);

      return true;
    } catch (error) {
      console.error("Error creating enrollment notification:", error);
      return false;
    }
  }

  // Create notification for payment received
  async createPaymentNotification(paymentData, enrollmentData, athleteData, trainerData) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      // Notify trainer about payment received
      const trainerNotification = {
        user_id: enrollmentData.trainer_id,
        sender_id: enrollmentData.athlete_id,
        related_id: paymentData.id,
        related_type: "payment",
        notification_type: "payment_received",
        category: "payment",
        title: "Payment Received",
        message: `${athleteData.full_name} made a payment of ${paymentData.currency} ${paymentData.amount} for "${enrollmentData.program_name}"`,
        data: JSON.stringify({
          payment_id: paymentData.id,
          enrollment_id: enrollmentData.id,
          program_name: enrollmentData.program_name,
          athlete_name: athleteData.full_name,
          amount: paymentData.amount,
          currency: paymentData.currency,
          payment_method: paymentData.payment_method,
        }),
        is_read: false,
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("notification");
      await this.sdk.create("notification", trainerNotification);

      // Notify athlete about successful payment
      const athleteNotification = {
        user_id: enrollmentData.athlete_id,
        sender_id: enrollmentData.trainer_id,
        related_id: paymentData.id,
        related_type: "payment",
        notification_type: "payment_received",
        category: "payment",
        title: "Payment Successful",
        message: `Your payment of ${paymentData.currency} ${paymentData.amount} for "${enrollmentData.program_name}" has been processed successfully`,
        data: JSON.stringify({
          payment_id: paymentData.id,
          enrollment_id: enrollmentData.id,
          program_name: enrollmentData.program_name,
          amount: paymentData.amount,
          currency: paymentData.currency,
          payment_method: paymentData.payment_method,
        }),
        is_read: false,
        created_at: createdAt,
        updated_at: createdAt,
      };

      await this.sdk.create("notification", athleteNotification);

      return true;
    } catch (error) {
      console.error("Error creating payment notification:", error);
      return false;
    }
  }

  // Create notification for progress updates
  async createProgressNotification(progressData, athleteData, trainerData, notificationType) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      let title = "";
      let message = "";
      let category = "progress";

      switch (notificationType) {
        case "exercise_completed":
          title = "Exercise Completed";
          message = `${athleteData.full_name} completed ${progressData.exercise_name}`;
          break;
        case "day_completed":
          title = "Day Completed";
          message = `${athleteData.full_name} completed ${progressData.day_title}`;
          break;
        case "week_completed":
          title = "Week Completed";
          message = `${athleteData.full_name} completed ${progressData.week_title}`;
          break;
        case "program_completed":
          title = "Program Completed";
          message = `${athleteData.full_name} completed the entire program!`;
          break;
        case "milestone_reached":
          title = "Milestone Reached";
          message = `${athleteData.full_name} reached ${progressData.milestone}% completion`;
          break;
        default:
          title = "Progress Update";
          message = `${athleteData.full_name} made progress in their program`;
      }

      // Notify trainer about athlete progress
      const trainerNotification = {
        user_id: trainerData.id,
        sender_id: athleteData.id,
        related_id: progressData.enrollment_id,
        related_type: "enrollment",
        notification_type: notificationType,
        category: category,
        title: title,
        message: message,
        data: JSON.stringify({
          enrollment_id: progressData.enrollment_id,
          program_name: progressData.program_name,
          athlete_name: athleteData.full_name,
          progress_data: progressData,
        }),
        is_read: false,
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("notification");
      await this.sdk.create("notification", trainerNotification);

      // Notify athlete about their own progress
      const athleteNotification = {
        user_id: athleteData.id,
        sender_id: trainerData.id,
        related_id: progressData.enrollment_id,
        related_type: "enrollment",
        notification_type: notificationType,
        category: category,
        title: title,
        message: message,
        data: JSON.stringify({
          enrollment_id: progressData.enrollment_id,
          program_name: progressData.program_name,
          progress_data: progressData,
        }),
        is_read: false,
        created_at: createdAt,
        updated_at: createdAt,
      };

      await this.sdk.create("notification", athleteNotification);

      return true;
    } catch (error) {
      console.error("Error creating progress notification:", error);
      return false;
    }
  }

  // Create notification for program updates
  async createProgramUpdateNotification(programData, trainerData, enrolledAthletes) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      // Notify all enrolled athletes about program update
      for (const athlete of enrolledAthletes) {
        const athleteNotification = {
          user_id: athlete.athlete_id,
          sender_id: trainerData.id,
          related_id: programData.id,
          related_type: "program",
          notification_type: "program_updated",
          category: "system",
          title: "Program Updated",
          message: `The program "${programData.program_name}" has been updated by your trainer`,
          data: JSON.stringify({
            program_id: programData.id,
            program_name: programData.program_name,
            trainer_name: trainerData.full_name,
            update_details: programData.update_details,
          }),
          is_read: false,
          created_at: createdAt,
          updated_at: createdAt,
        };

        this.sdk.setTable("notification");
        await this.sdk.create("notification", athleteNotification);
      }

      return true;
    } catch (error) {
      console.error("Error creating program update notification:", error);
      return false;
    }
  }

  // Create system alert notification for super admins
  async createSystemAlertNotification(alertData, superAdminIds) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      // Notify all super admins about system alert
      for (const adminId of superAdminIds) {
        const adminNotification = {
          user_id: adminId,
          sender_id: null, // System notification
          related_id: alertData.id,
          related_type: "system",
          notification_type: "system_alert",
          category: "system",
          title: alertData.title,
          message: alertData.message,
          data: JSON.stringify({
            alert_id: alertData.id,
            alert_type: alertData.type,
            alert_severity: alertData.severity,
            alert_details: alertData.details,
          }),
          is_read: false,
          created_at: createdAt,
          updated_at: createdAt,
        };

        this.sdk.setTable("notification");
        await this.sdk.create("notification", adminNotification);
      }

      return true;
    } catch (error) {
      console.error("Error creating system alert notification:", error);
      return false;
    }
  }

  // Create athlete message notification
  async createAthleteMessageNotification(messageData, senderData, recipientData) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      const messageNotification = {
        user_id: recipientData.id,
        sender_id: senderData.id,
        related_id: messageData.id,
        related_type: "user",
        notification_type: "athlete_message",
        category: "communication",
        title: "New Message",
        message: `You have a new message from ${senderData.full_name}`,
        data: JSON.stringify({
          message_id: messageData.id,
          sender_name: senderData.full_name,
          message_preview: messageData.content.substring(0, 100),
          message_type: messageData.type,
        }),
        is_read: false,
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("notification");
      await this.sdk.create("notification", messageNotification);

      return true;
    } catch (error) {
      console.error("Error creating athlete message notification:", error);
      return false;
    }
  }

  // Create refund request notification
  async createRefundRequestNotification(refundData, athleteData, trainerData, notificationType) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      let title = "";
      let message = "";
      let category = "refund";

      switch (notificationType) {
        case "refund_requested":
          title = "Refund Request Submitted";
          message = `Your refund request for ${refundData.currency} ${refundData.amount} has been submitted`;
          break;
        case "refund_approved":
          title = "Refund Approved";
          message = `Your refund request for ${refundData.currency} ${refundData.amount} has been approved`;
          break;
        case "refund_rejected":
          title = "Refund Rejected";
          message = `Your refund request for ${refundData.currency} ${refundData.amount} has been rejected`;
          break;
        default:
          title = "Refund Update";
          message = `Your refund request has been updated`;
      }

      // Notify athlete about refund status
      const athleteNotification = {
        user_id: athleteData.id,
        sender_id: trainerData.id,
        related_id: refundData.id,
        related_type: "refund_request",
        notification_type: notificationType,
        category: category,
        title: title,
        message: message,
        data: JSON.stringify({
          refund_id: refundData.id,
          amount: refundData.amount,
          currency: refundData.currency,
          reason: refundData.reason,
          status: refundData.status,
          program_name: refundData.program_name,
        }),
        is_read: false,
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("notification");
      await this.sdk.create("notification", athleteNotification);

      // Notify trainer about refund request (only for new requests)
      if (notificationType === "refund_requested") {
        const trainerNotification = {
          user_id: trainerData.id,
          sender_id: athleteData.id,
          related_id: refundData.id,
          related_type: "refund_request",
          notification_type: notificationType,
          category: category,
          title: "New Refund Request",
          message: `${athleteData.full_name} has requested a refund for ${refundData.currency} ${refundData.amount}`,
          data: JSON.stringify({
            refund_id: refundData.id,
            athlete_name: athleteData.full_name,
            amount: refundData.amount,
            currency: refundData.currency,
            reason: refundData.reason,
            program_name: refundData.program_name,
          }),
          is_read: false,
          created_at: createdAt,
          updated_at: createdAt,
        };

        await this.sdk.create("notification", trainerNotification);
      }

      return true;
    } catch (error) {
      console.error("Error creating refund notification:", error);
      return false;
    }
  }

  // Create post feed notification
  async createPostFeedNotification(postData, trainerData, enrolledAthletes, notificationType) {
    try {
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      let title = "";
      let message = "";
      let category = "social";

      switch (notificationType) {
        case "post_feed_created":
          title = "New Post from Your Trainer";
          message = `${trainerData.full_name} posted: "${postData.content.substring(0, 50)}${postData.content.length > 50 ? '...' : ''}"`;
          break;
        case "post_feed_comment":
          title = "New Comment on Post";
          message = `Someone commented on a post from ${trainerData.full_name}`;
          break;
        default:
          title = "Social Update";
          message = `New activity from your trainer`;
      }

      // Notify all enrolled athletes about trainer's post
      for (const athlete of enrolledAthletes) {
        const athleteNotification = {
          user_id: athlete.athlete_id,
          sender_id: trainerData.id,
          related_id: postData.id,
          related_type: "post_feed",
          notification_type: notificationType,
          category: category,
          title: title,
          message: message,
          data: JSON.stringify({
            post_id: postData.id,
            trainer_name: trainerData.full_name,
            post_content: postData.content,
            post_type: postData.type,
            media_urls: postData.media_urls || [],
          }),
          is_read: false,
          created_at: createdAt,
          updated_at: createdAt,
        };

        this.sdk.setTable("notification");
        await this.sdk.create("notification", athleteNotification);
      }

      return true;
    } catch (error) {
      console.error("Error creating post feed notification:", error);
      return false;
    }
  }

  // Get user notifications with pagination
  async getUserNotifications(userId, page = 1, limit = 20, unreadOnly = false) {
    try {
      const offset = (page - 1) * limit;
      
      let whereClause = `WHERE user_id = ${userId}`;
      if (unreadOnly) {
        whereClause += ` AND is_read = false`;
      }

      const notificationsQuery = `
        SELECT 
          n.*,
          u.email as sender_email,
          u.data as sender_data
        FROM kanglink_notification n
        LEFT JOIN kanglink_user u ON n.sender_id = u.id
        ${whereClause}
        ORDER BY n.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

      const notifications = await this.sdk.rawQuery(notificationsQuery);

      // Format notifications
      const formattedNotifications = notifications.map((notification) => {
        let senderData = null;
        if (notification.sender_data) {
          try {
            senderData = JSON.parse(notification.sender_data);
          } catch (e) {
            console.warn("Failed to parse sender data:", e);
          }
        }

        return {
          id: notification.id,
          notification_type: notification.notification_type,
          category: notification.category,
          title: notification.title,
          message: notification.message,
          data: notification.data ? JSON.parse(notification.data) : null,
          is_read: Boolean(notification.is_read),
          read_at: notification.read_at,
          created_at: notification.created_at,
          sender_id: notification.sender_id,
          sender_name: senderData ? `${senderData.first_name || ""} ${senderData.last_name || ""}`.trim() : null,
          sender_email: notification.sender_email,
          related_id: notification.related_id,
          related_type: notification.related_type,
        };
      });

      return {
        success: true,
        notifications: formattedNotifications,
        pagination: {
          page,
          limit,
          total: notifications.length,
        },
      };
    } catch (error) {
      console.error("Error getting user notifications:", error);
      return {
        success: false,
        error: "Failed to fetch notifications",
      };
    }
  }

  // Mark notification as read
  async markNotificationAsRead(notificationId, userId) {
    try {
      const updateQuery = `
        UPDATE kanglink_notification
        SET is_read = true, read_at = '${UtilService.sqlDateTimeFormat(new Date())}'
        WHERE id = ? AND user_id = ?
      `;

      await this.sdk.rawQuery(updateQuery, [notificationId, userId]);

      return true;
    } catch (error) {
      console.error("Error marking notification as read:", error);
      return false;
    }
  }

  // Mark all notifications as read for a user
  async markAllNotificationsAsRead(userId) {
    try {
      const updateQuery = `
        UPDATE kanglink_notification
        SET is_read = true, read_at = '${UtilService.sqlDateTimeFormat(new Date())}'
        WHERE user_id = ? AND is_read = false
      `;

      await this.sdk.rawQuery(updateQuery, [userId]);

      return true;
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
      return false;
    }
  }

  // Get unread notification count for a user
  async getUnreadNotificationCount(userId) {
    try {
      const countQuery = `
        SELECT COUNT(*) as count
        FROM kanglink_notification
        WHERE user_id = ? AND is_read = false
      `;

      const result = await this.sdk.rawQuery(countQuery, [userId]);
      return result[0]?.count || 0;
    } catch (error) {
      console.error("Error getting unread notification count:", error);
      return 0;
    }
  }
}

module.exports = NotificationService; 