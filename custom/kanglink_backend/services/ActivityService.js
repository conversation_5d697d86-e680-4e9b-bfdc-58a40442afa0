const UtilService = require("../../../baas/services/UtilService");

class ActivityService {
  constructor(sdk) {
    this.sdk = sdk;
  }

  // Create activity for exercise completion
  async createExerciseActivity(exerciseData, athleteData, trainerData, enrollmentData) {
    try {
      // Check if program has track_progress enabled
      const program = await this.sdk.rawQuery(`
        SELECT p.track_progress, p.program_name, sp.title as split_title
        FROM kanglink_program p
        JOIN kanglink_split sp ON sp.program_id = p.id
        WHERE sp.id = ${enrollmentData.split_id}
      `);

      if (!program || program.length === 0 || !program[0].track_progress) {
        return false; // Don't create activity if track_progress is disabled
      }

      const programData = program[0];
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      const activityData = {
        user_id: trainerData.id,
        actor_id: athleteData.id,
        activity_type: "workout_completed",
        related_id: exerciseData.exercise_instance_id,
        related_type: "exercise",
        title: "Exercise Completed",
        description: `${athleteData.full_name} completed ${exerciseData.exercise_name || "an exercise"} in ${programData.program_name}`,
        metadata: JSON.stringify({
          exercise_instance_id: exerciseData.exercise_instance_id,
          session_id: exerciseData.session_id,
          day_id: exerciseData.day_id,
          program_name: programData.program_name,
          split_title: programData.split_title,
          sets_completed: exerciseData.sets_completed,
          reps_completed: exerciseData.reps_completed,
          weight_used: exerciseData.weight_used,
          time_taken_seconds: exerciseData.time_taken_seconds,
          difficulty_rating: exerciseData.difficulty_rating,
          enrollment_id: enrollmentData.id,
        }),
        visibility: "trainer_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", activityData);

      return true;
    } catch (error) {
      console.error("Error creating exercise activity:", error);
      return false;
    }
  }

  // Create activity for day completion
  async createDayActivity(dayData, athleteData, trainerData, enrollmentData) {
    try {
      // Check if program has track_progress enabled
      const program = await this.sdk.rawQuery(`
        SELECT p.track_progress, p.program_name, sp.title as split_title
        FROM kanglink_program p
        JOIN kanglink_split sp ON sp.program_id = p.id
        WHERE sp.id = ${enrollmentData.split_id}
      `);

      if (!program || program.length === 0 || !program[0].track_progress) {
        return false; // Don't create activity if track_progress is disabled
      }

      const programData = program[0];
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      const activityData = {
        user_id: trainerData.id,
        actor_id: athleteData.id,
        activity_type: "day_completed",
        related_id: dayData.day_id,
        related_type: "day",
        title: "Day Completed",
        description: `${athleteData.full_name} completed ${dayData.day_title || "a day"} in ${programData.program_name}`,
        metadata: JSON.stringify({
          day_id: dayData.day_id,
          week_id: dayData.week_id,
          program_name: programData.program_name,
          split_title: programData.split_title,
          total_exercises: dayData.total_exercises,
          completed_exercises: dayData.completed_exercises,
          enrollment_id: enrollmentData.id,
        }),
        visibility: "trainer_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", activityData);

      return true;
    } catch (error) {
      console.error("Error creating day activity:", error);
      return false;
    }
  }

  // Create activity for week completion
  async createWeekActivity(weekData, athleteData, trainerData, enrollmentData) {
    try {
      // Check if program has track_progress enabled
      const program = await this.sdk.rawQuery(`
        SELECT p.track_progress, p.program_name, sp.title as split_title
        FROM kanglink_program p
        JOIN kanglink_split sp ON sp.program_id = p.id
        WHERE sp.id = ${enrollmentData.split_id}
      `);

      if (!program || program.length === 0 || !program[0].track_progress) {
        return false; // Don't create activity if track_progress is disabled
      }

      const programData = program[0];
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      const activityData = {
        user_id: trainerData.id,
        actor_id: athleteData.id,
        activity_type: "week_completed",
        related_id: weekData.week_id,
        related_type: "week",
        title: "Week Completed",
        description: `${athleteData.full_name} completed ${weekData.week_title || "a week"} in ${programData.program_name}`,
        metadata: JSON.stringify({
          week_id: weekData.week_id,
          program_name: programData.program_name,
          split_title: programData.split_title,
          total_days: weekData.total_days,
          completed_days: weekData.completed_days,
          enrollment_id: enrollmentData.id,
        }),
        visibility: "trainer_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", activityData);

      return true;
    } catch (error) {
      console.error("Error creating week activity:", error);
      return false;
    }
  }

  // Create activity for program completion
  async createProgramActivity(programData, athleteData, trainerData, enrollmentData) {
    try {
      // Check if program has track_progress enabled
      const program = await this.sdk.rawQuery(`
        SELECT p.track_progress, p.program_name, sp.title as split_title
        FROM kanglink_program p
        JOIN kanglink_split sp ON sp.program_id = p.id
        WHERE sp.id = ${enrollmentData.split_id}
      `);

      if (!program || program.length === 0 || !program[0].track_progress) {
        return false; // Don't create activity if track_progress is disabled
      }

      const programInfo = program[0];
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      const activityData = {
        user_id: trainerData.id,
        actor_id: athleteData.id,
        activity_type: "program_completed",
        related_id: enrollmentData.program_id,
        related_type: "program",
        title: "Program Completed",
        description: `${athleteData.full_name} completed ${programInfo.program_name}`,
        metadata: JSON.stringify({
          program_id: enrollmentData.program_id,
          program_name: programInfo.program_name,
          split_title: programInfo.split_title,
          total_days_completed: programData.total_days_completed,
          total_exercises_completed: programData.total_exercises_completed,
          progress_percentage: programData.progress_percentage,
          enrollment_id: enrollmentData.id,
        }),
        visibility: "trainer_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", activityData);

      return true;
    } catch (error) {
      console.error("Error creating program activity:", error);
      return false;
    }
  }

  // Create activity for milestone reached
  async createMilestoneActivity(milestoneData, athleteData, trainerData, enrollmentData) {
    try {
      // Check if program has track_progress enabled
      const program = await this.sdk.rawQuery(`
        SELECT p.track_progress, p.program_name, sp.title as split_title
        FROM kanglink_program p
        JOIN kanglink_split sp ON sp.program_id = p.id
        WHERE sp.id = ${enrollmentData.split_id}
      `);

      if (!program || program.length === 0 || !program[0].track_progress) {
        return false; // Don't create activity if track_progress is disabled
      }

      const programData = program[0];
      const createdAt = UtilService.sqlDateTimeFormat(new Date());

      const activityData = {
        user_id: trainerData.id,
        actor_id: athleteData.id,
        activity_type: "milestone_reached",
        related_id: milestoneData.milestone_id,
        related_type: "milestone",
        title: "Milestone Reached",
        description: `${athleteData.full_name} reached a milestone in ${programData.program_name}`,
        metadata: JSON.stringify({
          milestone_id: milestoneData.milestone_id,
          milestone_type: milestoneData.milestone_type,
          milestone_value: milestoneData.milestone_value,
          program_name: programData.program_name,
          split_title: programData.split_title,
          enrollment_id: enrollmentData.id,
        }),
        visibility: "trainer_only",
        created_at: createdAt,
        updated_at: createdAt,
      };

      this.sdk.setTable("activity");
      await this.sdk.create("activity", activityData);

      return true;
    } catch (error) {
      console.error("Error creating milestone activity:", error);
      return false;
    }
  }

  // Get trainer activities for their programs
  async getTrainerActivities(trainerId, filters = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        activity_type,
        visibility = "all",
        program_id,
        athlete_id,
        date_from,
        date_to,
      } = filters;

      const offset = (page - 1) * limit;

      // Build WHERE conditions
      let whereConditions = [
        "a.user_id = ?",
      ];
      let queryParams = [trainerId];

      if (activity_type) {
        whereConditions.push("a.activity_type = ?");
        queryParams.push(activity_type);
      }

      if (visibility !== "all") {
        whereConditions.push("a.visibility = ?");
        queryParams.push(visibility);
      } else {
        whereConditions.push("a.visibility IN ('public', 'trainer_only')");
      }

      if (program_id) {
        whereConditions.push("JSON_EXTRACT(a.metadata, '$.program_id') = ?");
        queryParams.push(program_id);
      }

      if (athlete_id) {
        whereConditions.push("a.actor_id = ?");
        queryParams.push(athlete_id);
      }

      if (date_from) {
        whereConditions.push("a.created_at >= ?");
        queryParams.push(date_from);
      }

      if (date_to) {
        whereConditions.push("a.created_at <= ?");
        queryParams.push(date_to);
      }

      const whereClause = whereConditions.join(" AND ");

      // Get activities with user and program information
      const activitiesQuery = `
        SELECT 
          a.*,
          u.data as user_data,
          u.email as user_email,
          JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.program_name')) as program_name,
          JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.split_title')) as split_title,
          JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.enrollment_id')) as enrollment_id
        FROM kanglink_activity a
        JOIN kanglink_user u ON a.actor_id = u.id
        WHERE ${whereClause}
        ORDER BY a.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

      const activities = await this.sdk.rawQuery(activitiesQuery, queryParams);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM kanglink_activity a
        JOIN kanglink_user u ON a.actor_id = u.id
        WHERE ${whereClause}
      `;

      const countResult = await this.sdk.rawQuery(countQuery, queryParams);
      const total = countResult[0]?.total || 0;

      // Format activities
      const formattedActivities = activities.map((activity) => {
        let userData = null;
        if (activity.user_data) {
          try {
            userData = JSON.parse(activity.user_data);
          } catch (e) {
            console.warn("Failed to parse user data:", e);
          }
        }

        return {
          id: activity.id,
          user_id: activity.user_id,
          user_name: userData ? `${userData.first_name || ""} ${userData.last_name || ""}`.trim() : "Unknown User",
          user_email: activity.user_email,
          actor_id: activity.actor_id,
          actor_name: userData ? `${userData.first_name || ""} ${userData.last_name || ""}`.trim() : "Unknown User",
          activity_type: activity.activity_type,
          title: activity.title,
          description: activity.description,
          metadata: activity.metadata ? JSON.parse(activity.metadata) : null,
          visibility: activity.visibility,
          related_id: activity.related_id,
          related_type: activity.related_type,
          program_name: activity.program_name,
          split_title: activity.split_title,
          enrollment_id: activity.enrollment_id,
          created_at: activity.created_at,
          updated_at: activity.updated_at,
        };
      });

      return {
        success: true,
        activities: formattedActivities,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1,
        },
      };
    } catch (error) {
      console.error("Error getting trainer activities:", error);
      return {
        success: false,
        error: "Failed to fetch activities",
      };
    }
  }
}

module.exports = ActivityService; 