const StripeService = require("../../../baas/services/StripeService");

class CommissionProcessingService {
  constructor(sdk) {
    this.sdk = sdk;
    this.sdk.setProjectId("kanglink");
  }

  /**
   * Process pending commissions that are ready for payout
   * This should be called periodically (e.g., every hour) to move funds from pending to available
   */
  async processPendingCommissions() {
    try {
      console.log("Starting commission processing...");

      // Get active payout settings
      const payoutSettingsQuery = `
        SELECT trainer_payout_time_hours
        FROM kanglink_payout_settings
        WHERE is_active = true
        ORDER BY created_at DESC
        LIMIT 1
      `;
      const payoutSettings = await this.sdk.rawQuery(payoutSettingsQuery);
      const payoutTimeHours = payoutSettings[0]?.trainer_payout_time_hours || 24;

      // Find commissions that are ready to be made available for payout
      const readyCommissionsQuery = `
        SELECT 
          c.*,
          e.status as enrollment_status,
          e.payment_status as enrollment_payment_status
        FROM kanglink_commission c
        LEFT JOIN kanglink_enrollment e ON c.enrollment_id = e.id
        WHERE c.payout_status = 'pending'
          AND c.payout_scheduled_at <= NOW()
          AND e.status NOT IN ('refund', 'cancelled')
          AND e.payment_status = 'paid'
      `;

      const readyCommissions = await this.sdk.rawQuery(readyCommissionsQuery);

      if (readyCommissions.length === 0) {
        console.log("No commissions ready for processing");
        return { processed: 0, errors: [] };
      }

      console.log(`Found ${readyCommissions.length} commissions ready for processing`);

      let processed = 0;
      const errors = [];

      // Process each commission
      for (const commission of readyCommissions) {
        try {
          // Check if enrollment still exists and is valid
          const enrollmentCheck = await this.sdk.rawQuery(
            'SELECT status, payment_status FROM kanglink_enrollment WHERE id = ?',
            [commission.enrollment_id]
          );

          const enrollment = enrollmentCheck[0];
          if (!enrollment) {
            console.log(`Enrollment ${commission.enrollment_id} not found, skipping commission ${commission.id}`);
            continue;
          }

          // Skip if enrollment has been refunded or cancelled
          if (enrollment.status === 'refund' || enrollment.status === 'cancelled') {
            console.log(`Enrollment ${commission.enrollment_id} has been refunded/cancelled, cancelling commission ${commission.id}`);
            
            await this.sdk.rawQuery(
              `UPDATE kanglink_commission 
               SET payout_status = 'cancelled', updated_at = NOW() 
               WHERE id = ?`,
              [commission.id]
            );
            continue;
          }

          // Skip if payment is not confirmed
          if (enrollment.payment_status !== 'paid') {
            console.log(`Enrollment ${commission.enrollment_id} payment not confirmed, keeping commission ${commission.id} pending`);
            continue;
          }

          // Update commission to processed status (making it available for withdrawal)
          await this.sdk.rawQuery(
            `UPDATE kanglink_commission 
             SET payout_status = 'processed', 
                 payout_processed_at = NOW(),
                 updated_at = NOW() 
             WHERE id = ?`,
            [commission.id]
          );

          processed++;
          console.log(`Processed commission ${commission.id} for trainer ${commission.trainer_id}`);

        } catch (error) {
          console.error(`Error processing commission ${commission.id}:`, error);
          errors.push({
            commission_id: commission.id,
            error: error.message
          });
        }
      }

      console.log(`Commission processing completed. Processed: ${processed}, Errors: ${errors.length}`);

      return {
        processed,
        errors,
        total_found: readyCommissions.length
      };

    } catch (error) {
      console.error("Error in commission processing:", error);
      throw error;
    }
  }

  /**
   * Handle refund scenarios - cancel related commissions
   */
  async handleRefund(enrollmentId) {
    try {
      console.log(`Handling refund for enrollment ${enrollmentId}`);

      // Find all commissions related to this enrollment
      const commissionsQuery = `
        SELECT * FROM kanglink_commission
        WHERE enrollment_id = ? AND payout_status IN ('pending', 'processed')
      `;
      const commissions = await this.sdk.rawQuery(commissionsQuery, [enrollmentId]);

      if (commissions.length === 0) {
        console.log(`No commissions found for enrollment ${enrollmentId}`);
        return { cancelled: 0 };
      }

      let cancelled = 0;

      for (const commission of commissions) {
        // If commission was already processed (withdrawn), we need to handle clawback
        if (commission.payout_status === 'processed') {
          // For now, just mark as cancelled - in a real system you might want to
          // create a negative commission entry or handle clawback differently
          console.log(`Commission ${commission.id} was already processed, marking as cancelled`);
        }

        // Cancel the commission
        await this.sdk.rawQuery(
          `UPDATE kanglink_commission 
           SET payout_status = 'cancelled', updated_at = NOW() 
           WHERE id = ?`,
          [commission.id]
        );

        cancelled++;
      }

      console.log(`Cancelled ${cancelled} commissions for enrollment ${enrollmentId}`);

      return { cancelled };

    } catch (error) {
      console.error(`Error handling refund for enrollment ${enrollmentId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate and create commission for a new enrollment
   */
  async createCommissionForEnrollment(enrollmentId) {
    try {
      console.log(`Creating commission for enrollment ${enrollmentId}`);

      // Get enrollment details with related data
      const enrollmentQuery = `
        SELECT 
          e.*,
          s.full_price,
          s.subscription_price,
          p.user_id as trainer_id
        FROM kanglink_enrollment e
        LEFT JOIN kanglink_split s ON e.split_id = s.id
        LEFT JOIN kanglink_program p ON e.program_id = p.id
        WHERE e.id = ?
      `;

      const enrollmentResult = await this.sdk.rawQuery(enrollmentQuery, [enrollmentId]);
      const enrollment = enrollmentResult[0];

      if (!enrollment) {
        throw new Error(`Enrollment ${enrollmentId} not found`);
      }

      // Check if commission already exists
      const existingCommission = await this.sdk.rawQuery(
        'SELECT id FROM kanglink_commission WHERE enrollment_id = ?',
        [enrollmentId]
      );

      if (existingCommission.length > 0) {
        console.log(`Commission already exists for enrollment ${enrollmentId}`);
        return { created: false, commission_id: existingCommission[0].id };
      }

      // Get payout settings
      const payoutSettingsQuery = `
        SELECT *
        FROM kanglink_payout_settings
        WHERE is_active = true
        ORDER BY created_at DESC
        LIMIT 1
      `;
      const payoutSettings = await this.sdk.rawQuery(payoutSettingsQuery);
      const settings = payoutSettings[0];

      if (!settings) {
        throw new Error("No active payout settings found");
      }

      // Determine commission type and percentages
      const isAffiliate = enrollment.affiliate_code && enrollment.affiliate_user_id;
      const commissionType = isAffiliate ? 'affiliate' : 'regular';
      
      const trainerPercentage = isAffiliate 
        ? settings.affiliate_trainer_percentage 
        : settings.split_trainer_percentage;
      
      const companyPercentage = isAffiliate 
        ? settings.affiliate_company_percentage 
        : settings.split_company_percentage;

      // Calculate amounts
      const totalAmount = enrollment.amount;
      const originalAmount = enrollment.original_amount || totalAmount;
      const discountAmount = enrollment.discount_amount || 0;
      
      const trainerAmount = (totalAmount * trainerPercentage) / 100;
      const companyAmount = (totalAmount * companyPercentage) / 100;

      // Calculate payout scheduled time
      const payoutScheduledAt = new Date();
      payoutScheduledAt.setHours(payoutScheduledAt.getHours() + settings.trainer_payout_time_hours);

      // Create commission record
      const commissionData = {
        enrollment_id: enrollmentId,
        program_id: enrollment.program_id,
        split_id: enrollment.split_id,
        trainer_id: enrollment.trainer_id,
        athlete_id: enrollment.athlete_id,
        commission_type: commissionType,
        total_amount: totalAmount,
        original_amount: originalAmount,
        discount_amount: discountAmount,
        company_amount: companyAmount,
        trainer_amount: trainerAmount,
        affiliate_code: enrollment.affiliate_code,
        affiliate_user_id: enrollment.affiliate_user_id,
        payout_status: 'pending',
        payout_scheduled_at: payoutScheduledAt.toISOString().slice(0, 19).replace('T', ' '),
        currency: enrollment.currency,
        created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
      };

      const insertQuery = `
        INSERT INTO kanglink_commission (
          enrollment_id, program_id, split_id, trainer_id, athlete_id,
          commission_type, total_amount, original_amount, discount_amount,
          company_amount, trainer_amount, affiliate_code, affiliate_user_id,
          payout_status, payout_scheduled_at, currency, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const insertResult = await this.sdk.rawQuery(insertQuery, [
        commissionData.enrollment_id,
        commissionData.program_id,
        commissionData.split_id,
        commissionData.trainer_id,
        commissionData.athlete_id,
        commissionData.commission_type,
        commissionData.total_amount,
        commissionData.original_amount,
        commissionData.discount_amount,
        commissionData.company_amount,
        commissionData.trainer_amount,
        commissionData.affiliate_code,
        commissionData.affiliate_user_id,
        commissionData.payout_status,
        commissionData.payout_scheduled_at,
        commissionData.currency,
        commissionData.created_at,
        commissionData.updated_at
      ]);

      console.log(`Created commission ${insertResult.insertId} for enrollment ${enrollmentId}`);

      return {
        created: true,
        commission_id: insertResult.insertId,
        commission_data: commissionData
      };

    } catch (error) {
      console.error(`Error creating commission for enrollment ${enrollmentId}:`, error);
      throw error;
    }
  }
}

module.exports = CommissionProcessingService;
