const APITestFramework = require("../../../tests/apitesting.base");

// Initialize test framework
const testFramework = new APITestFramework({
  environment: "test",
});

testFramework.describe("Discount Preview Endpoint Tests", () => {
  // Test that the endpoint exists and can be loaded
  testFramework.addTestCase(
    "should load discount routes without errors",
    async () => {
      try {
        const discountRoutes = require("../routes/discount");
        testFramework.expect(typeof discountRoutes).toBe("function");
        console.log("✅ Discount routes loaded successfully");
      } catch (error) {
        testFramework.fail(`Failed to load discount routes: ${error.message}`);
      }
    }
  );

  // Test that DiscountService can be instantiated
  testFramework.addTestCase("should instantiate DiscountService", async () => {
    try {
      const DiscountService = require("../services/DiscountService");
      const mockSdk = {
        setProjectId: () => {},
        setTable: () => {},
        findOne: async () => null,
        rawQuery: async () => [],
      };

      const discountService = new DiscountService(mockSdk);
      testFramework
        .expect(typeof discountService.calculateDiscountedAmount)
        .toBe("function");
      console.log("✅ DiscountService instantiated successfully");
    } catch (error) {
      testFramework.fail(
        `Failed to instantiate DiscountService: ${error.message}`
      );
    }
  });

  // Test discount calculation logic
  testFramework.addTestCase("should calculate discount correctly", async () => {
    try {
      const DiscountService = require("../services/DiscountService");
      const mockSdk = {
        setProjectId: () => {},
        setTable: () => {},
        findOne: async (table, criteria) => {
          if (table === "program_discount") {
            return {
              id: 1,
              program_id: 1,
              sale_discount_type: "percentage",
              sale_discount_value: 10.0,
            };
          }
          return null;
        },
        rawQuery: async () => [],
      };

      const discountService = new DiscountService(mockSdk);
      const result = await discountService.calculateDiscountedAmount({
        program_id: 1,
        split_id: 1,
        payment_type: "one_time",
        original_amount: 100.0,
        coupon_code: null,
        user_id: 1,
      });

      testFramework.expect(result.success).toBe(true);
      testFramework.expect(result.original_amount).toBe(100.0);
      testFramework.expect(result.final_amount).toBe(90.0); // 10% discount
      testFramework.expect(result.total_discount_amount).toBe(10.0);
      console.log("✅ Discount calculation works correctly");
    } catch (error) {
      testFramework.fail(`Discount calculation failed: ${error.message}`);
    }
  });
});

// Run the tests
testFramework
  .runTests()
  .then(() => {
    console.log("🎉 All discount preview tests completed!");
  })
  .catch((error) => {
    console.error("❌ Test execution failed:", error);
  });
