const APITestFramework = require("../../../tests/apitesting.base.js");

class TwoFactorAuthIntegrationTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    // Test 2FA Status Check
    this.framework.addTestCase("2FA Status Check", async () => {
      const response = await this.framework.makeRequest(
        "/v2/api/kanglink/member/lambda/2fa/status",
        {
          method: "GET",
          headers: {
            Authorization: "Bearer valid-token",
          },
        }
      );

      this.framework.assert(
        response.status === 200,
        "Should return 200 status code"
      );
      this.framework.assert(!response.body.error, "Should not return error");
      this.framework.assert(
        typeof response.body.enabled === "boolean",
        "Should return enabled status"
      );
    });

    // Test 2FA Enable
    this.framework.addTestCase("2FA Enable - QR Code", async () => {
      const response = await this.framework.makeRequest(
        "/v2/api/kanglink/member/lambda/2fa/enable",
        {
          method: "POST",
          headers: {
            Authorization: "Bearer valid-token",
          },
          body: JSON.stringify({
            type: "qr",
          }),
        }
      );

      this.framework.assert(
        response.status === 200,
        "Should return 200 status code"
      );
      this.framework.assert(!response.body.error, "Should not return error");
      this.framework.assert(response.body.qr_code, "Should return QR code");
      this.framework.assert(response.body.secret_key, "Should return secret key");
    });

    // Test 2FA Enable SMS
    this.framework.addTestCase("2FA Enable - SMS", async () => {
      const response = await this.framework.makeRequest(
        "/v2/api/kanglink/member/lambda/2fa/enable",
        {
          method: "POST",
          headers: {
            Authorization: "Bearer valid-token",
          },
          body: JSON.stringify({
            type: "sms",
            phone: "+1234567890",
          }),
        }
      );

      this.framework.assert(
        response.status === 200,
        "Should return 200 status code"
      );
      this.framework.assert(!response.body.error, "Should not return error");
    });

    // Test 2FA Verify
    this.framework.addTestCase("2FA Verify - Valid Code", async () => {
      const response = await this.framework.makeRequest(
        "/v2/api/kanglink/member/lambda/2fa/verify",
        {
          method: "POST",
          headers: {
            Authorization: "Bearer valid-token",
          },
          body: JSON.stringify({
            verification_code: "123456",
          }),
        }
      );

      this.framework.assert(
        response.status === 200,
        "Should return 200 status code"
      );
      this.framework.assert(
        response.body.valid === true,
        "Should return valid flag"
      );
    });

    // Test 2FA Verify - Invalid Code
    this.framework.addTestCase("2FA Verify - Invalid Code", async () => {
      const response = await this.framework.makeRequest(
        "/v2/api/kanglink/member/lambda/2fa/verify",
        {
          method: "POST",
          headers: {
            Authorization: "Bearer valid-token",
          },
          body: JSON.stringify({
            verification_code: "000000",
          }),
        }
      );

      this.framework.assert(
        response.status === 403,
        "Should return 403 status code"
      );
      this.framework.assert(response.body.error, "Should return error");
      this.framework.assert(
        response.body.valid === false,
        "Should return invalid flag"
      );
    });

    // Test 2FA Disable
    this.framework.addTestCase("2FA Disable", async () => {
      const response = await this.framework.makeRequest(
        "/v2/api/kanglink/member/lambda/2fa/disable",
        {
          method: "POST",
          headers: {
            Authorization: "Bearer valid-token",
          },
        }
      );

      this.framework.assert(
        response.status === 200,
        "Should return 200 status code"
      );
      this.framework.assert(!response.body.error, "Should not return error");
      this.framework.assert(
        response.body.message,
        "Should return success message"
      );
    });

    // Test 2FA Authorize
    this.framework.addTestCase("2FA Authorize", async () => {
      const response = await this.framework.makeRequest(
        "/v2/api/kanglink/member/lambda/2fa/authorize",
        {
          method: "POST",
          headers: {
            Authorization: "Bearer valid-token",
          },
        }
      );

      this.framework.assert(
        response.status === 200,
        "Should return 200 status code"
      );
      this.framework.assert(!response.body.error, "Should not return error");
      this.framework.assert(response.body.qr_code, "Should return QR code");
      this.framework.assert(response.body.access_token, "Should return access token");
    });
  }

  async runTests() {
    try {
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

module.exports = TwoFactorAuthIntegrationTests; 