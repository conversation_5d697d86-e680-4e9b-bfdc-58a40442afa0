const { describe, it, before, after } = require("../../../tests/apitesting.base");

describe("Trainer Athletes Management Endpoint", () => {
  let trainerToken;
  let athleteToken;
  let trainerId;
  let athleteId;
  let programId;
  let splitId;
  let enrollmentId;

  before(async () => {
    // Setup test data - create trainer, athlete, program, split, and enrollment
    const sdk = global.sdk;
    sdk.setProjectId("kanglink");

    // Create trainer user
    const trainerData = {
      email: `trainer_${Date.now()}@test.com`,
      password: "password123",
      role_id: "trainer",
      status: 0,
      verify: 1,
      data: JSON.stringify({
        first_name: "<PERSON>",
        last_name: "Trainer",
        photo: "trainer.jpg"
      })
    };

    const trainerResult = await sdk.create("user", trainerData);
    trainerId = trainerResult;

    // Create athlete user
    const athleteData = {
      email: `athlete_${Date.now()}@test.com`,
      password: "password123",
      role_id: "member",
      status: 0,
      verify: 1,
      data: JSON.stringify({
        first_name: "<PERSON>",
        last_name: "Athlete",
        photo: "athlete.jpg"
      })
    };

    const athleteResult = await sdk.create("user", athleteData);
    athleteId = athleteResult;

    // Create program
    const programData = {
      user_id: trainerId,
      program_name: "Test Fitness Program",
      type_of_program: "Strength Training",
      program_description: "A comprehensive strength training program",
      status: "published",
      currency: "USD"
    };

    const programResult = await sdk.create("program", programData);
    programId = programResult;

    // Create split
    const splitData = {
      program_id: programId,
      title: "Beginner Split",
      full_price: 99.99,
      subscription: 19.99,
      equipment_required: "Dumbbells, Bench"
    };

    const splitResult = await sdk.create("split", splitData);
    splitId = splitResult;

    // Create enrollment
    const enrollmentData = {
      trainer_id: trainerId,
      athlete_id: athleteId,
      program_id: programId,
      split_id: splitId,
      payment_type: "subscription",
      amount: 19.99,
      currency: "USD",
      enrollment_date: new Date(),
      status: "active",
      payment_status: "paid"
    };

    const enrollmentResult = await sdk.create("enrollment", enrollmentData);
    enrollmentId = enrollmentResult;

    // Create athlete progress
    const progressData = {
      athlete_id: athleteId,
      enrollment_id: enrollmentId,
      split_id: splitId,
      program_id: programId,
      trainer_id: trainerId,
      progress_percentage: 67.5,
      total_days_completed: 15,
      total_exercises_completed: 45,
      last_activity_date: new Date()
    };

    await sdk.create("athlete_progress", progressData);

    // Generate tokens
    const AuthService = require("../../../../baas/services/AuthService");
    const authService = new AuthService();
    
    trainerToken = await authService.generateToken({
      id: trainerId,
      email: trainerData.email,
      role_id: "trainer"
    });

    athleteToken = await authService.generateToken({
      id: athleteId,
      email: athleteData.email,
      role_id: "member"
    });
  });

  after(async () => {
    // Cleanup test data
    const sdk = global.sdk;
    sdk.setProjectId("kanglink");

    try {
      await sdk.rawQuery(`DELETE FROM kanglink_athlete_progress WHERE enrollment_id = ${enrollmentId}`);
      await sdk.rawQuery(`DELETE FROM kanglink_enrollment WHERE id = ${enrollmentId}`);
      await sdk.rawQuery(`DELETE FROM kanglink_split WHERE id = ${splitId}`);
      await sdk.rawQuery(`DELETE FROM kanglink_program WHERE id = ${programId}`);
      await sdk.rawQuery(`DELETE FROM kanglink_user WHERE id = ${trainerId}`);
      await sdk.rawQuery(`DELETE FROM kanglink_user WHERE id = ${athleteId}`);
    } catch (error) {
      console.log("Cleanup error:", error.message);
    }
  });

  it("should get trainer's athletes with default pagination", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes")
      .set("Authorization", `Bearer ${trainerToken}`)
      .expect(200);

    global.expect(response.body.error).to.be.false;
    global.expect(response.body.message).to.equal("Trainer athletes retrieved successfully");
    global.expect(response.body.data).to.be.an("array");
    global.expect(response.body.data.length).to.be.greaterThan(0);
    
    const athlete = response.body.data[0];
    global.expect(athlete).to.have.property("enrollment_id");
    global.expect(athlete).to.have.property("athlete_name");
    global.expect(athlete).to.have.property("program_name");
    global.expect(athlete).to.have.property("payment_type");
    global.expect(athlete).to.have.property("progress_percentage");
    global.expect(athlete).to.have.property("progress_status");
    
    global.expect(response.body.pagination).to.be.an("object");
    global.expect(response.body.pagination).to.have.property("page");
    global.expect(response.body.pagination).to.have.property("limit");
    global.expect(response.body.pagination).to.have.property("total");
  });

  it("should filter athletes by name", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes?athlete_name=Jane")
      .set("Authorization", `Bearer ${trainerToken}`)
      .expect(200);

    global.expect(response.body.error).to.be.false;
    global.expect(response.body.data).to.be.an("array");
    
    if (response.body.data.length > 0) {
      const athlete = response.body.data[0];
      global.expect(athlete.athlete_name).to.include("Jane");
    }
  });

  it("should filter athletes by payment type", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes?payment_type=subscription")
      .set("Authorization", `Bearer ${trainerToken}`)
      .expect(200);

    global.expect(response.body.error).to.be.false;
    global.expect(response.body.data).to.be.an("array");
    
    if (response.body.data.length > 0) {
      response.body.data.forEach(athlete => {
        global.expect(athlete.payment_type).to.equal("subscription");
      });
    }
  });

  it("should filter athletes by program name", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes?program_name=Fitness")
      .set("Authorization", `Bearer ${trainerToken}`)
      .expect(200);

    global.expect(response.body.error).to.be.false;
    global.expect(response.body.data).to.be.an("array");
    
    if (response.body.data.length > 0) {
      response.body.data.forEach(athlete => {
        global.expect(athlete.program_name).to.include("Fitness");
      });
    }
  });

  it("should filter athletes by progress", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes?progress=above_50")
      .set("Authorization", `Bearer ${trainerToken}`)
      .expect(200);

    global.expect(response.body.error).to.be.false;
    global.expect(response.body.data).to.be.an("array");
    
    if (response.body.data.length > 0) {
      response.body.data.forEach(athlete => {
        global.expect(athlete.progress_percentage).to.be.at.least(50);
        global.expect(athlete.progress_percentage).to.be.below(100);
      });
    }
  });

  it("should sort athletes by name", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes?sort_by=athlete_name&sort_order=asc")
      .set("Authorization", `Bearer ${trainerToken}`)
      .expect(200);

    global.expect(response.body.error).to.be.false;
    global.expect(response.body.data).to.be.an("array");
    global.expect(response.body.sorting.sort_by).to.equal("athlete_name");
    global.expect(response.body.sorting.sort_order).to.equal("asc");
  });

  it("should sort athletes by progress", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes?sort_by=progress&sort_order=desc")
      .set("Authorization", `Bearer ${trainerToken}`)
      .expect(200);

    global.expect(response.body.error).to.be.false;
    global.expect(response.body.data).to.be.an("array");
    global.expect(response.body.sorting.sort_by).to.equal("progress");
    global.expect(response.body.sorting.sort_order).to.equal("desc");
  });

  it("should handle pagination correctly", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes?page=1&limit=5")
      .set("Authorization", `Bearer ${trainerToken}`)
      .expect(200);

    global.expect(response.body.error).to.be.false;
    global.expect(response.body.pagination.page).to.equal(1);
    global.expect(response.body.pagination.limit).to.equal(5);
    global.expect(response.body.data.length).to.be.at.most(5);
  });

  it("should return 401 for non-trainer users", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes")
      .set("Authorization", `Bearer ${athleteToken}`)
      .expect(401);

    global.expect(response.body.error).to.be.true;
  });

  it("should return 400 for invalid sort parameters", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes?sort_by=invalid_field")
      .set("Authorization", `Bearer ${trainerToken}`)
      .expect(400);

    global.expect(response.body.error).to.be.true;
    global.expect(response.body.message).to.include("Invalid sort_by parameter");
  });

  it("should return 400 for invalid payment type filter", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes?payment_type=invalid_type")
      .set("Authorization", `Bearer ${trainerToken}`)
      .expect(400);

    global.expect(response.body.error).to.be.true;
    global.expect(response.body.message).to.include("Invalid payment_type parameter");
  });

  it("should return 400 for invalid progress filter", async () => {
    const response = await global.request
      .get("/v2/api/kanglink/custom/trainer/athletes?progress=invalid_progress")
      .set("Authorization", `Bearer ${trainerToken}`)
      .expect(400);

    global.expect(response.body.error).to.be.true;
    global.expect(response.body.message).to.include("Invalid progress parameter");
  });
});
