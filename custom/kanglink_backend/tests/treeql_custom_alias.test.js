// Simple test to demonstrate TreeQL custom alias enhancement
console.log("🧪 Testing TreeQL Custom Alias Enhancement...\n");

function testCustomAliasJoinParsing() {
  try {
    console.log("1. Testing join parsing with custom aliases...");

    // Simulate the join parsing
    const mockReq = {
      query: {
        join: ["user|athlete_id|athlete", "user|trainer_id|trainer"],
      },
    };

    const RequestUtils = require("../../../baas/core/RequestUtils");
    const { joins, hasJoin } = RequestUtils.getRequestJoins(mockReq);

    console.log("   ✅ hasJoin:", hasJoin);
    console.log("   ✅ joins.length:", joins.length);

    // Test first join parsing (new object format)
    const firstJoin = joins[0];
    console.log(
      "   ✅ First join - table:",
      firstJoin.table,
      "foreignKey:",
      firstJoin.foreignKey,
      "alias:",
      firstJoin.alias
    );

    // Test second join parsing (new object format)
    const secondJoin = joins[1];
    console.log(
      "   ✅ Second join - table:",
      secondJoin.table,
      "foreignKey:",
      secondJoin.foreignKey,
      "alias:",
      secondJoin.alias
    );

    console.log("   ✅ Join parsing works correctly with custom aliases\n");

    return true;
  } catch (error) {
    console.error("   ❌ Test failed:", error);
    return false;
  }
}

function testBackwardCompatibility() {
  try {
    console.log("2. Testing backward compatibility...");

    // Test that old syntax still works
    const mockReq = {
      query: {
        join: ["user|trainer_id"], // Old syntax without alias
      },
    };

    const RequestUtils = require("../../../baas/core/RequestUtils");
    const { joins, hasJoin } = RequestUtils.getRequestJoins(mockReq);

    const join = joins[0];
    console.log(
      "   ✅ Old syntax - table:",
      join.table,
      "foreignKey:",
      join.foreignKey,
      "alias:",
      join.alias || "undefined"
    );
    console.log("   ✅ Backward compatibility maintained\n");

    return true;
  } catch (error) {
    console.error("   ❌ Backward compatibility test failed:", error);
    return false;
  }
}

function demonstrateUsage() {
  console.log("3. Usage Examples:");
  console.log("   📝 Old way (causes conflicts):");
  console.log("      ?join=user|athlete_id&join=user|trainer_id");
  console.log(
    "      Result: { user: {...}, user: {...} } // Second overwrites first!"
  );
  console.log("");
  console.log("   📝 New way (with custom aliases):");
  console.log(
    "      ?join=user|athlete_id|athlete&join=user|trainer_id|trainer"
  );
  console.log(
    "      Result: { athlete: {...}, trainer: {...} } // No conflicts!"
  );
  console.log("");
  console.log("   📝 More examples:");
  console.log("      ?join=program|program_id|program_details");
  console.log("      ?join=split|split_id|selected_split");
  console.log("      ?join=user|created_by|creator");
  console.log("");
}

// Run the tests
console.log("🚀 Running TreeQL Custom Alias Tests...\n");

const test1 = testCustomAliasJoinParsing();
const test2 = testBackwardCompatibility();

demonstrateUsage();

if (test1 && test2) {
  console.log(
    "🎉 All tests passed! TreeQL custom alias enhancement is working correctly."
  );
  console.log("");
  console.log("📋 Summary of Enhancement:");
  console.log(
    "   • Added support for custom aliases in join syntax: table|foreignKey|alias"
  );
  console.log(
    "   • Resolves conflicts when joining the same table multiple times"
  );
  console.log("   • Maintains backward compatibility with existing syntax");
  console.log(
    "   • Enhanced DBUtil.guessRelationInfo to support custom aliases"
  );
  console.log("   • Updated matchBelongsTo and matchHasOneOrMany functions");
  console.log("");
  console.log("🔧 Implementation Details:");
  console.log("   • Modified mtpbk/baas/lambda/treeql.js");
  console.log("   • Enhanced mtpbk/baas/core/DBUtil.js");
  console.log("   • Added alias field to relationship objects");
  console.log("   • Updated result mapping to use aliases");
  process.exit(0);
} else {
  console.log("❌ Some tests failed!");
  process.exit(1);
}
