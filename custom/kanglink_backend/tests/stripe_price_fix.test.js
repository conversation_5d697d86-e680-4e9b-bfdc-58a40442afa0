// Test to verify Stripe price records are created with correct fields
const { TestSuite } = require("../../../tests/apitesting.base");

class StripePriceFixTest extends TestSuite {
  constructor() {
    super("Stripe Price Fix Tests");
  }

  async testStripePriceFields() {
    this.describe("Test that Stripe price records have required fields");
    
    try {
      // This test would need to be run after creating/updating a program with subscription pricing
      // For now, we'll just test the database structure
      
      const response = await this.makeRequest({
        method: "GET",
        url: "/v2/api/kanglink/custom/develop/test-stripe-price-fields",
        headers: {
          "Authorization": "Bearer test-token"
        }
      });

      if (response.status === 404) {
        this.info("Test endpoint not found - this is expected if not implemented");
        return;
      }

      const data = response.data;
      
      if (data.error) {
        this.info(`Test endpoint returned error: ${data.message}`);
        return;
      }

      // Check if the response contains the expected fields
      if (data.stripe_prices && data.stripe_prices.length > 0) {
        const priceRecord = data.stripe_prices[0];
        
        this.assert(
          priceRecord.hasOwnProperty('product_id'),
          "Stripe price record should have product_id field",
          "product_id field exists"
        );

        this.assert(
          priceRecord.hasOwnProperty('amount'),
          "Stripe price record should have amount field",
          "amount field exists"
        );

        this.assert(
          priceRecord.hasOwnProperty('type'),
          "Stripe price record should have type field",
          "type field exists"
        );

        this.assert(
          priceRecord.type === 'one_time' || priceRecord.type === 'recurring',
          "Type should be 'one_time' or 'recurring'",
          "type field has valid value"
        );

        this.success("Stripe price records have required fields");
      } else {
        this.info("No Stripe price records found to test");
      }

    } catch (error) {
      this.fail(`Stripe price fields test failed: ${error.message}`);
    }
  }

  async testEnrollmentEligibilityAfterFix() {
    this.describe("Test enrollment eligibility after Stripe price fix");
    
    try {
      // Test with a known split ID that should have subscription pricing
      const response = await this.makeRequest({
        method: "GET",
        url: "/v2/api/kanglink/custom/splits/78/eligibility",
        headers: {
          "Authorization": "Bearer test-token"
        }
      });

      const data = response.data;
      
      if (response.status === 401) {
        this.info("Authentication required - test skipped");
        return;
      }

      if (response.status === 404) {
        this.info("Split not found - test skipped");
        return;
      }

      this.assert(
        data.eligibility,
        "Response should contain eligibility object",
        "Eligibility object exists"
      );

      this.assert(
        data.eligibility.payment_options,
        "Eligibility should contain payment_options",
        "Payment options exist"
      );

      const subscriptionOption = data.eligibility.payment_options.subscription;
      
      if (subscriptionOption) {
        this.assert(
          typeof subscriptionOption.available === 'boolean',
          "Subscription available should be boolean",
          "Subscription availability is boolean"
        );

        if (!subscriptionOption.available) {
          this.info(`Subscription not available: ${subscriptionOption.reason}`);
          
          // Check if it's still the "Stripe price not configured" error
          if (subscriptionOption.reason === "Stripe price not configured") {
            this.fail("Subscription still shows 'Stripe price not configured' - fix may not be working");
          } else {
            this.info("Subscription unavailable for different reason - this may be expected");
          }
        } else {
          this.success("Subscription payment is available");
        }
      }

    } catch (error) {
      this.fail(`Enrollment eligibility test failed: ${error.message}`);
    }
  }

  async run() {
    await this.testStripePriceFields();
    await this.testEnrollmentEligibilityAfterFix();
  }
}

module.exports = StripePriceFixTest;
