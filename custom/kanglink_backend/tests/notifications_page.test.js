const ApiTestingBase = require("../../tests/apitesting.base.js");

class NotificationsPageTest extends ApiTestingBase {
  constructor() {
    super();
    this.baseUrl = "/v2/api/kanglink/custom/athlete/notifications";
  }

  async runTests() {
    console.log("🧪 Running Notifications Page Tests...");

    await this.testGetNotifications();
    await this.testGetNotificationsWithFilters();
    await this.testMarkNotificationAsRead();
    await this.testMarkAllNotificationsAsRead();
    await this.testGetUnreadCount();
    await this.testNotificationsUnauthorized();

    console.log("✅ Notifications Page Tests Completed!");
  }

  async testGetNotifications() {
    console.log("  📋 Testing: Get Notifications - Valid Request");

    try {
      const response = await this.makeRequest("GET", "", null, {
        headers: this.getAuthHeaders("member"),
      });

      this.assertResponse(response, {
        statusCode: 200,
        hasError: false,
        hasData: true,
        message: "Notifications retrieved successfully",
      });

      // Validate response structure
      const { data } = response.body;
      this.assertExists(data.notifications, "Notifications array should exist");
      this.assertIsArray(data.notifications, "Notifications should be an array");
      this.assertExists(data.pagination, "Pagination should exist");

      console.log("    ✅ Get Notifications - Valid Request: PASSED");
    } catch (error) {
      console.log(`    ❌ Get Notifications - Valid Request: FAILED - ${error.message}`);
    }
  }

  async testGetNotificationsWithFilters() {
    console.log("  📋 Testing: Get Notifications - With Filters");

    try {
      // Test with unread filter
      const unreadResponse = await this.makeRequest("GET", "?unread_only=true", null, {
        headers: this.getAuthHeaders("member"),
      });

      this.assertResponse(unreadResponse, {
        statusCode: 200,
        hasError: false,
        hasData: true,
      });

      // Test with pagination
      const paginatedResponse = await this.makeRequest("GET", "?page=1&limit=10", null, {
        headers: this.getAuthHeaders("member"),
      });

      this.assertResponse(paginatedResponse, {
        statusCode: 200,
        hasError: false,
        hasData: true,
      });

      console.log("    ✅ Get Notifications - With Filters: PASSED");
    } catch (error) {
      console.log(`    ❌ Get Notifications - With Filters: FAILED - ${error.message}`);
    }
  }

  async testMarkNotificationAsRead() {
    console.log("  📋 Testing: Mark Notification as Read");

    try {
      // First get notifications to find an unread one
      const getResponse = await this.makeRequest("GET", "", null, {
        headers: this.getAuthHeaders("member"),
      });

      if (getResponse.body.data.notifications.length > 0) {
        const unreadNotification = getResponse.body.data.notifications.find(
          (n) => !n.is_read
        );

        if (unreadNotification) {
          const response = await this.makeRequest("PUT", `/${unreadNotification.id}/read`, null, {
            headers: this.getAuthHeaders("member"),
          });

          this.assertResponse(response, {
            statusCode: 200,
            hasError: false,
            message: "Notification marked as read",
          });

          console.log("    ✅ Mark Notification as Read: PASSED");
        } else {
          console.log("    ⚠️  No unread notifications found, skipping test");
        }
      } else {
        console.log("    ⚠️  No notifications found, skipping test");
      }
    } catch (error) {
      console.log(`    ❌ Mark Notification as Read: FAILED - ${error.message}`);
    }
  }

  async testMarkAllNotificationsAsRead() {
    console.log("  📋 Testing: Mark All Notifications as Read");

    try {
      const response = await this.makeRequest("PUT", "/read-all", null, {
        headers: this.getAuthHeaders("member"),
      });

      this.assertResponse(response, {
        statusCode: 200,
        hasError: false,
        message: "All notifications marked as read",
      });

      console.log("    ✅ Mark All Notifications as Read: PASSED");
    } catch (error) {
      console.log(`    ❌ Mark All Notifications as Read: FAILED - ${error.message}`);
    }
  }

  async testGetUnreadCount() {
    console.log("  📋 Testing: Get Unread Count");

    try {
      const response = await this.makeRequest("GET", "/unread-count", null, {
        headers: this.getAuthHeaders("member"),
      });

      this.assertResponse(response, {
        statusCode: 200,
        hasError: false,
        hasData: true,
        message: "Unread count retrieved successfully",
      });

      // Validate response structure
      const { data } = response.body;
      this.assertExists(data.unread_count, "Unread count should exist");
      this.assertIsNumber(data.unread_count, "Unread count should be a number");

      console.log("    ✅ Get Unread Count: PASSED");
    } catch (error) {
      console.log(`    ❌ Get Unread Count: FAILED - ${error.message}`);
    }
  }

  async testNotificationsUnauthorized() {
    console.log("  📋 Testing: Notifications - Unauthorized");

    try {
      const response = await this.makeRequest("GET", "", null, {
        headers: {}, // No auth headers
      });

      this.assertResponse(response, {
        statusCode: 401,
        hasError: true,
      });

      console.log("    ✅ Notifications - Unauthorized: PASSED");
    } catch (error) {
      console.log(`    ❌ Notifications - Unauthorized: FAILED - ${error.message}`);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const test = new NotificationsPageTest();
  test.runTests().catch(console.error);
}

module.exports = NotificationsPageTest; 