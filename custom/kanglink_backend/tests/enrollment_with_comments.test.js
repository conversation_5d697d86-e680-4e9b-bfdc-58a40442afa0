const APITestFramework = require("../../tests/apitesting.base.js");

const testFramework = new APITestFramework();

// Test configuration
const BASE_URL = "http://localhost:3000";
const TEST_ATHLETE_TOKEN = "test_athlete_token"; // Replace with actual test token
const TEST_TRAINER_TOKEN = "test_trainer_token"; // Replace with actual test token

testFramework.describe("Enrollment with Comments API", () => {
  testFramework.addTestCase("Should fetch athlete enrollments where trainer has allowed comments", async () => {
    const response = await testFramework.makeRequest(
      `${BASE_URL}/v2/api/kanglink/custom/athlete/enrollments/with-comments`,
      {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${TEST_ATHLETE_TOKEN}`,
          "Content-Type": "application/json",
        },
        query: {
          page: 1,
          limit: 10,
        },
      }
    );

    // Assert response structure
    testFramework.assert(response.error === false, "Response should not have error");
    testFramework.assert(response.data, "Response should have data array");
    testFramework.assert(Array.isArray(response.data), "Data should be an array");
    testFramework.assert(response.pagination, "Response should have pagination");
    testFramework.assert(response.message, "Response should have message");

    // Assert pagination structure
    testFramework.assert(typeof response.pagination.page === "number", "Page should be a number");
    testFramework.assert(typeof response.pagination.limit === "number", "Limit should be a number");
    testFramework.assert(typeof response.pagination.total === "number", "Total should be a number");
    testFramework.assert(typeof response.pagination.totalPages === "number", "TotalPages should be a number");
    testFramework.assert(typeof response.pagination.hasNext === "boolean", "HasNext should be a boolean");
    testFramework.assert(typeof response.pagination.hasPrev === "boolean", "HasPrev should be a boolean");

    // If there are enrollments, validate their structure
    if (response.data.length > 0) {
      const enrollment = response.data[0];
      
      // Assert enrollment structure
      testFramework.assert(enrollment.id, "Enrollment should have id");
      testFramework.assert(enrollment.athlete_id, "Enrollment should have athlete_id");
      testFramework.assert(enrollment.program_id, "Enrollment should have program_id");
      testFramework.assert(enrollment.split_id, "Enrollment should have split_id");
      testFramework.assert(enrollment.status === "active", "Enrollment should be active");
      
      // Assert program structure
      testFramework.assert(enrollment.program, "Enrollment should have program object");
      testFramework.assert(enrollment.program.allow_comments === true, "Program should allow comments");
      testFramework.assert(enrollment.program.status, "Program should have status");
      testFramework.assert(["live", "published", "active"].includes(enrollment.program.status), "Program should be live, published, or active");
      
      // Assert split structure
      testFramework.assert(enrollment.split, "Enrollment should have split object");
      testFramework.assert(enrollment.split.title, "Split should have title");
      
      // Assert trainer structure
      testFramework.assert(enrollment.trainer, "Enrollment should have trainer object");
      testFramework.assert(enrollment.trainer.email, "Trainer should have email");
    }
  });

  testFramework.addTestCase("Should support search functionality", async () => {
    const searchTerm = "test";
    const response = await testFramework.makeRequest(
      `${BASE_URL}/v2/api/kanglink/custom/athlete/enrollments/with-comments`,
      {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${TEST_ATHLETE_TOKEN}`,
          "Content-Type": "application/json",
        },
        query: {
          page: 1,
          limit: 10,
          search: searchTerm,
        },
      }
    );

    testFramework.assert(response.error === false, "Response should not have error");
    testFramework.assert(response.data, "Response should have data array");
    
    // If there are results, they should match the search term
    if (response.data.length > 0) {
      const enrollment = response.data[0];
      const matchesSearch = 
        enrollment.program.program_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        enrollment.split.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        enrollment.program.type_of_program.toLowerCase().includes(searchTerm.toLowerCase());
      
      testFramework.assert(matchesSearch, "Results should match search term");
    }
  });

  testFramework.addTestCase("Should handle pagination correctly", async () => {
    const response = await testFramework.makeRequest(
      `${BASE_URL}/v2/api/kanglink/custom/athlete/enrollments/with-comments`,
      {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${TEST_ATHLETE_TOKEN}`,
          "Content-Type": "application/json",
        },
        query: {
          page: 2,
          limit: 5,
        },
      }
    );

    testFramework.assert(response.error === false, "Response should not have error");
    testFramework.assert(response.pagination.page === 2, "Page should be 2");
    testFramework.assert(response.pagination.limit === 5, "Limit should be 5");
    testFramework.assert(response.data.length <= 5, "Should return at most 5 items");
  });

  testFramework.addTestCase("Should return 401 for unauthorized access", async () => {
    try {
      await testFramework.makeRequest(
        `${BASE_URL}/v2/api/kanglink/custom/athlete/enrollments/with-comments`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          query: {
            page: 1,
            limit: 10,
          },
        }
      );
      
      testFramework.assert(false, "Should have thrown an error");
    } catch (error) {
      testFramework.assert(error.status === 401, "Should return 401 status");
    }
  });

  testFramework.addTestCase("Should only return enrollments for the authenticated athlete", async () => {
    const response = await testFramework.makeRequest(
      `${BASE_URL}/v2/api/kanglink/custom/athlete/enrollments/with-comments`,
      {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${TEST_ATHLETE_TOKEN}`,
          "Content-Type": "application/json",
        },
        query: {
          page: 1,
          limit: 50,
        },
      }
    );

    testFramework.assert(response.error === false, "Response should not have error");
    
    // All enrollments should belong to the authenticated athlete
    for (const enrollment of response.data) {
      testFramework.assert(enrollment.athlete_id === "athlete_id_from_token", "All enrollments should belong to authenticated athlete");
    }
  });
});

// Run the tests
async function runTests() {
  try {
    await testFramework.runTests();
    const report = testFramework.generateTestReport();
    console.log(report);
  } catch (error) {
    console.error("Test execution failed:", error);
  }
}

// Export for use in other test files
module.exports = {
  testFramework,
  runTests,
};

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
} 