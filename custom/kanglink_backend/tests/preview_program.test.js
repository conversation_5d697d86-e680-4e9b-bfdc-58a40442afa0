const ApiTestingBase = require("../../tests/apitesting.base.js");

class PreviewProgramTest extends ApiTestingBase {
  constructor() {
    super();
    this.baseUrl = "/v2/api/kanglink/custom/preview/program";
  }

  async runTests() {
    console.log("🧪 Running Preview Program Tests...");

    await this.testGetProgramPreview();
    await this.testGetProgramPreviewInvalidId();
    await this.testGetProgramPreviewNotFound();
    await this.testGetProgramPreviewUnauthorized();

    console.log("✅ Preview Program Tests Completed!");
  }

  async testGetProgramPreview() {
    console.log("  📋 Testing: Get Program Preview - Valid Request");

    try {
      // First, we need to get a valid split ID from the database
      const sdk = this.getSDK();
      sdk.setProjectId("kanglink");

      // Get a sample split
      const splits = await sdk.rawQuery(`
        SELECT s.id, s.title, p.program_name 
        FROM kanglink_split s 
        JOIN kanglink_program p ON s.program_id = p.id 
        LIMIT 1
      `);

      if (!splits || splits.length === 0) {
        console.log("    ⚠️  No splits found in database, skipping test");
        return;
      }

      const splitId = splits[0].id;
      const response = await this.makeRequest("GET", `/${splitId}`, null, {
        headers: this.getAuthHeaders("member"),
      });

      this.assertResponse(response, {
        statusCode: 200,
        hasError: false,
        hasData: true,
        message: "Program preview retrieved successfully",
      });

      // Validate response structure
      const { data } = response.body;
      this.assertExists(data.split, "Split data should exist");
      this.assertExists(data.split.program, "Program data should exist");
      this.assertExists(data.split.weeks, "Weeks array should exist");
      this.assertIsArray(data.split.weeks, "Weeks should be an array");

      // Validate split data
      this.assertExists(data.split.id, "Split ID should exist");
      this.assertExists(data.split.title, "Split title should exist");
      this.assertExists(data.split.program.name, "Program name should exist");
      this.assertExists(data.split.program.trainer, "Trainer data should exist");

      console.log("    ✅ Get Program Preview - Valid Request: PASSED");
    } catch (error) {
      console.log(`    ❌ Get Program Preview - Valid Request: FAILED - ${error.message}`);
    }
  }

  async testGetProgramPreviewInvalidId() {
    console.log("  📋 Testing: Get Program Preview - Invalid ID");

    try {
      const response = await this.makeRequest("GET", "/invalid", null, {
        headers: this.getAuthHeaders("member"),
      });

      this.assertResponse(response, {
        statusCode: 400,
        hasError: true,
        message: "Invalid split ID provided",
      });

      console.log("    ✅ Get Program Preview - Invalid ID: PASSED");
    } catch (error) {
      console.log(`    ❌ Get Program Preview - Invalid ID: FAILED - ${error.message}`);
    }
  }

  async testGetProgramPreviewNotFound() {
    console.log("  📋 Testing: Get Program Preview - Not Found");

    try {
      const response = await this.makeRequest("GET", "/999999", null, {
        headers: this.getAuthHeaders("member"),
      });

      this.assertResponse(response, {
        statusCode: 404,
        hasError: true,
        message: "Split not found",
      });

      console.log("    ✅ Get Program Preview - Not Found: PASSED");
    } catch (error) {
      console.log(`    ❌ Get Program Preview - Not Found: FAILED - ${error.message}`);
    }
  }

  async testGetProgramPreviewUnauthorized() {
    console.log("  📋 Testing: Get Program Preview - Unauthorized");

    try {
      const response = await this.makeRequest("GET", "/1", null, {
        headers: {}, // No auth headers
      });

      this.assertResponse(response, {
        statusCode: 401,
        hasError: true,
      });

      console.log("    ✅ Get Program Preview - Unauthorized: PASSED");
    } catch (error) {
      console.log(`    ❌ Get Program Preview - Unauthorized: FAILED - ${error.message}`);
    }
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const test = new PreviewProgramTest();
  test.runTests().catch(console.error);
}

module.exports = PreviewProgramTest; 