const APITestFramework = require("../../../tests/apitesting.base.js");

class SubscriptionAuthenticationHandlingTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Subscription Authentication Handling Tests", () => {
      // Test successful subscription creation
      this.framework.addTestCase("Should handle successful subscription creation", async () => {
        const StripeService = require("../../../baas/services/StripeService");
        
        // Mock successful subscription
        const mockStripe = {
          subscriptions: {
            create: async (params) => {
              // Verify new parameters are passed through
              this.framework.assert(
                params.payment_behavior === "default_incomplete",
                "Should use default_incomplete payment behavior"
              );
              this.framework.assert(
                params.expand && params.expand.includes("latest_invoice.payment_intent"),
                "Should expand latest_invoice.payment_intent"
              );
              
              return {
                id: "sub_test_123",
                status: "active",
                latest_invoice: {
                  payment_intent: {
                    id: "pi_test_123",
                    status: "succeeded"
                  }
                }
              };
            }
          }
        };

        const stripeService = new StripeService();
        stripeService.stripe = mockStripe;

        const subscriptionPayload = {
          customerId: "cus_test123",
          priceId: "price_test123",
          default_payment_method: "pm_test123",
          payment_behavior: "default_incomplete",
          expand: ["latest_invoice.payment_intent"],
          metadata: {
            projectId: "kanglink",
            split_id: "1",
            athlete_id: "1"
          }
        };

        const result = await stripeService.createStripeSubscription(subscriptionPayload);

        this.framework.assertions.assertEquals(
          result.status,
          "active",
          "Should return active subscription"
        );
      });

      // Test subscription requiring authentication
      this.framework.addTestCase("Should handle subscription requiring authentication", async () => {
        // Mock subscription that requires authentication
        const mockSubscription = {
          id: "sub_test_456",
          status: "incomplete",
          latest_invoice: {
            payment_intent: {
              id: "pi_test_456",
              status: "requires_action",
              client_secret: "pi_test_456_secret_xyz"
            }
          }
        };

        // Simulate enrollment endpoint logic
        const requiresAuthentication = 
          mockSubscription.status === "incomplete" && 
          mockSubscription.latest_invoice?.payment_intent?.status === "requires_action";

        this.framework.assert(
          requiresAuthentication,
          "Should detect when subscription requires authentication"
        );

        // Verify response structure for authentication required
        if (requiresAuthentication) {
          const authResponse = {
            error: true,
            message: "Payment requires additional authentication",
            requires_action: true,
            payment_intent: {
              id: mockSubscription.latest_invoice.payment_intent.id,
              client_secret: mockSubscription.latest_invoice.payment_intent.client_secret,
            },
            subscription_id: mockSubscription.id,
          };

          this.framework.assert(
            authResponse.requires_action === true,
            "Should indicate authentication is required"
          );
          this.framework.assert(
            authResponse.payment_intent.client_secret,
            "Should provide client secret for authentication"
          );
        }
      });

      // Test backward compatibility
      this.framework.addTestCase("Should maintain backward compatibility for existing subscription calls", async () => {
        const StripeService = require("../../../baas/services/StripeService");
        
        // Mock the Stripe library
        let capturedParams = null;
        const mockStripe = {
          subscriptions: {
            create: async (params) => {
              capturedParams = params;
              return {
                id: "sub_test_789",
                status: "active"
              };
            }
          }
        };

        const stripeService = new StripeService();
        stripeService.stripe = mockStripe;

        // Test with old-style parameters (like existing endpoints)
        const legacyPayload = {
          customerId: "cus_legacy",
          priceId: "price_legacy",
          default_payment_method: "pm_legacy",
          trial_from_plan: true,
          metadata: { projectId: "legacy" }
        };

        await stripeService.createStripeSubscription(legacyPayload);

        // Verify backward compatibility
        this.framework.assertions.assertEquals(
          capturedParams.payment_behavior,
          "error_if_incomplete",
          "Should default to error_if_incomplete for backward compatibility"
        );
        this.framework.assert(
          !capturedParams.expand,
          "Should not include expand when not specified"
        );
      });

      // Test comparison between payment types
      this.framework.addTestCase("Should understand differences between payment types", async () => {
        // One-time payment configuration
        const oneTimeConfig = {
          payment_method: "pm_test",
          confirm: true,
          automatic_payment_methods: {
            enabled: true,
            allow_redirects: "never"
          }
        };

        // Subscription configuration  
        const subscriptionConfig = {
          payment_behavior: "default_incomplete",
          expand: ["latest_invoice.payment_intent"]
        };

        // Verify different approaches
        this.framework.assert(
          oneTimeConfig.automatic_payment_methods.allow_redirects === "never",
          "One-time payments should disable redirects"
        );
        this.framework.assert(
          subscriptionConfig.payment_behavior === "default_incomplete",
          "Subscriptions should allow incomplete state for authentication"
        );
        this.framework.assert(
          oneTimeConfig.confirm === true,
          "One-time payments should confirm immediately"
        );
        this.framework.assert(
          subscriptionConfig.expand.includes("latest_invoice.payment_intent"),
          "Subscriptions should expand payment intent for status checking"
        );
      });
    });
  }

  async runTests() {
    try {
      const results = await this.framework.runTests();
      this.framework.generateTestReport();
      return results;
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new SubscriptionAuthenticationHandlingTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report;
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
