const BaseTest = require("../../../tests/apitesting.base");

class AthleteEndpointsFixedTest extends BaseTest {
  constructor() {
    super();
    this.testSuiteName = "Athlete Endpoints Fixed Tests";
  }

  async testAthleteLibraryEndpoint() {
    try {
      // Test the athlete library endpoint
      const response = await this.makeRequest({
        method: "GET",
        endpoint: "/v2/api/kanglink/custom/athlete/library",
        headers: {
          Authorization: `Bearer ${this.getValidToken()}`,
        },
      });

      console.log("Library Response:", JSON.stringify(response, null, 2));

      // Check response structure
      this.assertEqual(response.error, false, "Should not have error");
      this.assertTrue(
        response.data && typeof response.data === "object",
        "Should have data object"
      );

      // Check categorized structure
      const expectedCategories = ["owned", "subscribed", "pending_refund", "refunded"];
      expectedCategories.forEach(category => {
        this.assertTrue(
          Array.isArray(response.data[category]),
          `Should have ${category} array`
        );
      });

      console.log("✓ Athlete library endpoint working correctly");
    } catch (error) {
      console.error("Library endpoint error:", error);
      throw error;
    }
  }

  async testFavoriteProgramsEndpoint() {
    try {
      // Test the favorite programs endpoint
      const response = await this.makeRequest({
        method: "GET",
        endpoint: "/v2/api/kanglink/custom/athlete/favorite/programs",
        headers: {
          Authorization: `Bearer ${this.getValidToken()}`,
        },
      });

      console.log("Favorite Programs Response:", JSON.stringify(response, null, 2));

      // Check response structure
      this.assertEqual(response.error, false, "Should not have error");
      this.assertTrue(
        Array.isArray(response.data),
        "Should have data array"
      );

      // If there are favorite programs, check their structure
      if (response.data.length > 0) {
        const program = response.data[0];
        const requiredFields = [
          "favorite_id", "favorited_at", "id", "name", "description",
          "type", "image_url", "status", "created_at", "updated_at",
          "price", "average_rating", "review_count", "trainer"
        ];

        requiredFields.forEach(field => {
          this.assertTrue(
            program.hasOwnProperty(field),
            `Program should have ${field} field`
          );
        });

        // Check trainer object structure
        if (program.trainer) {
          const trainerFields = ["id", "full_name", "first_name", "last_name", "photo"];
          trainerFields.forEach(field => {
            this.assertTrue(
              program.trainer.hasOwnProperty(field),
              `Trainer should have ${field} field`
            );
          });
        }
      }

      console.log("✓ Favorite programs endpoint working correctly");
    } catch (error) {
      console.error("Favorite programs endpoint error:", error);
      throw error;
    }
  }

  async testFavoriteTrainersEndpoint() {
    try {
      // Test the favorite trainers endpoint
      const response = await this.makeRequest({
        method: "GET",
        endpoint: "/v2/api/kanglink/custom/athlete/favorite/trainers",
        headers: {
          Authorization: `Bearer ${this.getValidToken()}`,
        },
      });

      console.log("Favorite Trainers Response:", JSON.stringify(response, null, 2));

      // Check response structure
      this.assertEqual(response.error, false, "Should not have error");
      this.assertTrue(
        Array.isArray(response.data),
        "Should have data array"
      );

      console.log("✓ Favorite trainers endpoint working correctly");
    } catch (error) {
      console.error("Favorite trainers endpoint error:", error);
      throw error;
    }
  }

  async testSplitModelFields() {
    try {
      // Test that split model only has existing fields
      const splitModel = require('../models/split');
      const schema = splitModel.schema();
      const fieldNames = schema.map(field => field.name);

      console.log("Split model fields:", fieldNames);

      // Check that non-existent fields are not in the model
      const nonExistentFields = ['description', 'duration_weeks', 'status'];
      nonExistentFields.forEach(field => {
        this.assertFalse(
          fieldNames.includes(field),
          `Split model should not have ${field} field`
        );
      });

      // Check that existing fields are present
      const existingFields = ['id', 'program_id', 'equipment_required', 'title', 'full_price', 'subscription'];
      existingFields.forEach(field => {
        this.assertTrue(
          fieldNames.includes(field),
          `Split model should have ${field} field`
        );
      });

      console.log("✓ Split model fields are correct");
    } catch (error) {
      console.error("Split model test error:", error);
      throw error;
    }
  }

  getValidToken() {
    // Return a test token - in real tests this would be obtained from login
    return "test_token_here";
  }
}

module.exports = AthleteEndpointsFixedTest;
