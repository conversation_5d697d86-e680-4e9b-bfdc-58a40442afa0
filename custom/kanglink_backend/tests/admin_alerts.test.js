const { test, expect, describe, beforeAll, afterAll } = require("../../tests/apitesting.base.js");

describe("Admin Alerts Endpoints", () => {
  let superAdminToken;
  let superAdminId;
  let testAlertId;

  beforeAll(async () => {
    // Create test super admin user and get token
    const superAdminResponse = await test("POST", "/v2/api/kanglink/custom/auth/register", {
      email: "<EMAIL>",
      password: "TestPassword123!",
      role: "super_admin",
      data: {
        first_name: "Test",
        last_name: "Admin",
        full_name: "Test Admin",
      },
    });
    superAdminToken = superAdminResponse.data.token;
    superAdminId = superAdminResponse.data.user_id;
  });

  afterAll(async () => {
    // Clean up test data
    // This would typically be handled by the test framework
  });

  describe("GET /v2/api/kanglink/custom/admin/alerts - Get admin alerts", () => {
    test("Should get admin alerts successfully", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/admin/alerts", null, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.message).toBe("Admin alerts retrieved successfully");
      expect(response.data).toBeDefined();
      expect(response.data.alerts).toBeDefined();
      expect(Array.isArray(response.data.alerts)).toBe(true);
      expect(response.data.pagination).toBeDefined();
      expect(response.data.pagination.page).toBeDefined();
      expect(response.data.pagination.limit).toBeDefined();
      expect(response.data.pagination.total).toBeDefined();
    });

    test("Should get admin alerts with pagination", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/admin/alerts?page=1&limit=10", null, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.data.pagination.page).toBe(1);
      expect(response.data.pagination.limit).toBe(10);
    });

    test("Should get admin alerts with activity type filter", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/admin/alerts?activity_type=system_alert", null, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.data.alerts).toBeDefined();
      // All alerts should be of the specified activity type
      response.data.alerts.forEach(alert => {
        expect(alert.activity_type).toBe("system_alert");
      });
    });

    test("Should get admin alerts with date range filter", async () => {
      const dateFrom = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 7 days ago
      const dateTo = new Date().toISOString().split('T')[0]; // today
      
      const response = await test("GET", `/v2/api/kanglink/custom/admin/alerts?date_from=${dateFrom}&date_to=${dateTo}`, null, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.data.alerts).toBeDefined();
    });

    test("Should return 401 for unauthorized access", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/admin/alerts");

      expect(response.error).toBe(true);
      expect(response.message).toContain("Unauthorized");
    });

    test("Should return 403 for non-admin users", async () => {
      // Create a regular member user
      const memberResponse = await test("POST", "/v2/api/kanglink/custom/auth/register", {
        email: "<EMAIL>",
        password: "TestPassword123!",
        role: "member",
        data: {
          first_name: "Test",
          last_name: "Member",
          full_name: "Test Member",
        },
      });
      const memberToken = memberResponse.data.token;

      const response = await test("GET", "/v2/api/kanglink/custom/admin/alerts", null, {
        headers: { Authorization: `Bearer ${memberToken}` },
      });

      expect(response.error).toBe(true);
      expect(response.message).toContain("Forbidden");
    });
  });

  describe("GET /v2/api/kanglink/custom/admin/alerts/stats - Get admin alert statistics", () => {
    test("Should get admin alert statistics successfully", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/admin/alerts/stats", null, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.message).toBe("Admin alert statistics retrieved successfully");
      expect(response.data).toBeDefined();
      expect(response.data.alert_type_stats).toBeDefined();
      expect(Array.isArray(response.data.alert_type_stats)).toBe(true);
      expect(response.data.recent_alerts_count).toBeDefined();
      expect(typeof response.data.recent_alerts_count).toBe("number");
      expect(response.data.unread_alerts_count).toBeDefined();
      expect(typeof response.data.unread_alerts_count).toBe("number");
    });

    test("Should return 401 for unauthorized access", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/admin/alerts/stats");

      expect(response.error).toBe(true);
      expect(response.message).toContain("Unauthorized");
    });

    test("Should return 403 for non-admin users", async () => {
      // Create a trainer user
      const trainerResponse = await test("POST", "/v2/api/kanglink/custom/auth/register", {
        email: "<EMAIL>",
        password: "TestPassword123!",
        role: "trainer",
        data: {
          first_name: "Test",
          last_name: "Trainer",
          full_name: "Test Trainer",
        },
      });
      const trainerToken = trainerResponse.data.token;

      const response = await test("GET", "/v2/api/kanglink/custom/admin/alerts/stats", null, {
        headers: { Authorization: `Bearer ${trainerToken}` },
      });

      expect(response.error).toBe(true);
      expect(response.message).toContain("Forbidden");
    });
  });

  describe("POST /v2/api/kanglink/custom/admin/alerts/system - Create system alert", () => {
    test("Should create system alert successfully", async () => {
      const alertData = {
        title: "Test System Alert",
        description: "This is a test system alert for admin dashboard",
        metadata: {
          priority: "medium",
          category: "test",
          source: "admin_test"
        }
      };

      const response = await test("POST", "/v2/api/kanglink/custom/admin/alerts/system", alertData, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.message).toBe("System alert created successfully");
    });

    test("Should create system alert without metadata", async () => {
      const alertData = {
        title: "Simple System Alert",
        description: "This is a simple system alert without metadata"
      };

      const response = await test("POST", "/v2/api/kanglink/custom/admin/alerts/system", alertData, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.message).toBe("System alert created successfully");
    });

    test("Should return 400 for missing title", async () => {
      const alertData = {
        description: "This alert is missing a title"
      };

      const response = await test("POST", "/v2/api/kanglink/custom/admin/alerts/system", alertData, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(true);
      expect(response.message).toBe("Title and description are required");
    });

    test("Should return 400 for missing description", async () => {
      const alertData = {
        title: "Alert without description"
      };

      const response = await test("POST", "/v2/api/kanglink/custom/admin/alerts/system", alertData, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(true);
      expect(response.message).toBe("Title and description are required");
    });

    test("Should return 401 for unauthorized access", async () => {
      const alertData = {
        title: "Unauthorized Alert",
        description: "This should fail"
      };

      const response = await test("POST", "/v2/api/kanglink/custom/admin/alerts/system", alertData);

      expect(response.error).toBe(true);
      expect(response.message).toContain("Unauthorized");
    });

    test("Should return 403 for non-admin users", async () => {
      // Create a trainer user
      const trainerResponse = await test("POST", "/v2/api/kanglink/custom/auth/register", {
        email: "<EMAIL>",
        password: "TestPassword123!",
        role: "trainer",
        data: {
          first_name: "Test",
          last_name: "Trainer2",
          full_name: "Test Trainer2",
        },
      });
      const trainerToken = trainerResponse.data.token;

      const alertData = {
        title: "Trainer Alert",
        description: "This should fail for trainer"
      };

      const response = await test("POST", "/v2/api/kanglink/custom/admin/alerts/system", alertData, {
        headers: { Authorization: `Bearer ${trainerToken}` },
      });

      expect(response.error).toBe(true);
      expect(response.message).toContain("Forbidden");
    });
  });

  describe("Admin Alert Service Integration", () => {
    test("Should create program approval alert", async () => {
      // This test would verify the service method works correctly
      // In a real implementation, you might mock the SDK calls
      expect(true).toBe(true); // Placeholder for service integration test
    });

    test("Should create new athlete signup alert", async () => {
      // This test would verify the service method works correctly
      expect(true).toBe(true); // Placeholder for service integration test
    });

    test("Should create new trainer signup alert", async () => {
      // This test would verify the service method works correctly
      expect(true).toBe(true); // Placeholder for service integration test
    });

    test("Should create new transaction alert", async () => {
      // This test would verify the service method works correctly
      expect(true).toBe(true); // Placeholder for service integration test
    });

    test("Should create refund request alert", async () => {
      // This test would verify the service method works correctly
      expect(true).toBe(true); // Placeholder for service integration test
    });

    test("Should create refund decision alert", async () => {
      // This test would verify the service method works correctly
      expect(true).toBe(true); // Placeholder for service integration test
    });

    test("Should create low rated trainer alert", async () => {
      // This test would verify the service method works correctly
      expect(true).toBe(true); // Placeholder for service integration test
    });
  });

  describe("Error Handling", () => {
    test("Should handle database errors gracefully", async () => {
      // This test would verify error handling when database operations fail
      expect(true).toBe(true); // Placeholder for error handling test
    });

    test("Should handle invalid activity types", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/admin/alerts?activity_type=invalid_type", null, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.data.alerts).toBeDefined();
      expect(response.data.alerts.length).toBe(0); // Should return empty array for invalid type
    });

    test("Should handle invalid date formats", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/admin/alerts?date_from=invalid_date", null, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(false);
      // Should handle invalid date gracefully
    });
  });
}); 