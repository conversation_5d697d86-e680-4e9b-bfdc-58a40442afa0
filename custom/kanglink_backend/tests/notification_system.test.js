const { describe, test, expect, beforeAll, afterAll } = require("../../tests/apitesting.base.js");

describe("Notification System Tests", () => {
  let testData = {};

  beforeAll(async () => {
    // Setup test data
    testData = {
      athlete: {
        id: 1,
        full_name: "Test Athlete",
        email: "<EMAIL>",
      },
      trainer: {
        id: 2,
        full_name: "<PERSON> Trainer",
        email: "<EMAIL>",
      },
      admin: {
        id: 3,
        full_name: "Test Admin",
        email: "<EMAIL>",
      },
      enrollment: {
        id: 1,
        athlete_id: 1,
        trainer_id: 2,
        program_name: "Test Program",
        split_name: "Test Split",
        payment_amount: 99.99,
        payment_currency: "USD",
        payment_type: "stripe",
      },
      refund: {
        id: 1,
        athlete_id: 1,
        trainer_id: 2,
        amount: 99.99,
        currency: "USD",
        reason: "Not satisfied with program",
        status: "pending",
        program_name: "Test Program",
      },
      post: {
        id: 1,
        trainer_id: 2,
        content: "Great workout today! Keep pushing your limits.",
        type: "text",
        media_urls: [],
      },
    };
  });

  afterAll(async () => {
    // Cleanup test data
    // This would typically clean up any test data created
  });

  describe("Notification Service Tests", () => {
    test("should create enrollment notification", async () => {
      const response = await global.testRequest
        .post("/v2/api/kanglink/custom/test/notifications/enrollment")
        .send({
          enrollmentData: testData.enrollment,
          athleteData: testData.athlete,
          trainerData: testData.trainer,
        })
        .expect(200);

      expect(response.body.error).toBe(false);
      expect(response.body.message).toBe("Enrollment notification created successfully");
    });

    test("should create payment notification", async () => {
      const paymentData = {
        id: 1,
        amount: 99.99,
        currency: "USD",
        payment_method: "stripe",
      };

      const response = await global.testRequest
        .post("/v2/api/kanglink/custom/test/notifications/payment")
        .send({
          paymentData,
          enrollmentData: testData.enrollment,
          athleteData: testData.athlete,
          trainerData: testData.trainer,
        })
        .expect(200);

      expect(response.body.error).toBe(false);
      expect(response.body.message).toBe("Payment notification created successfully");
    });

    test("should create refund request notification", async () => {
      const response = await global.testRequest
        .post("/v2/api/kanglink/custom/test/notifications/refund")
        .send({
          refundData: testData.refund,
          athleteData: testData.athlete,
          trainerData: testData.trainer,
          notificationType: "refund_requested",
        })
        .expect(200);

      expect(response.body.error).toBe(false);
      expect(response.body.message).toBe("Refund notification created successfully");
    });

    test("should create post feed notification", async () => {
      const enrolledAthletes = [
        { athlete_id: 1 },
        { athlete_id: 4 },
      ];

      const response = await global.testRequest
        .post("/v2/api/kanglink/custom/test/notifications/post-feed")
        .send({
          postData: testData.post,
          trainerData: testData.trainer,
          enrolledAthletes,
          notificationType: "post_feed_created",
        })
        .expect(200);

      expect(response.body.error).toBe(false);
      expect(response.body.message).toBe("Post feed notification created successfully");
    });

    test("should create system alert notification", async () => {
      const alertData = {
        id: 1,
        title: "System Maintenance",
        message: "Scheduled maintenance on Sunday",
        type: "maintenance",
        severity: "info",
        details: "System will be down for 2 hours",
      };

      const adminIds = [testData.admin.id];

      const response = await global.testRequest
        .post("/v2/api/kanglink/custom/test/notifications/system-alert")
        .send({
          alertData,
          adminIds,
        })
        .expect(200);

      expect(response.body.error).toBe(false);
      expect(response.body.message).toBe("System alert notification created successfully");
    });
  });

  describe("Notification API Endpoints", () => {
    test("should get athlete notifications", async () => {
      const response = await global.testRequest
        .get("/v2/api/kanglink/custom/athlete/notifications")
        .set("Authorization", `Bearer ${global.testTokens.athlete}`)
        .expect(200);

      expect(response.body.error).toBe(false);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data.notifications)).toBe(true);
    });

    test("should get unread notification count", async () => {
      const response = await global.testRequest
        .get("/v2/api/kanglink/custom/athlete/notifications/unread-count")
        .set("Authorization", `Bearer ${global.testTokens.athlete}`)
        .expect(200);

      expect(response.body.error).toBe(false);
      expect(response.body.data).toBeDefined();
      expect(typeof response.body.data.count).toBe("number");
    });

    test("should mark notification as read", async () => {
      // First get notifications to get an ID
      const notificationsResponse = await global.testRequest
        .get("/v2/api/kanglink/custom/athlete/notifications")
        .set("Authorization", `Bearer ${global.testTokens.athlete}`)
        .expect(200);

      const notifications = notificationsResponse.body.data.notifications;
      if (notifications.length > 0) {
        const notificationId = notifications[0].id;

        const response = await global.testRequest
          .put(`/v2/api/kanglink/custom/athlete/notifications/${notificationId}/read`)
          .set("Authorization", `Bearer ${global.testTokens.athlete}`)
          .expect(200);

        expect(response.body.error).toBe(false);
        expect(response.body.message).toBe("Notification marked as read");
      }
    });

    test("should mark all notifications as read", async () => {
      const response = await global.testRequest
        .put("/v2/api/kanglink/custom/athlete/notifications/read-all")
        .set("Authorization", `Bearer ${global.testTokens.athlete}`)
        .expect(200);

      expect(response.body.error).toBe(false);
      expect(response.body.message).toBe("All notifications marked as read");
    });
  });

  describe("Notification Types Validation", () => {
    test("should validate notification types in database", async () => {
      const response = await global.testRequest
        .get("/v2/api/kanglink/custom/test/notifications/validate-types")
        .expect(200);

      expect(response.body.error).toBe(false);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.validTypes).toBeDefined();
      expect(Array.isArray(response.body.data.validTypes)).toBe(true);
      
      // Check that all expected notification types are present
      const expectedTypes = [
        "exercise_completed",
        "day_completed", 
        "week_completed",
        "program_completed",
        "milestone_reached",
        "new_enrollment",
        "payment_received",
        "program_updated",
        "athlete_message",
        "system_alert",
        "refund_requested",
        "refund_approved",
        "refund_rejected",
        "post_feed_created",
        "post_feed_comment"
      ];

      expectedTypes.forEach(type => {
        expect(response.body.data.validTypes).toContain(type);
      });
    });
  });

  describe("Notification Integration Tests", () => {
    test("should create notification when athlete enrolls in program", async () => {
      // This would test the full enrollment flow
      const enrollmentData = {
        athlete_id: testData.athlete.id,
        trainer_id: testData.trainer.id,
        program_id: 1,
        program_name: "Test Program",
        split_name: "Test Split",
        payment_amount: 99.99,
        payment_currency: "USD",
        payment_type: "stripe",
      };

      const response = await global.testRequest
        .post("/v2/api/kanglink/custom/enrollment")
        .set("Authorization", `Bearer ${global.testTokens.athlete}`)
        .send(enrollmentData)
        .expect(200);

      expect(response.body.error).toBe(false);
      
      // Verify notification was created
      const notificationsResponse = await global.testRequest
        .get("/v2/api/kanglink/custom/athlete/notifications")
        .set("Authorization", `Bearer ${global.testTokens.athlete}`)
        .expect(200);

      const notifications = notificationsResponse.body.data.notifications;
      const enrollmentNotification = notifications.find(n => n.notification_type === "new_enrollment");
      expect(enrollmentNotification).toBeDefined();
    });

    test("should create notification when refund is requested", async () => {
      const refundData = {
        athlete_id: testData.athlete.id,
        trainer_id: testData.trainer.id,
        enrollment_id: 1,
        amount: 99.99,
        currency: "USD",
        reason: "Not satisfied with program",
        program_name: "Test Program",
      };

      const response = await global.testRequest
        .post("/v2/api/kanglink/custom/refund-request")
        .set("Authorization", `Bearer ${global.testTokens.athlete}`)
        .send(refundData)
        .expect(200);

      expect(response.body.error).toBe(false);
      
      // Verify notification was created
      const notificationsResponse = await global.testRequest
        .get("/v2/api/kanglink/custom/athlete/notifications")
        .set("Authorization", `Bearer ${global.testTokens.athlete}`)
        .expect(200);

      const notifications = notificationsResponse.body.data.notifications;
      const refundNotification = notifications.find(n => n.notification_type === "refund_requested");
      expect(refundNotification).toBeDefined();
    });
  });
}); 