const { test, expect } = require("../../tests/apitesting.base.js");

test("Trainer activities endpoint test", async ({ sdk, testData }) => {
  // Test getting trainer activities
  const response = await sdk.get("/v2/api/kanglink/custom/trainer/activities");

  expect(response.error).toBe(false);
  expect(response.data).toBeDefined();
  expect(response.data.activities).toBeDefined();
  expect(Array.isArray(response.data.activities)).toBe(true);
  expect(response.data.pagination).toBeDefined();

  console.log("Trainer activities test passed successfully!");
  console.log("Activities found:", response.data.activities.length);
  console.log("Total activities:", response.data.pagination.total);
});

test("Trainer activities with visibility filter test", async ({ sdk, testData }) => {
  // Test getting trainer activities with trainer_only visibility
  const response = await sdk.get("/v2/api/kanglink/custom/trainer/activities?visibility=trainer_only");

  expect(response.error).toBe(false);
  expect(response.data).toBeDefined();
  expect(response.data.activities).toBeDefined();
  expect(Array.isArray(response.data.activities)).toBe(true);

  // Verify all activities have trainer_only visibility
  response.data.activities.forEach(activity => {
    expect(activity.visibility).toBe("trainer_only");
  });

  console.log("Trainer activities with visibility filter test passed successfully!");
  console.log("Activities found:", response.data.activities.length);
}); 