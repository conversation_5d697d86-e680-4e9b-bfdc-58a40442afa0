const { test, expect } = require('@playwright/test');

test.describe('Profile Completion Flow', () => {
  test('OAuth user should be redirected to profile completion if profile_update is false', async ({ page }) => {
    // Mock OAuth callback with profile_update: false
    await page.goto('/login/oauth?data=' + encodeURIComponent(JSON.stringify({
      error: false,
      role: 'member',
      token: 'mock_token',
      user_id: 1,
      profile_update: false
    })));

    // Should be redirected to profile completion page
    await expect(page).toHaveURL('/athlete/profile-completion');
  });

  test('OAuth user should be redirected to dashboard if profile_update is true', async ({ page }) => {
    // Mock OAuth callback with profile_update: true
    await page.goto('/login/oauth?data=' + encodeURIComponent(JSON.stringify({
      error: false,
      role: 'member',
      token: 'mock_token',
      user_id: 1,
      profile_update: true
    })));

    // Should be redirected to dashboard
    await expect(page).toHaveURL('/');
  });

  test('Trainer OAuth user should be redirected to trainer profile completion', async ({ page }) => {
    // Mock OAuth callback for trainer
    await page.goto('/login/oauth?data=' + encodeURIComponent(JSON.stringify({
      error: false,
      role: 'trainer',
      token: 'mock_token',
      user_id: 1,
      profile_update: false
    })));

    // Should be redirected to trainer profile completion page
    await expect(page).toHaveURL('/trainer/profile-completion');
  });
}); 