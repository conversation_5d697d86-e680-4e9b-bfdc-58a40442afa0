const axios = require("axios");

console.log("🧪 Debugging TreeQL Error...");

async function testTreeQLError() {
  try {
    const response = await axios.get(
      "http://127.0.0.1:5172/v1/api/records/kanglink/super_admin/enrollment",
      {
        params: {
          join: [
            "program",
            "split",
            "user|athlete_id|athlete",
            "user|trainer_id|trainer",
          ],
          filter: "athlete_id,eq,12",
          order: "id,desc",
          size: 10,
          page: 1,
        },
        headers: {
          authorization:
            "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJyb2xlIjoic3VwZXJfYWRtaW4iLCJpYXQiOjE3NTE1OTk4OTYsImV4cCI6MTc1MTYwMzQ5Nn0.Qg3Uh6Bq7DFNnM97ZV7DxFRKTUtsh-lp8k69gjC9XiY",
          "content-type": "application/json",
          "x-project":
            "a2FuZ2xpbms6M2MzdzB1MHNjdm1jdjM5cTBicm9kczFpdjExcTZuNWxm",
        },
      }
    );

    console.log("✅ Request successful:", response.data);
  } catch (error) {
    console.log("❌ Request failed:", error.response?.data || error.message);
    console.log("Status:", error.response?.status);

    // Let's also test the join parsing directly
    console.log("\n🔍 Testing join parsing...");
    const RequestUtils = require("../../../baas/core/RequestUtils");

    const mockReq = {
      query: {
        join: [
          "program",
          "split",
          "user|athlete_id|athlete",
          "user|trainer_id|trainer",
        ],
      },
    };

    const { joins, hasJoin } = RequestUtils.getRequestJoins(mockReq);
    console.log("hasJoin:", hasJoin);
    console.log("joins:", JSON.stringify(joins, null, 2));
  }
}

testTreeQLError();
