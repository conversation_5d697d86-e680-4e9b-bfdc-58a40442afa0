const testFramework = require("../../../tests/apitesting.base");

testFramework.describe("Rest Day Progress Counting", () => {
  let sdk;

  // Setup before each test
  testFramework.beforeEach(async () => {
    // Mock SDK for testing
    sdk = {
      setProjectId: () => {},
      rawQuery: async (query) => {
        // Mock enrollment data
        if (query.includes("FROM kanglink_enrollment")) {
          return [
            {
              id: 1,
              access_type: "live",
              split_snapshot: null,
            },
          ];
        }

        // Mock day progress data - 4 completed days (2 rest days, 2 workout days)
        if (query.includes("FROM kanglink_day_progress dp") && !query.includes("LEFT JOIN")) {
          return [
            { total_days_completed: 4 }, // Should count all 4 days including rest days
          ];
        }

        // Mock exercise progress data - only 2 exercises completed (from workout days)
        if (query.includes("FROM kanglink_exercise_progress ep")) {
          return [
            { total_exercises_completed: 2 }, // Only workout days have exercises
          ];
        }

        // Mock total days in split
        if (query.includes("total_days_in_split")) {
          return [
            { total_days_in_split: 14 }, // Total days in the program
          ];
        }

        // Mock total exercises in split
        if (query.includes("total_exercises_in_split")) {
          return [
            { total_exercises_in_split: 10 }, // Total exercises in the program
          ];
        }

        // Mock athlete progress record
        if (query.includes("FROM kanglink_athlete_progress")) {
          return [
            {
              id: 278,
              athlete_id: 3,
              enrollment_id: 1,
              current_week_id: 531,
              current_day_id: 5,
              total_days_completed: 3, // This should be updated to 4
              total_exercises_completed: 3, // This should be updated to 2
              progress_percentage: 28.57, // This should be updated to 28.57 (4/14 * 100)
              last_activity_date: "2024-01-15T11:00:00Z",
            },
          ];
        }

        return [];
      },
    };
  });

  testFramework.it("should count rest days in total_days_completed", async () => {
    // Mock the update query to capture the values
    let updateQuery = "";
    sdk.rawQuery = async (query) => {
      if (query.includes("UPDATE kanglink_athlete_progress")) {
        updateQuery = query;
        return [{ affectedRows: 1 }];
      }
      
      // Return the same mock data as before
      if (query.includes("FROM kanglink_enrollment")) {
        return [{ id: 1, access_type: "live", split_snapshot: null }];
      }
      if (query.includes("FROM kanglink_day_progress dp") && !query.includes("LEFT JOIN")) {
        return [{ total_days_completed: 4 }];
      }
      if (query.includes("FROM kanglink_exercise_progress ep")) {
        return [{ total_exercises_completed: 2 }];
      }
      if (query.includes("total_days_in_split")) {
        return [{ total_days_in_split: 14 }];
      }
      if (query.includes("total_exercises_in_split")) {
        return [{ total_exercises_in_split: 10 }];
      }
      if (query.includes("FROM kanglink_athlete_progress")) {
        return [{
          id: 278,
          athlete_id: 3,
          enrollment_id: 1,
          current_week_id: 531,
          current_day_id: 5,
          total_days_completed: 3,
          total_exercises_completed: 3,
          progress_percentage: 28.57,
          last_activity_date: "2024-01-15T11:00:00Z",
        }];
      }
      return [];
    };

    // Import the updateAthleteProgress function
    const athleteProgressRoutes = require("../routes/athlete_progress");
    
    // Mock the app object
    const app = {
      get: () => sdk,
      post: () => {},
    };

    // Call the routes to load the functions
    await athleteProgressRoutes(app);
    
    // Find the updateAthleteProgress function and call it
    // Since it's a helper function, we need to call it through the day completion endpoint
    const req = {
      body: {
        enrollment_id: 1,
        day_id: 2, // Rest day
        notes: "Rest day completed"
      },
      user_id: 3,
      app: {
        get: () => sdk,
      },
    };

    const res = {
      status: (code) => ({
        json: (data) => {
          testFramework.expect(code).toBe(200);
          testFramework.expect(data.error).toBe(false);
          
          // Check that the update query was called with correct values
          testFramework.expect(updateQuery).toContain("total_days_completed = 4");
          testFramework.expect(updateQuery).toContain("total_exercises_completed = 2");
          testFramework.expect(updateQuery).toContain("progress_percentage = 28.57");
        },
      }),
    };

    // Find the day completion endpoint and call it
    const dayCompleteEndpoint = app.post.mock.calls.find(call => 
      call[0].includes("/v2/api/kanglink/custom/athlete/day/complete")
    );
    
    if (dayCompleteEndpoint) {
      await dayCompleteEndpoint[1](req, res);
    }
  });

  testFramework.it("should calculate correct progress percentage with rest days", async () => {
    // Test the progress calculation logic
    const totalDaysCompleted = 4; // 2 rest days + 2 workout days
    const totalDaysInSplit = 14;
    const expectedPercentage = ((totalDaysCompleted / totalDaysInSplit) * 100).toFixed(2);
    
    testFramework.expect(expectedPercentage).toBe("28.57");
    
    // Verify that rest days are included in the calculation
    testFramework.expect(totalDaysCompleted).toBe(4); // Should include rest days
  });
}); 