const { test, expect, describe, beforeAll, afterAll } = require("../../tests/apitesting.base.js");

describe("Notification Endpoints", () => {
  let memberToken, trainerToken, superAdminToken;
  let memberId, trainerId, superAdminId;
  let testNotificationId;

  beforeAll(async () => {
    // Create test users and get tokens
    const memberResponse = await test("POST", "/v2/api/kanglink/custom/auth/register", {
      email: "<EMAIL>",
      password: "TestPassword123!",
      role: "member",
      data: {
        first_name: "Test",
        last_name: "Member",
        full_name: "Test Member",
      },
    });
    memberToken = memberResponse.data.token;
    memberId = memberResponse.data.user_id;

    const trainerResponse = await test("POST", "/v2/api/kanglink/custom/auth/register", {
      email: "<EMAIL>",
      password: "TestPassword123!",
      role: "trainer",
      data: {
        first_name: "Test",
        last_name: "Trainer",
        full_name: "Test Trainer",
      },
    });
    trainerToken = trainerResponse.data.token;
    trainerId = trainerResponse.data.user_id;

    const superAdminResponse = await test("POST", "/v2/api/kanglink/custom/auth/register", {
      email: "<EMAIL>",
      password: "TestPassword123!",
      role: "super_admin",
      data: {
        first_name: "Test",
        last_name: "Admin",
        full_name: "Test Admin",
      },
    });
    superAdminToken = superAdminResponse.data.token;
    superAdminId = superAdminResponse.data.user_id;
  });

  afterAll(async () => {
    // Clean up test data
    // This would typically be handled by the test framework
  });

  describe("Common Notification Endpoints", () => {
    test("GET /v2/api/kanglink/custom/notifications - Get user notifications", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/notifications", null, {
        headers: { Authorization: `Bearer ${memberToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.data).toBeDefined();
      expect(response.data.notifications).toBeDefined();
      expect(Array.isArray(response.data.notifications)).toBe(true);
    });

    test("GET /v2/api/kanglink/custom/notifications/unread-count - Get unread count", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/notifications/unread-count", null, {
        headers: { Authorization: `Bearer ${memberToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.data).toBeDefined();
      expect(response.data.unread_count).toBeDefined();
      expect(typeof response.data.unread_count).toBe("number");
    });

    test("PUT /v2/api/kanglink/custom/notifications/read-all - Mark all as read", async () => {
      const response = await test("PUT", "/v2/api/kanglink/custom/notifications/read-all", null, {
        headers: { Authorization: `Bearer ${memberToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.message).toBe("All notifications marked as read");
    });

    test("PUT /v2/api/kanglink/custom/notifications/:id/read - Mark specific notification as read", async () => {
      // First create a test notification
      const createResponse = await test("POST", "/v2/api/kanglink/custom/super_admin/notifications/system-alert", {
        title: "Test Alert",
        message: "This is a test system alert",
        type: "info",
        severity: "low",
      }, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(createResponse.error).toBe(false);

      // Get notifications to find the created one
      const getResponse = await test("GET", "/v2/api/kanglink/custom/notifications", null, {
        headers: { Authorization: `Bearer ${memberToken}` },
      });

      expect(getResponse.error).toBe(false);
      expect(getResponse.data.notifications.length).toBeGreaterThan(0);

      const notificationId = getResponse.data.notifications[0].id;

      // Mark as read
      const readResponse = await test("PUT", `/v2/api/kanglink/custom/notifications/${notificationId}/read`, null, {
        headers: { Authorization: `Bearer ${memberToken}` },
      });

      expect(readResponse.error).toBe(false);
      expect(readResponse.message).toBe("Notification marked as read");
    });
  });

  describe("Trainer Notification Endpoints", () => {
    test("GET /v2/api/kanglink/custom/trainer/notifications - Get trainer notifications", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/trainer/notifications", null, {
        headers: { Authorization: `Bearer ${trainerToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.data).toBeDefined();
      expect(response.data.notifications).toBeDefined();
      expect(Array.isArray(response.data.notifications)).toBe(true);
      expect(response.data.unread_count).toBeDefined();
    });

    test("GET /v2/api/kanglink/custom/trainer/notifications/unread-count - Get trainer unread count", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/trainer/notifications/unread-count", null, {
        headers: { Authorization: `Bearer ${trainerToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.data).toBeDefined();
      expect(response.data.unread_count).toBeDefined();
      expect(typeof response.data.unread_count).toBe("number");
    });

    test("PUT /v2/api/kanglink/custom/trainer/notifications/read-all - Mark all trainer notifications as read", async () => {
      const response = await test("PUT", "/v2/api/kanglink/custom/trainer/notifications/read-all", null, {
        headers: { Authorization: `Bearer ${trainerToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.message).toBe("All notifications marked as read");
    });
  });

  describe("Super Admin Notification Endpoints", () => {
    test("POST /v2/api/kanglink/custom/super_admin/notifications/system-alert - Create system alert", async () => {
      const alertData = {
        title: "System Maintenance",
        message: "Scheduled maintenance will occur tonight at 2 AM",
        type: "warning",
        severity: "medium",
        details: {
          maintenance_window: "2:00 AM - 4:00 AM",
          affected_services: ["database", "api"],
        },
      };

      const response = await test("POST", "/v2/api/kanglink/custom/super_admin/notifications/system-alert", alertData, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.message).toBe("System alert notification created successfully");
      expect(response.data).toBeDefined();
      expect(response.data.alert_id).toBeDefined();
      expect(response.data.recipients_count).toBeGreaterThan(0);
    });

    test("GET /v2/api/kanglink/custom/super_admin/notifications/system-alerts - Get system alerts", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/super_admin/notifications/system-alerts", null, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(false);
      expect(response.data).toBeDefined();
      expect(response.data.alerts).toBeDefined();
      expect(Array.isArray(response.data.alerts)).toBe(true);
      expect(response.data.pagination).toBeDefined();
    });

    test("PUT /v2/api/kanglink/custom/super_admin/notifications/system-alerts/:id/read - Mark system alert as read", async () => {
      // First create a system alert
      const createResponse = await test("POST", "/v2/api/kanglink/custom/super_admin/notifications/system-alert", {
        title: "Test System Alert",
        message: "This is a test system alert for reading",
        type: "info",
        severity: "low",
      }, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(createResponse.error).toBe(false);

      // Get system alerts to find the created one
      const getResponse = await test("GET", "/v2/api/kanglink/custom/super_admin/notifications/system-alerts", null, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(getResponse.error).toBe(false);
      expect(getResponse.data.alerts.length).toBeGreaterThan(0);

      const alertId = getResponse.data.alerts[0].id;

      // Mark as read
      const readResponse = await test("PUT", `/v2/api/kanglink/custom/super_admin/notifications/system-alerts/${alertId}/read`, null, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(readResponse.error).toBe(false);
      expect(readResponse.message).toBe("System alert marked as read");
    });
  });

  describe("Error Handling", () => {
    test("Should return 401 for unauthorized access", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/notifications");

      expect(response.error).toBe(true);
      expect(response.status).toBe(401);
    });

    test("Should return 404 for non-existent notification", async () => {
      const response = await test("PUT", "/v2/api/kanglink/custom/notifications/999999/read", null, {
        headers: { Authorization: `Bearer ${memberToken}` },
      });

      expect(response.error).toBe(true);
      expect(response.message).toBe("Notification not found");
    });

    test("Should return 400 for invalid system alert data", async () => {
      const response = await test("POST", "/v2/api/kanglink/custom/super_admin/notifications/system-alert", {
        // Missing required title and message
        type: "info",
        severity: "low",
      }, {
        headers: { Authorization: `Bearer ${superAdminToken}` },
      });

      expect(response.error).toBe(true);
      expect(response.message).toBe("Title and message are required");
    });
  });

  describe("Role-based Access Control", () => {
    test("Member should not access trainer notifications", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/trainer/notifications", null, {
        headers: { Authorization: `Bearer ${memberToken}` },
      });

      expect(response.error).toBe(true);
      expect(response.status).toBe(403);
    });

    test("Trainer should not access super admin notifications", async () => {
      const response = await test("POST", "/v2/api/kanglink/custom/super_admin/notifications/system-alert", {
        title: "Test",
        message: "Test message",
      }, {
        headers: { Authorization: `Bearer ${trainerToken}` },
      });

      expect(response.error).toBe(true);
      expect(response.status).toBe(403);
    });

    test("Member should not access system alerts", async () => {
      const response = await test("GET", "/v2/api/kanglink/custom/super_admin/notifications/system-alerts", null, {
        headers: { Authorization: `Bearer ${memberToken}` },
      });

      expect(response.error).toBe(true);
      expect(response.status).toBe(403);
    });
  });
}); 