/**
 * Test TreeQL Nested Joins Functionality
 * Tests comma-separated nested joins and mixed format support
 */

function testNestedJoins() {
  try {
    console.log("1. Testing nested joins with comma separation...");

    // Test nested joins: "token,user" should create a chain
    const mockReq = {
      query: {
        join: ["token,user"], // Nested join chain
      },
    };

    const RequestUtils = require("../../../baas/core/RequestUtils");
    const { joins, hasJoin } = RequestUtils.getRequestJoins(mockReq);

    console.log("   ✅ hasJoin:", hasJoin);
    console.log("   ✅ joins.length:", joins.length);
    console.log("   ✅ First join chain length:", joins[0].length);

    // Should be an array with two string elements
    const joinChain = joins[0];
    console.log("   ✅ Chain item 1:", joinChain[0]);
    console.log("   ✅ Chain item 2:", joinChain[1]);
    console.log("   ✅ Nested joins work correctly\n");

    return true;
  } catch (error) {
    console.error("   ❌ Test failed:", error);
    return false;
  }
}

function testMixedNestedJoins() {
  try {
    console.log("2. Testing mixed format nested joins...");

    // Test mixed format: "user|athlete_id|athlete,program|program_id"
    const mockReq = {
      query: {
        join: ["user|athlete_id|athlete,program|program_id"], // Mixed format chain
      },
    };

    const RequestUtils = require("../../../baas/core/RequestUtils");
    const { joins, hasJoin } = RequestUtils.getRequestJoins(mockReq);

    console.log("   ✅ hasJoin:", hasJoin);
    console.log("   ✅ joins.length:", joins.length);

    const joinChain = joins[0];
    console.log("   ✅ Chain length:", joinChain.length);

    // First item should be an object (new format)
    const firstJoin = joinChain[0];
    console.log("   ✅ First join type:", typeof firstJoin);
    if (typeof firstJoin === "object") {
      console.log("   ✅ First join - table:", firstJoin.table, "foreignKey:", firstJoin.foreignKey, "alias:", firstJoin.alias);
    }

    // Second item should be an object (new format without alias)
    const secondJoin = joinChain[1];
    console.log("   ✅ Second join type:", typeof secondJoin);
    if (typeof secondJoin === "object") {
      console.log("   ✅ Second join - table:", secondJoin.table, "foreignKey:", secondJoin.foreignKey, "alias:", secondJoin.alias || "undefined");
    }

    console.log("   ✅ Mixed format nested joins work correctly\n");

    return true;
  } catch (error) {
    console.error("   ❌ Test failed:", error);
    return false;
  }
}

function testLegacyNestedJoins() {
  try {
    console.log("3. Testing legacy nested joins...");

    // Test legacy format: "user|trainer_id,program"
    const mockReq = {
      query: {
        join: ["user|trainer_id,program"], // Legacy mixed chain
      },
    };

    const RequestUtils = require("../../../baas/core/RequestUtils");
    const { joins, hasJoin } = RequestUtils.getRequestJoins(mockReq);

    console.log("   ✅ hasJoin:", hasJoin);
    console.log("   ✅ joins.length:", joins.length);

    const joinChain = joins[0];
    console.log("   ✅ Chain length:", joinChain.length);

    // First item should be an object (pipe format)
    const firstJoin = joinChain[0];
    console.log("   ✅ First join type:", typeof firstJoin);
    if (typeof firstJoin === "object") {
      console.log("   ✅ First join - table:", firstJoin.table, "foreignKey:", firstJoin.foreignKey);
    }

    // Second item should be a string (legacy format)
    const secondJoin = joinChain[1];
    console.log("   ✅ Second join type:", typeof secondJoin);
    console.log("   ✅ Second join value:", secondJoin);

    console.log("   ✅ Legacy nested joins work correctly\n");

    return true;
  } catch (error) {
    console.error("   ❌ Test failed:", error);
    return false;
  }
}

// Run all tests
console.log("🧪 Testing TreeQL Nested Joins Functionality...\n");
console.log("🚀 Running TreeQL Nested Join Tests...\n");

const test1 = testNestedJoins();
const test2 = testMixedNestedJoins();
const test3 = testLegacyNestedJoins();

if (test1 && test2 && test3) {
  console.log("🎉 All nested join tests passed! TreeQL nested join functionality is working correctly.\n");
  
  console.log("📋 Summary of Nested Join Support:");
  console.log("   • Comma-separated nested joins: 'token,user'");
  console.log("   • Mixed format chains: 'user|athlete_id|athlete,program|program_id'");
  console.log("   • Legacy mixed chains: 'user|trainer_id,program'");
  console.log("   • Maintains full backward compatibility");
  console.log("   • Supports both objects and strings in chains");
} else {
  console.log("❌ Some nested join tests failed!");
  process.exit(1);
}
