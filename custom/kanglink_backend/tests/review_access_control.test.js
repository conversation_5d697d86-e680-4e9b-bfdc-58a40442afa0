const APITestFramework = require("../../../tests/apitesting.base");

// Create a simple test to verify the review access control functionality
async function testReviewAccessControl() {
  const framework = new APITestFramework();
  
  console.log("Testing Review Access Control...");
  
  try {
    // Test 1: Verify endpoint exists and requires authentication
    console.log("Test 1: Checking endpoint exists and requires authentication");
    const response1 = await framework.makeRequest("/v2/api/kanglink/custom/trainer/feed", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
        // No Authorization header - should return 401
      },
      body: {
        program_id: 1,
        post_type: "review",
        content: "Test review content",
        rating: 5
      }
    });
    
    console.log("Response 1:", response1.status);
    
    // Test 2: Verify that non-enrolled users cannot create reviews
    console.log("Test 2: Checking that non-enrolled users cannot create reviews");
    const response2 = await framework.makeRequest("/v2/api/kanglink/custom/trainer/feed", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-token"
      },
      body: {
        program_id: 999, // Non-existent program
        post_type: "review",
        content: "Test review content",
        rating: 5
      }
    });
    
    console.log("Response 2:", response2.status);
    
    // Test 3: Verify that enrolled users can create reviews
    console.log("Test 3: Checking that enrolled users can create reviews");
    const response3 = await framework.makeRequest("/v2/api/kanglink/custom/trainer/feed", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-token"
      },
      body: {
        program_id: 1,
        post_type: "review",
        content: "Test review content with sufficient length to meet requirements",
        rating: 5
      }
    });
    
    console.log("Response 3:", response3.status);
    
    // Test 4: Verify that users cannot create multiple reviews for the same program
    console.log("Test 4: Checking that users cannot create multiple reviews");
    const response4 = await framework.makeRequest("/v2/api/kanglink/custom/trainer/feed", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-token"
      },
      body: {
        program_id: 1,
        post_type: "review",
        content: "Another test review content with sufficient length",
        rating: 4
      }
    });
    
    console.log("Response 4:", response4.status);
    
    console.log("All tests completed successfully!");
    
  } catch (error) {
    console.error("Test failed:", error);
  }
}

// Run the test
testReviewAccessControl(); 