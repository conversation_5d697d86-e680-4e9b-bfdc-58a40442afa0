const { test, expect } = require('@playwright/test');

test.describe('Resend Verification Email', () => {
  test('should resend verification email for member', async ({ page }) => {
    // Mock the API response
    await page.route('**/v1/api/kanglink/member/lambda/resend_verification', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          error: false,
          message: "Verification email sent successfully"
        })
      });
    });

    // Navigate to verify email page with email parameter
    await page.goto('/verify-email?email=<EMAIL>&role=member');
    
    // Wait for error state (since no token provided)
    await page.waitForSelector('text=Verification Failed');
    
    // Click resend button
    await page.click('button:has-text("Resend Verification")');
    
    // Check for success message
    await expect(page.locator('text=Verification email sent successfully!')).toBeVisible();
  });

  test('should resend verification email for trainer', async ({ page }) => {
    // Mock the API response
    await page.route('**/v1/api/kanglink/trainer/lambda/resend_verification', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          error: false,
          message: "Verification email sent successfully"
        })
      });
    });

    // Navigate to verify email page with email parameter
    await page.goto('/verify-email?email=<EMAIL>&role=trainer');
    
    // Wait for error state (since no token provided)
    await page.waitForSelector('text=Verification Failed');
    
    // Click resend button
    await page.click('button:has-text("Resend Verification")');
    
    // Check for success message
    await expect(page.locator('text=Verification email sent successfully!')).toBeVisible();
  });

  test('should handle resend verification error', async ({ page }) => {
    // Mock the API response with error
    await page.route('**/v1/api/kanglink/member/lambda/resend_verification', async route => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          error: true,
          message: "User not found"
        })
      });
    });

    // Navigate to verify email page with email parameter
    await page.goto('/verify-email?email=<EMAIL>&role=member');
    
    // Wait for error state (since no token provided)
    await page.waitForSelector('text=Verification Failed');
    
    // Click resend button
    await page.click('button:has-text("Resend Verification")');
    
    // Check for error message
    await expect(page.locator('text=User not found')).toBeVisible();
  });

  test('should handle missing email parameter', async ({ page }) => {
    // Navigate to verify email page without email parameter
    await page.goto('/verify-email?role=member');
    
    // Wait for error state (since no token provided)
    await page.waitForSelector('text=Verification Failed');
    
    // Click resend button
    await page.click('button:has-text("Resend Verification")');
    
    // Check for error message about missing email
    await expect(page.locator('text=Email not found. Please try logging in again.')).toBeVisible();
  });
}); 