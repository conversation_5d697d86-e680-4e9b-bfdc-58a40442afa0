const { test, expect } = require("../../tests/apitesting.base.js");

test("Preview days limit test", async ({ sdk, testData }) => {
  // Test data for creating a program with multiple weeks
  const programData = {
    stepOneData: {
      program_name: "Multi-Week Test Program",
      type_of_program: "strength",
      program_description: "Test program with multiple weeks",
      image: "test-image.jpg",
      currency: "USD",
      status: "draft",
      days_for_preview: 3, // Set preview to 3 days
    },
    stepTwoData: {
      program_split: "1",
      description: "Test program with multiple weeks",
      weeks: [
        {
          id: "week-1",
          title: "Week 1",
          week_order: 1,
          days: [
            {
              id: "day-1",
              title: "Day 1",
              day_order: 1,
              is_rest_day: false,
              sessions: [
                {
                  id: "session-1",
                  title: "Session 1",
                  session_order: 1,
                  exercises: [
                    {
                      exercise_id: null,
                      exercise_name: "Push-ups",
                      video_url: null,
                      sets: "3",
                      reps_or_time: "10",
                      reps_time_type: "reps",
                      exercise_details: "Standard push-ups",
                      rest_duration_minutes: 1,
                      rest_duration_seconds: 0,
                      label: "A1",
                      label_number: "1",
                      is_linked: false,
                      exercise_order: 1,
                    },
                  ],
                },
              ],
            },
            {
              id: "day-2",
              title: "Day 2",
              day_order: 2,
              is_rest_day: false,
              sessions: [
                {
                  id: "session-2",
                  title: "Session 2",
                  session_order: 1,
                  exercises: [
                    {
                      exercise_id: null,
                      exercise_name: "Pull-ups",
                      video_url: null,
                      sets: "3",
                      reps_or_time: "8",
                      reps_time_type: "reps",
                      exercise_details: "Pull-ups",
                      rest_duration_minutes: 1,
                      rest_duration_seconds: 0,
                      label: "A1",
                      label_number: "1",
                      is_linked: false,
                      exercise_order: 1,
                    },
                  ],
                },
              ],
            },
            {
              id: "day-3",
              title: "Day 3",
              day_order: 3,
              is_rest_day: true,
              sessions: [],
            },
            {
              id: "day-4",
              title: "Day 4",
              day_order: 4,
              is_rest_day: false,
              sessions: [
                {
                  id: "session-4",
                  title: "Session 4",
                  session_order: 1,
                  exercises: [
                    {
                      exercise_id: null,
                      exercise_name: "Squats",
                      video_url: null,
                      sets: "3",
                      reps_or_time: "12",
                      reps_time_type: "reps",
                      exercise_details: "Bodyweight squats",
                      rest_duration_minutes: 1,
                      rest_duration_seconds: 0,
                      label: "A1",
                      label_number: "1",
                      is_linked: false,
                      exercise_order: 1,
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: "week-2",
          title: "Week 2",
          week_order: 2,
          days: [
            {
              id: "day-5",
              title: "Day 5",
              day_order: 1,
              is_rest_day: false,
              sessions: [
                {
                  id: "session-5",
                  title: "Session 5",
                  session_order: 1,
                  exercises: [
                    {
                      exercise_id: null,
                      exercise_name: "Lunges",
                      video_url: null,
                      sets: "3",
                      reps_or_time: "10",
                      reps_time_type: "reps",
                      exercise_details: "Walking lunges",
                      rest_duration_minutes: 1,
                      rest_duration_seconds: 0,
                      label: "A1",
                      label_number: "1",
                      is_linked: false,
                      exercise_order: 1,
                    },
                  ],
                },
              ],
            },
            {
              id: "day-6",
              title: "Day 6",
              day_order: 2,
              is_rest_day: false,
              sessions: [
                {
                  id: "session-6",
                  title: "Session 6",
                  session_order: 1,
                  exercises: [
                    {
                      exercise_id: null,
                      exercise_name: "Planks",
                      video_url: null,
                      sets: "3",
                      reps_or_time: "30",
                      reps_time_type: "time",
                      time_minutes: 0,
                      time_seconds: 30,
                      exercise_details: "Hold plank position",
                      rest_duration_minutes: 1,
                      rest_duration_seconds: 0,
                      label: "A1",
                      label_number: "1",
                      is_linked: false,
                      exercise_order: 1,
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      splitConfigurations: {
        "1": [
          {
            id: "week-1",
            title: "Week 1",
            week_order: 1,
            days: [
              {
                id: "day-1",
                title: "Day 1",
                day_order: 1,
                is_rest_day: false,
                sessions: [
                  {
                    id: "session-1",
                    title: "Session 1",
                    session_order: 1,
                    exercises: [
                      {
                        exercise_id: null,
                        exercise_name: "Push-ups",
                        video_url: null,
                        sets: "3",
                        reps_or_time: "10",
                        reps_time_type: "reps",
                        exercise_details: "Standard push-ups",
                        rest_duration_minutes: 1,
                        rest_duration_seconds: 0,
                        label: "A1",
                        label_number: "1",
                        is_linked: false,
                        exercise_order: 1,
                      },
                    ],
                  },
                ],
              },
              {
                id: "day-2",
                title: "Day 2",
                day_order: 2,
                is_rest_day: false,
                sessions: [
                  {
                    id: "session-2",
                    title: "Session 2",
                    session_order: 1,
                    exercises: [
                      {
                        exercise_id: null,
                        exercise_name: "Pull-ups",
                        video_url: null,
                        sets: "3",
                        reps_or_time: "8",
                        reps_time_type: "reps",
                        exercise_details: "Pull-ups",
                        rest_duration_minutes: 1,
                        rest_duration_seconds: 0,
                        label: "A1",
                        label_number: "1",
                        is_linked: false,
                        exercise_order: 1,
                      },
                    ],
                  },
                ],
              },
              {
                id: "day-3",
                title: "Day 3",
                day_order: 3,
                is_rest_day: true,
                sessions: [],
              },
              {
                id: "day-4",
                title: "Day 4",
                day_order: 4,
                is_rest_day: false,
                sessions: [
                  {
                    id: "session-4",
                    title: "Session 4",
                    session_order: 1,
                    exercises: [
                      {
                        exercise_id: null,
                        exercise_name: "Squats",
                        video_url: null,
                        sets: "3",
                        reps_or_time: "12",
                        reps_time_type: "reps",
                        exercise_details: "Bodyweight squats",
                        rest_duration_minutes: 1,
                        rest_duration_seconds: 0,
                        label: "A1",
                        label_number: "1",
                        is_linked: false,
                        exercise_order: 1,
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            id: "week-2",
            title: "Week 2",
            week_order: 2,
            days: [
              {
                id: "day-5",
                title: "Day 5",
                day_order: 1,
                is_rest_day: false,
                sessions: [
                  {
                    id: "session-5",
                    title: "Session 5",
                    session_order: 1,
                    exercises: [
                      {
                        exercise_id: null,
                        exercise_name: "Lunges",
                        video_url: null,
                        sets: "3",
                        reps_or_time: "10",
                        reps_time_type: "reps",
                        exercise_details: "Walking lunges",
                        rest_duration_minutes: 1,
                        rest_duration_seconds: 0,
                        label: "A1",
                        label_number: "1",
                        is_linked: false,
                        exercise_order: 1,
                      },
                    ],
                  },
                ],
              },
              {
                id: "day-6",
                title: "Day 6",
                day_order: 2,
                is_rest_day: false,
                sessions: [
                  {
                    id: "session-6",
                    title: "Session 6",
                    session_order: 1,
                    exercises: [
                      {
                        exercise_id: null,
                        exercise_name: "Planks",
                        video_url: null,
                        sets: "3",
                        reps_or_time: "30",
                        reps_time_type: "time",
                        time_minutes: 0,
                        time_seconds: 30,
                        exercise_details: "Hold plank position",
                        rest_duration_minutes: 1,
                        rest_duration_seconds: 0,
                        label: "A1",
                        label_number: "1",
                        is_linked: false,
                        exercise_order: 1,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      status: "draft",
      image: "test-image.jpg",
    },
  };

  // Create program
  const createResponse = await sdk.post("/v2/api/kanglink/custom/trainer/programs", {
    ...programData,
  });

  expect(createResponse.error).toBe(false);
  expect(createResponse.data).toBeDefined();

  const programId = createResponse.data;

  // Get program to get the split ID
  const getResponse = await sdk.get(`/v2/api/kanglink/custom/trainer/programs/${programId}`);

  expect(getResponse.error).toBe(false);
  expect(getResponse.data).toBeDefined();

  // Get the first split ID
  const splits = getResponse.data.stepOneData.splits;
  expect(splits).toBeDefined();
  expect(splits.length).toBeGreaterThan(0);

  const splitId = splits[0].id;

  // Test preview with days_for_preview = 3
  const previewResponse = await sdk.get(`/v2/api/kanglink/custom/preview/program/${splitId}`);

  expect(previewResponse.error).toBe(false);
  expect(previewResponse.data).toBeDefined();

  const previewData = previewResponse.data;
  
  // Verify preview info
  expect(previewData.split.preview_info).toBeDefined();
  expect(previewData.split.preview_info.days_for_preview).toBe(3);
  expect(previewData.split.preview_info.total_days_retrieved).toBe(3);
  expect(previewData.split.preview_info.weeks_in_preview).toBe(1); // Should only be 1 week since 3 days fit in first week

  // Verify we only get 3 days
  const totalDaysInPreview = previewData.split.weeks.reduce((total, week) => {
    return total + week.days.length;
  }, 0);
  
  expect(totalDaysInPreview).toBe(3);

  // Verify the days are in correct order (Day 1, Day 2, Day 3)
  const firstWeek = previewData.split.weeks[0];
  expect(firstWeek.days.length).toBe(3);
  expect(firstWeek.days[0].title).toBe("Day 1");
  expect(firstWeek.days[1].title).toBe("Day 2");
  expect(firstWeek.days[2].title).toBe("Day 3");

  console.log("Preview days limit test passed successfully!");
}); 