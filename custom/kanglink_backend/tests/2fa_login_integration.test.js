const APITestFramework = require("../../../tests/apitesting.base.js");

class TwoFactorAuthLoginIntegrationTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    const testUsers = [
      {
        role: "member",
        email: "<EMAIL>",
        password: "password123",
        description: "Member 2FA Login"
      },
      {
        role: "trainer", 
        email: "<EMAIL>",
        password: "password123",
        description: "Trainer 2FA Login"
      },
      {
        role: "super_admin",
        email: "<EMAIL>", 
        password: "password123",
        description: "Super Admin 2FA Login"
      }
    ];

    testUsers.forEach(({ role, email, password, description }) => {
      // Test initiating 2FA login
      this.framework.addTestCase(`${description} - Initiate 2FA Login`, async () => {
        const response = await this.framework.makeRequest(
          `/v1/api/kanglink/${role}/lambda/2fa/login`,
          {
            method: "POST",
            body: JSON.stringify({
              email: email,
              password: password,
              role: role
            })
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(!response.body.error, "Should not return error");
        this.framework.assert(response.body.qr_code, "Should return QR code");
        this.framework.assert(response.body.one_time_token, "Should return one-time token");
        this.framework.assert(response.body.user_id, "Should return user ID");
        this.framework.assert(response.body.expire_at, "Should return expiration time");
        this.framework.assert(response.body.role === role, "Should return correct role");
      });

      // Test authenticating with valid 2FA code
      this.framework.addTestCase(`${description} - Authenticate with Valid 2FA Code`, async () => {
        // First get the 2FA setup
        const setupResponse = await this.framework.makeRequest(
          `/v1/api/kanglink/${role}/lambda/2fa/login`,
          {
            method: "POST",
            body: JSON.stringify({
              email: email,
              password: password,
              role: role
            })
          }
        );

        this.framework.assert(
          setupResponse.status === 200,
          "Setup should return 200 status code"
        );
        this.framework.assert(!setupResponse.body.error, "Setup should not return error");
        this.framework.assert(setupResponse.body.one_time_token, "Should return one-time token");

        // Then authenticate with a valid code (this would be from authenticator app)
        const authResponse = await this.framework.makeRequest(
          `/v2/api/kanglink/${role}/lambda/2fa/auth`,
          {
            method: "POST",
            body: JSON.stringify({
              code: "123456", // This would be the actual code from authenticator
              token: setupResponse.body.one_time_token
            })
          }
        );

        // Note: This will likely fail with invalid code in test environment
        // but we can test the structure
        this.framework.assert(
          authResponse.body.hasOwnProperty("error"),
          "Should have error property"
        );
        this.framework.assert(
          authResponse.body.hasOwnProperty("message"),
          "Should have message property"
        );
      });

      // Test rejecting invalid 2FA code
      this.framework.addTestCase(`${description} - Reject Invalid 2FA Code`, async () => {
        // First get the 2FA setup
        const setupResponse = await this.framework.makeRequest(
          `/v1/api/kanglink/${role}/lambda/2fa/login`,
          {
            method: "POST",
            body: JSON.stringify({
              email: email,
              password: password,
              role: role
            })
          }
        );

        this.framework.assert(
          setupResponse.status === 200,
          "Setup should return 200 status code"
        );
        this.framework.assert(!setupResponse.body.error, "Setup should not return error");

        // Try to authenticate with invalid code
        const authResponse = await this.framework.makeRequest(
          `/v2/api/kanglink/${role}/lambda/2fa/auth`,
          {
            method: "POST",
            body: JSON.stringify({
              code: "000000", // Invalid code
              token: setupResponse.body.one_time_token
            })
          }
        );

        this.framework.assert(
          authResponse.body.error === true,
          "Should return error for invalid code"
        );
        this.framework.assert(
          authResponse.body.message,
          "Should return error message"
        );
      });

      // Test rejecting missing 2FA code
      this.framework.addTestCase(`${description} - Reject Missing 2FA Code`, async () => {
        // First get the 2FA setup
        const setupResponse = await this.framework.makeRequest(
          `/v1/api/kanglink/${role}/lambda/2fa/login`,
          {
            method: "POST",
            body: JSON.stringify({
              email: email,
              password: password,
              role: role
            })
          }
        );

        this.framework.assert(
          setupResponse.status === 200,
          "Setup should return 200 status code"
        );

        // Try to authenticate without code
        const authResponse = await this.framework.makeRequest(
          `/v2/api/kanglink/${role}/lambda/2fa/auth`,
          {
            method: "POST",
            body: JSON.stringify({
              token: setupResponse.body.one_time_token
            })
          }
        );

        this.framework.assert(
          authResponse.body.error === true,
          "Should return error for missing code"
        );
      });

      // Test rejecting missing token
      this.framework.addTestCase(`${description} - Reject Missing Token`, async () => {
        const authResponse = await this.framework.makeRequest(
          `/v2/api/kanglink/${role}/lambda/2fa/auth`,
          {
            method: "POST",
            body: JSON.stringify({
              code: "123456"
            })
          }
        );

        this.framework.assert(
          authResponse.body.error === true,
          "Should return error for missing token"
        );
      });
    });

    // Test error cases
    this.framework.addTestCase("2FA Login - Invalid Credentials", async () => {
      const response = await this.framework.makeRequest(
        `/v1/api/kanglink/member/lambda/2fa/login`,
        {
          method: "POST",
          body: JSON.stringify({
            email: "<EMAIL>",
            password: "wrongpassword",
            role: "member"
          })
        }
      );

      this.framework.assert(
        response.body.error === true,
        "Should return error for invalid credentials"
      );
    });

    this.framework.addTestCase("2FA Login - Missing Fields", async () => {
      const response = await this.framework.makeRequest(
        `/v1/api/kanglink/member/lambda/2fa/login`,
        {
          method: "POST",
          body: JSON.stringify({
            email: "<EMAIL>"
            // Missing password and role
          })
        }
      );

      this.framework.assert(
        response.body.error === true,
        "Should return error for missing fields"
      );
    });

    this.framework.addTestCase("2FA Login - Invalid Role", async () => {
      const response = await this.framework.makeRequest(
        `/v1/api/kanglink/invalid_role/lambda/2fa/login`,
        {
          method: "POST",
          body: JSON.stringify({
            email: "<EMAIL>",
            password: "password123",
            role: "invalid_role"
          })
        }
      );

      this.framework.assert(
        response.body.error === true,
        "Should return error for invalid role"
      );
    });

    // Test 2FA status endpoints (structure only, requires valid tokens)
    this.framework.addTestCase("2FA Status Check Structure", async () => {
      const response = await this.framework.makeRequest(
        `/v2/api/kanglink/member/lambda/2fa/status`,
        {
          method: "GET",
          headers: {
            Authorization: "Bearer invalid-token"
          }
        }
      );

      // Should return error for invalid token, but we can check structure
      this.framework.assert(
        response.body.hasOwnProperty("error"),
        "Should have error property"
      );
    });

    this.framework.addTestCase("2FA Enable Structure", async () => {
      const response = await this.framework.makeRequest(
        `/v2/api/kanglink/member/lambda/2fa/enable`,
        {
          method: "POST",
          headers: {
            Authorization: "Bearer invalid-token"
          },
          body: JSON.stringify({
            type: "qr"
          })
        }
      );

      this.framework.assert(
        response.body.hasOwnProperty("error"),
        "Should have error property"
      );
    });

    this.framework.addTestCase("2FA Disable Structure", async () => {
      const response = await this.framework.makeRequest(
        `/v2/api/kanglink/member/lambda/2fa/disable`,
        {
          method: "POST",
          headers: {
            Authorization: "Bearer invalid-token"
          }
        }
      );

      this.framework.assert(
        response.body.hasOwnProperty("error"),
        "Should have error property"
      );
    });

    this.framework.addTestCase("2FA Verify Structure", async () => {
      const response = await this.framework.makeRequest(
        `/v2/api/kanglink/member/lambda/2fa/verify`,
        {
          method: "POST",
          headers: {
            Authorization: "Bearer invalid-token"
          },
          body: JSON.stringify({
            verification_code: "123456"
          })
        }
      );

      this.framework.assert(
        response.body.hasOwnProperty("error"),
        "Should have error property"
      );
    });
  }

  async runTests() {
    return await this.framework.runTests();
  }
}

// Export the test instance for the test runner
module.exports = new TwoFactorAuthLoginIntegrationTests(); 