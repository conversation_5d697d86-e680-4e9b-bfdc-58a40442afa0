const { TestSuite } = require("../../../tests/apitesting.base");

class EnrollmentTest extends TestSuite {
  constructor() {
    super("Enrollment API Tests");
  }

  async testEnrollmentEndpointExists() {
    this.describe("Test enrollment endpoint exists and responds");
    
    try {
      // Test that the endpoint exists (should return 400 for missing params, not 404)
      const response = await this.makeRequest({
        method: "POST",
        url: "/v2/api/kanglink/custom/athlete/enrollment",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer test-token"
        },
        body: {}
      });

      // Should return 400 (bad request) not 404 (not found)
      this.assert(
        response.status === 400 || response.status === 401,
        `Expected 400 or 401 status, got ${response.status}`,
        "Enrollment endpoint exists and responds to requests"
      );

      this.success("Enrollment endpoint is accessible");
    } catch (error) {
      this.fail(`Enrollment endpoint test failed: ${error.message}`);
    }
  }

  async testEnrollmentValidation() {
    this.describe("Test enrollment parameter validation");
    
    try {
      const response = await this.makeRequest({
        method: "POST",
        url: "/v2/api/kanglink/custom/athlete/enrollment",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer test-token"
        },
        body: {
          // Missing required fields
        }
      });

      const data = response.data;
      
      this.assert(
        data.error === true,
        "Should return error for missing required fields",
        "Validation works for missing parameters"
      );

      this.assert(
        data.message && data.message.includes("required"),
        "Error message should mention required fields",
        "Error message is descriptive"
      );

      this.success("Parameter validation works correctly");
    } catch (error) {
      this.fail(`Validation test failed: ${error.message}`);
    }
  }

  async testEnrollmentStatusEndpoint() {
    this.describe("Test enrollment status endpoint");
    
    try {
      const response = await this.makeRequest({
        method: "GET",
        url: "/v2/api/kanglink/custom/athlete/enrollment/status/test-subscription-id",
        headers: {
          "Authorization": "Bearer test-token"
        }
      });

      // Should return 404 for non-existent subscription, not 500 error
      this.assert(
        response.status === 404 || response.status === 401,
        `Expected 404 or 401 status, got ${response.status}`,
        "Status endpoint exists and handles non-existent subscriptions"
      );

      this.success("Enrollment status endpoint is accessible");
    } catch (error) {
      this.fail(`Status endpoint test failed: ${error.message}`);
    }
  }

  async testLegacyEndpointRedirect() {
    this.describe("Test legacy endpoint redirect");
    
    try {
      const response = await this.makeRequest({
        method: "POST",
        url: "/v2/api/kanglink/custom/athlete/enroll",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer test-token"
        },
        body: {}
      });

      const data = response.data;
      
      this.assert(
        response.status === 301,
        `Expected 301 redirect status, got ${response.status}`,
        "Legacy endpoint returns redirect status"
      );

      this.assert(
        data.new_endpoint === "/v2/api/kanglink/custom/athlete/enrollment",
        "Redirect points to correct new endpoint",
        "Redirect URL is correct"
      );

      this.success("Legacy endpoint redirect works correctly");
    } catch (error) {
      this.fail(`Legacy endpoint test failed: ${error.message}`);
    }
  }

  async testResubscribeEndpointExists() {
    this.describe("Test resubscribe endpoint exists and responds");
    
    try {
      // Test that the endpoint exists (should return 400 for missing params, not 404)
      const response = await this.makeRequest({
        method: "POST",
        url: "/v2/api/kanglink/custom/enrollment/test-enrollment-id/resubscribe",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer test-token"
        },
        body: {}
      });

      // Should return 400 (bad request) not 404 (not found)
      this.assert(
        response.status === 400 || response.status === 401 || response.status === 404,
        `Expected 400, 401, or 404 status, got ${response.status}`,
        "Resubscribe endpoint exists and responds to requests"
      );

      this.success("Resubscribe endpoint is accessible");
    } catch (error) {
      this.fail(`Resubscribe endpoint test failed: ${error.message}`);
    }
  }

  async testResubscribeValidation() {
    this.describe("Test resubscribe parameter validation");
    
    try {
      const response = await this.makeRequest({
        method: "POST",
        url: "/v2/api/kanglink/custom/enrollment/test-enrollment-id/resubscribe",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer test-token"
        },
        body: {
          // Missing payment_method_id
        }
      });

      const data = response.data;
      
      // Should return error for missing payment_method_id (if endpoint is found)
      if (response.status !== 404 && response.status !== 401) {
        this.assert(
          data.error === true,
          "Should return error for missing payment_method_id",
          "Validation works for missing payment method"
        );

        this.assert(
          data.message && data.message.includes("payment_method_id"),
          "Error message should mention payment_method_id is required",
          "Error message is descriptive"
        );
      }

      this.success("Resubscribe parameter validation works correctly");
    } catch (error) {
      this.fail(`Resubscribe validation test failed: ${error.message}`);
    }
  }

  async testResubscribeWithNonExistentEnrollment() {
    this.describe("Test resubscribe with non-existent enrollment");
    
    try {
      const response = await this.makeRequest({
        method: "POST",
        url: "/v2/api/kanglink/custom/enrollment/99999/resubscribe",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer test-token"
        },
        body: {
          payment_method_id: "pm_test_payment_method"
        }
      });

      // Should return 404 for non-existent enrollment (if authenticated)
      if (response.status !== 401) {
        this.assert(
          response.status === 404,
          `Expected 404 status for non-existent enrollment, got ${response.status}`,
          "Non-existent enrollment returns 404"
        );

        const data = response.data;
        this.assert(
          data.error === true,
          "Should return error for non-existent enrollment",
          "Error flag is correct"
        );

        this.assert(
          data.message && data.message.includes("not found"),
          "Error message should indicate enrollment not found",
          "Error message is descriptive"
        );
      }

      this.success("Non-existent enrollment handling works correctly");
    } catch (error) {
      this.fail(`Non-existent enrollment test failed: ${error.message}`);
    }
  }

  async testResubscribeResponseFormat() {
    this.describe("Test resubscribe response format structure");
    
    try {
      const response = await this.makeRequest({
        method: "POST",
        url: "/v2/api/kanglink/custom/enrollment/test-enrollment-id/resubscribe",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer test-token"
        },
        body: {
          payment_method_id: "pm_test_payment_method"
        }
      });

      const data = response.data;
      
      // Verify response follows the expected structure
      this.assert(
        typeof data.error === "boolean",
        "Response should have boolean error field",
        "Error field format is correct"
      );

      this.assert(
        typeof data.message === "string",
        "Response should have string message field",
        "Message field format is correct"
      );

      // If successful, should have data field
      if (!data.error && data.data) {
        this.assert(
          typeof data.data === "object",
          "Success response should have data object",
          "Data field format is correct"
        );
      }

      this.success("Resubscribe response format is consistent");
    } catch (error) {
      this.fail(`Response format test failed: ${error.message}`);
    }
  }

  async testIntegrationFlow() {
    this.describe("Test complete enrollment and resubscribe integration flow");
    
    try {
      // Note: This is an integration test that would require proper authentication
      // and test data setup. In a real test environment, you would:
      // 1. Create a test enrollment with failed payment status
      // 2. Test the resubscribe endpoint with valid payment method
      // 3. Verify the enrollment is reactivated

      // For now, we'll just test that the endpoints exist and have correct structure
      const enrollmentResponse = await this.makeRequest({
        method: "POST",
        url: "/v2/api/kanglink/custom/athlete/enrollment",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer test-token"
        },
        body: {}
      });

      const resubscribeResponse = await this.makeRequest({
        method: "POST",
        url: "/v2/api/kanglink/custom/enrollment/test-id/resubscribe",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer test-token"
        },
        body: {}
      });

      // Both endpoints should exist (return 400 for missing params, not 404)
      this.assert(
        enrollmentResponse.status !== 404 && resubscribeResponse.status !== 404,
        "Both enrollment and resubscribe endpoints exist",
        "Integration endpoints are available"
      );

      this.success("Integration flow endpoints are properly configured");
    } catch (error) {
      this.fail(`Integration flow test failed: ${error.message}`);
    }
  }

  async run() {
    await this.testEnrollmentEndpointExists();
    await this.testEnrollmentValidation();
    await this.testEnrollmentStatusEndpoint();
    await this.testLegacyEndpointRedirect();
    await this.testResubscribeEndpointExists();
    await this.testResubscribeValidation();
    await this.testResubscribeWithNonExistentEnrollment();
    await this.testResubscribeResponseFormat();
    await this.testIntegrationFlow();
  }
}

module.exports = EnrollmentTest;
