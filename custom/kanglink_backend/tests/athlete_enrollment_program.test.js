const testFramework = require("../../../tests/apitesting.base");

testFramework.describe("Athlete Enrollment Program Endpoint", () => {
  let sdk;

  // Setup before each test
  testFramework.beforeEach(async () => {
    // Mock SDK for testing
    sdk = {
      setProjectId: () => {},
      rawQuery: async (query) => {
        // Mock enrollment data
        if (query.includes("FROM kanglink_enrollment e")) {
          return [
            {
              id: 1,
              trainer_id: 2,
              athlete_id: 3,
              program_id: 4,
              split_id: 5,
              payment_type: "one_time",
              amount: 100.0,
              currency: "USD",
              enrollment_date: "2024-01-15T10:30:00Z",
              status: "active",
              payment_status: "paid",
              program_name: "Strength Training Program",
              program_description: "Complete strength training program",
              program_image: "https://example.com/program.jpg",
              split_title: "Beginner Split",
              split_equipment: "Dumbbells, resistance bands",
              full_price: 100.0,
              subscription: 20.0,
              trainer_first_name: "<PERSON>",
              trainer_last_name: "Trainer",
              trainer_full_name: "<PERSON>",
              trainer_photo: "https://example.com/photo.jpg",
              trainer_email: "<EMAIL>",
            },
          ];
        }

        // Mock weeks/days/sessions/exercises data
        if (query.includes("FROM kanglink_week w")) {
          return [
            {
              week_id: 1,
              week_title: "Week 1",
              week_order: 1,
              split_id: 5,
              day_id: 1,
              day_title: "Day 1",
              day_order: 1,
              is_rest_day: false,
              session_id: 1,
              session_title: "Session 1 - Compound Movements",
              session_order: 1,
              exercise_instance_id: 1,
              exercise_label: "Push-ups",
              sets: 4,
              reps_or_time: "12-15",
              rest_duration_seconds: 60,
              exercise_notes: "Keep core tight",
              exercise_order: 1,
              exercise_id: 1,
              exercise_name: "Push-ups",
              exercise_description: "Standard push-up exercise",
              muscle_groups: "Chest, Triceps, Shoulders",
              equipment: "Body weight",
              difficulty_level: "Beginner",
              video_url: "https://example.com/video.mp4",
              thumbnail_url: "https://example.com/thumbnail.jpg",
              duration_seconds: 120,
            },
          ];
        }

        // Mock progress data
        if (query.includes("FROM kanglink_exercise_progress ep")) {
          return [
            {
              id: 1,
              athlete_id: 3,
              enrollment_id: 1,
              exercise_instance_id: 1,
              is_completed: true,
              sets_completed: 4,
              reps_completed: "15,14,13,12",
              weight_used: "bodyweight",
              time_taken_seconds: 300,
              difficulty_rating: 7,
              notes: "Felt good",
              completed_at: "2024-01-15T11:00:00Z",
            },
          ];
        }

        if (query.includes("FROM kanglink_day_progress")) {
          return [
            {
              id: 1,
              athlete_id: 3,
              enrollment_id: 1,
              day_id: 1,
              is_completed: true,
              total_exercises: 5,
              completed_exercises: 5,
              completed_at: "2024-01-15T12:00:00Z",
              notes: "Great workout",
            },
          ];
        }

        if (query.includes("FROM kanglink_athlete_progress")) {
          return [
            {
              id: 1,
              athlete_id: 3,
              enrollment_id: 1,
              current_week_id: 1,
              current_day_id: 2,
              total_days_completed: 1,
              total_exercises_completed: 5,
              progress_percentage: 12.5,
              last_activity_date: "2024-01-15T12:00:00Z",
            },
          ];
        }

        return [];
      },
    };
  });

  // Test successful enrollment program data retrieval
  testFramework.test(
    "should return complete enrollment program data with progress",
    async () => {
      const req = {
        user_id: 3,
        params: { enrollment_id: 1 },
      };

      const res = {
        status: (code) => ({
          json: (data) => ({ statusCode: code, data }),
        }),
      };

      const app = {
        get: () => sdk,
      };

      // Mock the endpoint function
      const getEnrollmentProgram = async (req, res) => {
        try {
          const athleteId = req.user_id;
          const enrollmentId = req.params.enrollment_id;

          sdk.setProjectId("kanglink");

          // Get enrollment data
          const enrollment = await sdk.rawQuery("FROM kanglink_enrollment e");
          if (!enrollment || enrollment.length === 0) {
            return res.status(404).json({
              error: true,
              message: "Enrollment not found or access denied",
            });
          }

          const enrollmentData = enrollment[0];

          // Get program structure
          const weeks = await sdk.rawQuery("FROM kanglink_week w");
          const exerciseProgress = await sdk.rawQuery(
            "FROM kanglink_exercise_progress ep"
          );
          const dayProgress = await sdk.rawQuery("FROM kanglink_day_progress");
          const overallProgress = await sdk.rawQuery(
            "FROM kanglink_athlete_progress"
          );

          // Structure the response (simplified for test)
          const responseData = {
            enrollment: {
              id: enrollmentData.id,
              trainer_id: enrollmentData.trainer_id,
              athlete_id: enrollmentData.athlete_id,
              program_id: enrollmentData.program_id,
              split_id: enrollmentData.split_id,
              payment_type: enrollmentData.payment_type,
              amount: enrollmentData.amount,
              currency: enrollmentData.currency,
              status: enrollmentData.status,
            },
            program: {
              id: enrollmentData.program_id,
              name: enrollmentData.program_name,
              description: enrollmentData.program_description,
              image_url: enrollmentData.program_image,
            },
            split: {
              id: enrollmentData.split_id,
              title: enrollmentData.split_title,
              equipment_required: enrollmentData.split_equipment,
              pricing: {
                full_price: 100.0,
                subscription_price: 20.0,
                price: 20.0,
                currency: "USD",
              },
            },
            trainer: {
              id: enrollmentData.trainer_id,
              email: enrollmentData.trainer_email,
              full_name: enrollmentData.trainer_full_name,
              first_name: enrollmentData.trainer_first_name,
              last_name: enrollmentData.trainer_last_name,
              photo: enrollmentData.trainer_photo,
            },
            weeks: [
              {
                id: weeks[0].week_id,
                title: weeks[0].week_title,
                description: weeks[0].week_description,
                week_order: weeks[0].week_order,
                days: [
                  {
                    id: weeks[0].day_id,
                    title: weeks[0].day_title,
                    description: weeks[0].day_description,
                    day_order: weeks[0].day_order,
                    equipment_required: weeks[0].equipment_required,
                    sessions: [
                      {
                        id: weeks[0].session_id,
                        title: weeks[0].session_title,
                        description: weeks[0].session_description,
                        session_order: weeks[0].session_order,
                        exercise_instances: [
                          {
                            id: weeks[0].exercise_instance_id,
                            label: weeks[0].exercise_label,
                            sets: weeks[0].sets,
                            reps_or_time: weeks[0].reps_or_time,
                            exercise: {
                              id: weeks[0].exercise_id,
                              name: weeks[0].exercise_name,
                              description: weeks[0].exercise_description,
                              muscle_groups: weeks[0].muscle_groups,
                              equipment: weeks[0].equipment,
                              difficulty_level: weeks[0].difficulty_level,
                            },
                            video: {
                              url: weeks[0].video_url,
                            },
                            progress: exerciseProgress[0],
                          },
                        ],
                      },
                    ],
                    progress: dayProgress[0],
                  },
                ],
                progress: {
                  total_days: 1,
                  completed_days: 1,
                  is_completed: true,
                },
              },
            ],
            overall_progress: overallProgress[0],
          };

          return res.status(200).json({
            error: false,
            data: responseData,
          });
        } catch (error) {
          return res.status(500).json({
            error: true,
            message: "Failed to get enrollment program data",
            details: error.message,
          });
        }
      };

      const result = await getEnrollmentProgram(req, res);

      testFramework.expect(result.statusCode).toBe(200);
      testFramework.expect(result.data.error).toBe(false);
      testFramework.expect(result.data.data.enrollment.id).toBe(1);
      testFramework
        .expect(result.data.data.program.name)
        .toBe("Strength Training Program");
      testFramework.expect(result.data.data.split.title).toBe("Beginner Split");
      testFramework
        .expect(result.data.data.trainer.full_name)
        .toBe("John Trainer");
      testFramework.expect(result.data.data.weeks.length).toBe(1);
      testFramework.expect(result.data.data.weeks[0].days.length).toBe(1);
      testFramework
        .expect(result.data.data.weeks[0].days[0].sessions.length)
        .toBe(1);
      testFramework
        .expect(
          result.data.data.weeks[0].days[0].sessions[0].exercise_instances
            .length
        )
        .toBe(1);
      testFramework
        .expect(
          result.data.data.weeks[0].days[0].sessions[0].exercise_instances[0]
            .progress.is_completed
        )
        .toBe(true);
    }
  );

  // Test enrollment not found
  testFramework.test(
    "should return 404 when enrollment not found",
    async () => {
      sdk.rawQuery = async (query) => {
        if (query.includes("FROM kanglink_enrollment e")) {
          return [];
        }
        return [];
      };

      const req = {
        user_id: 3,
        params: { enrollment_id: 999 },
      };

      const res = {
        status: (code) => ({
          json: (data) => ({ statusCode: code, data }),
        }),
      };

      const getEnrollmentProgram = async (req, res) => {
        try {
          const enrollment = await sdk.rawQuery("FROM kanglink_enrollment e");
          if (!enrollment || enrollment.length === 0) {
            return res.status(404).json({
              error: true,
              message: "Enrollment not found or access denied",
            });
          }
        } catch (error) {
          return res.status(500).json({
            error: true,
            message: "Failed to get enrollment program data",
          });
        }
      };

      const result = await getEnrollmentProgram(req, res);

      testFramework.expect(result.statusCode).toBe(404);
      testFramework.expect(result.data.error).toBe(true);
      testFramework
        .expect(result.data.message)
        .toBe("Enrollment not found or access denied");
    }
  );
});
