const APITestFramework = require("../../../tests/apitesting.base");

// Initialize test framework
const testFramework = new APITestFramework({
  environment: "test",
});

// Test suite for enrollment with commission tracking
testFramework.describe("Enrollment Commission Integration Tests", () => {
  let mockSdk;
  let mockStripeService;
  let mockCommissionService;

  testFramework.beforeEach(() => {
    // Mock SDK
    mockSdk = {
      setProjectId: () => {},
      setTable: () => {},
      findOne: async (table, criteria) => {
        if (table === "split") {
          return {
            id: 1,
            program_id: 1,
            trainer_id: 1,
            subscription: 50.00,
            full_price: 100.00,
            currency: "USD",
          };
        }
        if (table === "user") {
          return {
            id: 2,
            email: "<EMAIL>",
            stripe_uid: "cus_test123",
          };
        }
        if (table === "enrollment") {
          return null; // No existing enrollment
        }
        if (table === "payout_settings") {
          return {
            id: 1,
            trainer_payout_time_hours: 24,
            split_company_percentage: 30.00,
            split_trainer_percentage: 70.00,
            affiliate_company_percentage: 20.00,
            affiliate_trainer_percentage: 80.00,
            is_active: true,
          };
        }
        return null;
      },
      rawQuery: async (query) => {
        if (query.includes("kanglink_split s")) {
          return [{
            id: 1,
            program_id: 1,
            trainer_id: 1,
            subscription: 50.00,
            full_price: 100.00,
            currency: "USD",
            program_name: "Test Program",
          }];
        }
        if (query.includes("affiliate_link LIKE")) {
          return [{
            program_id: 1,
            trainer_id: 3,
            program_name: "Test Program",
          }];
        }
        return [];
      },
      create: async (table, data) => {
        return { id: 1, ...data };
      },
      updateById: async (table, id, data) => {
        return { id, ...data };
      },
    };

    // Mock Stripe Service
    mockStripeService = {
      retrieveStripeCustomer: async () => ({ id: "cus_test123" }),
      createPaymentIntentAutomatic: async () => ({
        id: "pi_test123",
        status: "succeeded",
      }),
    };

    // Mock Commission Service
    mockCommissionService = {
      validateAffiliateCode: async (code) => ({
        valid: true,
        program_id: 1,
        trainer_id: 3,
      }),
      createCommissionRecord: async (enrollmentId, data) => ({
        id: 1,
        enrollment_id: enrollmentId,
        ...data,
      }),
    };
  });

  // Test enrollment without affiliate code
  testFramework.test("should create enrollment without affiliate code", async () => {
    const enrollmentData = {
      split_id: 1,
      payment_type: "one_time",
      payment_method_id: "pm_test123",
    };

    // Mock the enrollment creation process
    const enrollment = await mockSdk.create("enrollment", {
      trainer_id: 1,
      athlete_id: 2,
      program_id: 1,
      split_id: 1,
      payment_type: "one_time",
      amount: 100.00,
      currency: "USD",
      status: "active",
      payment_status: "paid",
      affiliate_code: null,
      affiliate_user_id: null,
      commission_calculated: false,
    });

    testFramework.expect(enrollment.id).toBe(1);
    testFramework.expect(enrollment.affiliate_code).toBe(null);
    testFramework.expect(enrollment.affiliate_user_id).toBe(null);
    testFramework.expect(enrollment.commission_calculated).toBe(false);
  });

  // Test enrollment with affiliate code
  testFramework.test("should create enrollment with valid affiliate code", async () => {
    const enrollmentData = {
      split_id: 1,
      payment_type: "one_time",
      payment_method_id: "pm_test123",
      affiliate_code: "TEST123",
    };

    // Validate affiliate code
    const validation = await mockCommissionService.validateAffiliateCode("TEST123");
    testFramework.expect(validation.valid).toBe(true);

    // Mock the enrollment creation process with affiliate
    const enrollment = await mockSdk.create("enrollment", {
      trainer_id: 1,
      athlete_id: 2,
      program_id: 1,
      split_id: 1,
      payment_type: "one_time",
      amount: 100.00,
      currency: "USD",
      status: "active",
      payment_status: "paid",
      affiliate_code: "TEST123",
      affiliate_user_id: 3,
      commission_calculated: false,
    });

    testFramework.expect(enrollment.affiliate_code).toBe("TEST123");
    testFramework.expect(enrollment.affiliate_user_id).toBe(3);
  });

  // Test commission creation after successful enrollment
  testFramework.test("should create commission after successful enrollment", async () => {
    const enrollmentId = 1;
    const commissionData = {
      trainer_id: 1,
      athlete_id: 2,
      program_id: 1,
      split_id: 1,
      amount: 100.00,
      currency: "USD",
      affiliate_code: null,
      affiliate_user_id: null,
    };

    const commission = await mockCommissionService.createCommissionRecord(
      enrollmentId,
      commissionData
    );

    testFramework.expect(commission.id).toBe(1);
    testFramework.expect(commission.enrollment_id).toBe(enrollmentId);
    testFramework.expect(commission.trainer_id).toBe(1);
    testFramework.expect(commission.athlete_id).toBe(2);
  });

  // Test commission creation with affiliate
  testFramework.test("should create affiliate commission after successful enrollment", async () => {
    const enrollmentId = 1;
    const commissionData = {
      trainer_id: 1,
      athlete_id: 2,
      program_id: 1,
      split_id: 1,
      amount: 100.00,
      currency: "USD",
      affiliate_code: "TEST123",
      affiliate_user_id: 3,
    };

    const commission = await mockCommissionService.createCommissionRecord(
      enrollmentId,
      commissionData
    );

    testFramework.expect(commission.id).toBe(1);
    testFramework.expect(commission.enrollment_id).toBe(enrollmentId);
    testFramework.expect(commission.affiliate_code).toBe("TEST123");
    testFramework.expect(commission.affiliate_user_id).toBe(3);
  });

  // Test enrollment validation with invalid affiliate code
  testFramework.test("should reject enrollment with invalid affiliate code", async () => {
    // Mock invalid affiliate code validation
    mockCommissionService.validateAffiliateCode = async () => ({
      valid: false,
      message: "Invalid or expired affiliate code",
    });

    const validation = await mockCommissionService.validateAffiliateCode("INVALID");
    testFramework.expect(validation.valid).toBe(false);
    testFramework.expect(validation.message).toBe("Invalid or expired affiliate code");
  });

  // Test enrollment with subscription payment type
  testFramework.test("should handle subscription enrollment correctly", async () => {
    const enrollmentData = {
      split_id: 1,
      payment_type: "subscription",
      payment_method_id: "pm_test123",
    };

    const enrollment = await mockSdk.create("enrollment", {
      trainer_id: 1,
      athlete_id: 2,
      program_id: 1,
      split_id: 1,
      payment_type: "subscription",
      amount: 50.00, // subscription price
      currency: "USD",
      status: "active",
      payment_status: "paid",
      affiliate_code: null,
      affiliate_user_id: null,
      commission_calculated: false,
    });

    testFramework.expect(enrollment.payment_type).toBe("subscription");
    testFramework.expect(enrollment.amount).toBe(50.00);
  });

  // Test commission calculation for different payment types
  testFramework.test("should calculate commission for different payment types", async () => {
    const CommissionService = require("../services/CommissionService");
    const commissionService = new CommissionService(mockSdk);

    const payoutSettings = {
      split_company_percentage: 30.00,
      split_trainer_percentage: 70.00,
      affiliate_company_percentage: 20.00,
      affiliate_trainer_percentage: 80.00,
      trainer_payout_time_hours: 24,
    };

    // Test one-time payment commission
    const oneTimeData = {
      trainer_id: 1,
      athlete_id: 2,
      program_id: 1,
      split_id: 1,
      amount: 100.00,
      currency: "USD",
    };

    const oneTimeCommission = commissionService.calculateCommission(oneTimeData, payoutSettings);
    testFramework.expect(oneTimeCommission.total_amount).toBe(100.00);
    testFramework.expect(oneTimeCommission.trainer_amount).toBe(70.00);
    testFramework.expect(oneTimeCommission.company_amount).toBe(30.00);

    // Test subscription payment commission
    const subscriptionData = {
      ...oneTimeData,
      amount: 50.00,
    };

    const subscriptionCommission = commissionService.calculateCommission(subscriptionData, payoutSettings);
    testFramework.expect(subscriptionCommission.total_amount).toBe(50.00);
    testFramework.expect(subscriptionCommission.trainer_amount).toBe(35.00);
    testFramework.expect(subscriptionCommission.company_amount).toBe(15.00);
  });

  // Test webhook commission creation
  testFramework.test("should create commission in webhook for successful payment", async () => {
    const enrollment = {
      id: 1,
      trainer_id: 1,
      athlete_id: 2,
      program_id: 1,
      split_id: 1,
      amount: 100.00,
      currency: "USD",
      affiliate_code: null,
      affiliate_user_id: null,
      commission_calculated: false,
    };

    // Simulate webhook commission creation
    if (!enrollment.commission_calculated) {
      const commission = await mockCommissionService.createCommissionRecord(
        enrollment.id,
        {
          trainer_id: enrollment.trainer_id,
          athlete_id: enrollment.athlete_id,
          program_id: enrollment.program_id,
          split_id: enrollment.split_id,
          amount: enrollment.amount,
          currency: enrollment.currency,
          affiliate_code: enrollment.affiliate_code,
          affiliate_user_id: enrollment.affiliate_user_id,
        }
      );

      testFramework.expect(commission.id).toBe(1);
      testFramework.expect(commission.enrollment_id).toBe(enrollment.id);

      // Mark enrollment as commission calculated
      const updatedEnrollment = await mockSdk.updateById("enrollment", enrollment.id, {
        commission_calculated: true,
      });

      testFramework.expect(updatedEnrollment.commission_calculated).toBe(true);
    }
  });
});

// Run the tests
if (require.main === module) {
  testFramework.run().then(() => {
    console.log("Enrollment commission integration tests completed");
  });
}

module.exports = testFramework;
