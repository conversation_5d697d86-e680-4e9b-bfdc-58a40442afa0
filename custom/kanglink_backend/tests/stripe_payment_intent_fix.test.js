const APITestFramework = require("../../../tests/apitesting.base.js");

class StripePaymentIntentFixTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Stripe Payment Intent Fix Tests", () => {
      // Test that createPaymentIntentAutomatic now handles all parameters
      this.framework.addTestCase("Should handle payment_method, confirm, and metadata parameters", async () => {
        const StripeService = require("../../../baas/services/StripeService");
        
        // Mock the Stripe library
        const mockStripe = {
          paymentIntents: {
            create: async (params) => {
              // Verify all expected parameters are passed through
              this.framework.assert(
                params.hasOwnProperty('payment_method'),
                "Should include payment_method parameter"
              );
              this.framework.assert(
                params.hasOwnProperty('confirm'),
                "Should include confirm parameter"
              );
              this.framework.assert(
                params.hasOwnProperty('metadata'),
                "Should include metadata parameter"
              );
              this.framework.assert(
                params.hasOwnProperty('automatic_payment_methods'),
                "Should include automatic_payment_methods parameter"
              );
              
              // Return mock payment intent
              return {
                id: "pi_test_123",
                status: "succeeded",
                amount: params.amount,
                currency: params.currency,
                customer: params.customer,
                payment_method: params.payment_method,
                metadata: params.metadata
              };
            }
          }
        };

        // Create a test instance and override the stripe property
        const stripeService = new StripeService();
        stripeService.stripe = mockStripe;

        const testPayload = {
          amount: 10000, // $100.00
          currency: "usd",
          customer: "cus_test123",
          payment_method: "pm_test123",
          confirm: true,
          metadata: {
            projectId: "kanglink",
            split_id: "1",
            athlete_id: "1",
            payment_type: "one_time"
          }
        };

        const result = await stripeService.createPaymentIntentAutomatic(testPayload);

        // Verify the result
        this.framework.assertions.assertEquals(
          result.id,
          "pi_test_123",
          "Should return payment intent with correct ID"
        );
        this.framework.assertions.assertEquals(
          result.status,
          "succeeded",
          "Should return succeeded status when confirmed"
        );
        this.framework.assertions.assertEquals(
          result.payment_method,
          "pm_test123",
          "Should include the payment method"
        );
        this.framework.assertions.assertEquals(
          result.metadata.projectId,
          "kanglink",
          "Should include metadata"
        );
      });

      // Test backward compatibility
      this.framework.addTestCase("Should maintain backward compatibility with minimal parameters", async () => {
        const StripeService = require("../../../baas/services/StripeService");
        
        // Mock the Stripe library
        const mockStripe = {
          paymentIntents: {
            create: async (params) => {
              // Verify basic parameters are still handled
              this.framework.assert(
                params.hasOwnProperty('amount'),
                "Should include amount parameter"
              );
              this.framework.assert(
                params.hasOwnProperty('currency'),
                "Should include currency parameter"
              );
              this.framework.assert(
                params.hasOwnProperty('customer'),
                "Should include customer parameter"
              );
              this.framework.assert(
                params.hasOwnProperty('automatic_payment_methods'),
                "Should include automatic_payment_methods parameter"
              );
              
              // Return mock payment intent
              return {
                id: "pi_test_456",
                status: "requires_payment_method",
                amount: params.amount,
                currency: params.currency,
                customer: params.customer
              };
            }
          }
        };

        // Create a test instance and override the stripe property
        const stripeService = new StripeService();
        stripeService.stripe = mockStripe;

        const minimalPayload = {
          amount: 5000, // $50.00
          customer: "cus_test456"
          // No currency (should default to "usd")
          // No payment_method, confirm, or metadata
        };

        const result = await stripeService.createPaymentIntentAutomatic(minimalPayload);

        // Verify the result
        this.framework.assertions.assertEquals(
          result.id,
          "pi_test_456",
          "Should return payment intent with correct ID"
        );
        this.framework.assertions.assertEquals(
          result.currency,
          "usd",
          "Should default currency to USD"
        );
      });

      // Test enrollment flow parameters
      this.framework.addTestCase("Should handle enrollment-specific parameters correctly", async () => {
        const enrollmentPayload = {
          amount: Math.round(100 * 100), // $100.00 converted to cents
          currency: "usd",
          customer: "cus_athlete123",
          payment_method: "pm_card123",
          confirm: true,
          metadata: {
            projectId: "kanglink",
            split_id: "5",
            athlete_id: "10",
            trainer_id: "3",
            payment_type: "one_time",
            stripe_price_id: "price_123"
          }
        };

        // Verify all enrollment parameters are present
        this.framework.assert(
          enrollmentPayload.amount === 10000,
          "Amount should be converted to cents"
        );
        this.framework.assert(
          enrollmentPayload.confirm === true,
          "Should confirm payment immediately"
        );
        this.framework.assert(
          enrollmentPayload.metadata.projectId === "kanglink",
          "Should include kanglink project ID"
        );
        this.framework.assert(
          enrollmentPayload.metadata.payment_type === "one_time",
          "Should specify one-time payment type"
        );
      });
    });
  }

  async runTests() {
    try {
      const results = await this.framework.runTests();
      this.framework.generateTestReport();
      return results;
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new StripePaymentIntentFixTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report;
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
