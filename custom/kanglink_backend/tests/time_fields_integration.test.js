const { test, expect } = require("../../tests/apitesting.base.js");

test("Time fields integration test", async ({ sdk, testData }) => {
  // Test data for creating a program with time-based exercises
  const programData = {
    stepOneData: {
      program_name: "Time-Based Test Program",
      type_of_program: "strength",
      program_description: "Test program with time-based exercises",
      image: "test-image.jpg",
      currency: "USD",
      status: "draft",
    },
    stepTwoData: {
      program_split: "1",
      description: "Test program with time-based exercises",
      weeks: [
        {
          id: "week-1",
          title: "Week 1",
          week_order: 1,
          days: [
            {
              id: "day-1",
              title: "Day 1",
              day_order: 1,
              is_rest_day: false,
              sessions: [
                {
                  id: "session-1",
                  title: "Session 1",
                  session_order: 1,
                  exercises: [
                    {
                      exercise_id: null,
                      exercise_name: "Plank Hold",
                      video_url: null,
                      sets: "3",
                      reps_or_time: "60",
                      reps_time_type: "time",
                      time_minutes: 1,
                      time_seconds: 0,
                      exercise_details: "Hold plank position",
                      rest_duration_minutes: 2,
                      rest_duration_seconds: 0,
                      label: "A1",
                      label_number: "1",
                      is_linked: false,
                      exercise_order: 1,
                    },
                    {
                      exercise_id: null,
                      exercise_name: "Wall Sit",
                      video_url: null,
                      sets: "3",
                      reps_or_time: "45",
                      reps_time_type: "time",
                      time_minutes: 0,
                      time_seconds: 45,
                      exercise_details: "Sit against wall",
                      rest_duration_minutes: 1,
                      rest_duration_seconds: 30,
                      label: "A2",
                      label_number: "2",
                      is_linked: false,
                      exercise_order: 2,
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      splitConfigurations: {
        "1": [
          {
            id: "week-1",
            title: "Week 1",
            week_order: 1,
            days: [
              {
                id: "day-1",
                title: "Day 1",
                day_order: 1,
                is_rest_day: false,
                sessions: [
                  {
                    id: "session-1",
                    title: "Session 1",
                    session_order: 1,
                    exercises: [
                      {
                        exercise_id: null,
                        exercise_name: "Plank Hold",
                        video_url: null,
                        sets: "3",
                        reps_or_time: "60",
                        reps_time_type: "time",
                        time_minutes: 1,
                        time_seconds: 0,
                        exercise_details: "Hold plank position",
                        rest_duration_minutes: 2,
                        rest_duration_seconds: 0,
                        label: "A1",
                        label_number: "1",
                        is_linked: false,
                        exercise_order: 1,
                      },
                      {
                        exercise_id: null,
                        exercise_name: "Wall Sit",
                        video_url: null,
                        sets: "3",
                        reps_or_time: "45",
                        reps_time_type: "time",
                        time_minutes: 0,
                        time_seconds: 45,
                        exercise_details: "Sit against wall",
                        rest_duration_minutes: 1,
                        rest_duration_seconds: 30,
                        label: "A2",
                        label_number: "2",
                        is_linked: false,
                        exercise_order: 2,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      status: "draft",
      image: "test-image.jpg",
    },
  };

  // Create program
  const createResponse = await sdk.post("/v2/api/kanglink/custom/trainer/programs", {
    ...programData,
  });

  expect(createResponse.error).toBe(false);
  expect(createResponse.data).toBeDefined();

  const programId = createResponse.data;

  // Get program to verify time fields are saved correctly
  const getResponse = await sdk.get(`/v2/api/kanglink/custom/trainer/programs/${programId}`);

  expect(getResponse.error).toBe(false);
  expect(getResponse.data).toBeDefined();

  // Verify the program structure includes time fields
  const stepTwoData = getResponse.data.stepTwoData;
  expect(stepTwoData).toBeDefined();
  expect(stepTwoData.weeks).toBeDefined();
  expect(stepTwoData.weeks.length).toBeGreaterThan(0);

  const week = stepTwoData.weeks[0];
  expect(week.days).toBeDefined();
  expect(week.days.length).toBeGreaterThan(0);

  const day = week.days[0];
  expect(day.sessions).toBeDefined();
  expect(day.sessions.length).toBeGreaterThan(0);

  const session = day.sessions[0];
  expect(session.exercises).toBeDefined();
  expect(session.exercises.length).toBeGreaterThan(0);

  // Check first exercise (Plank Hold)
  const firstExercise = session.exercises[0];
  expect(firstExercise.reps_time_type).toBe("time");
  expect(firstExercise.time_minutes).toBe(1);
  expect(firstExercise.time_seconds).toBe(0);

  // Check second exercise (Wall Sit)
  const secondExercise = session.exercises[1];
  expect(secondExercise.reps_time_type).toBe("time");
  expect(secondExercise.time_minutes).toBe(0);
  expect(secondExercise.time_seconds).toBe(45);

  // Test preview endpoint
  const previewResponse = await sdk.get(`/v2/api/kanglink/custom/preview/program/${programId}`);

  expect(previewResponse.error).toBe(false);
  expect(previewResponse.data).toBeDefined();

  // Verify preview includes time fields
  const previewData = previewResponse.data;
  expect(previewData.split.weeks).toBeDefined();
  expect(previewData.split.weeks.length).toBeGreaterThan(0);

  const previewWeek = previewData.split.weeks[0];
  const previewDay = previewWeek.days[0];
  const previewSession = previewDay.sessions[0];

  // Check that exercises in preview have time fields
  expect(previewSession.exercises).toBeDefined();
  expect(previewSession.exercises.length).toBeGreaterThan(0);

  const previewFirstExercise = previewSession.exercises[0];
  expect(previewFirstExercise.time_minutes).toBeDefined();
  expect(previewFirstExercise.time_seconds).toBeDefined();

  console.log("Time fields integration test passed successfully!");
}); 