const testFramework = require("../../tests/apitesting.base.js");

// Test webhook handling for discount-related events
testFramework.describe("Webhook Discount Event Tests", () => {
  let sdk;

  testFramework.beforeAll(async () => {
    sdk = testFramework.getSdk();
  });

  testFramework.test("should handle customer.discount.created webhook", async () => {
    const StripeWebhookService = require("../../../baas/services/StripeWebhookService");

    // Mock webhook event for customer.discount.created
    const mockEvent = {
      id: "evt_test_discount_created",
      type: "customer.discount.created",
      data: {
        object: {
          id: "di_test_discount",
          customer: "cus_test_customer",
          coupon: {
            id: "test_coupon_id",
            metadata: {
              projectId: "kanglink",
              split_id: "123",
              program_id: "456",
              athlete_id: "789",
              trainer_id: "101",
              discount_amount: "10.00",
              original_amount: "50.00"
            }
          },
          start: 1234567890,
          end: null
        }
      },
      created: 1234567890
    };

    // Test the webhook handler
    const result = await StripeWebhookService.handleCustomerDiscountCreated({
      sdk,
      event: mockEvent
    });

    testFramework.expect(result).toBe("Customer discount created successfully");
  });

  testFramework.test("should handle customer.discount.created webhook without metadata", async () => {
    const StripeWebhookService = require("../../../baas/services/StripeWebhookService");

    // Mock webhook event without coupon metadata
    const mockEvent = {
      id: "evt_test_discount_created_no_meta",
      type: "customer.discount.created",
      data: {
        object: {
          id: "di_test_discount_no_meta",
          customer: "cus_test_customer",
          coupon: {
            id: "test_coupon_id_no_meta"
            // No metadata
          },
          start: 1234567890,
          end: null
        }
      },
      created: 1234567890
    };

    // Test the webhook handler - should still work with default projectId
    const result = await StripeWebhookService.handleCustomerDiscountCreated({
      sdk,
      event: mockEvent
    });

    testFramework.expect(result).toBe("Customer discount created successfully");
  });

  testFramework.test("should verify webhook event types are handled", () => {
    // This test verifies that the webhook router includes the new event type
    const webhookFile = require("../../../baas/lambda/stripe_webhook.js");
    
    // We can't easily test the actual routing without mocking the entire express app,
    // but we can verify the handler exists
    const StripeWebhookService = require("../../../baas/services/StripeWebhookService");
    
    testFramework.expect(typeof StripeWebhookService.handleCustomerDiscountCreated).toBe("function");
  });

  testFramework.test("should calculate discount percentage correctly", () => {
    // Test the discount percentage calculation logic used in enrollment
    const originalAmount = 50.00;
    const discountAmount = 10.00;
    const expectedPercentage = Math.round((discountAmount / originalAmount) * 100);
    
    testFramework.expect(expectedPercentage).toBe(20);

    // Test edge cases
    const originalAmount2 = 100.00;
    const discountAmount2 = 25.50;
    const expectedPercentage2 = Math.round((discountAmount2 / originalAmount2) * 100);
    
    testFramework.expect(expectedPercentage2).toBe(26); // 25.5% rounds to 26%
  });
});
