const APITestFramework = require("../../tests/apitesting.base");

class AthleteFavoritesTests {
  constructor() {
    this.framework = new APITestFramework();
    this.BASE_URL = "http://localhost:5172";
    this.authToken = "Bearer test_token"; // Mock token for testing
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Athlete Favorites API Tests", () => {

      // Test favorite programs endpoints
      this.framework.addTestCase("should get empty favorite programs list initially", async () => {
        // Mock empty response
        this.framework.mockRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs`,
          {
            error: false,
            data: []
          },
          { status: 200 }
        );

        const response = await this.framework.makeRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: this.authToken,
            },
          }
        );

        this.framework.assert(response.status === 200, "Should return 200 status");
        this.framework.assert(response.body.error === false, "Error should be false");
        this.framework.assert(Array.isArray(response.body.data), "Data should be array");
        this.framework.assert(response.body.data.length === 0, "Should be empty initially");
      });

      this.framework.addTestCase("should add program to favorites", async () => {
        // Mock successful add response
        this.framework.mockRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs/1`,
          {
            error: false,
            data: {
              id: 1,
              message: "Program added to favorites successfully"
            }
          },
          { status: 200 }
        );

        const response = await this.framework.makeRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs/1`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: this.authToken,
            },
          }
        );

        this.framework.assert(response.status === 200, "Should return 200 status");
        this.framework.assert(response.body.error === false, "Error should be false");
        this.framework.assert(response.body.data.message === "Program added to favorites successfully", "Should return success message");
        this.framework.assert(response.body.data.id !== undefined, "Should return favorite ID");
      });

      this.framework.addTestCase("should not add same program to favorites twice", async () => {
        // Mock conflict response
        this.framework.mockRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs/1`,
          {
            error: true,
            message: "Program already in favorites"
          },
          { status: 409 }
        );

        const response = await this.framework.makeRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs/1`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: this.authToken,
            },
          }
        );

        this.framework.assert(response.status === 409, "Should return 409 status");
        this.framework.assert(response.body.error === true, "Error should be true");
        this.framework.assert(response.body.message === "Program already in favorites", "Should return conflict message");
      });

      this.framework.addTestCase("should get favorite programs list with added program", async () => {
        // Mock response with favorite program
        this.framework.mockRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs`,
          {
            error: false,
            data: [{
              favorite_id: 1,
              favorited_at: "2024-01-15T10:30:00Z",
              id: 1,
              name: "Test Fitness Program",
              description: "A comprehensive fitness program",
              type: "strength",
              image_url: "https://example.com/program.jpg",
              status: "active",
              price: 20.00,
              average_rating: 4.5,
              review_count: 12,
              trainer: {
                id: 2,
                full_name: "Test Trainer",
                first_name: "Test",
                last_name: "Trainer",
                photo: "https://example.com/trainer.jpg"
              }
            }]
          },
          { status: 200 }
        );

        const response = await this.framework.makeRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: this.authToken,
            },
          }
        );

        this.framework.assert(response.status === 200, "Should return 200 status");
        this.framework.assert(response.body.error === false, "Error should be false");
        this.framework.assert(Array.isArray(response.body.data), "Data should be array");
        this.framework.assert(response.body.data.length === 1, "Should have one favorite");

        const favoriteProgram = response.body.data[0];
        this.framework.assert(favoriteProgram.id === 1, "Should have correct program ID");
        this.framework.assert(favoriteProgram.name === "Test Fitness Program", "Should have correct program name");
        this.framework.assert(favoriteProgram.favorite_id !== undefined, "Should have favorite ID");
        this.framework.assert(favoriteProgram.trainer !== undefined, "Should have trainer info");
      });

      this.framework.addTestCase("should remove program from favorites", async () => {
        // Mock successful delete response
        this.framework.mockRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs/1`,
          {
            error: false,
            data: {
              message: "Program removed from favorites successfully"
            }
          },
          { status: 200 }
        );

        const response = await this.framework.makeRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs/1`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: this.authToken,
            },
          }
        );

        this.framework.assert(response.status === 200, "Should return 200 status");
        this.framework.assert(response.body.error === false, "Error should be false");
        this.framework.assert(response.body.data.message === "Program removed from favorites successfully", "Should return success message");
      });

      // Test favorite trainers endpoints
      this.framework.addTestCase("should get empty favorite trainers list initially", async () => {
        // Mock empty response
        this.framework.mockRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/trainers`,
          {
            error: false,
            data: []
          },
          { status: 200 }
        );

        const response = await this.framework.makeRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/trainers`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: this.authToken,
            },
          }
        );

        this.framework.assert(response.status === 200, "Should return 200 status");
        this.framework.assert(response.body.error === false, "Error should be false");
        this.framework.assert(Array.isArray(response.body.data), "Data should be array");
        this.framework.assert(response.body.data.length === 0, "Should be empty initially");
      });

      this.framework.addTestCase("should add trainer to favorites", async () => {
        // Mock successful add response
        this.framework.mockRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/trainers/2`,
          {
            error: false,
            data: {
              id: 1,
              message: "Trainer added to favorites successfully"
            }
          },
          { status: 200 }
        );

        const response = await this.framework.makeRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/trainers/2`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: this.authToken,
            },
          }
        );

        this.framework.assert(response.status === 200, "Should return 200 status");
        this.framework.assert(response.body.error === false, "Error should be false");
        this.framework.assert(response.body.data.message === "Trainer added to favorites successfully", "Should return success message");
        this.framework.assert(response.body.data.id !== undefined, "Should return favorite ID");
      });

      // Test error cases
      this.framework.addTestCase("should handle invalid program ID", async () => {
        // Mock bad request response
        this.framework.mockRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs/invalid`,
          {
            error: true,
            message: "Invalid program ID"
          },
          { status: 400 }
        );

        const response = await this.framework.makeRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs/invalid`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: this.authToken,
            },
          }
        );

        this.framework.assert(response.status === 400, "Should return 400 status");
        this.framework.assert(response.body.error === true, "Error should be true");
        this.framework.assert(response.body.message === "Invalid program ID", "Should return error message");
      });

      this.framework.addTestCase("should handle non-existent program", async () => {
        // Mock not found response
        this.framework.mockRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs/99999`,
          {
            error: true,
            message: "Program not found or inactive"
          },
          { status: 404 }
        );

        const response = await this.framework.makeRequest(
          `${this.BASE_URL}/v2/api/kanglink/custom/athlete/favorite/programs/99999`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: this.authToken,
            },
          }
        );

        this.framework.assert(response.status === 404, "Should return 404 status");
        this.framework.assert(response.body.error === true, "Error should be true");
        this.framework.assert(response.body.message === "Program not found or inactive", "Should return error message");
      });

    });
  }

  async runTests() {
    try {
      const results = await this.framework.runTests();
      this.framework.generateTestReport();
      return results;
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new AthleteFavoritesTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });

  // Test favorite trainers endpoints
  testFramework.test(
    "should get empty favorite trainers list initially",
    async () => {
      const response = await sdk.callRestAPI(
        "kanglink/custom/athlete/favorite/trainers",
        "GET"
      );

      testFramework.expect(response.error).toBe(false);
      testFramework.expect(Array.isArray(response.data)).toBe(true);
      testFramework.expect(response.data.length).toBe(0);
    }
  );

  testFramework.test("should add trainer to favorites", async () => {
    const response = await sdk.callRestAPI(
      `kanglink/custom/athlete/favorite/trainers/${testTrainer.id}`,
      "POST"
    );

    testFramework.expect(response.error).toBe(false);
    testFramework
      .expect(response.data.message)
      .toBe("Trainer added to favorites successfully");
    testFramework.expect(response.data.id).toBeDefined();
  });

  testFramework.test(
    "should not add same trainer to favorites twice",
    async () => {
      const response = await sdk.callRestAPI(
        `kanglink/custom/athlete/favorite/trainers/${testTrainer.id}`,
        "POST"
      );

      testFramework.expect(response.error).toBe(true);
      testFramework
        .expect(response.message)
        .toBe("Trainer already in favorites");
    }
  );

  testFramework.test(
    "should get favorite trainers list with added trainer",
    async () => {
      const response = await sdk.callRestAPI(
        "kanglink/custom/athlete/favorite/trainers",
        "GET"
      );

      testFramework.expect(response.error).toBe(false);
      testFramework.expect(Array.isArray(response.data)).toBe(true);
      testFramework.expect(response.data.length).toBe(1);

      const favoriteTrainer = response.data[0];
      testFramework.expect(favoriteTrainer.id).toBe(testTrainer.id);
      testFramework.expect(favoriteTrainer.full_name).toBe("Test Trainer");
      testFramework.expect(favoriteTrainer.favorite_id).toBeDefined();
      testFramework.expect(favoriteTrainer.favorited_at).toBeDefined();
      testFramework.expect(favoriteTrainer.program_count).toBeDefined();
    }
  );

  testFramework.test("should remove trainer from favorites", async () => {
    const response = await sdk.callRestAPI(
      `kanglink/custom/athlete/favorite/trainers/${testTrainer.id}`,
      "DELETE"
    );

    testFramework.expect(response.error).toBe(false);
    testFramework
      .expect(response.data.message)
      .toBe("Trainer removed from favorites successfully");
  });

  testFramework.test(
    "should not remove non-existent favorite trainer",
    async () => {
      const response = await sdk.callRestAPI(
        `kanglink/custom/athlete/favorite/trainers/${testTrainer.id}`,
        "DELETE"
      );

      testFramework.expect(response.error).toBe(true);
      testFramework.expect(response.message).toBe("Favorite not found");
    }
  );

  // Test error cases
  testFramework.test("should handle invalid program ID", async () => {
    const response = await sdk.callRestAPI(
      "kanglink/custom/athlete/favorite/programs/invalid",
      "POST"
    );

    testFramework.expect(response.error).toBe(true);
    testFramework.expect(response.message).toBe("Invalid program ID");
  });

  testFramework.test("should handle invalid trainer ID", async () => {
    const response = await sdk.callRestAPI(
      "kanglink/custom/athlete/favorite/trainers/invalid",
      "POST"
    );

    testFramework.expect(response.error).toBe(true);
    testFramework.expect(response.message).toBe("Invalid trainer ID");
  });

  testFramework.test("should handle non-existent program", async () => {
    const response = await sdk.callRestAPI(
      "kanglink/custom/athlete/favorite/programs/99999",
      "POST"
    );

    testFramework.expect(response.error).toBe(true);
    testFramework
      .expect(response.message)
      .toBe("Program not found or inactive");
  });

  testFramework.test("should handle non-existent trainer", async () => {
    const response = await sdk.callRestAPI(
      "kanglink/custom/athlete/favorite/trainers/99999",
      "POST"
    );

    testFramework.expect(response.error).toBe(true);
    testFramework
      .expect(response.message)
      .toBe("Trainer not found or inactive");
  });
});
