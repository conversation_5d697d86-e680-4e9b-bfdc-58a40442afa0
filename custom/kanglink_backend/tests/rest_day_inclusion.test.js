const testFramework = require("../../../tests/apitesting.base");

testFramework.describe("Rest Day Inclusion in Program Data", () => {
  let sdk;

  // Setup before each test
  testFramework.beforeEach(async () => {
    // Mock SDK for testing
    sdk = {
      setProjectId: () => {},
      rawQuery: async (query) => {
        // Mock enrollment data
        if (query.includes("FROM kanglink_enrollment e")) {
          return [
            {
              id: 1,
              trainer_id: 2,
              athlete_id: 3,
              program_id: 4,
              split_id: 5,
              payment_type: "subscription",
              amount: 20.0,
              currency: "USD",
              enrollment_date: "2024-01-15T10:30:00Z",
              status: "active",
              payment_status: "paid",
              access_type: "live",
              program_name: "Strength Training Program",
              program_description: "Complete strength training program",
              program_image: "https://example.com/program.jpg",
              split_title: "Beginner Split",
              split_equipment: "Dumbbells, resistance bands",
              full_price: 100.0,
              subscription: 20.0,
              trainer_first_name: "<PERSON>",
              trainer_last_name: "<PERSON>er",
              trainer_full_name: "<PERSON>",
              trainer_photo: "https://example.com/photo.jpg",
              trainer_email: "<EMAIL>",
            },
          ];
        }

        // Mock weeks/days/sessions/exercises data with rest days
        if (query.includes("FROM kanglink_week w")) {
          return [
            // Workout day with exercises
            {
              week_id: 1,
              week_title: "Week 1",
              week_order: 1,
              split_id: 5,
              day_id: 1,
              day_title: "Day 1 - Upper Body",
              day_order: 1,
              is_rest_day: false,
              session_id: 1,
              session_title: "Session 1 - Compound Movements",
              session_order: 1,
              exercise_instance_id: 1,
              exercise_label: "Push-ups",
              sets: 4,
              reps_or_time: "12-15",
              reps_time_type: "reps",
              time_minutes: null,
              time_seconds: null,
              rest_duration_seconds: 60,
              exercise_details: "Keep core tight",
              exercise_order: 1,
              exercise_id: 1,
              exercise_name: "Push-ups",
              exercise_video_url: "https://example.com/video.mp4",
              instance_exercise_name: null,
              video_url: null,
            },
            // Rest day - no sessions or exercises
            {
              week_id: 1,
              week_title: "Week 1",
              week_order: 1,
              split_id: 5,
              day_id: 2,
              day_title: "Day 2 - Rest",
              day_order: 2,
              is_rest_day: true,
              session_id: null,
              session_title: null,
              session_order: null,
              exercise_instance_id: null,
              exercise_label: null,
              sets: null,
              reps_or_time: null,
              reps_time_type: null,
              time_minutes: null,
              time_seconds: null,
              rest_duration_seconds: null,
              exercise_details: null,
              exercise_order: null,
              exercise_id: null,
              exercise_name: null,
              exercise_video_url: null,
              instance_exercise_name: null,
              video_url: null,
            },
            // Another workout day
            {
              week_id: 1,
              week_title: "Week 1",
              week_order: 1,
              split_id: 5,
              day_id: 3,
              day_title: "Day 3 - Lower Body",
              day_order: 3,
              is_rest_day: false,
              session_id: 2,
              session_title: "Session 2 - Leg Day",
              session_order: 1,
              exercise_instance_id: 2,
              exercise_label: "Squats",
              sets: 3,
              reps_or_time: "10-12",
              reps_time_type: "reps",
              time_minutes: null,
              time_seconds: null,
              rest_duration_seconds: 90,
              exercise_details: "Keep chest up",
              exercise_order: 1,
              exercise_id: 2,
              exercise_name: "Squats",
              exercise_video_url: "https://example.com/squats.mp4",
              instance_exercise_name: null,
              video_url: null,
            },
          ];
        }

        // Mock progress data
        if (query.includes("FROM kanglink_exercise_progress ep")) {
          return [
            {
              id: 1,
              athlete_id: 3,
              enrollment_id: 1,
              exercise_instance_id: 1,
              is_completed: true,
              sets_completed: 4,
              reps_completed: "15,14,13,12",
              weight_used: "bodyweight",
              time_taken_seconds: 300,
              difficulty_rating: 7,
              notes: "Felt good",
              completed_at: "2024-01-15T11:00:00Z",
            },
          ];
        }

        if (query.includes("FROM kanglink_day_progress")) {
          return [
            {
              id: 1,
              athlete_id: 3,
              enrollment_id: 1,
              day_id: 1,
              is_completed: true,
              total_exercises: 1,
              completed_exercises: 1,
              completed_at: "2024-01-15T11:00:00Z",
              notes: "Great workout",
            },
          ];
        }

        if (query.includes("FROM kanglink_athlete_progress")) {
          return [
            {
              id: 1,
              athlete_id: 3,
              enrollment_id: 1,
              current_week_id: 1,
              current_day_id: 2,
              total_days_completed: 1,
              total_exercises_completed: 1,
              progress_percentage: 33.33,
              last_activity_date: "2024-01-15T11:00:00Z",
            },
          ];
        }

        return [];
      },
    };
  });

  testFramework.it("should include rest days in program data", async () => {
    const req = {
      params: { enrollment_id: "1" },
      user_id: 3,
      app: {
        get: () => sdk,
      },
    };

    const res = {
      status: (code) => ({
        json: (data) => {
          testFramework.expect(code).toBe(200);
          testFramework.expect(data.error).toBe(false);
          testFramework.expect(data.data).toBeDefined();
          testFramework.expect(data.data.weeks).toBeDefined();
          testFramework.expect(data.data.weeks.length).toBeGreaterThan(0);

          const week = data.data.weeks[0];
          testFramework.expect(week.days).toBeDefined();
          testFramework.expect(week.days.length).toBe(3); // Should have 3 days including rest day

          // Check that rest day is included
          const restDay = week.days.find(day => day.is_rest_day === true);
          testFramework.expect(restDay).toBeDefined();
          testFramework.expect(restDay.title).toBe("Day 2 - Rest");
          testFramework.expect(restDay.day_order).toBe(2);
          testFramework.expect(restDay.sessions).toBeDefined();
          testFramework.expect(restDay.sessions.length).toBe(0); // Rest days should have no sessions

          // Check that workout days are also included
          const workoutDays = week.days.filter(day => day.is_rest_day === false);
          testFramework.expect(workoutDays.length).toBe(2);

          const firstWorkoutDay = workoutDays[0];
          testFramework.expect(firstWorkoutDay.title).toBe("Day 1 - Upper Body");
          testFramework.expect(firstWorkoutDay.sessions.length).toBeGreaterThan(0);
          testFramework.expect(firstWorkoutDay.sessions[0].exercise_instances.length).toBeGreaterThan(0);

          const secondWorkoutDay = workoutDays[1];
          testFramework.expect(secondWorkoutDay.title).toBe("Day 3 - Lower Body");
          testFramework.expect(secondWorkoutDay.sessions.length).toBeGreaterThan(0);
          testFramework.expect(secondWorkoutDay.sessions[0].exercise_instances.length).toBeGreaterThan(0);
        },
      }),
    };

    // Import the actual endpoint function
    const athleteProgressRoutes = require("../routes/athlete_progress");
    
    // Mock the app object
    const app = {
      get: () => sdk,
      post: () => {},
      get: () => {},
    };

    // Call the endpoint
    await athleteProgressRoutes(app);
    
    // Find the enrollment program endpoint and call it
    const enrollmentProgramEndpoint = app.get.mock.calls.find(call => 
      call[0].includes("/v2/api/kanglink/custom/athlete/enrollment/:enrollment_id/program")
    );
    
    if (enrollmentProgramEndpoint) {
      await enrollmentProgramEndpoint[1](req, res);
    }
  });

  testFramework.it("should handle rest days with proper progress data", async () => {
    const req = {
      params: { enrollment_id: "1" },
      user_id: 3,
      app: {
        get: () => sdk,
      },
    };

    const res = {
      status: (code) => ({
        json: (data) => {
          testFramework.expect(code).toBe(200);
          testFramework.expect(data.error).toBe(false);
          
          const week = data.data.weeks[0];
          const restDay = week.days.find(day => day.is_rest_day === true);
          
          // Check rest day progress structure
          testFramework.expect(restDay.progress).toBeDefined();
          testFramework.expect(restDay.progress.is_completed).toBeDefined();
          testFramework.expect(restDay.progress.total_exercises).toBeDefined();
          testFramework.expect(restDay.progress.completed_exercises).toBeDefined();
          
          // Rest days should have 0 exercises by default
          testFramework.expect(restDay.progress.total_exercises).toBe(0);
          testFramework.expect(restDay.progress.completed_exercises).toBe(0);
        },
      }),
    };

    // Import the actual endpoint function
    const athleteProgressRoutes = require("../routes/athlete_progress");
    
    // Mock the app object
    const app = {
      get: () => sdk,
      post: () => {},
      get: () => {},
    };

    // Call the endpoint
    await athleteProgressRoutes(app);
    
    // Find the enrollment program endpoint and call it
    const enrollmentProgramEndpoint = app.get.mock.calls.find(call => 
      call[0].includes("/v2/api/kanglink/custom/athlete/enrollment/:enrollment_id/program")
    );
    
    if (enrollmentProgramEndpoint) {
      await enrollmentProgramEndpoint[1](req, res);
    }
  });
}); 