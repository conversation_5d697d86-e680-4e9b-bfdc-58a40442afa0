const { TestSuite } = require("../../tests/apitesting.base");

class TrainerActivityTest extends TestSuite {
  constructor() {
    super("Trainer Activity API Tests");
    this.baseUrl = "/v2/api/kanglink/custom/trainer/activities";
    this.authToken = null;
    this.trainerId = null;
  }

  async setup() {
    console.log("Setting up trainer activity tests...");
    this.trainerId = 1; // Replace with actual test trainer ID
    this.authToken = "test_token"; // Replace with actual test token
  }

  async teardown() {
    console.log("Cleaning up trainer activity tests...");
  }

  // Test getting trainer activities
  async testGetTrainerActivities() {
    const response = await this.makeRequest({
      method: "GET",
      url: this.baseUrl,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Activities endpoint should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
    this.assertTrue(response.data.data, "Response should have data");
    
    const data = response.data.data;
    this.assertTrue(Array.isArray(data.activities), "Should have activities array");
    this.assertTrue(data.pagination, "Should have pagination");
  }

  // Test getting trainer activities with filters
  async testGetTrainerActivitiesWithFilters() {
    const response = await this.makeRequest({
      method: "GET",
      url: `${this.baseUrl}?page=1&limit=5&activity_type=workout_completed&visibility=trainer_only`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Filtered activities should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
    
    const data = response.data.data;
    this.assertTrue(data.pagination.limit === 5, "Should respect limit parameter");
    this.assertTrue(data.pagination.page === 1, "Should respect page parameter");
  }

  // Test getting trainer activity statistics
  async testGetTrainerActivityStats() {
    const response = await this.makeRequest({
      method: "GET",
      url: `${this.baseUrl}/stats`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Activity stats endpoint should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
    this.assertTrue(response.data.data, "Response should have data");
    
    const data = response.data.data;
    this.assertTrue(Array.isArray(data.activity_type_stats), "Should have activity type stats");
    this.assertTrue(typeof data.recent_activity_count === 'number', "Should have recent activity count");
    this.assertTrue(Array.isArray(data.top_athletes), "Should have top athletes");
  }

  // Test getting trainer activity summary by program
  async testGetTrainerActivityPrograms() {
    const response = await this.makeRequest({
      method: "GET",
      url: `${this.baseUrl}/programs`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Program activity summary endpoint should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
    this.assertTrue(Array.isArray(response.data.data), "Should have program activity summary array");
  }

  // Test getting trainer activity summary by athlete
  async testGetTrainerActivityAthletes() {
    const response = await this.makeRequest({
      method: "GET",
      url: `${this.baseUrl}/athletes`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Athlete activity summary endpoint should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
    this.assertTrue(Array.isArray(response.data.data), "Should have athlete activity summary array");
  }

  // Test unauthorized access
  async testUnauthorizedAccess() {
    const response = await this.makeRequest({
      method: "GET",
      url: this.baseUrl,
      headers: {}
    });

    this.assertEqual(response.status, 401, "Should return 401 for unauthorized access");
  }

  // Test activity creation when track_progress is enabled
  async testActivityCreationWithTrackProgress() {
    // This test would require setting up test data with a program that has track_progress enabled
    // and then triggering an exercise completion to verify activity creation
    console.log("Activity creation test requires test data setup");
  }

  async run() {
    console.log("🏃‍♂️ Running Trainer Activity Tests...");
    
    try {
      await this.setup();
      
      await this.testGetTrainerActivities();
      await this.testGetTrainerActivitiesWithFilters();
      await this.testGetTrainerActivityStats();
      await this.testGetTrainerActivityPrograms();
      await this.testGetTrainerActivityAthletes();
      await this.testUnauthorizedAccess();
      
      console.log("✅ All Trainer Activity tests passed!");
    } catch (error) {
      console.error("❌ Trainer Activity test failed:", error);
      throw error;
    } finally {
      await this.teardown();
    }
  }
}

module.exports = TrainerActivityTest; 