const APITestFramework = require("../../../tests/apitesting.base.js");

class EnrollmentErrorHandlingTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Enrollment Error Handling Tests", () => {
      // Test that enrollment endpoint properly handles captureSplitSnapshot errors
      this.framework.addTestCase(
        "Should return 400 error when captureSplitSnapshot fails for one-time purchase",
        async () => {
          // Mock SDK to simulate captureSplitSnapshot failure
          const mockSdk = {
            setProjectId: () => {},
            setTable: () => {},
            rawQuery: async (query) => {
              if (query.includes("kanglink_split")) {
                return [
                  {
                    id: 1,
                    program_id: 1,
                    title: "Test Split",
                    full_price: 100,
                    subscription: 50,
                    trainer_id: 1,
                    program_name: "Test Program",
                    currency: "USD",
                  },
                ];
              }
              if (query.includes("kanglink_week")) {
                // Simulate database error for weeks query
                throw new Error("Database connection failed");
              }
              return [];
            },
            findOne: async (table, criteria) => {
              if (table === "user") {
                return {
                  id: 1,
                  email: "<EMAIL>",
                  stripe_uid: "cus_test",
                };
              }
              if (table === "enrollment") {
                return null; // No existing enrollment
              }
              if (table === "stripe_price") {
                return { id: 1, stripe_id: "price_test", name: "Test Price" };
              }
              return null;
            },
            create: async () => ({ id: 1 }),
            updateById: async () => ({}),
          };

          // Mock Stripe service
          const mockStripe = {
            retrieveStripeCustomer: async () => ({ id: "cus_test" }),
            createPaymentIntentAutomatic: async () => ({ id: "pi_test" }),
          };

          // Mock request and response
          const req = {
            user_id: 1,
            body: {
              split_id: 1,
              payment_type: "one_time",
              payment_method_id: "pm_test",
            },
          };

          const res = {
            statusCode: null,
            responseData: null,
            status: function (code) {
              this.statusCode = code;
              return this;
            },
            json: function (data) {
              this.responseData = data;
              return this;
            },
          };

          // Mock app with SDK
          const app = {
            get: (key) => {
              if (key === "sdk") return mockSdk;
              return null;
            },
          };

          // Import and test the enrollment function
          // Note: This is a simplified test - in a real scenario, we'd need to properly mock the entire module
          try {
            // Simulate the captureSplitSnapshot call that would fail
            const splitSnapshot = await mockSdk.rawQuery(
              "SELECT * FROM kanglink_week WHERE split_id = 1"
            );

            // This should throw an error due to our mock
            this.framework.assert(
              false,
              "Expected captureSplitSnapshot to throw an error"
            );
          } catch (error) {
            this.framework.assertions.assertEquals(
              error.message,
              "Database connection failed",
              "Should propagate the database error"
            );
          }
        }
      );

      // Test that enrollment content endpoint handles captureSplitSnapshot errors
      this.framework.addTestCase(
        "Should return 500 error when live content capture fails",
        async () => {
          // Mock SDK that fails on captureSplitSnapshot
          const mockSdk = {
            setProjectId: () => {},
            setTable: () => {},
            findOne: async (table, criteria) => {
              if (table === "enrollment") {
                return {
                  id: 1,
                  athlete_id: 1,
                  split_id: 1,
                  status: "active",
                  access_type: "live",
                  payment_type: "subscription",
                };
              }
              return null;
            },
            rawQuery: async (query) => {
              if (query.includes("kanglink_split")) {
                throw new Error("Split data query failed");
              }
              return [];
            },
          };

          try {
            // Simulate the captureSplitSnapshot call for live content
            await mockSdk.rawQuery(
              "SELECT s.* FROM kanglink_split s WHERE s.id = 1"
            );
            this.framework.assert(
              false,
              "Expected captureSplitSnapshot to throw an error"
            );
          } catch (error) {
            this.framework.assertions.assertEquals(
              error.message,
              "Split data query failed",
              "Should propagate the split query error"
            );
          }
        }
      );

      // Test successful captureSplitSnapshot
      this.framework.addTestCase(
        "Should successfully capture split snapshot when data is available",
        async () => {
          // Mock SDK with successful data
          const mockSdk = {
            rawQuery: async (query) => {
              if (query.includes("kanglink_split")) {
                return [
                  {
                    id: 1,
                    program_id: 1,
                    title: "Test Split",
                    program_name: "Test Program",
                    program_description: "Test Description",
                    type_of_program: "fitness",
                    currency: "USD",
                    trainer_id: 1,
                  },
                ];
              }
              if (query.includes("kanglink_week")) {
                return [{ id: 1, split_id: 1, week_order: 1, title: "Week 1" }];
              }
              if (query.includes("kanglink_day")) {
                return [{ id: 1, week_id: 1, day_order: 1, title: "Day 1" }];
              }
              if (query.includes("kanglink_session")) {
                return [
                  { id: 1, day_id: 1, session_order: 1, title: "Session 1" },
                ];
              }
              if (query.includes("kanglink_exercise_instance")) {
                return [
                  {
                    id: 1,
                    session_id: 1,
                    exercise_order: 1,
                    exercise_id: 1,
                    video_url: "http://example.com/video.mp4",
                  },
                ];
              }

              return [];
            },
          };

          // Simulate successful captureSplitSnapshot
          const splitData = await mockSdk.rawQuery(
            "SELECT s.* FROM kanglink_split s WHERE s.id = 1"
          );
          this.framework.assertions.assertEquals(
            splitData.length,
            1,
            "Should return split data"
          );
          this.framework.assertions.assertEquals(
            splitData[0].id,
            1,
            "Should return correct split ID"
          );

          const weeks = await mockSdk.rawQuery(
            "SELECT * FROM kanglink_week WHERE split_id = 1"
          );
          this.framework.assertions.assertEquals(
            weeks.length,
            1,
            "Should return week data"
          );

          // Test that the snapshot structure would be created correctly
          const snapshot = {
            split: splitData[0],
            weeks: weeks,
            snapshot_date: new Date().toISOString(),
            snapshot_version: "1.0",
          };

          this.framework.assertions.assertEquals(
            snapshot.split.id,
            1,
            "Snapshot should contain split data"
          );
          this.framework.assertions.assertEquals(
            snapshot.weeks.length,
            1,
            "Snapshot should contain weeks data"
          );
          this.framework.assert(
            snapshot.snapshot_date,
            "Snapshot should have timestamp"
          );
        }
      );
    });
  }

  async runTests() {
    try {
      const results = await this.framework.runTests();
      this.framework.generateTestReport();
      return results;
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new EnrollmentErrorHandlingTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report;
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
