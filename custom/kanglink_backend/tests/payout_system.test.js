const APITestFramework = require("../../../tests/apitesting.base");
const CommissionService = require("../services/CommissionService");

// Initialize test framework
const testFramework = new APITestFramework({
  environment: "test",
});

// Test suite for payout system
testFramework.describe("Payout System Tests", () => {
  let sdk;
  let commissionService;
  let testPayoutSettings;
  let testEnrollmentData;

  // Setup before each test
  testFramework.beforeEach(async () => {
    // Mock SDK for testing
    sdk = {
      setProjectId: () => {},
      setTable: () => {},
      findOne: async (table, criteria) => {
        if (table === "payout_settings" && criteria.is_active) {
          return testPayoutSettings;
        }
        return null;
      },
      create: async (table, data) => {
        return { id: 1, ...data };
      },
      updateById: async (table, id, data) => {
        return { id, ...data };
      },
      rawQuery: async (query) => {
        // Mock query responses based on query content
        if (query.includes("payout_status = 'pending'")) {
          return [
            {
              id: 1,
              trainer_id: 1,
              athlete_id: 2,
              total_amount: 100.00,
              trainer_amount: 70.00,
              company_amount: 30.00,
              payout_status: "pending",
              commission_type: "regular",
            },
          ];
        }
        return [];
      },
    };

    commissionService = new CommissionService(sdk);

    // Test data
    testPayoutSettings = {
      id: 1,
      trainer_payout_time_hours: 24,
      split_company_percentage: 30.00,
      split_trainer_percentage: 70.00,
      affiliate_company_percentage: 20.00,
      affiliate_trainer_percentage: 80.00,
      is_active: true,
    };

    testEnrollmentData = {
      trainer_id: 1,
      athlete_id: 2,
      program_id: 1,
      split_id: 1,
      amount: 100.00,
      currency: "USD",
    };
  });

  // Test commission calculation for regular enrollments
  testFramework.test("should calculate regular commission correctly", async () => {
    const commission = commissionService.calculateCommission(
      testEnrollmentData,
      testPayoutSettings
    );

    testFramework.expect(commission.commission_type).toBe("regular");
    testFramework.expect(commission.total_amount).toBe(100.00);
    testFramework.expect(commission.company_amount).toBe(30.00);
    testFramework.expect(commission.trainer_amount).toBe(70.00);
    testFramework.expect(commission.affiliate_code).toBe(null);
    testFramework.expect(commission.affiliate_user_id).toBe(null);
  });

  // Test commission calculation for affiliate enrollments
  testFramework.test("should calculate affiliate commission correctly", async () => {
    const affiliateEnrollmentData = {
      ...testEnrollmentData,
      affiliate_code: "TEST123",
      affiliate_user_id: 3,
    };

    const commission = commissionService.calculateCommission(
      affiliateEnrollmentData,
      testPayoutSettings
    );

    testFramework.expect(commission.commission_type).toBe("affiliate");
    testFramework.expect(commission.total_amount).toBe(100.00);
    testFramework.expect(commission.company_amount).toBe(20.00);
    testFramework.expect(commission.trainer_amount).toBe(80.00);
    testFramework.expect(commission.affiliate_code).toBe("TEST123");
    testFramework.expect(commission.affiliate_user_id).toBe(3);
  });

  // Test payout scheduling
  testFramework.test("should schedule payout correctly", async () => {
    const commission = commissionService.calculateCommission(
      testEnrollmentData,
      testPayoutSettings
    );

    testFramework.expect(commission.payout_scheduled_at).toBeDefined();
    testFramework.expect(commission.payout_status).toBe("pending");

    // Check that payout is scheduled 24 hours from now
    const scheduledTime = new Date(commission.payout_scheduled_at);
    const expectedTime = new Date();
    expectedTime.setHours(expectedTime.getHours() + 24);

    // Allow 1 minute tolerance for test execution time
    const timeDiff = Math.abs(scheduledTime.getTime() - expectedTime.getTime());
    testFramework.expect(timeDiff).toBeLessThan(60000); // 1 minute in milliseconds
  });

  // Test commission record creation
  testFramework.test("should create commission record successfully", async () => {
    const enrollmentId = 1;
    const commission = await commissionService.createCommissionRecord(
      enrollmentId,
      testEnrollmentData
    );

    testFramework.expect(commission.id).toBe(1);
    testFramework.expect(commission.enrollment_id).toBe(enrollmentId);
    testFramework.expect(commission.trainer_id).toBe(testEnrollmentData.trainer_id);
    testFramework.expect(commission.athlete_id).toBe(testEnrollmentData.athlete_id);
  });

  // Test getting pending payouts
  testFramework.test("should get pending payouts correctly", async () => {
    const pendingPayouts = await commissionService.getPendingPayouts();

    testFramework.expect(Array.isArray(pendingPayouts)).toBe(true);
    testFramework.expect(pendingPayouts.length).toBeGreaterThan(0);
    testFramework.expect(pendingPayouts[0].payout_status).toBe("pending");
  });

  // Test marking commission as processed
  testFramework.test("should mark commission as processed", async () => {
    const commissionId = 1;
    const updatedCommission = await commissionService.markCommissionProcessed(commissionId);

    testFramework.expect(updatedCommission.id).toBe(commissionId);
    testFramework.expect(updatedCommission.payout_status).toBe("processed");
    testFramework.expect(updatedCommission.payout_processed_at).toBeDefined();
  });

  // Test affiliate code validation
  testFramework.test("should validate affiliate code correctly", async () => {
    // Mock successful validation
    sdk.rawQuery = async (query) => {
      if (query.includes("affiliate_link LIKE")) {
        return [
          {
            program_id: 1,
            trainer_id: 3,
            program_name: "Test Program",
          },
        ];
      }
      return [];
    };

    const validation = await commissionService.validateAffiliateCode("TEST123");

    testFramework.expect(validation.valid).toBe(true);
    testFramework.expect(validation.program_id).toBe(1);
    testFramework.expect(validation.trainer_id).toBe(3);
  });

  // Test invalid affiliate code
  testFramework.test("should reject invalid affiliate code", async () => {
    // Mock failed validation
    sdk.rawQuery = async () => [];

    const validation = await commissionService.validateAffiliateCode("INVALID");

    testFramework.expect(validation.valid).toBe(false);
    testFramework.expect(validation.message).toBe("Invalid or expired affiliate code");
  });

  // Test commission calculation with different amounts
  testFramework.test("should calculate commission for different amounts", async () => {
    const testAmounts = [50.00, 150.00, 200.00];

    for (const amount of testAmounts) {
      const enrollmentData = { ...testEnrollmentData, amount };
      const commission = commissionService.calculateCommission(
        enrollmentData,
        testPayoutSettings
      );

      testFramework.expect(commission.total_amount).toBe(amount);
      testFramework.expect(commission.company_amount).toBe(amount * 0.30);
      testFramework.expect(commission.trainer_amount).toBe(amount * 0.70);
    }
  });

  // Test error handling for missing payout settings
  testFramework.test("should handle missing payout settings", async () => {
    // Mock no payout settings found
    sdk.findOne = async () => null;

    try {
      await commissionService.createCommissionRecord(1, testEnrollmentData);
      testFramework.fail("Should have thrown an error");
    } catch (error) {
      testFramework.expect(error.message).toBe("No active payout settings found");
    }
  });

  // Test commission summary calculations
  testFramework.test("should calculate trainer commission summary", async () => {
    // Mock commission summary data
    sdk.rawQuery = async (query) => {
      if (query.includes("SUM(trainer_amount)")) {
        return [
          {
            payout_status: "pending",
            count: 5,
            total_amount: 350.00,
            currency: "USD",
          },
          {
            payout_status: "processed",
            count: 10,
            total_amount: 700.00,
            currency: "USD",
          },
        ];
      }
      return [];
    };

    const summary = await commissionService.getTrainerCommissionSummary(1);

    testFramework.expect(Array.isArray(summary)).toBe(true);
    testFramework.expect(summary.length).toBe(2);
    testFramework.expect(summary[0].payout_status).toBe("pending");
    testFramework.expect(summary[0].total_amount).toBe(350.00);
  });
});

});

// Test suite for payout API endpoints
testFramework.describe("Payout API Endpoints Tests", () => {
  let mockApp;
  let mockRequest;
  let mockResponse;

  testFramework.beforeEach(() => {
    // Mock Express app and request/response objects
    mockApp = {
      get: (path, middleware, handler) => {
        mockApp.routes = mockApp.routes || {};
        mockApp.routes[path] = { middleware, handler };
      },
      post: (path, middleware, handler) => {
        mockApp.routes = mockApp.routes || {};
        mockApp.routes[path] = { middleware, handler };
      },
      put: (path, middleware, handler) => {
        mockApp.routes = mockApp.routes || {};
        mockApp.routes[path] = { middleware, handler };
      },
    };

    mockRequest = {
      body: {},
      params: {},
      query: {},
      user_id: 1,
    };

    mockResponse = {
      status: function(code) {
        this.statusCode = code;
        return this;
      },
      json: function(data) {
        this.data = data;
        return this;
      },
      statusCode: 200,
      data: null,
    };
  });

  // Test payout settings validation
  testFramework.test("should validate payout settings percentages", () => {
    const PayoutSettings = require("../models/payout_settings");

    // Test valid percentages
    const validData = {
      split_company_percentage: 30.00,
      split_trainer_percentage: 70.00,
      affiliate_company_percentage: 20.00,
      affiliate_trainer_percentage: 80.00,
    };

    const validErrors = PayoutSettings.validatePercentages(validData);
    testFramework.expect(validErrors.length).toBe(0);

    // Test invalid split percentages
    const invalidSplitData = {
      split_company_percentage: 40.00,
      split_trainer_percentage: 70.00, // Total = 110%
      affiliate_company_percentage: 20.00,
      affiliate_trainer_percentage: 80.00,
    };

    const splitErrors = PayoutSettings.validatePercentages(invalidSplitData);
    testFramework.expect(splitErrors.length).toBe(1);
    testFramework.expect(splitErrors[0].field).toBe("split_percentages");

    // Test invalid affiliate percentages
    const invalidAffiliateData = {
      split_company_percentage: 30.00,
      split_trainer_percentage: 70.00,
      affiliate_company_percentage: 30.00,
      affiliate_trainer_percentage: 80.00, // Total = 110%
    };

    const affiliateErrors = PayoutSettings.validatePercentages(invalidAffiliateData);
    testFramework.expect(affiliateErrors.length).toBe(1);
    testFramework.expect(affiliateErrors[0].field).toBe("affiliate_percentages");
  });

  // Test commission model validation
  testFramework.test("should validate commission model fields", () => {
    const Commission = require("../models/commission");
    const schema = Commission.schema();

    // Check required fields
    const requiredFields = schema.filter(field =>
      field.validation && field.validation.includes("required")
    );

    testFramework.expect(requiredFields.length).toBeGreaterThan(0);

    // Check commission type mapping
    const commissionTypeField = schema.find(field => field.name === "commission_type");
    testFramework.expect(commissionTypeField.mapping).toBe("regular:Regular,affiliate:Affiliate");

    // Check payout status mapping
    const payoutStatusField = schema.find(field => field.name === "payout_status");
    testFramework.expect(payoutStatusField.mapping).toBe("pending:Pending,processed:Processed,failed:Failed,cancelled:Cancelled");
  });

  // Test affiliate code generation
  testFramework.test("should generate unique affiliate codes", () => {
    // Load the super admin routes to access the helper function
    const fs = require('fs');
    const path = require('path');
    const routeFile = fs.readFileSync(path.join(__dirname, '../routes/super_admin.js'), 'utf8');

    // Extract the generateAffiliateCode function
    const functionMatch = routeFile.match(/function generateAffiliateCode\(programId, trainerId\) \{[\s\S]*?\}/);
    testFramework.expect(functionMatch).toBeDefined();

    // Test that function exists and would generate codes
    const programId = 123;
    const trainerId = 456;

    // Since we can't easily execute the function, we'll test the logic
    const timestamp = Date.now().toString(36);
    const programCode = programId.toString(36);
    const trainerCode = trainerId.toString(36);
    const expectedFormat = `${programCode}${trainerCode}${timestamp}`.toUpperCase();

    testFramework.expect(expectedFormat.length).toBeGreaterThan(0);
    testFramework.expect(expectedFormat).toMatch(/^[A-Z0-9]+$/);
  });
});

// Run the tests
if (require.main === module) {
  testFramework.run().then(() => {
    console.log("Payout system tests completed");
  });
}

module.exports = testFramework;
