const APITestFramework = require("../../../../tests/apitesting.base.js");
const BASE_URL = "http://localhost:5172";

class ResendVerificationApiTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("member Resend Verification API Tests", () => {
      // Setup before running tests
      this.framework.beforeEach(async () => {
        // Setup code here if needed
      });

      // Successful resend test
      this.framework.addTestCase("member Resend Verification - Success", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/kanglink/member/lambda/resend_verification",
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
              role: "member",
            }),
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(!response.body.error, "Should not return error");
        this.framework.assert(
          response.body.message === "Verification email sent successfully",
          "Should return success message"
        );
      });

      // Missing email test
      this.framework.addTestCase("member Resend Verification - Missing Email", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/kanglink/member/lambda/resend_verification",
          {
            method: "POST",
            body: JSON.stringify({
              role: "member",
            }),
          }
        );

        this.framework.assert(
          response.status === 400,
          "Should return 400 status code"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
        this.framework.assert(
          response.body.message === "Email is required",
          "Should return email required message"
        );
      });

      // User not found test
      this.framework.addTestCase("member Resend Verification - User Not Found", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/kanglink/member/lambda/resend_verification",
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
              role: "member",
            }),
          }
        );

        this.framework.assert(
          response.status === 404,
          "Should return 404 status code"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
        this.framework.assert(
          response.body.message === "User not found",
          "Should return user not found message"
        );
      });

      // Already verified user test
      this.framework.addTestCase("member Resend Verification - Already Verified", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/kanglink/member/lambda/resend_verification",
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
              role: "member",
            }),
          }
        );

        this.framework.assert(
          response.status === 400,
          "Should return 400 status code"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
        this.framework.assert(
          response.body.message === "User is already verified",
          "Should return already verified message"
        );
      });

      // Invalid role test
      this.framework.addTestCase("member Resend Verification - Invalid Role", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/kanglink/member/lambda/resend_verification",
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
              role: "invalid_role",
            }),
          }
        );

        this.framework.assert(
          response.status === 403,
          "Should return 403 status code"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
      });

      // Empty body test
      this.framework.addTestCase("member Resend Verification - Empty Body", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/kanglink/member/lambda/resend_verification",
          {
            method: "POST",
            body: JSON.stringify({}),
          }
        );

        this.framework.assert(
          response.status === 400,
          "Should return 400 status code"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
        this.framework.assert(
          response.body.message === "Email is required",
          "Should return email required message"
        );
      });
    });
  }

  async runTests() {
    await this.framework.runTests();
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tests = new ResendVerificationApiTests();
  tests.runTests();
}

module.exports = ResendVerificationApiTests; 