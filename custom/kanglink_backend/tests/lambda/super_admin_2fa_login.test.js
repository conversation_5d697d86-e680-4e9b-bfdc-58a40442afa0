const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

class TwoFactorAuthTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    // Test 2FA Login
    this.framework.addTestCase("super_admin 2FA Login - Success", async () => {
      const response = await this.framework.makeRequest(
        BASE_URL + "/v1/api/kanglink/super_admin/lambda/2fa/login",
        {
          method: "POST",
          body: JSON.stringify({
            email: "<EMAIL>",
            password: "validPassword",
            role: "super_admin",
          }),
        }
      );

      this.framework.assert(
        response.status === 200,
        "Should return 200 status code"
      );
      this.framework.assert(!response.body.error, "Should not return error");
      this.framework.assert(response.body.qr_code, "Should return QR code");
      this.framework.assert(
        response.body.one_time_token,
        "Should return one-time token"
      );
    });

    this.framework.addTestCase(
      "super_admin 2FA Login - Invalid Credentials",
      async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/kanglink/super_admin/lambda/2fa/login",
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
              password: "wrongPassword",
              role: "super_admin",
            }),
          }
        );

        this.framework.assert(
          response.status === 403,
          "Should return 403 status code"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
        this.framework.assert(
          response.body.message === "Invalid Credentials",
          "Should return error message"
        );
      }
    );

    // Test 2FA Enable
    this.framework.addTestCase("super_admin 2FA Enable - QR Code", async () => {
      const response = await this.framework.makeRequest(
        BASE_URL + "/v2/api/kanglink/super_admin/lambda/2fa/enable",
        {
          method: "POST",
          headers: {
            Authorization: "Bearer valid-token",
          },
          body: JSON.stringify({
            type: "qr",
            token: "123456",
          }),
        }
      );

      this.framework.assert(
        response.status === 200,
        "Should return 200 status code"
      );
      this.framework.assert(!response.body.error, "Should not return error");
      this.framework.assert(response.body.qr_code, "Should return QR code");
    });

    // Test 2FA Enable SMS
    this.framework.addTestCase("super_admin 2FA Enable - SMS", async () => {
      const response = await this.framework.makeRequest(
        BASE_URL + "/v2/api/kanglink/super_admin/lambda/2fa/enable",
        {
          method: "POST",
          headers: {
            Authorization: "Bearer valid-token",
          },
          body: JSON.stringify({
            type: "sms",
            phone: "+1234567890",
            token: "123456",
          }),
        }
      );

      this.framework.assert(
        response.status === 200,
        "Should return 200 status code"
      );
      this.framework.assert(!response.body.error, "Should not return error");
    });

    // Test 2FA Verify
    this.framework.addTestCase("super_admin 2FA Verify - Valid Token", async () => {
      const response = await this.framework.makeRequest(
        BASE_URL + "/v2/api/kanglink/super_admin/lambda/2fa/verify",
        {
          method: "POST",
          headers: {
            Authorization: "Bearer valid-token",
          },
          body: JSON.stringify({
            token: "valid-2fa-token",
          }),
        }
      );

      this.framework.assert(
        response.status === 200,
        "Should return 200 status code"
      );
      this.framework.assert(
        response.body.valid === true,
        "Should return valid flag"
      );
    });

    // Test 2FA Disable
    this.framework.addTestCase("super_admin 2FA Disable", async () => {
      const response = await this.framework.makeRequest(
        BASE_URL + "/v2/api/kanglink/super_admin/lambda/2fa/disable",
        {
          method: "POST",
          headers: {
            Authorization: "Bearer valid-token",
          },
        }
      );

      this.framework.assert(
        response.status === 200,
        "Should return 200 status code"
      );
      this.framework.assert(!response.body.error, "Should not return error");
      this.framework.assert(
        response.body.message,
        "Should return success message"
      );
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      // without generating a report (let the test runner handle that)
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new TwoFactorAuthTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
