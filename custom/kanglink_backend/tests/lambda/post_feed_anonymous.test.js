const { describe, it, before, after } = require("mocha");
const { expect } = require("chai");
const apiTestingBase = require("../apitesting.base");

describe("Post Feed Anonymous Tests", function () {
  this.timeout(30000);

  let authToken;
  let testProgramId;
  let testUserId;
  let createdPostId;

  before(async function () {
    try {
      // Get auth token for testing
      const loginResponse = await apiTestingBase.loginAs("member");
      authToken = loginResponse.token;
      testUserId = loginResponse.user_id;

      // Create a test program for testing posts
      const programResponse = await apiTestingBase.makeAuthenticatedRequest(
        "POST",
        "/v2/api/kanglink/custom/program",
        {
          program_name: "Test Program for Anonymous Posts",
          type_of_program: "fitness",
          program_description: "Test program for anonymous posts",
          allow_comments: true,
          status: "active"
        },
        authToken
      );

      if (!programResponse.error && programResponse.data) {
        testProgramId = programResponse.data.program.id;
      } else {
        throw new Error("Failed to create test program");
      }
    } catch (error) {
      console.error("Setup failed:", error);
      throw error;
    }
  });

  after(async function () {
    try {
      // Cleanup: Delete created post if it exists
      if (createdPostId) {
        await apiTestingBase.makeAuthenticatedRequest(
          "DELETE",
          `/v2/api/kanglink/custom/trainer/feed/${createdPostId}`,
          {},
          authToken
        );
      }

      // Cleanup: Delete test program
      if (testProgramId) {
        await apiTestingBase.makeAuthenticatedRequest(
          "DELETE",
          `/v2/api/kanglink/custom/program/${testProgramId}`,
          {},
          authToken
        );
      }
    } catch (error) {
      console.error("Cleanup failed:", error);
    }
  });

  describe("POST /v2/api/kanglink/custom/trainer/feed - Anonymous Posts", function () {
    it("should create an anonymous post successfully", async function () {
      const response = await apiTestingBase.makeAuthenticatedRequest(
        "POST",
        "/v2/api/kanglink/custom/trainer/feed",
        {
          program_id: testProgramId,
          post_type: "update",
          content: "This is an anonymous test post",
          is_private: false,
          is_anonymous: true,
          visibility_scope: "public"
        },
        authToken
      );

      expect(response.error).to.be.false;
      expect(response.data).to.exist;
      expect(response.data.post).to.exist;
      expect(response.data.post.content).to.equal("This is an anonymous test post");
      expect(response.data.post.is_anonymous).to.be.true;
      expect(response.data.post.user_id).to.be.null;
      expect(response.message).to.equal("Post created successfully");

      createdPostId = response.data.post.id;
    });

    it("should create a non-anonymous post successfully", async function () {
      const response = await apiTestingBase.makeAuthenticatedRequest(
        "POST",
        "/v2/api/kanglink/custom/trainer/feed",
        {
          program_id: testProgramId,
          post_type: "update",
          content: "This is a non-anonymous test post",
          is_private: false,
          is_anonymous: false,
          visibility_scope: "public"
        },
        authToken
      );

      expect(response.error).to.be.false;
      expect(response.data).to.exist;
      expect(response.data.post).to.exist;
      expect(response.data.post.content).to.equal("This is a non-anonymous test post");
      expect(response.data.post.is_anonymous).to.be.false;
      expect(response.data.post.user_id).to.equal(testUserId);
      expect(response.message).to.equal("Post created successfully");
    });

    it("should default to non-anonymous when is_anonymous is not provided", async function () {
      const response = await apiTestingBase.makeAuthenticatedRequest(
        "POST",
        "/v2/api/kanglink/custom/trainer/feed",
        {
          program_id: testProgramId,
          post_type: "update",
          content: "This is a default test post",
          is_private: false,
          visibility_scope: "public"
        },
        authToken
      );

      expect(response.error).to.be.false;
      expect(response.data).to.exist;
      expect(response.data.post).to.exist;
      expect(response.data.post.is_anonymous).to.be.false;
      expect(response.data.post.user_id).to.equal(testUserId);
    });
  });

  describe("GET /v2/api/kanglink/custom/trainer/feed - Retrieve Anonymous Posts", function () {
    it("should retrieve posts with proper anonymous user data", async function () {
      const response = await apiTestingBase.makeAuthenticatedRequest(
        "GET",
        `/v2/api/kanglink/custom/trainer/feed?program_id=${testProgramId}`,
        {},
        authToken
      );

      expect(response.error).to.be.false;
      expect(response.data).to.exist;
      expect(Array.isArray(response.data)).to.be.true;

      // Find our anonymous post
      const anonymousPost = response.data.find(
        post => post.content === "This is an anonymous test post"
      );

      expect(anonymousPost).to.exist;
      expect(anonymousPost.is_anonymous).to.be.true;
      expect(anonymousPost.user_id).to.be.null;
      expect(anonymousPost.user).to.exist;
      expect(anonymousPost.user.id).to.be.null;
      expect(anonymousPost.user.email).to.equal("Anonymous");
      expect(anonymousPost.user.data).to.exist;
      expect(anonymousPost.user.data.first_name).to.equal("Anonymous");
      expect(anonymousPost.user.data.last_name).to.equal("User");

      // Find our non-anonymous post
      const regularPost = response.data.find(
        post => post.content === "This is a non-anonymous test post"
      );

      expect(regularPost).to.exist;
      expect(regularPost.is_anonymous).to.be.false;
      expect(regularPost.user_id).to.equal(testUserId);
      expect(regularPost.user).to.exist;
      expect(regularPost.user.id).to.equal(testUserId);
      expect(regularPost.user.email).to.not.equal("Anonymous");
    });
  });

  describe("GET /v2/api/kanglink/custom/trainer/feed/:post_id - Single Anonymous Post", function () {
    it("should retrieve a single anonymous post with proper user data", async function () {
      if (!createdPostId) {
        this.skip();
      }

      const response = await apiTestingBase.makeAuthenticatedRequest(
        "GET",
        `/v2/api/kanglink/custom/trainer/feed/${createdPostId}`,
        {},
        authToken
      );

      expect(response.error).to.be.false;
      expect(response.data).to.exist;
      expect(response.data.is_anonymous).to.be.true;
      expect(response.data.user_id).to.be.null;
      expect(response.data.user).to.exist;
      expect(response.data.user.id).to.be.null;
      expect(response.data.user.email).to.equal("Anonymous");
      expect(response.data.user.data).to.exist;
      expect(response.data.user.data.first_name).to.equal("Anonymous");
      expect(response.data.user.data.last_name).to.equal("User");
    });
  });

  describe("Anonymous Post Validation", function () {
    it("should still require authentication even for anonymous posts", async function () {
      const response = await apiTestingBase.makeRequest(
        "POST",
        "/v2/api/kanglink/custom/trainer/feed",
        {
          program_id: testProgramId,
          post_type: "update",
          content: "This should fail without auth",
          is_anonymous: true
        }
      );

      expect(response.error).to.be.true;
      expect(response.message).to.include("authorization");
    });

    it("should validate required fields for anonymous posts", async function () {
      const response = await apiTestingBase.makeAuthenticatedRequest(
        "POST",
        "/v2/api/kanglink/custom/trainer/feed",
        {
          post_type: "update",
          content: "Missing program_id",
          is_anonymous: true
        },
        authToken
      );

      expect(response.error).to.be.true;
      expect(response.message).to.include("program_id");
    });
  });
}); 