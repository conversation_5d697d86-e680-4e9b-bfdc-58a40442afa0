const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * Update Password API Tests
 * Class-based implementation of the Update Password API tests
 */
class UpdatePasswordTests {
  constructor() {
    this.framework = new APITestFramework();
    this.baseUrl = BASE_URL;

    // Define expected response schema
    this.updatePasswordSchema = {
      error: false,
      message: "string",
    };

    this.setupTests();
  }

  setupTests() {
    this.framework.describe("trainer Password Update API Tests", () => {
      let authToken;

      // Setup before each test
      this.framework.beforeEach(async () => {
        // You could set up test data or authentication here
        authToken = "Bearer YOUR_AUTH_TOKEN";
      });

      // Original test case with enhancements
      this.framework.addTestCase(
        "trainer Update Password - Success Path",
        async () => {
          // Create spy to track request
          const requestSpy = this.framework.createSpy(
            this.framework,
            "makeRequest"
          );

          const response = await this.framework.makeRequest(
            `${this.baseUrl}/v1/api/kanglink/trainer/lambda/update/password`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: authToken,
              },
              body: JSON.stringify({
                password: "a123456",
              }),
            }
          );

          // Original assertions still work
          this.framework.assert(
            response.status === 200,
            "Update Password trainer should return 200 status"
          );
          this.framework.assert(
            response.body.error === false,
            "Update Email trainer error flag should be false"
          );

          // New enhanced assertions
          this.framework.assertions.assertResponseValid(
            response,
            this.updatePasswordSchema
          );
          this.framework.assertions.assertEquals(
            response.status,
            200,
            "Status code should be 200"
          );

          // Verify request was made correctly
          this.framework.assert(
            requestSpy.callCount() === 1,
            "API should be called exactly once"
          );
        }
      );

      // Additional test cases using new features
      this.framework.addTestCase(
        "trainer Update Password - Invalid Password",
        async () => {
          await this.framework.assertions.assertThrows(
            async () => {
              await this.framework.makeRequest(
                `${this.baseUrl}/v1/api/kanglink/trainer/lambda/update/password`,
                {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: authToken,
                  },
                  body: JSON.stringify({
                    password: "", // Invalid empty password
                  }),
                }
              );
            },
            Error,
            "Should throw error for invalid password"
          );
        }
      );

      // Test with mocked response
      this.framework.addTestCase(
        "trainer Update Password - Mocked Success Response",
        async () => {
          // Mock the API response
          this.framework.mockRequest(
            `${this.baseUrl}/v1/api/kanglink/trainer/lambda/update/password`,
            {
              error: false,
              message: "Password updated successfully",
            },
            {
              status: 200,
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

          const response = await this.framework.makeRequest(
            `${this.baseUrl}/v1/api/kanglink/trainer/lambda/update/password`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: authToken,
              },
              body: JSON.stringify({
                password: "newPassword123",
              }),
            }
          );

          this.framework.assertions.assertEquals(
            response.body.message,
            "Password updated successfully",
            "Should return success message"
          );
        }
      );
    });
  }

  // Helper method to run all tests
  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new UpdatePasswordTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
