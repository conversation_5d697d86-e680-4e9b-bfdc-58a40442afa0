// Simple test to verify enrollment endpoint works
const http = require('http');

async function testEnrollmentEndpoint() {
  console.log('Testing enrollment endpoint...');
  
  const options = {
    hostname: 'localhost',
    port: 5172,
    path: '/v2/api/kanglink/custom/athlete/enrollment',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-token'
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response: ${data}`);
        
        // Should return 400 (bad request) not 404 (not found)
        if (res.statusCode === 400 || res.statusCode === 401) {
          console.log('✅ Enrollment endpoint exists and responds correctly');
          resolve(true);
        } else if (res.statusCode === 404) {
          console.log('❌ Enrollment endpoint not found');
          resolve(false);
        } else {
          console.log(`⚠️  Unexpected status code: ${res.statusCode}`);
          resolve(false);
        }
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Request failed: ${err.message}`);
      resolve(false);
    });

    // Send empty body to test validation
    req.write('{}');
    req.end();
  });
}

async function testLegacyEndpoint() {
  console.log('\nTesting legacy endpoint...');
  
  const options = {
    hostname: 'localhost',
    port: 5172,
    path: '/v2/api/kanglink/custom/athlete/enroll',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-token'
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response: ${data}`);
        
        // Should return 301 (redirect)
        if (res.statusCode === 301) {
          console.log('✅ Legacy endpoint redirects correctly');
          resolve(true);
        } else {
          console.log(`❌ Legacy endpoint should return 301, got ${res.statusCode}`);
          resolve(false);
        }
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Request failed: ${err.message}`);
      resolve(false);
    });

    req.write('{}');
    req.end();
  });
}

async function testStatusEndpoint() {
  console.log('\nTesting status endpoint...');
  
  const options = {
    hostname: 'localhost',
    port: 5172,
    path: '/v2/api/kanglink/custom/athlete/enrollment/status/test-id',
    method: 'GET',
    headers: {
      'Authorization': 'Bearer test-token'
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        console.log(`Response: ${data}`);
        
        // Should return 404 (not found) or 401 (unauthorized) not 500 (server error)
        if (res.statusCode === 404 || res.statusCode === 401) {
          console.log('✅ Status endpoint exists and handles requests correctly');
          resolve(true);
        } else {
          console.log(`❌ Status endpoint should return 404 or 401, got ${res.statusCode}`);
          resolve(false);
        }
      });
    });

    req.on('error', (err) => {
      console.log(`❌ Request failed: ${err.message}`);
      resolve(false);
    });

    req.end();
  });
}

async function runTests() {
  console.log('=== Enrollment Endpoint Tests ===\n');
  
  const results = [];
  
  results.push(await testEnrollmentEndpoint());
  results.push(await testLegacyEndpoint());
  results.push(await testStatusEndpoint());
  
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`\n=== Test Results ===`);
  console.log(`Passed: ${passed}/${total}`);
  
  if (passed === total) {
    console.log('🎉 All tests passed!');
  } else {
    console.log('⚠️  Some tests failed. Check server is running on port 5172');
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
