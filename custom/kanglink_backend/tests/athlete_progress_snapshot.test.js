const { TestSuite } = require("../../../tests/apitesting.base");

const testSuite = new TestSuite("Athlete Progress Snapshot Tests");

// Test data
const testData = {
  trainerId: 11,
  athleteId: 12,
  programId: 34,
  splitId: 73,
  snapshotEnrollmentId: 7, // One-time purchase with snapshot
  liveEnrollmentId: 8,     // Subscription with live data
};

testSuite.addTest("Get snapshot enrollment program data", async (sdk) => {
  sdk.setProjectId("kanglink");
  
  const response = await sdk.callRestAPI(
    {},
    "GET",
    `/v2/api/kanglink/custom/athlete/enrollment/${testData.snapshotEnrollmentId}/program`,
    { user_id: testData.athleteId, role: "member" }
  );

  testSuite.assert(response.error === false, "Should return success");
  testSuite.assert(response.data, "Should return data");
  testSuite.assert(response.data.enrollment, "Should return enrollment data");
  testSuite.assert(response.data.enrollment.access_type === "snapshot", "Should be snapshot access type");
  testSuite.assert(response.data.weeks, "Should return weeks data");
  testSuite.assert(Array.isArray(response.data.weeks), "Weeks should be an array");
  
  // Verify snapshot data structure
  if (response.data.weeks.length > 0) {
    const firstWeek = response.data.weeks[0];
    testSuite.assert(firstWeek.days, "Week should have days");
    testSuite.assert(firstWeek.progress, "Week should have progress");
    
    if (firstWeek.days.length > 0) {
      const firstDay = firstWeek.days[0];
      testSuite.assert(firstDay.sessions, "Day should have sessions");
      testSuite.assert(firstDay.progress, "Day should have progress");
      testSuite.assert(typeof firstDay.progress.total_exercises === 'number', "Day progress should have total_exercises");
      testSuite.assert(typeof firstDay.progress.completed_exercises === 'number', "Day progress should have completed_exercises");
      
      if (firstDay.sessions.length > 0) {
        const firstSession = firstDay.sessions[0];
        testSuite.assert(firstSession.exercise_instances, "Session should have exercise_instances");
        
        if (firstSession.exercise_instances.length > 0) {
          const firstExercise = firstSession.exercise_instances[0];
          testSuite.assert(firstExercise.progress, "Exercise should have progress");
          testSuite.assert(typeof firstExercise.progress.is_completed === 'boolean', "Exercise progress should have is_completed");
        }
      }
    }
  }
  
  console.log("✓ Snapshot enrollment data structure verified");
  return response;
});

testSuite.addTest("Mark exercise complete in snapshot enrollment", async (sdk) => {
  sdk.setProjectId("kanglink");
  
  // First get the program data to find an exercise
  const programResponse = await sdk.callRestAPI(
    {},
    "GET",
    `/v2/api/kanglink/custom/athlete/enrollment/${testData.snapshotEnrollmentId}/program`,
    { user_id: testData.athleteId, role: "member" }
  );
  
  testSuite.assert(programResponse.error === false, "Should get program data");
  testSuite.assert(programResponse.data.weeks.length > 0, "Should have weeks");
  
  const firstWeek = programResponse.data.weeks[0];
  const firstDay = firstWeek.days[0];
  const firstSession = firstDay.sessions[0];
  const firstExercise = firstSession.exercise_instances[0];
  
  // Mark exercise as complete
  const response = await sdk.callRestAPI(
    {
      enrollment_id: testData.snapshotEnrollmentId,
      exercise_instance_id: firstExercise.id,
      sets_completed: 3,
      reps_completed: "10,10,10",
      weight_used: "50kg",
      time_taken_seconds: 300,
      difficulty_rating: 7,
      notes: "Test completion for snapshot"
    },
    "POST",
    "/v2/api/kanglink/custom/athlete/exercise/complete",
    { user_id: testData.athleteId, role: "member" }
  );

  testSuite.assert(response.error === false, "Should mark exercise as complete");
  testSuite.assert(response.data, "Should return completion data");
  testSuite.assert(response.data.exercise_instance_id === firstExercise.id, "Should return correct exercise ID");
  
  console.log("✓ Exercise marked complete in snapshot enrollment");
  return response;
});

testSuite.addTest("Verify progress update for snapshot enrollment", async (sdk) => {
  sdk.setProjectId("kanglink");
  
  // Get updated program data
  const response = await sdk.callRestAPI(
    {},
    "GET",
    `/v2/api/kanglink/custom/athlete/enrollment/${testData.snapshotEnrollmentId}/program`,
    { user_id: testData.athleteId, role: "member" }
  );
  
  testSuite.assert(response.error === false, "Should get updated program data");
  
  // Find the exercise we just completed
  let foundCompletedExercise = false;
  for (const week of response.data.weeks) {
    for (const day of week.days) {
      for (const session of day.sessions) {
        for (const exercise of session.exercise_instances) {
          if (exercise.progress && exercise.progress.is_completed) {
            foundCompletedExercise = true;
            testSuite.assert(exercise.progress.sets_completed === 3, "Should have correct sets completed");
            testSuite.assert(exercise.progress.reps_completed === "10,10,10", "Should have correct reps completed");
            testSuite.assert(exercise.progress.weight_used === "50kg", "Should have correct weight used");
            testSuite.assert(exercise.progress.notes === "Test completion for snapshot", "Should have correct notes");
            break;
          }
        }
      }
    }
  }
  
  testSuite.assert(foundCompletedExercise, "Should find at least one completed exercise");
  
  // Verify day progress was updated
  const firstDay = response.data.weeks[0].days[0];
  testSuite.assert(firstDay.progress.completed_exercises > 0, "Day should have completed exercises");
  testSuite.assert(firstDay.progress.total_exercises > 0, "Day should have total exercises count");
  
  console.log("✓ Progress correctly updated for snapshot enrollment");
  return response;
});

testSuite.addTest("Compare snapshot vs live data structure", async (sdk) => {
  sdk.setProjectId("kanglink");
  
  // Get snapshot enrollment data
  const snapshotResponse = await sdk.callRestAPI(
    {},
    "GET",
    `/v2/api/kanglink/custom/athlete/enrollment/${testData.snapshotEnrollmentId}/program`,
    { user_id: testData.athleteId, role: "member" }
  );
  
  // Get live enrollment data (assuming we have one)
  const liveResponse = await sdk.callRestAPI(
    {},
    "GET",
    `/v2/api/kanglink/custom/athlete/enrollment/${testData.liveEnrollmentId}/program`,
    { user_id: testData.athleteId, role: "member" }
  );
  
  if (liveResponse.error === false) {
    // Both should have the same structure
    testSuite.assert(snapshotResponse.data.enrollment.access_type === "snapshot", "Snapshot should have snapshot access_type");
    testSuite.assert(liveResponse.data.enrollment.access_type === "live", "Live should have live access_type");
    
    // Both should have weeks array
    testSuite.assert(Array.isArray(snapshotResponse.data.weeks), "Snapshot should have weeks array");
    testSuite.assert(Array.isArray(liveResponse.data.weeks), "Live should have weeks array");
    
    // Both should have progress structure
    if (snapshotResponse.data.weeks.length > 0 && liveResponse.data.weeks.length > 0) {
      const snapshotWeek = snapshotResponse.data.weeks[0];
      const liveWeek = liveResponse.data.weeks[0];
      
      testSuite.assert(snapshotWeek.progress, "Snapshot week should have progress");
      testSuite.assert(liveWeek.progress, "Live week should have progress");
      
      testSuite.assert(typeof snapshotWeek.progress.total_days === 'number', "Snapshot week progress should have total_days");
      testSuite.assert(typeof liveWeek.progress.total_days === 'number', "Live week progress should have total_days");
    }
    
    console.log("✓ Snapshot and live data structures are consistent");
  } else {
    console.log("⚠ Live enrollment not available for comparison");
  }
  
  return { snapshot: snapshotResponse, live: liveResponse };
});

module.exports = testSuite;
