const BaseTest = require("../../../tests/apitesting.base");

class SubscriptionExpiryTest extends BaseTest {
  constructor() {
    super();
    this.testSuiteName = "Subscription Expiry Tests";
  }

  async testSubscriptionExpiryCalculation() {
    try {
      console.log("=== Testing Subscription Expiry Calculation ===");
      
      // Test the helper function logic
      const testEnrollments = [
        {
          payment_type: 'subscription',
          stripe_period_end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
          expiry_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
          days_until_expiry: 3
        },
        {
          payment_type: 'subscription',
          stripe_period_end: null,
          expiry_date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days from now
          days_until_expiry: 8
        },
        {
          payment_type: 'subscription',
          stripe_period_end: null,
          expiry_date: null,
          days_until_expiry: 15
        },
        {
          payment_type: 'one_time',
          stripe_period_end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          expiry_date: null,
          days_until_expiry: null
        }
      ];

      // Test each scenario
      testEnrollments.forEach((enrollment, index) => {
        const result = this.calculateDaysUntilExpiry(enrollment);
        console.log(`Test ${index + 1}:`, {
          payment_type: enrollment.payment_type,
          stripe_period_end: enrollment.stripe_period_end,
          expiry_date: enrollment.expiry_date,
          days_until_expiry: enrollment.days_until_expiry,
          calculated_result: result
        });

        if (enrollment.payment_type === 'one_time') {
          this.assertEqual(result, null, 'One-time payments should return null');
        } else {
          this.assertTrue(typeof result === 'number' || result === null, 'Should return number or null');
        }
      });

      console.log("✓ Subscription expiry calculation logic working correctly");
    } catch (error) {
      console.error("Subscription expiry calculation test error:", error);
      throw error;
    }
  }

  async testAthleteLibraryWithExpiry() {
    try {
      console.log("=== Testing Athlete Library with Expiry Info ===");
      
      // Test the athlete library endpoint
      const response = await this.makeRequest({
        method: "GET",
        endpoint: "/v2/api/kanglink/custom/athlete/library",
        headers: {
          Authorization: `Bearer ${this.getValidToken()}`,
        },
      });

      console.log("Library Response with Expiry:", JSON.stringify(response, null, 2));

      // Check response structure
      this.assertEqual(response.error, false, "Should not have error");
      this.assertTrue(
        response.data && typeof response.data === "object",
        "Should have data object"
      );

      // Check subscription_info structure for subscribed enrollments
      if (response.data.subscribed && response.data.subscribed.length > 0) {
        const subscription = response.data.subscribed[0];
        
        this.assertTrue(
          subscription.hasOwnProperty('subscription_info'),
          'Should have subscription_info object'
        );

        const subInfo = subscription.subscription_info;
        const expectedFields = [
          'billing_failed', 'stripe_subscription_id', 'days_until_expiry',
          'expiry_date', 'stripe_period_end', 'stripe_status', 'will_cancel_at_period_end'
        ];

        expectedFields.forEach(field => {
          this.assertTrue(
            subInfo.hasOwnProperty(field),
            `subscription_info should have ${field} field`
          );
        });

        // Validate days_until_expiry
        if (subInfo.days_until_expiry !== null) {
          this.assertTrue(
            typeof subInfo.days_until_expiry === 'number',
            'days_until_expiry should be a number when not null'
          );
        }

        console.log("Subscription info:", subInfo);
      }

      console.log("✓ Athlete library endpoint includes subscription expiry information");
    } catch (error) {
      console.error("Athlete library with expiry test error:", error);
      throw error;
    }
  }

  async testSubscriptionStatusInterpretation() {
    try {
      console.log("=== Testing Subscription Status Interpretation ===");
      
      const testStatuses = [
        { days_until_expiry: 30, expected: 'healthy' },
        { days_until_expiry: 7, expected: 'expiring_soon' },
        { days_until_expiry: 1, expected: 'expiring_very_soon' },
        { days_until_expiry: 0, expected: 'expires_today' },
        { days_until_expiry: -1, expected: 'expired' },
        { days_until_expiry: null, expected: 'unknown' }
      ];

      testStatuses.forEach(test => {
        const status = this.interpretSubscriptionStatus(test.days_until_expiry);
        console.log(`Days: ${test.days_until_expiry} -> Status: ${status}`);
        this.assertEqual(status, test.expected, `Should correctly interpret ${test.days_until_expiry} days`);
      });

      console.log("✓ Subscription status interpretation working correctly");
    } catch (error) {
      console.error("Subscription status interpretation test error:", error);
      throw error;
    }
  }

  // Helper method to replicate the calculation logic
  calculateDaysUntilExpiry(enrollment) {
    if (enrollment.payment_type !== 'subscription') {
      return null; // Only applicable to subscriptions
    }

    let daysUntilExpiry = null;

    // Priority 1: Use Stripe subscription current_period_end (most accurate)
    if (enrollment.stripe_period_end) {
      const stripeEndDate = new Date(enrollment.stripe_period_end);
      const today = new Date();
      const diffTime = stripeEndDate - today;
      daysUntilExpiry = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    // Priority 2: Use enrollment expiry_date as fallback
    else if (enrollment.expiry_date) {
      const expiryDate = new Date(enrollment.expiry_date);
      const today = new Date();
      const diffTime = expiryDate - today;
      daysUntilExpiry = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
    // Priority 3: Use calculated days_until_expiry from SQL
    else if (enrollment.days_until_expiry !== null) {
      daysUntilExpiry = enrollment.days_until_expiry;
    }

    return daysUntilExpiry;
  }

  // Helper method to interpret subscription status based on days until expiry
  interpretSubscriptionStatus(daysUntilExpiry) {
    if (daysUntilExpiry === null) return 'unknown';
    if (daysUntilExpiry < 0) return 'expired';
    if (daysUntilExpiry === 0) return 'expires_today';
    if (daysUntilExpiry <= 1) return 'expiring_very_soon';
    if (daysUntilExpiry <= 7) return 'expiring_soon';
    return 'healthy';
  }

  getValidToken() {
    // Return a test token - in real tests this would be obtained from login
    return "test_token_here";
  }
}

module.exports = SubscriptionExpiryTest;
