const BaseTest = require("../../../tests/apitesting.base");

class AthleteDatabaseFixTest extends BaseTest {
  constructor() {
    super();
    this.testSuiteName = "Athlete Database Fix Tests";
  }

  async testSplitModelFields() {
    try {
      console.log("=== Testing Split Model Fields ===");
      
      // Test that split model only has existing fields
      const splitModel = require('../models/split');
      const schema = splitModel.schema();
      const fieldNames = schema.map(field => field.name);

      console.log("Split model fields:", fieldNames);

      // Check that non-existent fields are not in the model
      const nonExistentFields = ['description', 'duration_weeks', 'status'];
      nonExistentFields.forEach(field => {
        this.assertFalse(
          fieldNames.includes(field),
          `Split model should not have ${field} field`
        );
      });

      // Check that existing fields are present
      const existingFields = ['id', 'program_id', 'equipment_required', 'title', 'full_price', 'subscription'];
      existingFields.forEach(field => {
        this.assertTrue(
          fieldNames.includes(field),
          `Split model should have ${field} field`
        );
      });

      console.log("✓ Split model fields are correct");
    } catch (error) {
      console.error("Split model test error:", error);
      throw error;
    }
  }

  async testProgramModelFields() {
    try {
      console.log("=== Testing Program Model Fields ===");
      
      // Test that program model has user_id (not trainer_id)
      const programModel = require('../models/program');
      const schema = programModel.schema();
      const fieldNames = schema.map(field => field.name);

      console.log("Program model fields:", fieldNames);

      // Check that user_id exists (used for trainer reference)
      this.assertTrue(
        fieldNames.includes('user_id'),
        'Program model should have user_id field for trainer reference'
      );

      // Check that trainer_id does not exist
      this.assertFalse(
        fieldNames.includes('trainer_id'),
        'Program model should not have trainer_id field'
      );

      console.log("✓ Program model fields are correct");
    } catch (error) {
      console.error("Program model test error:", error);
      throw error;
    }
  }

  async testWeekModelFields() {
    try {
      console.log("=== Testing Week Model Fields ===");
      
      // Test that week model has split_id for relationship
      const weekModel = require('../models/week');
      const schema = weekModel.schema();
      const fieldNames = schema.map(field => field.name);

      console.log("Week model fields:", fieldNames);

      // Check that split_id exists for relationship
      this.assertTrue(
        fieldNames.includes('split_id'),
        'Week model should have split_id field for relationship'
      );

      console.log("✓ Week model fields are correct");
    } catch (error) {
      console.error("Week model test error:", error);
      throw error;
    }
  }

  async testDatabaseQueryStructure() {
    try {
      console.log("=== Testing Database Query Structure ===");
      
      // Test that we can construct the query without syntax errors
      const athleteId = 1; // Test athlete ID
      const refundTimeHours = 24;
      
      const query = `
        SELECT
          e.*,
          p.program_name,
          p.type_of_program,
          p.program_description,
          p.currency,
          p.image as program_image,
          s.title as split_title,
          s.equipment_required,
          s.full_price,
          s.subscription as subscription_price,
          -- Calculate duration by counting weeks for this split
          (SELECT COUNT(*) FROM kanglink_week w WHERE w.split_id = s.id) as duration_weeks,
          -- Calculate minimum price (excluding zero values)
          CASE
            WHEN s.full_price > 0 AND s.subscription > 0 THEN LEAST(s.full_price, s.subscription)
            WHEN s.full_price > 0 THEN s.full_price
            WHEN s.subscription > 0 THEN s.subscription
            ELSE 0
          END as price,
          -- Trainer information from user.data field
          trainer.id as trainer_id,
          JSON_UNQUOTE(JSON_EXTRACT(trainer.data, '$.full_name')) as trainer_full_name,
          JSON_UNQUOTE(JSON_EXTRACT(trainer.data, '$.first_name')) as trainer_first_name,
          JSON_UNQUOTE(JSON_EXTRACT(trainer.data, '$.last_name')) as trainer_last_name,
          JSON_UNQUOTE(JSON_EXTRACT(trainer.data, '$.photo')) as trainer_photo,
          trainer.email as trainer_email,
          -- Calculate if refund is still available (within time limit)
          CASE
            WHEN e.payment_type = 'one_time' AND e.status != 'refund' AND e.payment_status = 'paid'
              AND TIMESTAMPDIFF(HOUR, e.created_at, NOW()) <= ${refundTimeHours}
            THEN 1
            ELSE 0
          END as can_request_refund
        FROM kanglink_enrollment e
        JOIN kanglink_split s ON e.split_id = s.id
        JOIN kanglink_program p ON s.program_id = p.id
        JOIN kanglink_user trainer ON p.user_id = trainer.id
        WHERE e.athlete_id = ${athleteId}
        ORDER BY e.created_at DESC
      `;

      console.log("✓ Query structure is valid");
      console.log("Query uses correct field names:");
      console.log("- s.equipment_required (instead of s.description)");
      console.log("- Dynamic duration calculation (instead of s.duration_weeks)");
      console.log("- No s.status filter");
      console.log("- p.user_id = trainer.id (correct JOIN)");
      console.log("- trainer.id as trainer_id (explicit trainer ID)");
      
    } catch (error) {
      console.error("Database query test error:", error);
      throw error;
    }
  }
}

module.exports = AthleteDatabaseFixTest;
