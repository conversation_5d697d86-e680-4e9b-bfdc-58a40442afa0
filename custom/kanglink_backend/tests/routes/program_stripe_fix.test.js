const { TestSuite } = require("../../../../tests/apitesting.base");

const testSuite = new TestSuite("Program Stripe Fix Tests");

// Test program creation with valid currency and pricing
testSuite.addTest("Create program with valid Stripe pricing", async (test) => {
  try {
    // First, register a trainer
    const trainerRegisterResponse = await test.post("/v1/api/lambda/trainer/register", {
      first_name: "Test",
      last_name: "Trainer",
      email: `trainer_${Date.now()}@test.com`,
      password: "password123",
      confirm_password: "password123",
    });

    test.assert(trainerRegisterResponse.status === 200, "Trainer registration should succeed");
    test.assert(!trainerRegisterResponse.data.error, "Trainer registration should not have errors");

    const trainerId = trainerRegisterResponse.data.model.id;
    const trainerToken = trainerRegisterResponse.data.token;

    // Test program payload with valid currency and pricing
    const programPayload = {
      stepOneData: {
        program_name: "Test Stripe Program",
        type_of_program: "Body building",
        program_description: "Test program for Stripe integration",
        payment_plan: ["one_time"],
        track_progress: true,
        allow_comments: true,
        allow_private_messages: true,
        target_levels: ["beginner"],
        split_program: 1,
        splits: [
          {
            title: "Test Split",
            full_price: 99.99,
            subscription: 0,
            split_id: "test-split-1"
          }
        ],
        currency: "USD",
        days_for_preview: 7
      },
      stepTwoData: {
        program_split: "test-split-1",
        description: "Test program description",
        weeks: [
          {
            id: "week-1",
            title: "Week 1",
            equipment_required: "",
            days: [
              {
                id: "day-1",
                title: "Day 1",
                is_rest_day: false,
                sessions: [
                  {
                    id: "session-1",
                    title: "Session 1",
                    exercises: [
                      {
                        id: "exercise-1",
                        sets: "3",
                        reps_or_time: "10",
                        exercise_details: "Test exercise",
                        rest_duration_minutes: 1,
                        rest_duration_seconds: 30,
                        label: "A",
                        label_number: "1",
                        is_linked: false,
                        exercise_order: 1,
                        user_id: 0,
                        session_id: "",
                        exercise_id: "",
                        exercise_name: "Test Exercise",
                        video_url: null,
                        reps_time_type: "reps"
                      }
                    ],
                    session_letter: "A",
                    session_number: 1,
                    linked_session_id: null,
                    day_id: ""
                  }
                ],
                week_id: ""
              }
            ],
            split_id: ""
          }
        ],
        splitConfigurations: {
          "test-split-1": [
            {
              id: "week-1",
              title: "Week 1",
              equipment_required: "",
              days: [
                {
                  id: "day-1",
                  title: "Day 1",
                  is_rest_day: false,
                  sessions: [
                    {
                      id: "session-1",
                      title: "Session 1",
                      exercises: [
                        {
                          id: "exercise-1",
                          sets: "3",
                          reps_or_time: "10",
                          exercise_details: "Test exercise",
                          rest_duration_minutes: 1,
                          rest_duration_seconds: 30,
                          label: "A",
                          label_number: "1",
                          is_linked: false,
                          exercise_order: 1,
                          user_id: 0,
                          session_id: "",
                          exercise_id: "",
                          exercise_name: "Test Exercise",
                          video_url: null,
                          reps_time_type: "reps"
                        }
                      ],
                      session_letter: "A",
                      session_number: 1,
                      linked_session_id: null,
                      day_id: ""
                    }
                  ],
                  week_id: ""
                }
              ],
              split_id: ""
            }
          ]
        }
      }
    };

    // Create program
    const createResponse = await test.post(
      "/v2/api/kanglink/custom/trainer/programs/draft",
      programPayload,
      {
        Authorization: `Bearer ${trainerToken}`
      }
    );

    console.log("Program creation response:", JSON.stringify(createResponse.data, null, 2));

    test.assert(createResponse.status === 200, "Program creation should succeed");
    test.assert(!createResponse.data.error, "Program creation should not have errors");
    test.assert(createResponse.data.program, "Response should contain program data");

    console.log("✅ Program created successfully with Stripe integration");

  } catch (error) {
    console.error("Test failed with error:", error);
    test.assert(false, `Test failed: ${error.message}`);
  }
});

// Test program creation with invalid currency (should fallback to USD)
testSuite.addTest("Create program with invalid currency (fallback test)", async (test) => {
  try {
    // First, register a trainer
    const trainerRegisterResponse = await test.post("/v1/api/lambda/trainer/register", {
      first_name: "Test",
      last_name: "Trainer2",
      email: `trainer2_${Date.now()}@test.com`,
      password: "password123",
      confirm_password: "password123",
    });

    test.assert(trainerRegisterResponse.status === 200, "Trainer registration should succeed");

    const trainerToken = trainerRegisterResponse.data.token;

    // Test program payload with invalid currency (should fallback to USD)
    const programPayload = {
      stepOneData: {
        program_name: "Test Invalid Currency Program",
        type_of_program: "Body building",
        program_description: "Test program with invalid currency",
        payment_plan: ["one_time"],
        track_progress: true,
        allow_comments: true,
        allow_private_messages: true,
        target_levels: ["beginner"],
        split_program: 1,
        splits: [
          {
            title: "Test Split",
            full_price: 50.00,
            subscription: 0,
            split_id: "test-split-2"
          }
        ],
        currency: null, // Invalid currency - should fallback to USD
        days_for_preview: 7
      },
      stepTwoData: {
        program_split: "test-split-2",
        description: "Test program description",
        weeks: [
          {
            id: "week-1",
            title: "Week 1",
            equipment_required: "",
            days: [
              {
                id: "day-1",
                title: "Day 1",
                is_rest_day: false,
                sessions: [
                  {
                    id: "session-1",
                    title: "Session 1",
                    exercises: [
                      {
                        id: "exercise-1",
                        sets: "3",
                        reps_or_time: "10",
                        exercise_details: "Test exercise",
                        rest_duration_minutes: 1,
                        rest_duration_seconds: 30,
                        label: "A",
                        label_number: "1",
                        is_linked: false,
                        exercise_order: 1,
                        user_id: 0,
                        session_id: "",
                        exercise_id: "",
                        exercise_name: "Test Exercise",
                        video_url: null,
                        reps_time_type: "reps"
                      }
                    ],
                    session_letter: "A",
                    session_number: 1,
                    linked_session_id: null,
                    day_id: ""
                  }
                ],
                week_id: ""
              }
            ],
            split_id: ""
          }
        ],
        splitConfigurations: {
          "test-split-2": [
            {
              id: "week-1",
              title: "Week 1",
              equipment_required: "",
              days: [
                {
                  id: "day-1",
                  title: "Day 1",
                  is_rest_day: false,
                  sessions: [
                    {
                      id: "session-1",
                      title: "Session 1",
                      exercises: [
                        {
                          id: "exercise-1",
                          sets: "3",
                          reps_or_time: "10",
                          exercise_details: "Test exercise",
                          rest_duration_minutes: 1,
                          rest_duration_seconds: 30,
                          label: "A",
                          label_number: "1",
                          is_linked: false,
                          exercise_order: 1,
                          user_id: 0,
                          session_id: "",
                          exercise_id: "",
                          exercise_name: "Test Exercise",
                          video_url: null,
                          reps_time_type: "reps"
                        }
                      ],
                      session_letter: "A",
                      session_number: 1,
                      linked_session_id: null,
                      day_id: ""
                    }
                  ],
                  week_id: ""
                }
              ],
              split_id: ""
            }
          ]
        }
      }
    };

    // Create program
    const createResponse = await test.post(
      "/v2/api/kanglink/custom/trainer/programs/draft",
      programPayload,
      {
        Authorization: `Bearer ${trainerToken}`
      }
    );

    console.log("Program creation response (invalid currency):", JSON.stringify(createResponse.data, null, 2));

    test.assert(createResponse.status === 200, "Program creation should succeed even with invalid currency");
    test.assert(!createResponse.data.error, "Program creation should not have errors");

    console.log("✅ Program created successfully with currency fallback");

  } catch (error) {
    console.error("Test failed with error:", error);
    test.assert(false, `Test failed: ${error.message}`);
  }
});

module.exports = testSuite;
