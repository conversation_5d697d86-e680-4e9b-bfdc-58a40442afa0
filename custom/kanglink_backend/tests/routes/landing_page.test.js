const APITestFramework = require("../../../../tests/apitesting.base.js");
const BASE_URL = "http://localhost:5172";

/**
 * Landing Page API Test Suite
 * 
 * Tests the landing page endpoints for public program and trainer discovery
 */
class LandingPageApiTests {
  constructor() {
    this.framework = new APITestFramework();
    this.testData = {
      testToken: "your-test-token-here", // Replace with actual test token
    };
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Landing Page API Tests", () => {
      // Setup before running tests
      this.framework.beforeEach(async () => {
        // Setup code here if needed
      });

      // ========== TOP RATED PROGRAMS TESTS ==========

      // Test 1: Top Rated Programs - Public Access
      this.framework.addTestCase("Top Rated Programs - Public Access", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/top-rated-programs`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(
          response.body.error === false || response.body.success === true,
          "Should not return error"
        );
        
        if (response.body.data) {
          this.framework.assert(
            Array.isArray(response.body.data),
            "Should return programs array"
          );
          this.framework.assert(
            response.body.pagination,
            "Should include pagination"
          );
        }
      });

      // Test 2: Top Rated Programs - With Filters
      this.framework.addTestCase("Top Rated Programs - With Filters", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/top-rated-programs?page=1&limit=5&min_rating=4.0&category=strength`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        
        if (response.body.pagination) {
          this.framework.assert(
            response.body.pagination.limit === 5,
            "Should respect limit parameter"
          );
        }
      });

      // Test 3: Top Rated Programs - With Authentication
      this.framework.addTestCase("Top Rated Programs - With Authentication", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/top-rated-programs`,
          {
            method: "GET",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`
            }
          }
        );

        // Should work with or without auth
        this.framework.assert(
          response.status === 200 || response.status === 401,
          "Should return 200 or 401 (if token invalid)"
        );
      });

      // ========== PROGRAMS YOU MAY LIKE TESTS ==========

      // Test 4: Programs You May Like - Authenticated
      this.framework.addTestCase("Programs You May Like - Authenticated", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/programs-you-may-like`,
          {
            method: "GET",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`
            }
          }
        );

        if (response.status === 401) {
          console.log(`⚠️  Authentication required - update testToken for full testing`);
          return;
        }

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code for authenticated request"
        );
      });

      // Test 5: Programs You May Like - No Auth (Should Fail)
      this.framework.addTestCase("Programs You May Like - No Auth", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/programs-you-may-like`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 401,
          "Should return 401 status code for unauthenticated request"
        );
      });

      // ========== ALL PROGRAMS TESTS ==========

      // Test 6: All Programs - Search Functionality
      this.framework.addTestCase("All Programs - Search Functionality", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/all-programs?search=fitness&page=1&limit=10`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        
        if (response.body.filters) {
          this.framework.assert(
            response.body.filters.search === "fitness",
            "Should include search term in filters"
          );
        }
      });

      // Test 7: All Programs - Multiple Filters
      this.framework.addTestCase("All Programs - Multiple Filters", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/all-programs?category=strength&level=intermediate&min_rating=3.5`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
      });

      // ========== TOP RATED TRAINERS TESTS ==========

      // Test 8: Top Rated Trainers - Public Access
      this.framework.addTestCase("Top Rated Trainers - Public Access", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/top-rated-trainers`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        
        if (response.body.data) {
          this.framework.assert(
            Array.isArray(response.body.data),
            "Should return trainers array"
          );
        }
      });

      // Test 9: Top Rated Trainers - With Filters
      this.framework.addTestCase("Top Rated Trainers - With Filters", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/top-rated-trainers?specialization=strength&limit=5`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
      });

      // ========== ALL TRAINERS TESTS ==========

      // Test 10: All Trainers - Search Functionality
      this.framework.addTestCase("All Trainers - Search Functionality", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/all-trainers?search=trainer&page=1&limit=10`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
      });

      // Test 11: All Trainers - Experience Range Filter
      this.framework.addTestCase("All Trainers - Experience Range Filter", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/all-trainers?min_experience=2&max_experience=10`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
      });

      // ========== FAVORITE FUNCTIONALITY TESTS ==========

      // Test 12: Programs with Favorite Information
      this.framework.addTestCase("Programs with Favorite Information", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/all-programs?limit=5`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        
        if (response.body.data && response.body.data.length > 0) {
          const program = response.body.data[0];
          this.framework.assert(
            program.hasOwnProperty('favorite'),
            "Program should have favorite property"
          );
          this.framework.assert(
            Array.isArray(program.favorite),
            "Favorite should be an array"
          );
          
          // Log the favorite structure for verification
          console.log(`📝 Program ${program.id} favorite structure:`, program.favorite);
        }
      });

      // Test 13: Top Rated Programs with Favorite Information
      this.framework.addTestCase("Top Rated Programs with Favorite Information", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/top-rated-programs?limit=5`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        
        if (response.body.data && response.body.data.length > 0) {
          const program = response.body.data[0];
          this.framework.assert(
            program.hasOwnProperty('favorite'),
            "Program should have favorite property"
          );
          this.framework.assert(
            Array.isArray(program.favorite),
            "Favorite should be an array"
          );
        }
      });

      // Test 14: Programs You May Like with Favorite Information
      this.framework.addTestCase("Programs You May Like with Favorite Information", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/programs-you-may-like?limit=5`,
          {
            method: "GET",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`
            }
          }
        );

        // Should work with or without auth
        this.framework.assert(
          response.status === 200 || response.status === 401,
          "Should return 200 or 401 (if token invalid)"
        );
        
        if (response.status === 200 && response.body.data && response.body.data.length > 0) {
          const program = response.body.data[0];
          this.framework.assert(
            program.hasOwnProperty('favorite'),
            "Program should have favorite property"
          );
          this.framework.assert(
            Array.isArray(program.favorite),
            "Favorite should be an array"
          );
        }
      });

      // ========== TRAINER FAVORITE FUNCTIONALITY TESTS ==========

      // Test 15: Top Rated Trainers with Favorite Information
      this.framework.addTestCase("Top Rated Trainers with Favorite Information", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/top-rated-trainers?limit=5`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        
        if (response.body.data && response.body.data.length > 0) {
          const trainer = response.body.data[0];
          this.framework.assert(
            trainer.hasOwnProperty('favorite'),
            "Trainer should have favorite property"
          );
          this.framework.assert(
            Array.isArray(trainer.favorite),
            "Favorite should be an array"
          );
          
          // Log the favorite structure for verification
          console.log(`📝 Trainer ${trainer.id} favorite structure:`, trainer.favorite);
        }
      });

      // Test 16: All Trainers with Favorite Information
      this.framework.addTestCase("All Trainers with Favorite Information", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/all-trainers?limit=5`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        
        if (response.body.data && response.body.data.length > 0) {
          const trainer = response.body.data[0];
          this.framework.assert(
            trainer.hasOwnProperty('favorite'),
            "Trainer should have favorite property"
          );
          this.framework.assert(
            Array.isArray(trainer.favorite),
            "Favorite should be an array"
          );
        }
      });

      // Test 17: Trainers You May Like with Favorite Information
      this.framework.addTestCase("Trainers You May Like with Favorite Information", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/trainers-you-may-like?limit=5`,
          {
            method: "GET",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`
            }
          }
        );

        // Should work with or without auth
        this.framework.assert(
          response.status === 200 || response.status === 401,
          "Should return 200 or 401 (if token invalid)"
        );
        
        if (response.status === 200 && response.body.data && response.body.data.length > 0) {
          const trainer = response.body.data[0];
          this.framework.assert(
            trainer.hasOwnProperty('favorite'),
            "Trainer should have favorite property"
          );
          this.framework.assert(
            Array.isArray(trainer.favorite),
            "Favorite should be an array"
          );
        }
      });

      // ========== TRAINER PRICING FUNCTIONALITY TESTS ==========

      // Test 18: Top Rated Trainers with Pricing Information
      this.framework.addTestCase("Top Rated Trainers with Pricing Information", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/top-rated-trainers?limit=5`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        
        if (response.body.data && response.body.data.length > 0) {
          const trainer = response.body.data[0];
          this.framework.assert(
            trainer.hasOwnProperty('pricing'),
            "Trainer should have pricing property"
          );
          this.framework.assert(
            trainer.hasOwnProperty('price'),
            "Trainer should have price property"
          );
          this.framework.assert(
            typeof trainer.price === 'number',
            "Price should be a number"
          );
          
          // Log the pricing structure for verification
          console.log(`📝 Trainer ${trainer.id} pricing structure:`, trainer.pricing);
          console.log(`💰 Trainer ${trainer.id} minimum price:`, trainer.price);
        }
      });

      // Test 19: All Trainers with Pricing Information
      this.framework.addTestCase("All Trainers with Pricing Information", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/all-trainers?limit=5`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        
        if (response.body.data && response.body.data.length > 0) {
          const trainer = response.body.data[0];
          this.framework.assert(
            trainer.hasOwnProperty('pricing'),
            "Trainer should have pricing property"
          );
          this.framework.assert(
            trainer.hasOwnProperty('price'),
            "Trainer should have price property"
          );
          this.framework.assert(
            typeof trainer.price === 'number',
            "Price should be a number"
          );
        }
      });

      // Test 20: Trainers You May Like with Pricing Information
      this.framework.addTestCase("Trainers You May Like with Pricing Information", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/trainers-you-may-like?limit=5`,
          {
            method: "GET",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`
            }
          }
        );

        // Should work with or without auth
        this.framework.assert(
          response.status === 200 || response.status === 401,
          "Should return 200 or 401 (if token invalid)"
        );
        
        if (response.status === 200 && response.body.data && response.body.data.length > 0) {
          const trainer = response.body.data[0];
          this.framework.assert(
            trainer.hasOwnProperty('pricing'),
            "Trainer should have pricing property"
          );
          this.framework.assert(
            trainer.hasOwnProperty('price'),
            "Trainer should have price property"
          );
          this.framework.assert(
            typeof trainer.price === 'number',
            "Price should be a number"
          );
        }
      });

      // ========== PAGINATION AND VALIDATION TESTS ==========

      // Test 21: Pagination - Large Limit Constraint
      this.framework.addTestCase("Pagination - Large Limit Constraint", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/all-programs?limit=100`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        
        if (response.body.pagination) {
          this.framework.assert(
            response.body.pagination.limit <= 50,
            "Limit should be capped at 50"
          );
        }
      });

      // Test 22: Invalid Parameters - Graceful Handling
      this.framework.addTestCase("Invalid Parameters - Graceful Handling", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/all-programs?page=invalid&limit=abc`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code even with invalid params"
        );
        
        if (response.body.pagination) {
          this.framework.assert(
            response.body.pagination.page === 1,
            "Should default to page 1 for invalid page"
          );
        }
      });

      // Test 23: Performance - Response Time
      this.framework.addTestCase("Performance - Response Time", async () => {
        const startTime = Date.now();
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/landing/top-rated-programs`,
          {
            method: "GET"
          }
        );
        const responseTime = Date.now() - startTime;

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(
          responseTime < 5000,
          "Response time should be under 5 seconds"
        );
        
        console.log(`⏱️  Response time: ${responseTime}ms`);
      });
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new LandingPageApiTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
