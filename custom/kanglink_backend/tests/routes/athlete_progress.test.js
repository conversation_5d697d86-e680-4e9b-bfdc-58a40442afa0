const { TestSuite } = require("../../../tests/apitesting.base");

const testSuite = new TestSuite("Athlete Progress Tracking Tests");

// Test data
let testData = {
  athleteToken: null,
  trainerToken: null,
  enrollmentId: null,
  exerciseInstanceId: null,
  dayId: null,
  notificationId: null
};

// Setup test data
testSuite.addTest("Setup - Create test users and enrollment", async (api) => {
  // This test assumes you have existing test data or will create it
  // For now, we'll use placeholder IDs that should be replaced with actual test data
  testData.enrollmentId = 1;
  testData.exerciseInstanceId = 1;
  testData.dayId = 1;
  
  return {
    success: true,
    message: "Test data setup complete"
  };
});

// Test creating progress tracking tables
testSuite.addTest("Create progress tracking tables", async (api) => {
  const response = await api.post("/v2/api/kanglink/develop/create-progress-tables", {}, {
    headers: {
      Authorization: "Bearer super_admin_token" // Replace with actual super admin token
    }
  });

  if (response.status !== 200) {
    return {
      success: false,
      message: `Failed to create tables: ${response.data?.message || 'Unknown error'}`
    };
  }

  return {
    success: true,
    message: "Progress tracking tables created successfully"
  };
});

// Test marking exercise as complete
testSuite.addTest("Mark exercise as complete", async (api) => {
  const exerciseData = {
    enrollment_id: testData.enrollmentId,
    exercise_instance_id: testData.exerciseInstanceId,
    sets_completed: 3,
    reps_completed: "10,10,8",
    weight_used: "50kg",
    time_taken_seconds: 300,
    difficulty_rating: 4,
    notes: "Test exercise completion"
  };

  const response = await api.post("/v2/api/kanglink/custom/athlete/exercise/complete", exerciseData, {
    headers: {
      Authorization: `Bearer ${testData.athleteToken}` // Replace with actual athlete token
    }
  });

  if (response.status !== 200) {
    return {
      success: false,
      message: `Failed to mark exercise complete: ${response.data?.message || 'Unknown error'}`
    };
  }

  if (!response.data.data.exercise_instance_id) {
    return {
      success: false,
      message: "Response missing exercise_instance_id"
    };
  }

  return {
    success: true,
    message: "Exercise marked as complete successfully",
    data: response.data.data
  };
});

// Test marking day as complete
testSuite.addTest("Mark day as complete", async (api) => {
  const dayData = {
    enrollment_id: testData.enrollmentId,
    day_id: testData.dayId,
    notes: "Test day completion"
  };

  const response = await api.post("/v2/api/kanglink/custom/athlete/day/complete", dayData, {
    headers: {
      Authorization: `Bearer ${testData.athleteToken}`
    }
  });

  if (response.status !== 200) {
    return {
      success: false,
      message: `Failed to mark day complete: ${response.data?.message || 'Unknown error'}`
    };
  }

  if (!response.data.data.day_id) {
    return {
      success: false,
      message: "Response missing day_id"
    };
  }

  return {
    success: true,
    message: "Day marked as complete successfully",
    data: response.data.data
  };
});

// Test getting athlete progress
testSuite.addTest("Get athlete progress", async (api) => {
  const response = await api.get(`/v2/api/kanglink/custom/athlete/progress/${testData.enrollmentId}`, {
    headers: {
      Authorization: `Bearer ${testData.athleteToken}`
    }
  });

  if (response.status !== 200) {
    return {
      success: false,
      message: `Failed to get athlete progress: ${response.data?.message || 'Unknown error'}`
    };
  }

  if (!response.data.data) {
    return {
      success: false,
      message: "Response missing progress data"
    };
  }

  return {
    success: true,
    message: "Athlete progress retrieved successfully",
    data: {
      hasOverallProgress: !!response.data.data.overall_progress,
      dayProgressCount: response.data.data.day_progress?.length || 0,
      exerciseProgressCount: response.data.data.exercise_progress?.length || 0
    }
  };
});

// Test getting trainer's athletes progress
testSuite.addTest("Get trainer athletes progress", async (api) => {
  const response = await api.get("/v2/api/kanglink/custom/trainer/athletes-progress", {
    headers: {
      Authorization: `Bearer ${testData.trainerToken}` // Replace with actual trainer token
    }
  });

  if (response.status !== 200) {
    return {
      success: false,
      message: `Failed to get trainer athletes progress: ${response.data?.message || 'Unknown error'}`
    };
  }

  if (!Array.isArray(response.data.data)) {
    return {
      success: false,
      message: "Response data should be an array"
    };
  }

  return {
    success: true,
    message: "Trainer athletes progress retrieved successfully",
    data: {
      athleteCount: response.data.data.length
    }
  };
});

// Test getting trainer notifications
testSuite.addTest("Get trainer notifications", async (api) => {
  const response = await api.get("/v2/api/kanglink/custom/trainer/notifications", {
    headers: {
      Authorization: `Bearer ${testData.trainerToken}`
    }
  });

  if (response.status !== 200) {
    return {
      success: false,
      message: `Failed to get trainer notifications: ${response.data?.message || 'Unknown error'}`
    };
  }

  if (!response.data.data.notifications || !Array.isArray(response.data.data.notifications)) {
    return {
      success: false,
      message: "Response missing notifications array"
    };
  }

  // Store first notification ID for read test
  if (response.data.data.notifications.length > 0) {
    testData.notificationId = response.data.data.notifications[0].id;
  }

  return {
    success: true,
    message: "Trainer notifications retrieved successfully",
    data: {
      notificationCount: response.data.data.notifications.length,
      unreadCount: response.data.data.unread_count
    }
  };
});

// Test marking notification as read
testSuite.addTest("Mark notification as read", async (api) => {
  if (!testData.notificationId) {
    return {
      success: true,
      message: "No notifications to mark as read (skipped)"
    };
  }

  const response = await api.put(`/v2/api/kanglink/custom/trainer/notifications/${testData.notificationId}/read`, {}, {
    headers: {
      Authorization: `Bearer ${testData.trainerToken}`
    }
  });

  if (response.status !== 200) {
    return {
      success: false,
      message: `Failed to mark notification as read: ${response.data?.message || 'Unknown error'}`
    };
  }

  return {
    success: true,
    message: "Notification marked as read successfully"
  };
});

// Test validation - missing required fields
testSuite.addTest("Validation - Mark exercise complete without required fields", async (api) => {
  const response = await api.post("/v2/api/kanglink/custom/athlete/exercise/complete", {
    // Missing enrollment_id and exercise_instance_id
    sets_completed: 3
  }, {
    headers: {
      Authorization: `Bearer ${testData.athleteToken}`
    }
  });

  if (response.status !== 400) {
    return {
      success: false,
      message: `Expected 400 status, got ${response.status}`
    };
  }

  return {
    success: true,
    message: "Validation correctly rejected missing required fields"
  };
});

// Test validation - mark day complete without required fields
testSuite.addTest("Validation - Mark day complete without required fields", async (api) => {
  const response = await api.post("/v2/api/kanglink/custom/athlete/day/complete", {
    // Missing enrollment_id and day_id
    notes: "Test notes"
  }, {
    headers: {
      Authorization: `Bearer ${testData.athleteToken}`
    }
  });

  if (response.status !== 400) {
    return {
      success: false,
      message: `Expected 400 status, got ${response.status}`
    };
  }

  return {
    success: true,
    message: "Validation correctly rejected missing required fields"
  };
});

module.exports = testSuite;
