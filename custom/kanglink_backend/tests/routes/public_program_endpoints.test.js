const APITestFramework = require("../../../../tests/apitesting.base.js");
const BASE_URL = "http://localhost:5172";

/**
 * Public Program Endpoints Test Suite
 *
 * Tests the public program endpoints that don't require authentication:
 * 1. GET /v2/api/kanglink/custom/public/program/:program_id
 * 2. GET /v2/api/kanglink/custom/public/program/:program_id/reviews
 */
class PublicProgramEndpointsTests {
  constructor() {
    this.framework = new APITestFramework();
    this.testData = {
      validProgramId: 1,
      invalidProgramId: "invalid",
      nonExistentProgramId: 99999,
    };
    this.setupTests();
  }

  validateProgramDetailsResponse(response) {
    const { data } = response.data;

    // Required fields validation
    const requiredFields = [
      "id",
      "user_id",
      "program_name",
      "type_of_program",
      "program_description",
      "target_levels",
      "currency",
      "rating",
      "review_count",
      "splits",
      "trainer",
    ];

    for (const field of requiredFields) {
      if (!(field in data)) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Type validations
    if (typeof data.id !== "number") throw new Error("id should be number");
    if (typeof data.program_name !== "string")
      throw new Error("program_name should be string");
    if (!Array.isArray(data.target_levels))
      throw new Error("target_levels should be array");
    if (!Array.isArray(data.splits)) throw new Error("splits should be array");
    if (typeof data.rating !== "number")
      throw new Error("rating should be number");
    if (data.rating < 0 || data.rating > 5)
      throw new Error("rating should be between 0-5");

    // Trainer object validation
    const trainer = data.trainer;
    if (!trainer.id || !trainer.email) {
      throw new Error("Trainer object missing required fields");
    }

    return true;
  }

  validateReviewsResponse(response) {
    const { data, pagination } = response.data;

    if (!Array.isArray(data)) {
      throw new Error("Reviews data should be an array");
    }

    // Validate pagination object
    const requiredPaginationFields = [
      "page",
      "limit",
      "total",
      "num_pages",
      "has_next",
      "has_prev",
    ];
    for (const field of requiredPaginationFields) {
      if (!(field in pagination)) {
        throw new Error(`Missing pagination field: ${field}`);
      }
    }

    // Validate individual review items
    for (const review of data) {
      if (!review.id || !review.user_id || !review.program_id) {
        throw new Error("Review missing required fields");
      }
      if (
        typeof review.rating !== "number" ||
        review.rating < 1 ||
        review.rating > 5
      ) {
        throw new Error("Review rating should be number between 1-5");
      }
      if (!review.user || !review.user.id) {
        throw new Error("Review user object missing required fields");
      }
    }

    return true;
  }

  setupTests() {
    // Test 1: Get program details - valid program
    this.framework.addTest({
      name: "Get Program Details - Valid Program",
      method: "GET",
      url: `${BASE_URL}/v2/api/kanglink/custom/public/program/1`,
      headers: {},
      body: {},
      expectedStatus: [200, 404], // 200 if program exists, 404 if not
      expectedSchema: [this.programDetailsSchema, this.errorResponseSchema],
      description:
        "Should return program details with splits and trainer info for published programs",
    });

    // Test 2: Get program details - invalid program ID
    this.framework.addTest({
      name: "Get Program Details - Invalid Program ID",
      method: "GET",
      url: `${BASE_URL}/v2/api/kanglink/custom/public/program/invalid`,
      headers: {},
      body: {},
      expectedStatus: 400,
      expectedSchema: this.errorResponseSchema,
      description: "Should return 400 for invalid program ID",
    });

    // Test 3: Get program details - non-existent program
    this.framework.addTest({
      name: "Get Program Details - Non-existent Program",
      method: "GET",
      url: `${BASE_URL}/v2/api/kanglink/custom/public/program/99999`,
      headers: {},
      body: {},
      expectedStatus: 404,
      expectedSchema: this.errorResponseSchema,
      description: "Should return 404 for non-existent program",
    });

    // Test 4: Get program reviews - valid program
    this.framework.addTest({
      name: "Get Program Reviews - Valid Program",
      method: "GET",
      url: `${BASE_URL}/v2/api/kanglink/custom/public/program/1/reviews`,
      headers: {},
      body: {},
      expectedStatus: [200, 404], // 200 if program exists, 404 if not
      expectedSchema: [this.reviewsListSchema, this.errorResponseSchema],
      description:
        "Should return paginated list of public reviews for the program",
    });

    // Test 5: Get program reviews - with pagination
    this.framework.addTest({
      name: "Get Program Reviews - With Pagination",
      method: "GET",
      url: `${BASE_URL}/v2/api/kanglink/custom/public/program/1/reviews?page=1&limit=5`,
      headers: {},
      body: {},
      expectedStatus: [200, 404],
      expectedSchema: [this.reviewsListSchema, this.errorResponseSchema],
      description:
        "Should return paginated reviews with specified page and limit",
    });

    // Test 6: Get program reviews - with sorting
    this.framework.addTest({
      name: "Get Program Reviews - With Sorting",
      method: "GET",
      url: `${BASE_URL}/v2/api/kanglink/custom/public/program/1/reviews?sort_by=rating&sort_order=desc`,
      headers: {},
      body: {},
      expectedStatus: [200, 404],
      expectedSchema: [this.reviewsListSchema, this.errorResponseSchema],
      description: "Should return reviews sorted by rating in descending order",
    });

    // Test 7: Get program reviews - invalid sort parameter
    this.framework.addTest({
      name: "Get Program Reviews - Invalid Sort Parameter",
      method: "GET",
      url: `${BASE_URL}/v2/api/kanglink/custom/public/program/1/reviews?sort_by=invalid_field`,
      headers: {},
      body: {},
      expectedStatus: 400,
      expectedSchema: this.errorResponseSchema,
      description: "Should return 400 for invalid sort_by parameter",
    });

    // Test 8: Get program reviews - invalid program ID
    this.framework.addTest({
      name: "Get Program Reviews - Invalid Program ID",
      method: "GET",
      url: `${BASE_URL}/v2/api/kanglink/custom/public/program/invalid/reviews`,
      headers: {},
      body: {},
      expectedStatus: 400,
      expectedSchema: this.errorResponseSchema,
      description: "Should return 400 for invalid program ID",
    });

    // Test 9: Get program reviews - non-existent program
    this.framework.addTest({
      name: "Get Program Reviews - Non-existent Program",
      method: "GET",
      url: `${BASE_URL}/v2/api/kanglink/custom/public/program/99999/reviews`,
      headers: {},
      body: {},
      expectedStatus: 404,
      expectedSchema: this.errorResponseSchema,
      description: "Should return 404 for non-existent program",
    });
  }

  async runTests() {
    console.log("🧪 Starting Public Program Endpoints Tests...\n");

    try {
      const results = await this.framework.runAllTests();

      console.log("\n📊 Test Results Summary:");
      console.log(`✅ Passed: ${results.passed}`);
      console.log(`❌ Failed: ${results.failed}`);
      console.log(`📊 Total: ${results.total}`);
      console.log(`🎯 Success Rate: ${results.successRate}%`);

      if (results.failed > 0) {
        console.log("\n❌ Failed Tests:");
        results.failures.forEach((failure) => {
          console.log(`  - ${failure.name}: ${failure.error}`);
        });
      }

      return results;
    } catch (error) {
      console.error("❌ Error running tests:", error);
      throw error;
    }
  }
}

// Export for use in other test files or direct execution
module.exports = PublicProgramEndpointsTests;

// Run tests if this file is executed directly
if (require.main === module) {
  const testSuite = new PublicProgramEndpointsTests();
  testSuite
    .runTests()
    .then((results) => {
      process.exit(results.failed > 0 ? 1 : 0);
    })
    .catch((error) => {
      console.error("Test execution failed:", error);
      process.exit(1);
    });
}
