const { test, expect } = require("@playwright/test");
const { ApiTestingBase } = require("../../tests/apitesting.base");

test.describe("Program Status Update with Notifications", () => {
  let apiTesting;
  let superAdminToken;
  let trainerToken;
  let programId;
  let trainerId;

  test.beforeAll(async () => {
    apiTesting = new ApiTestingBase();
    
    // Create super admin user
    const superAdminResponse = await apiTesting.createUser({
      email: "<EMAIL>",
      password: "password123",
      role: "super_admin",
      data: {
        full_name: "Super Admin",
        first_name: "<PERSON>",
        last_name: "Admin"
      }
    });
    superAdminToken = superAdminResponse.token;

    // Create trainer user
    const trainerResponse = await apiTesting.createUser({
      email: "<EMAIL>",
      password: "password123",
      role: "trainer",
      data: {
        full_name: "Test Trainer",
        first_name: "Test",
        last_name: "Trainer"
      }
    });
    trainerToken = trainerResponse.token;
    trainerId = trainerResponse.user.id;

    // Create a program for testing
    const programResponse = await apiTesting.createProgram({
      token: trainerToken,
      programData: {
        program_name: "Test Program for Status Update",
        description: "A test program for status update testing",
        price: 99.99,
        status: "pending_approval"
      }
    });
    programId = programResponse.program.id;
  });

  test("should update program status to rejected and send notification", async () => {
    const response = await apiTesting.makeRequest({
      method: "PUT",
      endpoint: `/v2/api/kanglink/custom/super_admin/programs/${programId}/status`,
      token: superAdminToken,
      body: {
        status: "rejected",
        rejection_reason: "Program does not meet quality standards"
      }
    });

    expect(response.status).toBe(200);
    expect(response.data.error).toBe(false);
    expect(response.data.message).toContain("Program status updated to rejected successfully");
    expect(response.data.data.status).toBe("rejected");
    expect(response.data.data.rejection_reason).toBe("Program does not meet quality standards");

    // Check if notification was created
    const notificationsResponse = await apiTesting.makeRequest({
      method: "GET",
      endpoint: `/v1/api/kanglink/custom/trainer/notifications`,
      token: trainerToken,
      params: {
        page: 1,
        limit: 10
      }
    });

    expect(notificationsResponse.status).toBe(200);
    expect(notificationsResponse.data.data).toBeDefined();
    
    const notifications = notificationsResponse.data.data;
    const programNotification = notifications.find(n => 
      n.notification_type === "program_rejected" && 
      n.related_id === programId
    );
    
    expect(programNotification).toBeDefined();
    expect(programNotification.title).toBe("Program Rejected");
    expect(programNotification.message).toContain("Test Program for Status Update");
    expect(programNotification.message).toContain("Program does not meet quality standards");
  });

  test("should update program status to deleted and send notification", async () => {
    // Create another program for deletion test
    const programResponse = await apiTesting.createProgram({
      token: trainerToken,
      programData: {
        program_name: "Test Program for Deletion",
        description: "A test program for deletion testing",
        price: 149.99,
        status: "published"
      }
    });
    const deleteProgramId = programResponse.program.id;

    const response = await apiTesting.makeRequest({
      method: "PUT",
      endpoint: `/v2/api/kanglink/custom/super_admin/programs/${deleteProgramId}/status`,
      token: superAdminToken,
      body: {
        status: "deleted",
        rejection_reason: "Program violates community guidelines"
      }
    });

    expect(response.status).toBe(200);
    expect(response.data.error).toBe(false);
    expect(response.data.message).toContain("Program status updated to deleted successfully");
    expect(response.data.data.status).toBe("deleted");
    expect(response.data.data.rejection_reason).toBe("Program violates community guidelines");

    // Check if notification was created
    const notificationsResponse = await apiTesting.makeRequest({
      method: "GET",
      endpoint: `/v1/api/kanglink/custom/trainer/notifications`,
      token: trainerToken,
      params: {
        page: 1,
        limit: 10
      }
    });

    expect(notificationsResponse.status).toBe(200);
    expect(notificationsResponse.data.data).toBeDefined();
    
    const notifications = notificationsResponse.data.data;
    const programNotification = notifications.find(n => 
      n.notification_type === "program_deleted" && 
      n.related_id === deleteProgramId
    );
    
    expect(programNotification).toBeDefined();
    expect(programNotification.title).toBe("Program Deleted");
    expect(programNotification.message).toContain("Test Program for Deletion");
    expect(programNotification.message).toContain("Program violates community guidelines");
  });

  test("should update program status to archived and send notification", async () => {
    // Create another program for archiving test
    const programResponse = await apiTesting.createProgram({
      token: trainerToken,
      programData: {
        program_name: "Test Program for Archiving",
        description: "A test program for archiving testing",
        price: 199.99,
        status: "published"
      }
    });
    const archiveProgramId = programResponse.program.id;

    const response = await apiTesting.makeRequest({
      method: "PUT",
      endpoint: `/v2/api/kanglink/custom/super_admin/programs/${archiveProgramId}/status`,
      token: superAdminToken,
      body: {
        status: "archived",
        rejection_reason: "Program is no longer relevant"
      }
    });

    expect(response.status).toBe(200);
    expect(response.data.error).toBe(false);
    expect(response.data.message).toContain("Program status updated to archived successfully");
    expect(response.data.data.status).toBe("archived");
    expect(response.data.data.rejection_reason).toBe("Program is no longer relevant");

    // Check if notification was created
    const notificationsResponse = await apiTesting.makeRequest({
      method: "GET",
      endpoint: `/v1/api/kanglink/custom/trainer/notifications`,
      token: trainerToken,
      params: {
        page: 1,
        limit: 10
      }
    });

    expect(notificationsResponse.status).toBe(200);
    expect(notificationsResponse.data.data).toBeDefined();
    
    const notifications = notificationsResponse.data.data;
    const programNotification = notifications.find(n => 
      n.notification_type === "program_archived" && 
      n.related_id === archiveProgramId
    );
    
    expect(programNotification).toBeDefined();
    expect(programNotification.title).toBe("Program Archived");
    expect(programNotification.message).toContain("Test Program for Archiving");
    expect(programNotification.message).toContain("Program is no longer relevant");
  });

  test("should require rejection reason for rejected/archived/deleted status", async () => {
    const response = await apiTesting.makeRequest({
      method: "PUT",
      endpoint: `/v2/api/kanglink/custom/super_admin/programs/${programId}/status`,
      token: superAdminToken,
      body: {
        status: "rejected"
        // Missing rejection_reason
      }
    });

    expect(response.status).toBe(400);
    expect(response.data.error).toBe(true);
    expect(response.data.message).toContain("Rejection reason is required");
  });

  test("should validate program ID", async () => {
    const response = await apiTesting.makeRequest({
      method: "PUT",
      endpoint: `/v2/api/kanglink/custom/super_admin/programs/invalid_id/status`,
      token: superAdminToken,
      body: {
        status: "rejected",
        rejection_reason: "Test reason"
      }
    });

    expect(response.status).toBe(400);
    expect(response.data.error).toBe(true);
    expect(response.data.message).toContain("Invalid program ID provided");
  });

  test("should validate status values", async () => {
    const response = await apiTesting.makeRequest({
      method: "PUT",
      endpoint: `/v2/api/kanglink/custom/super_admin/programs/${programId}/status`,
      token: superAdminToken,
      body: {
        status: "invalid_status",
        rejection_reason: "Test reason"
      }
    });

    expect(response.status).toBe(400);
    expect(response.data.error).toBe(true);
    expect(response.data.message).toContain("Invalid status");
  });

  test("should handle non-existent program", async () => {
    const response = await apiTesting.makeRequest({
      method: "PUT",
      endpoint: `/v2/api/kanglink/custom/super_admin/programs/99999/status`,
      token: superAdminToken,
      body: {
        status: "rejected",
        rejection_reason: "Test reason"
      }
    });

    expect(response.status).toBe(404);
    expect(response.data.error).toBe(true);
    expect(response.data.message).toContain("Program not found");
  });
}); 