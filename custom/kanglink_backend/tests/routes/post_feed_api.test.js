const APITestFramework = require("../../../../tests/apitesting.base.js");
const BASE_URL = "http://localhost:5172";

/**
 * Post Feed API Test Suite
 * 
 * Tests the post feed endpoints for trainer social media functionality
 */
class PostFeedApiTests {
  constructor() {
    this.framework = new APITestFramework();
    this.testData = {
      validProgramId: 123,
      invalidProgramId: "invalid",
      nonExistentProgramId: 99999,
      testToken: "your-test-token-here", // Replace with actual test token
      testPostData: {
        program_id: 123,
        post_type: "post",
        content: "This is a test post for the program!",
        visibility_scope: "public",
        is_private: false
      },
      testReviewData: {
        program_id: 123,
        post_type: "review",
        content: "Amazing program! Really helped me build strength and confidence.",
        rating: 5,
        visibility_scope: "public",
        is_private: false
      }
    };
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Post Feed API Tests", () => {
      // Setup before running tests
      this.framework.beforeEach(async () => {
        // Setup code here if needed
      });

      // ========== GET FEED TESTS ==========

      // Test 1: Get paginated posts for a program
      this.framework.addTestCase("Get Program Feed - Valid Program", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/trainer/feed?program_id=${this.testData.validProgramId}&page=1&limit=10`,
          {
            method: "GET",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`
            }
          }
        );

        // Handle case where authentication is required
        if (response.status === 401) {
          console.log(`⚠️  Authentication required - update testToken for full testing`);
          return;
        }

        // Handle case where program doesn't exist
        if (response.status === 404) {
          console.log(`⚠️  Program ${this.testData.validProgramId} not found - this is expected if no test data exists`);
          return;
        }

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(
          response.body.error === false || response.body.success === true,
          "Should not return error"
        );
        
        if (response.body.data) {
          this.framework.assert(
            Array.isArray(response.body.data),
            "Should return posts array"
          );
        }
      });

      // Test 2: Get feed - missing authentication
      this.framework.addTestCase("Get Program Feed - Missing Authentication", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/trainer/feed?program_id=${this.testData.validProgramId}`,
          {
            method: "GET"
          }
        );

        this.framework.assert(
          response.status === 401,
          "Should return 401 status code for missing authentication"
        );
      });

      // Test 3: Get feed - invalid program ID
      this.framework.addTestCase("Get Program Feed - Invalid Program ID", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/trainer/feed?program_id=${this.testData.invalidProgramId}`,
          {
            method: "GET",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`
            }
          }
        );

        if (response.status === 401) {
          console.log(`⚠️  Authentication required - update testToken for full testing`);
          return;
        }

        this.framework.assert(
          response.status === 400,
          "Should return 400 status code for invalid program ID"
        );
      });

      // ========== CREATE POST TESTS ==========

      // Test 4: Create a new post
      this.framework.addTestCase("Create Post - Valid Data", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/trainer/feed`,
          {
            method: "POST",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.testData.testPostData)
          }
        );

        if (response.status === 401) {
          console.log(`⚠️  Authentication required - update testToken for full testing`);
          return;
        }

        // Could be 200 or 201 depending on implementation
        this.framework.assert(
          response.status === 200 || response.status === 201,
          "Should return 200 or 201 status code for successful post creation"
        );
        
        if (response.body.success || !response.body.error) {
          this.framework.assert(
            response.body.data,
            "Should return post data"
          );
        }
      });

      // Test 5: Create a review post with rating
      this.framework.addTestCase("Create Review - Valid Data", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/trainer/feed`,
          {
            method: "POST",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.testData.testReviewData)
          }
        );

        if (response.status === 401) {
          console.log(`⚠️  Authentication required - update testToken for full testing`);
          return;
        }

        this.framework.assert(
          response.status === 200 || response.status === 201,
          "Should return 200 or 201 status code for successful review creation"
        );
        
        if (response.body.success || !response.body.error) {
          this.framework.assert(
            response.body.data,
            "Should return review data"
          );
        }
      });

      // Test 6: Create post - missing authentication
      this.framework.addTestCase("Create Post - Missing Authentication", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/trainer/feed`,
          {
            method: "POST",
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(this.testData.testPostData)
          }
        );

        this.framework.assert(
          response.status === 401,
          "Should return 401 status code for missing authentication"
        );
      });

      // Test 7: Create post - invalid data
      this.framework.addTestCase("Create Post - Invalid Data", async () => {
        const invalidPostData = {
          // Missing required fields
          content: "Test post without program_id"
        };

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/trainer/feed`,
          {
            method: "POST",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(invalidPostData)
          }
        );

        if (response.status === 401) {
          console.log(`⚠️  Authentication required - update testToken for full testing`);
          return;
        }

        this.framework.assert(
          response.status === 400,
          "Should return 400 status code for invalid post data"
        );
      });

      // ========== UPDATE POST TESTS ==========

      // Test 8: Update post - placeholder (would need existing post ID)
      this.framework.addTestCase("Update Post - Placeholder Test", async () => {
        // This test would require an existing post ID
        // For now, we'll just test the endpoint structure
        const testPostId = 1;
        
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/trainer/feed/${testPostId}`,
          {
            method: "PUT",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              content: "Updated post content"
            })
          }
        );

        if (response.status === 401) {
          console.log(`⚠️  Authentication required - update testToken for full testing`);
          return;
        }

        // Could be 404 if post doesn't exist, which is expected
        this.framework.assert(
          response.status === 200 || response.status === 404,
          "Should return 200 for successful update or 404 for non-existent post"
        );
      });

      // ========== DELETE POST TESTS ==========

      // Test 9: Delete post - placeholder (would need existing post ID)
      this.framework.addTestCase("Delete Post - Placeholder Test", async () => {
        const testPostId = 1;
        
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/trainer/feed/${testPostId}`,
          {
            method: "DELETE",
            headers: {
              'Authorization': `Bearer ${this.testData.testToken}`
            }
          }
        );

        if (response.status === 401) {
          console.log(`⚠️  Authentication required - update testToken for full testing`);
          return;
        }

        // Could be 404 if post doesn't exist, which is expected
        this.framework.assert(
          response.status === 200 || response.status === 404,
          "Should return 200 for successful deletion or 404 for non-existent post"
        );
      });
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new PostFeedApiTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
