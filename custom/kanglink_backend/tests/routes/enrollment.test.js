const {
  describe,
  it,
  before,
  after,
} = require("../../../tests/apitesting.base");

describe("Enrollment API Tests", () => {
  let testData = {};

  before(async (sdk) => {
    // Set up test data
    sdk.setProjectId("kanglink");

    // Create test trainer
    sdk.setTable("user");
    const trainer = await sdk.create("user", {
      email: "<EMAIL>",
      password: "password123",
      role_id: "trainer",
      status: 0,
      verify: true,
      data: JSON.stringify({
        full_name: "Test Trainer",
        first_name: "Test",
        last_name: "Trainer",
      }),
    });
    testData.trainerId = trainer.id;

    // Create test athlete
    const athlete = await sdk.create("user", {
      email: "<EMAIL>",
      password: "password123",
      role_id: "member",
      status: 0,
      verify: true,
      data: JSON.stringify({
        full_name: "Test Athlete",
        first_name: "Test",
        last_name: "Athlete",
      }),
    });
    testData.athleteId = athlete.id;

    // Create test program
    sdk.setTable("program");
    const program = await sdk.create("program", {
      user_id: trainer.id,
      program_name: "Test Enrollment Program",
      program_description: "Test program for enrollment testing",
      type_of_program: "strength",
      status: "published",
      currency: "USD",
    });
    testData.programId = program.id;

    // Create test split with both pricing options
    sdk.setTable("split");
    const split = await sdk.create("split", {
      program_id: program.id,
      title: "Test Split",
      equipment_required: "None",
      full_price: 149.99,
      subscription: 29.99,
    });
    testData.splitId = split.id;

    // Create Stripe prices for testing
    sdk.setTable("stripe_price");

    // One-time price
    const oneTimePrice = await sdk.create("stripe_price", {
      name: `${program.program_name} - ${split.title} - One Time`,
      stripe_id: "price_test_onetime_123",
      amount: 149.99,
      type: "one_time",
      status: 1,
    });
    testData.oneTimePriceId = oneTimePrice.id;

    // Subscription price
    const subscriptionPrice = await sdk.create("stripe_price", {
      name: `${program.program_name} - ${split.title} Monthly`,
      stripe_id: "price_test_subscription_123",
      amount: 29.99,
      type: "recurring",
      status: 1,
    });
    testData.subscriptionPriceId = subscriptionPrice.id;

    // Generate auth tokens for testing
    const AuthService = require("../../../../baas/services/AuthService");
    const authService = new AuthService();

    // Create trainer token
    testData.trainerToken = await authService.generateAccessToken({
      user_id: trainer.id,
      role: "trainer",
      email: trainer.email,
    });

    // Create athlete token
    testData.athleteToken = await authService.generateAccessToken({
      user_id: athlete.id,
      role: "member",
      email: athlete.email,
    });

    console.log("Test data created:", testData);
  });

  after(async (sdk) => {
    // Clean up test data
    sdk.setProjectId("kanglink");

    // Clean up enrollments
    sdk.setTable("enrollment");
    await sdk.rawQuery(
      `DELETE FROM kanglink_enrollment WHERE athlete_id = ${testData.athleteId}`
    );

    // Clean up splits
    sdk.setTable("split");
    await sdk.deleteById("split", testData.splitId);

    // Clean up programs
    sdk.setTable("program");
    await sdk.deleteById("program", testData.programId);

    // Clean up users
    sdk.setTable("user");
    await sdk.deleteById("user", testData.trainerId);
    await sdk.deleteById("user", testData.athleteId);

    // Clean up stripe prices
    sdk.setTable("stripe_price");
    await sdk.deleteById("stripe_price", testData.oneTimePriceId);
    await sdk.deleteById("stripe_price", testData.subscriptionPriceId);
  });

  describe("POST /v2/api/kanglink/custom/athlete/enroll", () => {
    it("should validate required fields", async (sdk, request) => {
      const response = await request
        .post("/v2/api/kanglink/custom/athlete/enroll")
        .set("Authorization", `Bearer ${testData.athleteToken}`)
        .send({});

      response.status.should.equal(400);
      response.body.should.have.property("error", true);
      response.body.message.should.include("required");
    });

    it("should validate payment_type enum", async (sdk, request) => {
      const response = await request
        .post("/v2/api/kanglink/custom/athlete/enroll")
        .set("Authorization", `Bearer ${testData.athleteToken}`)
        .send({
          split_id: testData.splitId,
          payment_type: "invalid_type",
          payment_method_id: "pm_test_123",
        });

      response.status.should.equal(400);
      response.body.should.have.property("error", true);
      response.body.message.should.include("payment_type must be");
    });

    it("should reject enrollment for non-existent split", async (sdk, request) => {
      const response = await request
        .post("/v2/api/kanglink/custom/athlete/enroll")
        .set("Authorization", `Bearer ${testData.athleteToken}`)
        .send({
          split_id: 99999,
          payment_type: "one_time",
          payment_method_id: "pm_test_123",
        });

      response.status.should.equal(404);
      response.body.should.have.property("error", true);
      response.body.message.should.include("Split not found");
    });

    it("should reject enrollment when pricing is not available", async (sdk, request) => {
      // Create a split without pricing
      sdk.setTable("split");
      const splitNoPricing = await sdk.create("split", {
        program_id: testData.programId,
        title: "No Pricing Split",
        equipment_required: "None",
        full_price: 0,
        subscription: 0,
      });

      const response = await request
        .post("/v2/api/kanglink/custom/athlete/enroll")
        .set("Authorization", `Bearer ${testData.athleteToken}`)
        .send({
          split_id: splitNoPricing.id,
          payment_type: "one_time",
          payment_method_id: "pm_test_123",
        });

      response.status.should.equal(400);
      response.body.should.have.property("error", true);
      response.body.message.should.include("price not available");

      // Clean up
      await sdk.deleteById("split", splitNoPricing.id);
    });

    it("should reject enrollment when Stripe price is not configured", async (sdk, request) => {
      // Create a split with pricing but no Stripe price
      sdk.setTable("split");
      const splitNoStripePrice = await sdk.create("split", {
        program_id: testData.programId,
        title: "No Stripe Price Split",
        equipment_required: "None",
        full_price: 99.99,
        subscription: 19.99,
      });

      const response = await request
        .post("/v2/api/kanglink/custom/athlete/enroll")
        .set("Authorization", `Bearer ${testData.athleteToken}`)
        .send({
          split_id: splitNoStripePrice.id,
          payment_type: "one_time",
          payment_method_id: "pm_test_123",
        });

      response.status.should.equal(400);
      response.body.should.have.property("error", true);
      response.body.message.should.include("pricing not configured");

      // Clean up
      await sdk.deleteById("split", splitNoStripePrice.id);
    });
  });

  describe("GET /v2/api/kanglink/custom/athlete/enrollments", () => {
    it("should return empty array when no enrollments exist", async (sdk, request) => {
      const response = await request
        .get("/v2/api/kanglink/custom/athlete/enrollments")
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      response.status.should.equal(200);
      response.body.should.have.property("error", false);
      response.body.data.should.be.an("array").that.is.empty;
    });

    it("should return enrollments with related data", async (sdk, request) => {
      // Create a test enrollment
      sdk.setTable("enrollment");
      const enrollment = await sdk.create("enrollment", {
        trainer_id: testData.trainerId,
        athlete_id: testData.athleteId,
        program_id: testData.programId,
        split_id: testData.splitId,
        payment_type: "one_time",
        amount: 149.99,
        currency: "USD",
        status: "active",
        payment_status: "paid",
        enrollment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      const response = await request
        .get("/v2/api/kanglink/custom/athlete/enrollments")
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      response.status.should.equal(200);
      response.body.should.have.property("error", false);
      response.body.data.should.be.an("array").with.length(1);

      const enrollmentData = response.body.data[0];
      enrollmentData.should.have.property("id", enrollment.id);
      enrollmentData.should.have.property("payment_type", "one_time");
      enrollmentData.should.have.property("amount", 149.99);
      enrollmentData.should.have.property(
        "program_name",
        "Test Enrollment Program"
      );
      enrollmentData.should.have.property("split_title", "Test Split");

      // Clean up
      await sdk.deleteById("enrollment", enrollment.id);
    });
  });

  describe("GET /v2/api/kanglink/custom/trainer/enrollments", () => {
    it("should return trainer's enrollments", async (sdk, request) => {
      // Create a test enrollment
      sdk.setTable("enrollment");
      const enrollment = await sdk.create("enrollment", {
        trainer_id: testData.trainerId,
        athlete_id: testData.athleteId,
        program_id: testData.programId,
        split_id: testData.splitId,
        payment_type: "subscription",
        amount: 29.99,
        currency: "USD",
        status: "active",
        payment_status: "paid",
        enrollment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      const response = await request
        .get("/v2/api/kanglink/custom/trainer/enrollments")
        .set("Authorization", `Bearer ${testData.trainerToken}`);

      response.status.should.equal(200);
      response.body.should.have.property("error", false);
      response.body.data.should.be.an("array").with.length(1);

      const enrollmentData = response.body.data[0];
      enrollmentData.should.have.property("payment_type", "subscription");
      enrollmentData.should.have.property("amount", 29.99);

      // Clean up
      await sdk.deleteById("enrollment", enrollment.id);
    });
  });

  describe("GET /v2/api/kanglink/custom/splits/:split_id", () => {
    it("should return split details with pricing", async (sdk, request) => {
      const response = await request
        .get(`/v2/api/kanglink/custom/splits/${testData.splitId}`)
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      response.status.should.equal(200);
      response.body.should.have.property("error", false);
      response.body.data.should.have.property("id", testData.splitId);
      response.body.data.should.have.property("full_price", 149.99);
      response.body.data.should.have.property("subscription", 29.99);
      response.body.data.should.have.property(
        "program_name",
        "Test Enrollment Program"
      );
    });

    it("should return 404 for non-existent split", async (sdk, request) => {
      const response = await request
        .get("/v2/api/kanglink/custom/splits/99999")
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      response.status.should.equal(404);
      response.body.should.have.property("error", true);
    });
  });

  describe("GET /v2/api/kanglink/custom/splits", () => {
    it("should return available splits for enrollment", async (sdk, request) => {
      const response = await request
        .get("/v2/api/kanglink/custom/splits")
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      response.status.should.equal(200);
      response.body.should.have.property("error", false);
      response.body.data.should.be.an("array");

      // Should include our test split since program is published
      const testSplit = response.body.data.find(
        (s) => s.id === testData.splitId
      );
      testSplit.should.exist;
      testSplit.should.have.property("full_price", 149.99);
      testSplit.should.have.property("subscription", 29.99);
    });

    it("should filter by trainer_id", async (sdk, request) => {
      const response = await request
        .get(`/v2/api/kanglink/custom/splits?trainer_id=${testData.trainerId}`)
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      response.status.should.equal(200);
      response.body.data.should.be.an("array");
      response.body.data.forEach((split) => {
        split.should.have.property("trainer_id", testData.trainerId);
      });
    });

    it("should filter by program_id", async (sdk, request) => {
      const response = await request
        .get(`/v2/api/kanglink/custom/splits?program_id=${testData.programId}`)
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      response.status.should.equal(200);
      response.body.data.should.be.an("array");
      response.body.data.forEach((split) => {
        split.should.have.property("program_id", testData.programId);
      });
    });
  });

  describe("POST /v2/api/kanglink/custom/enrollment/:enrollment_id/cancel", () => {
    it("should cancel enrollment successfully", async (sdk, request) => {
      // Create a test enrollment
      sdk.setTable("enrollment");
      const enrollment = await sdk.create("enrollment", {
        trainer_id: testData.trainerId,
        athlete_id: testData.athleteId,
        program_id: testData.programId,
        split_id: testData.splitId,
        payment_type: "subscription",
        amount: 29.99,
        currency: "USD",
        status: "active",
        payment_status: "paid",
        stripe_subscription_id: "sub_test_123",
        enrollment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      const response = await request
        .post(`/v2/api/kanglink/custom/enrollment/${enrollment.id}/cancel`)
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      response.status.should.equal(200);
      response.body.should.have.property("error", false);
      response.body.message.should.include("cancelled successfully");

      // Verify enrollment status was updated
      const updatedEnrollment = await sdk.findOne("enrollment", {
        id: enrollment.id,
      });
      updatedEnrollment.should.have.property("status", "cancelled");

      // Clean up
      await sdk.deleteById("enrollment", enrollment.id);
    });

    it("should prevent cancelling non-existent enrollment", async (sdk, request) => {
      const response = await request
        .post("/v2/api/kanglink/custom/enrollment/99999/cancel")
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      response.status.should.equal(404);
      response.body.should.have.property("error", true);
      response.body.message.should.include("Enrollment not found");
    });

    it("should prevent athlete from cancelling other athlete's enrollment", async (sdk, request) => {
      // Create another athlete
      sdk.setTable("user");
      const otherAthlete = await sdk.create("user", {
        email: "<EMAIL>",
        password: "password123",
        role_id: "member",
        status: 0,
        verify: true,
      });

      // Create enrollment for other athlete
      sdk.setTable("enrollment");
      const enrollment = await sdk.create("enrollment", {
        trainer_id: testData.trainerId,
        athlete_id: otherAthlete.id,
        program_id: testData.programId,
        split_id: testData.splitId,
        payment_type: "one_time",
        amount: 149.99,
        currency: "USD",
        status: "active",
        payment_status: "paid",
        enrollment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      const response = await request
        .post(`/v2/api/kanglink/custom/enrollment/${enrollment.id}/cancel`)
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      response.status.should.equal(403);
      response.body.should.have.property("error", true);
      response.body.message.should.include("only cancel your own");

      // Clean up
      await sdk.deleteById("enrollment", enrollment.id);
      await sdk.deleteById("user", otherAthlete.id);
    });

    it("should allow trainer to cancel enrollments for their programs", async (sdk, request) => {
      // Create enrollment
      sdk.setTable("enrollment");
      const enrollment = await sdk.create("enrollment", {
        trainer_id: testData.trainerId,
        athlete_id: testData.athleteId,
        program_id: testData.programId,
        split_id: testData.splitId,
        payment_type: "subscription",
        amount: 29.99,
        currency: "USD",
        status: "active",
        payment_status: "paid",
        enrollment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      const response = await request
        .post(`/v2/api/kanglink/custom/enrollment/${enrollment.id}/cancel`)
        .set("Authorization", `Bearer ${testData.trainerToken}`);

      response.status.should.equal(200);
      response.body.should.have.property("error", false);

      // Clean up
      await sdk.deleteById("enrollment", enrollment.id);
    });

    it("should prevent cancelling already cancelled enrollment", async (sdk, request) => {
      // Create cancelled enrollment
      sdk.setTable("enrollment");
      const enrollment = await sdk.create("enrollment", {
        trainer_id: testData.trainerId,
        athlete_id: testData.athleteId,
        program_id: testData.programId,
        split_id: testData.splitId,
        payment_type: "one_time",
        amount: 149.99,
        currency: "USD",
        status: "cancelled",
        payment_status: "paid",
        enrollment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      const response = await request
        .post(`/v2/api/kanglink/custom/enrollment/${enrollment.id}/cancel`)
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      response.status.should.equal(400);
      response.body.should.have.property("error", true);
      response.body.message.should.include("already cancelled");

      // Clean up
      await sdk.deleteById("enrollment", enrollment.id);
    });
  });

  describe("Enrollment Business Logic Tests", () => {
    it("should prevent duplicate enrollment in same split", async (sdk, request) => {
      // Create existing enrollment
      sdk.setTable("enrollment");
      const existingEnrollment = await sdk.create("enrollment", {
        trainer_id: testData.trainerId,
        athlete_id: testData.athleteId,
        program_id: testData.programId,
        split_id: testData.splitId,
        payment_type: "subscription",
        amount: 29.99,
        currency: "USD",
        status: "active",
        payment_status: "paid",
        enrollment_date: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

      // Try to enroll again
      const response = await request
        .post("/v2/api/kanglink/custom/athlete/enroll")
        .set("Authorization", `Bearer ${testData.athleteToken}`)
        .send({
          split_id: testData.splitId,
          payment_type: "one_time",
          payment_method_id: "pm_test_123",
        });

      response.status.should.equal(400);
      response.body.should.have.property("error", true);
      response.body.message.should.include("Already enrolled");

      // Clean up
      await sdk.deleteById("enrollment", existingEnrollment.id);
    });

    it("should handle different payment types correctly", async (sdk, request) => {
      // Test that subscription uses subscription price
      const subscriptionResponse = await request
        .get(`/v2/api/kanglink/custom/splits/${testData.splitId}`)
        .set("Authorization", `Bearer ${testData.athleteToken}`);

      subscriptionResponse.body.data.should.have.property(
        "subscription",
        29.99
      );
      subscriptionResponse.body.data.should.have.property("full_price", 149.99);

      // Verify pricing logic would work correctly
      const subscriptionAmount = subscriptionResponse.body.data.subscription;
      const oneTimeAmount = subscriptionResponse.body.data.full_price;

      subscriptionAmount.should.equal(29.99);
      oneTimeAmount.should.equal(149.99);
      oneTimeAmount.should.be.greaterThan(subscriptionAmount);
    });
  });
});
