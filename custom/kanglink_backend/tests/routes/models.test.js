const APITestFramework = require("../../../../tests/apitesting.base.js");

/**
 * Models Test Suite
 * 
 * Tests the discount system models for validation and functionality
 */
class ModelsTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Discount System Models Tests", () => {
      // Setup before running tests
      this.framework.beforeEach(async () => {
        // Setup code here if needed
      });

      // ========== PROGRAM DISCOUNT MODEL TESTS ==========

      // Test 1: ProgramDiscount Model - Valid Data
      this.framework.addTestCase("ProgramDiscount Model - Valid Data", async () => {
        try {
          // Try to require the model - it might not exist
          const ProgramDiscount = require('../../models/program_discount');
          
          const programDiscountData = {
            program_id: 1,
            affiliate_link: 'https://example.com/affiliate',
            sale_discount_type: 'percentage',
            sale_discount_value: 10,
            sale_apply_to_all: true
          };
          
          const programDiscount = new ProgramDiscount(programDiscountData);
          
          this.framework.assert(
            programDiscount !== null,
            "ProgramDiscount model should be created"
          );
          
          if (typeof programDiscount.isValid === 'function') {
            this.framework.assert(
              programDiscount.isValid(),
              "ProgramDiscount should be valid with correct data"
            );
          }
        } catch (error) {
          if (error.code === 'MODULE_NOT_FOUND') {
            console.log('⚠️  ProgramDiscount model not found - skipping test');
            return;
          }
          throw error;
        }
      });

      // Test 2: ProgramDiscount Model - Invalid Data
      this.framework.addTestCase("ProgramDiscount Model - Invalid Data", async () => {
        try {
          const ProgramDiscount = require('../../models/program_discount');
          
          const invalidData = {
            // Missing required program_id
            affiliate_link: 'invalid-url',
            sale_discount_value: -5 // Invalid negative value
          };
          
          const programDiscount = new ProgramDiscount(invalidData);
          
          if (typeof programDiscount.isValid === 'function') {
            this.framework.assert(
              !programDiscount.isValid(),
              "ProgramDiscount should be invalid with incorrect data"
            );
          }
        } catch (error) {
          if (error.code === 'MODULE_NOT_FOUND') {
            console.log('⚠️  ProgramDiscount model not found - skipping test');
            return;
          }
          // Model validation might throw errors, which is expected
          this.framework.assert(
            true,
            "Model validation correctly threw error for invalid data"
          );
        }
      });

      // ========== DISCOUNT MODEL TESTS ==========

      // Test 3: Discount Model - Valid Data
      this.framework.addTestCase("Discount Model - Valid Data", async () => {
        try {
          const Discount = require('../../models/discount');
          
          const discountData = {
            program_id: 1,
            split_id: 1,
            discount_type: 'percentage',
            discount_value: 15,
            applies_to: 'subscription',
            is_active: true
          };
          
          const discount = new Discount(discountData);
          
          this.framework.assert(
            discount !== null,
            "Discount model should be created"
          );
          
          if (typeof discount.isValid === 'function') {
            this.framework.assert(
              discount.isValid(),
              "Discount should be valid with correct data"
            );
          }
        } catch (error) {
          if (error.code === 'MODULE_NOT_FOUND') {
            console.log('⚠️  Discount model not found - skipping test');
            return;
          }
          throw error;
        }
      });

      // Test 4: Discount Model - Invalid Discount Type
      this.framework.addTestCase("Discount Model - Invalid Discount Type", async () => {
        try {
          const Discount = require('../../models/discount');
          
          const invalidData = {
            program_id: 1,
            split_id: 1,
            discount_type: 'invalid_type', // Invalid type
            discount_value: 15,
            applies_to: 'subscription',
            is_active: true
          };
          
          const discount = new Discount(invalidData);
          
          if (typeof discount.isValid === 'function') {
            this.framework.assert(
              !discount.isValid(),
              "Discount should be invalid with incorrect discount type"
            );
          }
        } catch (error) {
          if (error.code === 'MODULE_NOT_FOUND') {
            console.log('⚠️  Discount model not found - skipping test');
            return;
          }
          // Model validation might throw errors, which is expected
          this.framework.assert(
            true,
            "Model validation correctly handled invalid discount type"
          );
        }
      });

      // ========== COUPON MODEL TESTS ==========

      // Test 5: Coupon Model - Valid Data
      this.framework.addTestCase("Coupon Model - Valid Data", async () => {
        try {
          const Coupon = require('../../models/coupon');
          
          const couponData = {
            code: 'SAVE20',
            discount_type: 'percentage',
            discount_value: 20,
            max_uses: 100,
            expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
            is_active: true
          };
          
          const coupon = new Coupon(couponData);
          
          this.framework.assert(
            coupon !== null,
            "Coupon model should be created"
          );
          
          if (typeof coupon.isValid === 'function') {
            this.framework.assert(
              coupon.isValid(),
              "Coupon should be valid with correct data"
            );
          }
        } catch (error) {
          if (error.code === 'MODULE_NOT_FOUND') {
            console.log('⚠️  Coupon model not found - skipping test');
            return;
          }
          throw error;
        }
      });

      // Test 6: Coupon Model - Expired Coupon
      this.framework.addTestCase("Coupon Model - Expired Coupon", async () => {
        try {
          const Coupon = require('../../models/coupon');
          
          const expiredCouponData = {
            code: 'EXPIRED',
            discount_type: 'percentage',
            discount_value: 20,
            max_uses: 100,
            expires_at: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
            is_active: true
          };
          
          const coupon = new Coupon(expiredCouponData);
          
          if (typeof coupon.isExpired === 'function') {
            this.framework.assert(
              coupon.isExpired(),
              "Coupon should be detected as expired"
            );
          }
        } catch (error) {
          if (error.code === 'MODULE_NOT_FOUND') {
            console.log('⚠️  Coupon model not found - skipping test');
            return;
          }
          throw error;
        }
      });

      // ========== COUPON USAGE MODEL TESTS ==========

      // Test 7: CouponUsage Model - Valid Data
      this.framework.addTestCase("CouponUsage Model - Valid Data", async () => {
        try {
          const CouponUsage = require('../../models/coupon_usage');
          
          const usageData = {
            coupon_id: 1,
            user_id: 1,
            program_id: 1,
            split_id: 1,
            discount_amount: 19.99,
            used_at: new Date()
          };
          
          const usage = new CouponUsage(usageData);
          
          this.framework.assert(
            usage !== null,
            "CouponUsage model should be created"
          );
          
          if (typeof usage.isValid === 'function') {
            this.framework.assert(
              usage.isValid(),
              "CouponUsage should be valid with correct data"
            );
          }
        } catch (error) {
          if (error.code === 'MODULE_NOT_FOUND') {
            console.log('⚠️  CouponUsage model not found - skipping test');
            return;
          }
          throw error;
        }
      });

      // ========== MODEL INTEGRATION TESTS ==========

      // Test 8: Model Integration - Discount Calculation
      this.framework.addTestCase("Model Integration - Discount Calculation", async () => {
        try {
          // Test if models can work together for discount calculations
          const Discount = require('../../models/discount');
          
          const discount = new Discount({
            program_id: 1,
            split_id: 1,
            discount_type: 'percentage',
            discount_value: 20,
            applies_to: 'full_price',
            is_active: true
          });
          
          // Test discount calculation if method exists
          if (typeof discount.calculateDiscount === 'function') {
            const originalPrice = 100;
            const discountedPrice = discount.calculateDiscount(originalPrice);
            
            this.framework.assert(
              discountedPrice === 80,
              "20% discount should reduce $100 to $80"
            );
          } else {
            console.log('⚠️  calculateDiscount method not found - skipping calculation test');
          }
        } catch (error) {
          if (error.code === 'MODULE_NOT_FOUND') {
            console.log('⚠️  Models not found - skipping integration test');
            return;
          }
          throw error;
        }
      });
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new ModelsTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
