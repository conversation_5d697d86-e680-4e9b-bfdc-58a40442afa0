const APITestFramework = require("../../../../tests/apitesting.base.js");
const BASE_URL = "http://localhost:5172";

/**
 * Discount API Test Suite
 *
 * Tests the discount-related endpoints for trainer programs
 */
class DiscountApiTests {
  constructor() {
    this.framework = new APITestFramework();
    this.testData = {
      validProgramId: 123,
      invalidProgramId: "invalid",
      nonExistentProgramId: 99999,
      testToken: "your-test-token-here", // Replace with actual test token
    };
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Discount API Tests", () => {
      // Setup before running tests
      this.framework.beforeEach(async () => {
        // Setup code here if needed
      });

      // Test 1: Get discounts for a program - valid program
      this.framework.addTestCase(
        "Get Program Discounts - Valid Program",
        async () => {
          const response = await this.framework.makeRequest(
            `${BASE_URL}/v2/api/kanglink/custom/trainer/programs/${this.testData.validProgramId}/discounts`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${this.testData.testToken}`,
              },
            }
          );

          // Handle case where authentication is required
          if (response.status === 401) {
            console.log(
              `⚠️  Authentication required - update testToken for full testing`
            );
            return;
          }

          // Handle case where program doesn't exist
          if (response.status === 404) {
            console.log(
              `⚠️  Program ${this.testData.validProgramId} not found - this is expected if no test data exists`
            );
            return;
          }

          this.framework.assert(
            response.status === 200,
            "Should return 200 status code"
          );
          this.framework.assert(
            response.body.error === false || response.body.success === true,
            "Should not return error"
          );
        }
      );

      // Test 2: Get discounts - invalid program ID
      this.framework.addTestCase(
        "Get Program Discounts - Invalid Program ID",
        async () => {
          const response = await this.framework.makeRequest(
            `${BASE_URL}/v2/api/kanglink/custom/trainer/programs/${this.testData.invalidProgramId}/discounts`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${this.testData.testToken}`,
              },
            }
          );

          if (response.status === 401) {
            console.log(
              `⚠️  Authentication required - update testToken for full testing`
            );
            return;
          }

          this.framework.assert(
            response.status === 400,
            "Should return 400 status code for invalid program ID"
          );
        }
      );

      // Test 3: Get discounts - non-existent program
      this.framework.addTestCase(
        "Get Program Discounts - Non-existent Program",
        async () => {
          const response = await this.framework.makeRequest(
            `${BASE_URL}/v2/api/kanglink/custom/trainer/programs/${this.testData.nonExistentProgramId}/discounts`,
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${this.testData.testToken}`,
              },
            }
          );

          if (response.status === 401) {
            console.log(
              `⚠️  Authentication required - update testToken for full testing`
            );
            return;
          }

          this.framework.assert(
            response.status === 404,
            "Should return 404 status code for non-existent program"
          );
        }
      );

      // Test 4: Get discounts - missing authentication
      this.framework.addTestCase(
        "Get Program Discounts - Missing Authentication",
        async () => {
          const response = await this.framework.makeRequest(
            `${BASE_URL}/v2/api/kanglink/custom/trainer/programs/${this.testData.validProgramId}/discounts`,
            {
              method: "GET",
            }
          );

          this.framework.assert(
            response.status === 401,
            "Should return 401 status code for missing authentication"
          );
        }
      );
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new DiscountApiTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
