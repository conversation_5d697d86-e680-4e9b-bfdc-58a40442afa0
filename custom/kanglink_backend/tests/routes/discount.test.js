const APITestFramework = require("../../../../tests/apitesting.base.js");
const BASE_URL = "http://localhost:5172";

class DiscountApiTests {
  constructor() {
    this.framework = new APITestFramework();
    this.testProgramId = 1;
    this.testSplitId = 1;
    this.authToken = null;
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Trainer Discount API Tests", () => {
      // Setup before running tests
      this.framework.beforeEach(async () => {
        // Get auth token for trainer role
        const loginResponse = await this.framework.makeRequest(
          BASE_URL + "/v1/api/kanglink/trainer/lambda/login",
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
              password: "a123456",
              role: "trainer",
            }),
          }
        );

        if (loginResponse.status === 200 && loginResponse.body.token) {
          this.authToken = loginResponse.body.token;
        }
      });

      // Test GET discount endpoint - Success
      this.framework.addTestCase("Trainer GET Discount - Success", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL +
            `/v2/api/kanglink/custom/trainer/programs/${this.testProgramId}/discounts`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${this.authToken}`,
            },
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(
          response.body.success === true,
          "Should return success flag"
        );
        this.framework.assert(
          response.body.data !== undefined,
          "Should return data object"
        );
        this.framework.assert(
          typeof response.body.data.affiliateLink === "string",
          "Should return affiliate link"
        );
        this.framework.assert(
          Array.isArray(response.body.data.subscriptionDiscounts),
          "Should return subscription discounts array"
        );
        this.framework.assert(
          Array.isArray(response.body.data.fullPriceDiscounts),
          "Should return full price discounts array"
        );
      });

      // Test POST discount endpoint - Success
      this.framework.addTestCase(
        "Trainer POST Discount - Success",
        async () => {
          const requestBody = {
            affiliateLink: "https://example.com/affiliate",
            saleDiscount: {
              type: "percentage",
              value: 10,
              applyToAll: true,
            },
            subscriptionDiscounts: [
              {
                tierId: this.testSplitId,
                discountType: "percentage",
                discountValue: 15,
              },
            ],
            fullPriceDiscounts: [
              {
                tierId: this.testSplitId,
                discountType: "fixed",
                discountValue: 50,
              },
            ],
            promoCode: {
              code: "NEWUSER10",
              discountType: "percentage",
              discountValue: 10,
              appliesTo: {
                subscription: true,
                fullPayment: false,
              },
              isActive: true,
              usageLimit: 50,
              expiryDate: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toISOString(),
            },
          };

          const response = await this.framework.makeRequest(
            BASE_URL +
              `/v2/api/kanglink/custom/trainer/programs/${this.testProgramId}/discounts`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.authToken}`,
              },
              body: JSON.stringify(requestBody),
            }
          );

          this.framework.assert(
            response.status === 201,
            "Should return 201 status code"
          );
          this.framework.assert(
            response.body.success === true,
            "Should return success flag"
          );
          this.framework.assert(
            response.body.message ===
              "Discount configuration created successfully",
            "Should return success message"
          );
          this.framework.assert(
            response.body.data.programId == this.testProgramId,
            "Should return correct program ID"
          );
          this.framework.assert(
            response.body.data.programDiscountId !== undefined,
            "Should return program discount ID"
          );
          this.framework.assert(
            response.body.data.couponId !== null,
            "Should return coupon ID"
          );
        }
      );

      // Test POST discount endpoint - Conflict (already exists)
      this.framework.addTestCase(
        "Trainer POST Discount - Already Exists",
        async () => {
          const requestBody = {
            affiliateLink: "https://example.com/affiliate",
          };

          const response = await this.framework.makeRequest(
            BASE_URL +
              `/v2/api/kanglink/custom/trainer/programs/${this.testProgramId}/discounts`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.authToken}`,
              },
              body: JSON.stringify(requestBody),
            }
          );

          this.framework.assert(
            response.status === 409,
            "Should return 409 status code"
          );
          this.framework.assert(
            response.body.success === false,
            "Should return error flag"
          );
          this.framework.assert(
            response.body.message.includes("already exists"),
            "Should return conflict message"
          );
        }
      );

      // Test GET discount endpoint - Invalid Program ID
      this.framework.addTestCase(
        "Trainer GET Discount - Invalid Program ID",
        async () => {
          const response = await this.framework.makeRequest(
            BASE_URL +
              "/v2/api/kanglink/custom/trainer/programs/999999/discounts",
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.authToken}`,
              },
            }
          );

          this.framework.assert(
            response.status === 404,
            "Should return 404 status code"
          );
          this.framework.assert(
            response.body.success === false,
            "Should return error flag"
          );
          this.framework.assert(
            response.body.message === "Program not found or access denied",
            "Should return correct error message"
          );
        }
      );

      // Test PUT discount endpoint - Success with basic data
      this.framework.addTestCase(
        "Trainer PUT Discount - Success Basic",
        async () => {
          const requestBody = {
            affiliateLink: "https://example.com/affiliate",
            saleDiscount: {
              type: "percentage",
              value: 10,
              applyToAll: true,
            },
            subscriptionDiscounts: [
              {
                tierId: this.testSplitId,
                discountType: "percentage",
                discountValue: 15,
              },
            ],
            fullPriceDiscounts: [
              {
                tierId: this.testSplitId,
                discountType: "fixed",
                discountValue: 50,
              },
            ],
          };

          const response = await this.framework.makeRequest(
            BASE_URL +
              `/v2/api/kanglink/custom/trainer/programs/${this.testProgramId}/discounts`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.authToken}`,
              },
              body: JSON.stringify(requestBody),
            }
          );

          this.framework.assert(
            response.status === 200,
            "Should return 200 status code"
          );
          this.framework.assert(
            response.body.success === true,
            "Should return success flag"
          );
          this.framework.assert(
            response.body.message === "Discount settings updated successfully",
            "Should return success message"
          );
          this.framework.assert(
            response.body.data.programId == this.testProgramId,
            "Should return correct program ID"
          );
        }
      );

      // Test PUT discount endpoint - Success with promo code
      this.framework.addTestCase(
        "Trainer PUT Discount - Success with Promo Code",
        async () => {
          const requestBody = {
            affiliateLink: "https://example.com/affiliate",
            promoCode: {
              code: "SAVE20",
              discountType: "percentage",
              discountValue: 20,
              appliesTo: {
                subscription: true,
                fullPayment: true,
              },
              isActive: true,
              usageLimit: 100,
              expiryDate: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toISOString(), // 30 days from now
            },
          };

          const response = await this.framework.makeRequest(
            BASE_URL +
              `/v2/api/kanglink/custom/trainer/programs/${this.testProgramId}/discounts`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.authToken}`,
              },
              body: JSON.stringify(requestBody),
            }
          );

          this.framework.assert(
            response.status === 200,
            "Should return 200 status code"
          );
          this.framework.assert(
            response.body.success === true,
            "Should return success flag"
          );
        }
      );

      // Test PUT discount endpoint - Invalid percentage value
      this.framework.addTestCase(
        "Trainer PUT Discount - Invalid Percentage",
        async () => {
          const requestBody = {
            saleDiscount: {
              type: "percentage",
              value: 150, // Invalid: over 100%
              applyToAll: true,
            },
          };

          const response = await this.framework.makeRequest(
            BASE_URL +
              `/v2/api/kanglink/custom/trainer/programs/${this.testProgramId}/discounts`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.authToken}`,
              },
              body: JSON.stringify(requestBody),
            }
          );

          this.framework.assert(
            response.status === 500,
            "Should return 500 status code"
          );
          this.framework.assert(
            response.body.success === false,
            "Should return error flag"
          );
          this.framework.assert(
            response.body.message.includes("percentage cannot exceed 100%"),
            "Should return percentage validation error"
          );
        }
      );

      // Test PUT discount endpoint - Invalid discount type
      this.framework.addTestCase(
        "Trainer PUT Discount - Invalid Discount Type",
        async () => {
          const requestBody = {
            saleDiscount: {
              type: "invalid_type", // Invalid type
              value: 10,
              applyToAll: true,
            },
          };

          const response = await this.framework.makeRequest(
            BASE_URL +
              `/v2/api/kanglink/custom/trainer/programs/${this.testProgramId}/discounts`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.authToken}`,
              },
              body: JSON.stringify(requestBody),
            }
          );

          this.framework.assert(
            response.status === 500,
            "Should return 500 status code"
          );
          this.framework.assert(
            response.body.success === false,
            "Should return error flag"
          );
          this.framework.assert(
            response.body.message.includes("must be 'fixed' or 'percentage'"),
            "Should return type validation error"
          );
        }
      );

      // Test PUT discount endpoint - Invalid promo code
      this.framework.addTestCase(
        "Trainer PUT Discount - Invalid Promo Code",
        async () => {
          const requestBody = {
            promoCode: {
              code: "AB", // Too short
              discountType: "percentage",
              discountValue: 20,
              appliesTo: {
                subscription: true,
                fullPayment: false,
              },
            },
          };

          const response = await this.framework.makeRequest(
            BASE_URL +
              `/v2/api/kanglink/custom/trainer/programs/${this.testProgramId}/discounts`,
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.authToken}`,
              },
              body: JSON.stringify(requestBody),
            }
          );

          this.framework.assert(
            response.status === 500,
            "Should return 500 status code"
          );
          this.framework.assert(
            response.body.success === false,
            "Should return error flag"
          );
          this.framework.assert(
            response.body.message.includes("at least 3 characters long"),
            "Should return promo code length validation error"
          );
        }
      );

      // Test PUT discount endpoint - Missing program ID
      this.framework.addTestCase(
        "Trainer PUT Discount - Missing Program ID",
        async () => {
          const response = await this.framework.makeRequest(
            BASE_URL + "/v2/api/kanglink/custom/trainer/programs//discounts",
            {
              method: "PUT",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.authToken}`,
              },
              body: JSON.stringify({}),
            }
          );

          this.framework.assert(
            response.status === 404,
            "Should return 404 status code for invalid URL"
          );
        }
      );

      // Test DELETE discount endpoint - Success
      this.framework.addTestCase(
        "Trainer DELETE Discount - Success",
        async () => {
          const response = await this.framework.makeRequest(
            BASE_URL +
              `/v2/api/kanglink/custom/trainer/programs/${this.testProgramId}/discounts`,
            {
              method: "DELETE",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.authToken}`,
              },
            }
          );

          this.framework.assert(
            response.status === 200,
            "Should return 200 status code"
          );
          this.framework.assert(
            response.body.success === true,
            "Should return success flag"
          );
          this.framework.assert(
            response.body.message ===
              "Discount configuration deleted successfully",
            "Should return success message"
          );
          this.framework.assert(
            response.body.data.deletionSummary !== undefined,
            "Should return deletion summary"
          );
        }
      );

      // Test DELETE discount endpoint - Not Found
      this.framework.addTestCase(
        "Trainer DELETE Discount - Not Found",
        async () => {
          const response = await this.framework.makeRequest(
            BASE_URL +
              `/v2/api/kanglink/custom/trainer/programs/${this.testProgramId}/discounts`,
            {
              method: "DELETE",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${this.authToken}`,
              },
            }
          );

          this.framework.assert(
            response.status === 404,
            "Should return 404 status code"
          );
          this.framework.assert(
            response.body.success === false,
            "Should return error flag"
          );
          this.framework.assert(
            response.body.message ===
              "No discount configuration found for this program",
            "Should return not found message"
          );
        }
      );

      // Test unauthorized access
      this.framework.addTestCase(
        "Trainer Discount - Unauthorized Access",
        async () => {
          const response = await this.framework.makeRequest(
            BASE_URL +
              `/v2/api/kanglink/custom/trainer/programs/${this.testProgramId}/discounts`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                // No Authorization header
              },
            }
          );

          this.framework.assert(
            response.status === 401 || response.status === 403,
            "Should return 401 or 403 status code"
          );
        }
      );
    });
  }

  async runTests() {
    try {
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new DiscountApiTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report;
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
