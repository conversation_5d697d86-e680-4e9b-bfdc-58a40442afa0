const { testApi } = require("../../tests/apitesting.base");

describe("Validate Referral Endpoint", () => {
  const baseUrl = "/v2/api/kanglink/custom/validate-referral";

  test("should return 400 when program_id is missing", async () => {
    const response = await testApi({
      method: "GET",
      url: `${baseUrl}?ref=TEST123`,
      expectedStatus: 400,
    });

    expect(response.error).toBe(true);
    expect(response.message).toContain("Program ID and referral code are required");
  });

  test("should return 400 when ref is missing", async () => {
    const response = await testApi({
      method: "GET",
      url: `${baseUrl}?program_id=1`,
      expectedStatus: 400,
    });

    expect(response.error).toBe(true);
    expect(response.message).toContain("Program ID and referral code are required");
  });

  test("should return 404 when program does not exist", async () => {
    const response = await testApi({
      method: "GET",
      url: `${baseUrl}?program_id=999999&ref=TEST123`,
      expectedStatus: 404,
    });

    expect(response.error).toBe(true);
    expect(response.message).toContain("Program not found");
    expect(response.data.is_valid).toBe(false);
    expect(response.data.reason).toBe("Program not found");
  });

  test("should return invalid when program has no discount settings", async () => {
    // First create a program without discount settings
    const programResponse = await testApi({
      method: "POST",
      url: "/v2/api/kanglink/custom/trainer/programs",
      body: {
        program_name: "Test Program for Referral",
        program_description: "Test description",
        type_of_program: "strength",
        target_levels: ["beginner"],
        payment_plan: ["one_time"],
        track_progress: true,
        allow_comments: true,
        allow_private_messages: false,
      },
      expectedStatus: 201,
    });

    const programId = programResponse.data.id;

    const response = await testApi({
      method: "GET",
      url: `${baseUrl}?program_id=${programId}&ref=TEST123`,
      expectedStatus: 200,
    });

    expect(response.error).toBe(false);
    expect(response.data.is_valid).toBe(false);
    expect(response.data.reason).toBe("No discount settings available for this program");
  });

  test("should return invalid when referral code does not match", async () => {
    // First create a program with discount settings
    const programResponse = await testApi({
      method: "POST",
      url: "/v2/api/kanglink/custom/trainer/programs",
      body: {
        program_name: "Test Program for Referral 2",
        program_description: "Test description",
        type_of_program: "strength",
        target_levels: ["beginner"],
        payment_plan: ["one_time"],
        track_progress: true,
        allow_comments: true,
        allow_private_messages: false,
      },
      expectedStatus: 201,
    });

    const programId = programResponse.data.id;

    // Create discount settings with affiliate link
    await testApi({
      method: "POST",
      url: "/v2/api/kanglink/custom/trainer/programs/discounts",
      body: {
        program_id: programId,
        affiliate_link: "VALID123",
        sale_discount_type: "percentage",
        sale_discount_value: 10,
        sale_apply_to_all: true,
      },
      expectedStatus: 201,
    });

    const response = await testApi({
      method: "GET",
      url: `${baseUrl}?program_id=${programId}&ref=INVALID456`,
      expectedStatus: 200,
    });

    expect(response.error).toBe(false);
    expect(response.data.is_valid).toBe(false);
    expect(response.data.reason).toBe("Referral code does not match");
  });

  test("should return valid when referral code matches", async () => {
    // First create a program with discount settings
    const programResponse = await testApi({
      method: "POST",
      url: "/v2/api/kanglink/custom/trainer/programs",
      body: {
        program_name: "Test Program for Referral 3",
        program_description: "Test description",
        type_of_program: "strength",
        target_levels: ["beginner"],
        payment_plan: ["one_time"],
        track_progress: true,
        allow_comments: true,
        allow_private_messages: false,
      },
      expectedStatus: 201,
    });

    const programId = programResponse.data.id;

    // Create discount settings with affiliate link
    await testApi({
      method: "POST",
      url: "/v2/api/kanglink/custom/trainer/programs/discounts",
      body: {
        program_id: programId,
        affiliate_link: "VALID789",
        sale_discount_type: "percentage",
        sale_discount_value: 15,
        sale_apply_to_all: true,
      },
      expectedStatus: 201,
    });

    const response = await testApi({
      method: "GET",
      url: `${baseUrl}?program_id=${programId}&ref=VALID789`,
      expectedStatus: 200,
    });

    expect(response.error).toBe(false);
    expect(response.data.is_valid).toBe(true);
    expect(response.data.program_discount).toBeDefined();
    expect(response.data.program_discount.sale_discount_type).toBe("percentage");
    expect(response.data.program_discount.sale_discount_value).toBe(15);
    expect(response.data.program_discount.sale_apply_to_all).toBe(true);
  });
}); 