const APITestFramework = require("../../../../tests/apitesting.base.js");
const BASE_URL = "http://localhost:5172";

/**
 * Public Program Endpoints Test Suite
 * 
 * Tests the public program endpoints that don't require authentication:
 * 1. GET /v2/api/kanglink/custom/public/program/:program_id
 * 2. GET /v2/api/kanglink/custom/public/program/:program_id/reviews
 */
class PublicProgramApiTests {
  constructor() {
    this.framework = new APITestFramework();
    this.testData = {
      validProgramId: 1,
      invalidProgramId: "invalid",
      nonExistentProgramId: 99999,
    };
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Public Program API Tests", () => {
      // Setup before running tests
      this.framework.beforeEach(async () => {
        // Setup code here if needed
      });

      // ========== PROGRAM DETAILS ENDPOINT TESTS ==========

      // Test 1: Get program details - valid published program
      this.framework.addTestCase("Program Details - Valid Published Program", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/public/program/${this.testData.validProgramId}`,
          {
            method: "GET",
          }
        );

        // Handle case where program doesn't exist (acceptable for test environment)
        if (response.status === 404) {
          console.log(`⚠️  Program ${this.testData.validProgramId} not found - this is expected if no test data exists`);
          return;
        }

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(
          response.body.error === false,
          "Should not return error"
        );
        this.framework.assert(
          response.body.data,
          "Should return program data"
        );

        // Validate required fields
        const data = response.body.data;
        this.framework.assert(data.id, "Should have program ID");
        this.framework.assert(data.program_name, "Should have program name");
        this.framework.assert(data.trainer, "Should have trainer information");
        this.framework.assert(Array.isArray(data.splits), "Should have splits array");
        this.framework.assert(typeof data.rating === 'number', "Should have numeric rating");
        this.framework.assert(data.rating >= 0 && data.rating <= 5, "Rating should be between 0-5");
        this.framework.assert(typeof data.review_count === 'number', "Should have numeric review count");
      });

      // Test 2: Get program details - invalid program ID
      this.framework.addTestCase("Program Details - Invalid Program ID", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/public/program/${this.testData.invalidProgramId}`,
          {
            method: "GET",
          }
        );

        this.framework.assert(
          response.status === 400,
          "Should return 400 status code for invalid ID"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
        this.framework.assert(
          response.body.message.includes("Invalid program ID"),
          "Should return appropriate error message"
        );
      });

      // Test 3: Get program details - non-existent program
      this.framework.addTestCase("Program Details - Non-existent Program", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/public/program/${this.testData.nonExistentProgramId}`,
          {
            method: "GET",
          }
        );

        this.framework.assert(
          response.status === 404,
          "Should return 404 status code for non-existent program"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
        this.framework.assert(
          response.body.message.includes("not found"),
          "Should return appropriate error message"
        );
      });

      // ========== PROGRAM REVIEWS ENDPOINT TESTS ==========

      // Test 4: Get program reviews - valid program
      this.framework.addTestCase("Program Reviews - Valid Program", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/public/program/${this.testData.validProgramId}/reviews`,
          {
            method: "GET",
          }
        );

        // Handle case where program doesn't exist (acceptable for test environment)
        if (response.status === 404) {
          console.log(`⚠️  Program ${this.testData.validProgramId} not found for reviews - this is expected if no test data exists`);
          return;
        }

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(
          response.body.error === false,
          "Should not return error"
        );
        this.framework.assert(
          Array.isArray(response.body.data),
          "Should return reviews array"
        );
        this.framework.assert(
          response.body.pagination,
          "Should return pagination object"
        );

        // Validate pagination structure
        const pagination = response.body.pagination;
        this.framework.assert(typeof pagination.page === 'number', "Pagination should have page number");
        this.framework.assert(typeof pagination.limit === 'number', "Pagination should have limit");
        this.framework.assert(typeof pagination.total === 'number', "Pagination should have total count");
        this.framework.assert(typeof pagination.num_pages === 'number', "Pagination should have num_pages");
        this.framework.assert(typeof pagination.has_next === 'boolean', "Pagination should have has_next flag");
        this.framework.assert(typeof pagination.has_prev === 'boolean', "Pagination should have has_prev flag");
      });

      // Test 5: Get program reviews - with pagination
      this.framework.addTestCase("Program Reviews - With Pagination", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/public/program/${this.testData.validProgramId}/reviews?page=1&limit=5`,
          {
            method: "GET",
          }
        );

        if (response.status === 404) {
          console.log(`⚠️  Program ${this.testData.validProgramId} not found for reviews`);
          return;
        }

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );

        const pagination = response.body.pagination;
        this.framework.assert(
          pagination.page === 1,
          "Should return correct page number"
        );
        this.framework.assert(
          pagination.limit === 5,
          "Should return correct limit"
        );
      });

      // Test 6: Get program reviews - invalid sort parameter
      this.framework.addTestCase("Program Reviews - Invalid Sort Parameter", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/public/program/${this.testData.validProgramId}/reviews?sort_by=invalid_field`,
          {
            method: "GET",
          }
        );

        this.framework.assert(
          response.status === 400,
          "Should return 400 status code for invalid sort parameter"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
        this.framework.assert(
          response.body.message.includes("Invalid sort_by parameter"),
          "Should return appropriate error message"
        );
      });

      // Test 7: Get program reviews - invalid program ID
      this.framework.addTestCase("Program Reviews - Invalid Program ID", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/public/program/${this.testData.invalidProgramId}/reviews`,
          {
            method: "GET",
          }
        );

        this.framework.assert(
          response.status === 400,
          "Should return 400 status code for invalid program ID"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
        this.framework.assert(
          response.body.message.includes("Invalid program ID"),
          "Should return appropriate error message"
        );
      });

      // Test 8: Get program reviews - with sorting
      this.framework.addTestCase("Program Reviews - With Sorting", async () => {
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/kanglink/custom/public/program/${this.testData.validProgramId}/reviews?sort_by=rating&sort_order=desc`,
          {
            method: "GET",
          }
        );

        if (response.status === 404) {
          console.log(`⚠️  Program ${this.testData.validProgramId} not found for reviews`);
          return;
        }

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(
          Array.isArray(response.body.data),
          "Should return reviews array"
        );

        // If there are reviews, validate they have proper structure
        const reviews = response.body.data;
        if (reviews.length > 0) {
          const review = reviews[0];
          this.framework.assert(review.id, "Review should have ID");
          this.framework.assert(review.rating, "Review should have rating");
          this.framework.assert(review.user, "Review should have user info");
          this.framework.assert(review.content, "Review should have content");
        }
      });
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new PublicProgramApiTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
