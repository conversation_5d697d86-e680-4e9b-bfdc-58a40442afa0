const { testApi } = require("../../tests/apitesting.base");

describe("Review Comments Endpoints", () => {
  const baseUrl = "/v2/api/kanglink/custom/trainer/feed";

  test("should get comments for a review", async () => {
    const response = await testApi({
      method: "GET",
      url: `${baseUrl}/1/comments?include_replies=true&limit=50`,
      expectedStatus: 200,
    });

    expect(response.error).toBe(false);
    expect(Array.isArray(response.data)).toBe(true);
  });

  test("should add a comment to a review", async () => {
    const response = await testApi({
      method: "POST",
      url: `${baseUrl}/1/comments`,
      body: {
        content: "Great review!",
        parent_comment_id: null,
        is_private: false,
        attachments: null,
        mentioned_users: null,
      },
      expectedStatus: 200,
    });

    expect(response.error).toBe(false);
    expect(response.data).toHaveProperty("id");
    expect(response.data).toHaveProperty("content");
    expect(response.data.content).toBe("Great review!");
  });

  test("should delete a comment", async () => {
    // First add a comment
    const addResponse = await testApi({
      method: "POST",
      url: `${baseUrl}/1/comments`,
      body: {
        content: "Test comment to delete",
        parent_comment_id: null,
        is_private: false,
        attachments: null,
        mentioned_users: null,
      },
      expectedStatus: 200,
    });

    expect(addResponse.error).toBe(false);
    const commentId = addResponse.data.id;

    // Then delete it
    const deleteResponse = await testApi({
      method: "DELETE",
      url: `${baseUrl}/comments/${commentId}`,
      expectedStatus: 200,
    });

    expect(deleteResponse.error).toBe(false);
  });

  test("should return 400 when adding comment without content", async () => {
    const response = await testApi({
      method: "POST",
      url: `${baseUrl}/1/comments`,
      body: {
        content: "",
        parent_comment_id: null,
        is_private: false,
        attachments: null,
        mentioned_users: null,
      },
      expectedStatus: 400,
    });

    expect(response.error).toBe(true);
  });
}); 