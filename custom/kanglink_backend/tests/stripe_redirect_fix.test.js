const APITestFramework = require("../../../tests/apitesting.base.js");

class StripeRedirectFixTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Stripe Redirect Fix Tests", () => {
      // Test that automatic_payment_methods includes allow_redirects: "never"
      this.framework.addTestCase("Should disable redirect-based payment methods", async () => {
        const StripeService = require("../../../baas/services/StripeService");
        
        // Mock the Stripe library to capture the parameters
        let capturedParams = null;
        const mockStripe = {
          paymentIntents: {
            create: async (params) => {
              capturedParams = params;
              
              // Return mock payment intent
              return {
                id: "pi_test_123",
                status: "succeeded",
                amount: params.amount,
                currency: params.currency,
                customer: params.customer,
                payment_method: params.payment_method,
                metadata: params.metadata,
                automatic_payment_methods: params.automatic_payment_methods
              };
            }
          }
        };

        // Create a test instance and override the stripe property
        const stripeService = new StripeService();
        stripeService.stripe = mockStripe;

        const testPayload = {
          amount: 10000, // $100.00
          currency: "usd",
          customer: "cus_test123",
          payment_method: "pm_test123",
          confirm: true,
          metadata: {
            projectId: "kanglink",
            split_id: "1",
            athlete_id: "1",
            payment_type: "one_time"
          }
        };

        const result = await stripeService.createPaymentIntentAutomatic(testPayload);

        // Verify automatic_payment_methods configuration
        this.framework.assert(
          capturedParams.automatic_payment_methods,
          "Should include automatic_payment_methods"
        );
        this.framework.assertions.assertEquals(
          capturedParams.automatic_payment_methods.enabled,
          true,
          "Should enable automatic payment methods"
        );
        this.framework.assertions.assertEquals(
          capturedParams.automatic_payment_methods.allow_redirects,
          "never",
          "Should disable redirect-based payment methods"
        );
      });

      // Test that custom automatic_payment_methods can override defaults
      this.framework.addTestCase("Should allow custom automatic_payment_methods override", async () => {
        const StripeService = require("../../../baas/services/StripeService");
        
        // Mock the Stripe library to capture the parameters
        let capturedParams = null;
        const mockStripe = {
          paymentIntents: {
            create: async (params) => {
              capturedParams = params;
              return {
                id: "pi_test_456",
                status: "requires_payment_method",
                automatic_payment_methods: params.automatic_payment_methods
              };
            }
          }
        };

        // Create a test instance and override the stripe property
        const stripeService = new StripeService();
        stripeService.stripe = mockStripe;

        const testPayload = {
          amount: 5000,
          customer: "cus_test456",
          automatic_payment_methods: {
            enabled: true,
            allow_redirects: "always" // Custom override
          }
        };

        await stripeService.createPaymentIntentAutomatic(testPayload);

        // Verify custom configuration is used
        this.framework.assertions.assertEquals(
          capturedParams.automatic_payment_methods.allow_redirects,
          "always",
          "Should use custom allow_redirects setting"
        );
      });

      // Test enrollment-specific configuration
      this.framework.addTestCase("Should configure payment intent for enrollment flow", async () => {
        const enrollmentConfig = {
          amount: Math.round(100 * 100), // $100.00 in cents
          currency: "usd",
          customer: "cus_athlete123",
          payment_method: "pm_card123",
          confirm: true,
          metadata: {
            projectId: "kanglink",
            split_id: "5",
            athlete_id: "10",
            trainer_id: "3",
            payment_type: "one_time",
            stripe_price_id: "price_123"
          }
        };

        // Verify the configuration prevents redirect issues
        this.framework.assert(
          enrollmentConfig.confirm === true,
          "Should confirm payment immediately to avoid redirects"
        );
        this.framework.assert(
          enrollmentConfig.payment_method,
          "Should include payment method to avoid redirect prompts"
        );
        this.framework.assert(
          enrollmentConfig.metadata.projectId === "kanglink",
          "Should include project metadata"
        );
      });

      // Test error message understanding
      this.framework.addTestCase("Should understand Stripe redirect error requirements", async () => {
        const errorMessage = "This PaymentIntent is configured to accept payment methods enabled in your Dashboard. Because some of these payment methods might redirect your customer off of your page, you must provide a `return_url`. If you don't want to accept redirect-based payment methods, set `automatic_payment_methods[enabled]` to `true` and `automatic_payment_methods[allow_redirects]` to `never` when creating Setup Intents and Payment Intents.";
        
        // Verify our solution addresses the error
        const solution = {
          automatic_payment_methods: {
            enabled: true,
            allow_redirects: "never"
          }
        };

        this.framework.assert(
          solution.automatic_payment_methods.enabled === true,
          "Solution should enable automatic payment methods"
        );
        this.framework.assert(
          solution.automatic_payment_methods.allow_redirects === "never",
          "Solution should disable redirects as suggested by error"
        );
      });
    });
  }

  async runTests() {
    try {
      const results = await this.framework.runTests();
      this.framework.generateTestReport();
      return results;
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new StripeRedirectFixTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report;
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
