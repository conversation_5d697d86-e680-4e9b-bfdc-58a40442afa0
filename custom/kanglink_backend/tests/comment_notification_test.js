const { test, describe, expect } = require("../../../tests/apitesting.base.js");

describe("Comment Notification Test", () => {
  let athleteToken, trainerToken;
  let athleteId, trainerId, programId, splitId, postId;

  test("Setup test data", async ({ sdk }) => {
    // Create test athlete
    const athleteData = {
      email: "<EMAIL>",
      password: "password123",
      role: "member",
      data: JSON.stringify({
        first_name: "Test",
        last_name: "Athlete",
        full_name: "Test Athlete"
      })
    };
    
    const athleteResponse = await sdk.callRestAPI({
      endpoint: "/v2/api/kanglink/custom/auth/register",
      method: "POST",
      data: athleteData
    });
    
    expect(athleteResponse.error).toBe(false);
    athleteToken = athleteResponse.data.access_token;
    athleteId = athleteResponse.data.user_id;

    // Create test trainer
    const trainerData = {
      email: "<EMAIL>",
      password: "password123",
      role: "trainer",
      data: JSON.stringify({
        first_name: "Test",
        last_name: "Trainer",
        full_name: "Test Trainer"
      })
    };
    
    const trainerResponse = await sdk.callRestAPI({
      endpoint: "/v2/api/kanglink/custom/auth/register",
      method: "POST",
      data: trainerData
    });
    
    expect(trainerResponse.error).toBe(false);
    trainerToken = trainerResponse.data.access_token;
    trainerId = trainerResponse.data.user_id;

    // Create test program
    const programData = {
      program_name: "Test Comment Program",
      program_description: "Test program for comment notifications",
      type_of_program: "strength",
      currency: "USD"
    };
    
    const programResponse = await sdk.callRestAPI({
      endpoint: "/v2/api/kanglink/custom/trainer/program",
      method: "POST",
      headers: { Authorization: `Bearer ${trainerToken}` },
      data: programData
    });
    
    expect(programResponse.error).toBe(false);
    programId = programResponse.data.program_id;

    // Create test split
    const splitData = {
      title: "Test Comment Split",
      equipment_required: "Basic equipment",
      full_price: 99.99,
      subscription: 19.99
    };
    
    const splitResponse = await sdk.callRestAPI({
      endpoint: `/v2/api/kanglink/custom/trainer/program/${programId}/split`,
      method: "POST",
      headers: { Authorization: `Bearer ${trainerToken}` },
      data: splitData
    });
    
    expect(splitResponse.error).toBe(false);
    splitId = splitResponse.data.split_id;
  });

  test("Test comment notification creation", async ({ sdk }) => {
    // Create a post feed first
    const postData = {
      program_id: programId,
      post_type: "announcement",
      content: "Test post for comment notifications",
      visibility_scope: "program_members"
    };
    
    const postResponse = await sdk.callRestAPI({
      endpoint: "/v2/api/kanglink/custom/trainer/feed",
      method: "POST",
      headers: { Authorization: `Bearer ${trainerToken}` },
      data: postData
    });
    
    expect(postResponse.error).toBe(false);
    postId = postResponse.data.post.id;

    // Create a comment on the post (athlete commenting on trainer's post)
    const commentData = {
      content: "Test comment on trainer's post",
      is_private: false
    };
    
    const commentResponse = await sdk.callRestAPI({
      endpoint: `/v2/api/kanglink/custom/trainer/feed/${postId}/comment`,
      method: "POST",
      headers: { Authorization: `Bearer ${athleteToken}` },
      data: commentData
    });
    
    expect(commentResponse.error).toBe(false);
    expect(commentResponse.data.id).toBeDefined();

    // Wait a moment for notification to be created
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Verify notification was created for post author (trainer)
    const trainerNotifications = await sdk.callRestAPI({
      endpoint: "/v2/api/kanglink/custom/athlete/notifications",
      method: "GET",
      headers: { Authorization: `Bearer ${trainerToken}` }
    });
    
    expect(trainerNotifications.error).toBe(false);
    expect(trainerNotifications.data.notifications).toBeDefined();
    
    const commentNotification = trainerNotifications.data.notifications.find(
      n => n.notification_type === "post_feed_comment"
    );
    
    console.log("Trainer notifications:", trainerNotifications.data.notifications);
    console.log("Found comment notification:", commentNotification);
    
    expect(commentNotification).toBeDefined();
    expect(commentNotification.title).toContain("New Comment");
  });
});