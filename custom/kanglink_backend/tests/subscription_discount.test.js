const testFramework = require("../../tests/apitesting.base.js");

// Test subscription discount functionality
testFramework.describe("Subscription Discount Tests", () => {
  let sdk;
  let testProgramId;
  let testSplitId;
  let testAthleteId;
  let testCouponCode;

  testFramework.beforeAll(async () => {
    sdk = testFramework.getSdk();
    
    // Create test data
    await setupTestData();
  });

  async function setupTestData() {
    // Create test athlete
    sdk.setTable("user");
    const athlete = await sdk.create("user", {
      email: `test-athlete-${Date.now()}@example.com`,
      password: "hashedpassword",
      role_id: "athlete",
      verify: true,
      status: 1,
      data: JSON.stringify({
        first_name: "Test",
        last_name: "Athlete"
      })
    });
    testAthleteId = athlete.id;

    // Create test trainer
    const trainer = await sdk.create("user", {
      email: `test-trainer-${Date.now()}@example.com`,
      password: "hashedpassword",
      role_id: "trainer",
      verify: true,
      status: 1,
      data: JSON.stringify({
        first_name: "Test",
        last_name: "Trainer"
      })
    });

    // Create test program
    sdk.setTable("program");
    const program = await sdk.create("program", {
      name: "Test Subscription Program",
      description: "Test program for subscription discounts",
      trainer_id: trainer.id,
      status: "published",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
    testProgramId = program.id;

    // Create test split
    sdk.setTable("split");
    const split = await sdk.create("split", {
      name: "Test Split",
      description: "Test split for subscription",
      program_id: testProgramId,
      trainer_id: trainer.id,
      subscription_price: 50.00,
      full_price: 200.00,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
    testSplitId = split.id;

    // Create test coupon
    sdk.setTable("coupon");
    testCouponCode = `TEST-SUB-${Date.now()}`;
    await sdk.create("coupon", {
      code: testCouponCode,
      program_id: testProgramId,
      discount_type: "percentage",
      discount_value: 20.0, // 20% off
      applies_to: "both", // Apply to both subscription and one-time
      is_active: true,
      usage_limit: 100,
      used_count: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
  }

  testFramework.test("should apply discount to subscription payment", async () => {
    const DiscountService = require("../services/DiscountService");
    const discountService = new DiscountService(sdk);

    // Test discount calculation for subscription
    const result = await discountService.calculateDiscountedAmount({
      program_id: testProgramId,
      split_id: testSplitId,
      payment_type: "subscription",
      original_amount: 50.00,
      coupon_code: testCouponCode,
      user_id: testAthleteId,
    });

    testFramework.expect(result.success).toBe(true);
    testFramework.expect(result.original_amount).toBe(50.00);
    testFramework.expect(result.final_amount).toBe(40.00); // 50 - 20% = 40
    testFramework.expect(result.total_discount_amount).toBe(10.00);
    testFramework.expect(result.applied_discounts.length).toBe(1);
    testFramework.expect(result.applied_discounts[0].type).toBe("coupon");
    testFramework.expect(result.applied_discounts[0].coupon_code).toBe(testCouponCode);
  });

  testFramework.test("should calculate correct discount percentage for Stripe coupon", async () => {
    const originalAmount = 50.00;
    const discountAmount = 10.00; // 20% of 50
    const expectedPercentage = Math.round((discountAmount / originalAmount) * 100);
    
    testFramework.expect(expectedPercentage).toBe(20);
  });

  testFramework.afterAll(async () => {
    // Clean up test data
    if (testAthleteId) {
      sdk.setTable("user");
      await sdk.deleteById("user", testAthleteId);
    }
    
    if (testSplitId) {
      sdk.setTable("split");
      await sdk.deleteById("split", testSplitId);
    }
    
    if (testProgramId) {
      sdk.setTable("program");
      await sdk.deleteById("program", testProgramId);
    }
  });
});
