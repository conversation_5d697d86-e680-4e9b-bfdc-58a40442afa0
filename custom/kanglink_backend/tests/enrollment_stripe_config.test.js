const APITestFramework = require("../../../tests/apitesting.base.js");

class EnrollmentStripeConfigTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("Enrollment Stripe Configuration Tests", () => {
      // Test that enrollment endpoint passes correct Stripe configuration
      this.framework.addTestCase("Should pass allow_redirects never for enrollment payments", async () => {
        // Simulate the enrollment payment configuration
        const enrollmentPaymentConfig = {
          amount: Math.round(100 * 100), // $100.00 in cents
          currency: "usd",
          customer: "cus_athlete123",
          payment_method: "pm_card123",
          confirm: true,
          automatic_payment_methods: {
            enabled: true,
            allow_redirects: "never", // This should be passed from enrollment endpoint
          },
          metadata: {
            projectId: "kanglink",
            split_id: "5",
            athlete_id: "10",
            trainer_id: "3",
            payment_type: "one_time",
            stripe_price_id: "price_123"
          }
        };

        // Verify the configuration
        this.framework.assert(
          enrollmentPaymentConfig.automatic_payment_methods.allow_redirects === "never",
          "Enrollment should disable redirects for server-side confirmation"
        );
        this.framework.assert(
          enrollmentPaymentConfig.confirm === true,
          "Enrollment should confirm payment immediately"
        );
        this.framework.assert(
          enrollmentPaymentConfig.payment_method,
          "Enrollment should include payment method"
        );
      });

      // Test that StripeService accepts custom automatic_payment_methods
      this.framework.addTestCase("Should accept custom automatic_payment_methods configuration", async () => {
        const StripeService = require("../../../baas/services/StripeService");
        
        // Mock the Stripe library to capture parameters
        let capturedParams = null;
        const mockStripe = {
          paymentIntents: {
            create: async (params) => {
              capturedParams = params;
              return {
                id: "pi_test_123",
                status: "succeeded",
                automatic_payment_methods: params.automatic_payment_methods
              };
            }
          }
        };

        const stripeService = new StripeService();
        stripeService.stripe = mockStripe;

        // Test with enrollment-style configuration
        const enrollmentPayload = {
          amount: 10000,
          currency: "usd",
          customer: "cus_test",
          payment_method: "pm_test",
          confirm: true,
          automatic_payment_methods: {
            enabled: true,
            allow_redirects: "never"
          },
          metadata: { projectId: "kanglink" }
        };

        await stripeService.createPaymentIntentAutomatic(enrollmentPayload);

        // Verify the custom configuration was passed through
        this.framework.assertions.assertEquals(
          capturedParams.automatic_payment_methods.allow_redirects,
          "never",
          "Should pass through custom allow_redirects setting"
        );
      });

      // Test that StripeService uses defaults when no custom config provided
      this.framework.addTestCase("Should use default automatic_payment_methods when not specified", async () => {
        const StripeService = require("../../../baas/services/StripeService");
        
        // Mock the Stripe library to capture parameters
        let capturedParams = null;
        const mockStripe = {
          paymentIntents: {
            create: async (params) => {
              capturedParams = params;
              return {
                id: "pi_test_456",
                status: "requires_payment_method",
                automatic_payment_methods: params.automatic_payment_methods
              };
            }
          }
        };

        const stripeService = new StripeService();
        stripeService.stripe = mockStripe;

        // Test with minimal configuration (like mobile endpoint)
        const mobilePayload = {
          amount: 5000,
          currency: "usd",
          customer: "cus_mobile"
        };

        await stripeService.createPaymentIntentAutomatic(mobilePayload);

        // Verify default configuration is used
        this.framework.assertions.assertEquals(
          capturedParams.automatic_payment_methods.enabled,
          true,
          "Should default to enabled: true"
        );
        this.framework.assert(
          !capturedParams.automatic_payment_methods.hasOwnProperty('allow_redirects'),
          "Should not include allow_redirects when not specified"
        );
      });

      // Test configuration addresses Stripe error
      this.framework.addTestCase("Should address Stripe redirect error with proper configuration", async () => {
        // The error message from Stripe
        const stripeErrorRequirement = "set `automatic_payment_methods[enabled]` to `true` and `automatic_payment_methods[allow_redirects]` to `never`";
        
        // Our enrollment configuration
        const ourConfig = {
          automatic_payment_methods: {
            enabled: true,
            allow_redirects: "never"
          }
        };

        // Verify our configuration matches Stripe's requirement
        this.framework.assert(
          ourConfig.automatic_payment_methods.enabled === true,
          "Configuration should set enabled to true as required by Stripe"
        );
        this.framework.assert(
          ourConfig.automatic_payment_methods.allow_redirects === "never",
          "Configuration should set allow_redirects to never as required by Stripe"
        );
      });
    });
  }

  async runTests() {
    try {
      const results = await this.framework.runTests();
      this.framework.generateTestReport();
      return results;
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new EnrollmentStripeConfigTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report;
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
