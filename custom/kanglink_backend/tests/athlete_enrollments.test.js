const APITestFramework = require("../../../tests/apitesting.base");

class AthleteEnrollmentsTest {
  constructor() {
    this.framework = new APITestFramework({ environment: "test" });
    this.testAthleteId = null;
    this.testTrainerId = null;
    this.testProgramId = null;
    this.testSplitId = null;
    this.testEnrollmentId = null;
  }

  describe(message) {
    console.log(`\n📝 ${message}`);
  }

  assert(condition, message, description) {
    if (condition) {
      console.log(`✅ ${description || message}`);
    } else {
      console.log(`❌ ${description || message}`);
      throw new Error(message);
    }
  }

  success(message) {
    console.log(`🎉 ${message}`);
  }

  error(message) {
    console.log(`💥 ${message}`);
  }

  getSdk() {
    return this.framework.getSdk();
  }

  getValidToken(userId, role) {
    return this.framework.getValidToken(userId, role);
  }

  async makeRequest(options) {
    return this.framework.makeRequest(options);
  }

  async setup() {
    const sdk = this.getSdk();
    sdk.setProjectId("kanglink");

    // Create test trainer
    sdk.setTable("user");
    const trainer = await sdk.create("user", {
      email: `trainer_${Date.now()}@test.com`,
      password: "password123",
      role_id: "trainer",
      status: 1,
      verify: true,
      data: JSON.stringify({
        full_name: "Test Trainer",
        first_name: "Test",
        last_name: "Trainer",
        photo: "https://example.com/photo.jpg",
      }),
    });
    this.testTrainerId = trainer.id;

    // Create test athlete
    const athlete = await sdk.create("user", {
      email: `athlete_${Date.now()}@test.com`,
      password: "password123",
      role_id: "member",
      status: 1,
      verify: true,
      data: JSON.stringify({
        full_name: "Test Athlete",
        first_name: "Test",
        last_name: "Athlete",
      }),
    });
    this.testAthleteId = athlete.id;

    // Create test program
    sdk.setTable("program");
    const program = await sdk.create("program", {
      user_id: this.testTrainerId,
      program_name: "Test Program",
      type_of_program: "strength",
      program_description: "Test program description",
      currency: "USD",
      status: "published",
    });
    this.testProgramId = program.id;

    // Create test split
    sdk.setTable("split");
    const split = await sdk.create("split", {
      program_id: this.testProgramId,
      title: "Test Split",
      description: "Test split description",
      full_price: 100.0,
      subscription: 20.0,
      duration_weeks: 8,
    });
    this.testSplitId = split.id;

    // Create payout settings
    sdk.setTable("payout_settings");
    await sdk.create("payout_settings", {
      trainer_payout_time_hours: 24,
      split_company_percentage: 30.0,
      split_trainer_percentage: 70.0,
      affiliate_company_percentage: 20.0,
      affiliate_trainer_percentage: 80.0,
      is_active: true,
    });
  }

  async cleanup() {
    const sdk = this.getSdk();
    // Clean up test data
    if (this.testEnrollmentId) {
      sdk.setTable("enrollment");
      await sdk.deleteById("enrollment", this.testEnrollmentId);
    }
    if (this.testSplitId) {
      sdk.setTable("split");
      await sdk.deleteById("split", this.testSplitId);
    }
    if (this.testProgramId) {
      sdk.setTable("program");
      await sdk.deleteById("program", this.testProgramId);
    }
    if (this.testAthleteId) {
      sdk.setTable("user");
      await sdk.deleteById("user", this.testAthleteId);
    }
    if (this.testTrainerId) {
      sdk.setTable("user");
      await sdk.deleteById("user", this.testTrainerId);
    }
  }

  async testEmptyEnrollments() {
    this.describe("Test empty enrollments response");

    const response = await this.makeRequest({
      method: "GET",
      url: "/v2/api/kanglink/custom/athlete/enrollments",
      headers: {
        Authorization: `Bearer ${this.getValidToken(
          this.testAthleteId,
          "member"
        )}`,
      },
    });

    this.assert(
      response.status === 200,
      `Expected 200 status, got ${response.status}`,
      "Should return 200 status"
    );
    this.assert(
      response.data.error === false,
      "Should not have error",
      "Response should not have error"
    );
    this.assert(
      response.data.data,
      "Should have data object",
      "Response should have data object"
    );
    this.assert(
      response.data.data.owned,
      "Should have owned category",
      "Should have owned category"
    );
    this.assert(
      response.data.data.subscribed,
      "Should have subscribed category",
      "Should have subscribed category"
    );
    this.assert(
      response.data.data.pending_refund,
      "Should have pending_refund category",
      "Should have pending_refund category"
    );
    this.assert(
      response.data.data.refunded,
      "Should have refunded category",
      "Should have refunded category"
    );
    this.assert(
      response.data.data.owned.length === 0,
      "Should have no owned enrollments",
      "Should have no owned enrollments"
    );
    this.assert(
      response.data.meta.total_enrollments === 0,
      "Should have 0 total enrollments",
      "Should have 0 total enrollments"
    );

    this.success("Empty enrollments test passed");
  }

  async testOwnedEnrollment() {
    this.describe("Test owned enrollment for one-time payment");

    // Create owned enrollment (one-time payment)
    const sdk = this.getSdk();
    sdk.setTable("enrollment");
    const enrollment = await sdk.create("enrollment", {
      trainer_id: this.testTrainerId,
      athlete_id: this.testAthleteId,
      program_id: this.testProgramId,
      split_id: this.testSplitId,
      payment_type: "one_time",
      amount: 100.0,
      currency: "USD",
      status: "active",
      payment_status: "paid",
      stripe_customer_id: "cus_test123",
      stripe_payment_intent_id: "pi_test123",
    });
    this.testEnrollmentId = enrollment.id;

    const response = await this.makeRequest({
      method: "GET",
      url: "/v2/api/kanglink/custom/athlete/enrollments",
      headers: {
        Authorization: `Bearer ${this.getValidToken(
          this.testAthleteId,
          "member"
        )}`,
      },
    });

    this.assert(
      response.status === 200,
      `Expected 200 status, got ${response.status}`,
      "Should return 200 status"
    );
    this.assert(
      response.data.error === false,
      "Should not have error",
      "Response should not have error"
    );
    this.assert(
      response.data.data.owned.length === 1,
      "Should have 1 owned enrollment",
      "Should have 1 owned enrollment"
    );
    this.assert(
      response.data.meta.total_enrollments === 1,
      "Should have 1 total enrollment",
      "Should have 1 total enrollment"
    );

    const ownedEnrollment = response.data.data.owned[0];
    this.assert(
      ownedEnrollment.id === enrollment.id,
      "Should match enrollment ID",
      "Should match enrollment ID"
    );
    this.assert(
      ownedEnrollment.payment_type === "one_time",
      "Should be one-time payment",
      "Should be one-time payment"
    );
    this.assert(
      ownedEnrollment.status === "active",
      "Should be active status",
      "Should be active status"
    );
    this.assert(
      ownedEnrollment.program,
      "Should have program data",
      "Should have program data"
    );
    this.assert(
      ownedEnrollment.split,
      "Should have split data",
      "Should have split data"
    );
    this.assert(
      ownedEnrollment.trainer,
      "Should have trainer data",
      "Should have trainer data"
    );
    this.assert(
      ownedEnrollment.pricing,
      "Should have pricing data",
      "Should have pricing data"
    );
    this.assert(
      ownedEnrollment.refund_info,
      "Should have refund info",
      "Should have refund info"
    );
    this.assert(
      ownedEnrollment.refund_info.can_request === true,
      "Should be able to request refund",
      "Should be able to request refund"
    );

    this.success("Owned enrollment test passed");
  }

  async testAuthenticationRequired() {
    this.describe("Test authentication requirement");

    const response = await this.makeRequest({
      method: "GET",
      url: "/v2/api/kanglink/custom/athlete/enrollments",
    });

    this.assert(
      response.status === 401,
      `Expected 401 status, got ${response.status}`,
      "Should return 401 for unauthenticated request"
    );
    this.success("Authentication requirement test passed");
  }

  async run() {
    try {
      await this.setup();
      await this.testEmptyEnrollments();
      await this.testOwnedEnrollment();
      await this.testAuthenticationRequired();
      await this.cleanup();
    } catch (error) {
      this.error(`Test suite failed: ${error.message}`);
      throw error;
    }
  }
}

// Run the test
const test = new AthleteEnrollmentsTest();
test
  .run()
  .then(() => {
    console.log("All tests completed successfully");
  })
  .catch((error) => {
    console.error("Test suite failed:", error);
    process.exit(1);
  });

module.exports = AthleteEnrollmentsTest;
