const { TestSuite } = require("../../tests/apitesting.base");

class TrainerDashboardTest extends TestSuite {
  constructor() {
    super("Trainer Dashboard API Tests");
    this.baseUrl = "/v2/api/kanglink/custom/trainer/dashboard";
    this.authToken = null;
    this.trainerId = null;
  }

  async setup() {
    // Setup test data - you may need to adjust this based on your test environment
    console.log("Setting up trainer dashboard tests...");
    
    // You would typically create test users, programs, enrollments here
    // For now, we'll assume test data exists
    this.trainerId = 1; // Replace with actual test trainer ID
    this.authToken = "test_token"; // Replace with actual test token
  }

  async teardown() {
    console.log("Cleaning up trainer dashboard tests...");
    // Clean up test data if needed
  }

  // Test dashboard stats endpoint
  async testDashboardStats() {
    const response = await this.makeRequest({
      method: "GET",
      url: `${this.baseUrl}/stats`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Stats endpoint should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
    this.assertTrue(response.data.data, "Response should have data");
    
    const data = response.data.data;
    this.assertTrue(typeof data.activeProgramsCount === 'number', "Should have activeProgramsCount");
    this.assertTrue(typeof data.activeAthletesCount === 'number', "Should have activeAthletesCount");
    this.assertTrue(typeof data.monthlyRevenue === 'number', "Should have monthlyRevenue");
    this.assertTrue(data.currency, "Should have currency");
    this.assertTrue(data.period, "Should have period");
  }

  // Test notifications endpoint
  async testNotifications() {
    const response = await this.makeRequest({
      method: "GET",
      url: `${this.baseUrl}/notifications`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Notifications endpoint should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
    this.assertTrue(response.data.data, "Response should have data");
    
    const data = response.data.data;
    this.assertTrue(Array.isArray(data.notifications), "Should have notifications array");
    this.assertTrue(data.pagination, "Should have pagination");
    this.assertTrue(typeof data.unreadCount === 'number', "Should have unreadCount");
  }

  // Test notifications with filters
  async testNotificationsWithFilters() {
    const response = await this.makeRequest({
      method: "GET",
      url: `${this.baseUrl}/notifications?page=1&limit=5&unread_only=true&category=progress`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Filtered notifications should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
    
    const data = response.data.data;
    this.assertTrue(data.pagination.limit === 5, "Should respect limit parameter");
    this.assertTrue(data.pagination.page === 1, "Should respect page parameter");
  }

  // Test activities endpoint
  async testActivities() {
    const response = await this.makeRequest({
      method: "GET",
      url: `${this.baseUrl}/activities`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Activities endpoint should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
    this.assertTrue(response.data.data, "Response should have data");
    
    const data = response.data.data;
    this.assertTrue(Array.isArray(data.activities), "Should have activities array");
    this.assertTrue(data.pagination, "Should have pagination");
  }

  // Test activities with filters
  async testActivitiesWithFilters() {
    const response = await this.makeRequest({
      method: "GET",
      url: `${this.baseUrl}/activities?page=1&limit=10&activity_type=workout_completed&visibility=trainer_only`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Filtered activities should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
  }

  // Test dashboard summary endpoint
  async testDashboardSummary() {
    const response = await this.makeRequest({
      method: "GET",
      url: `${this.baseUrl}/summary`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Summary endpoint should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
    this.assertTrue(response.data.data, "Response should have data");
    
    const data = response.data.data;
    this.assertTrue(data.stats, "Should have stats");
    this.assertTrue(Array.isArray(data.recentNotifications), "Should have recentNotifications array");
    this.assertTrue(Array.isArray(data.recentActivities), "Should have recentActivities array");
  }

  // Test mark notification as read (would need a real notification ID)
  async testMarkNotificationAsRead() {
    // This test would need a real notification ID
    // For now, we'll test the endpoint structure
    const notificationId = 999; // Replace with actual test notification ID
    
    const response = await this.makeRequest({
      method: "PUT",
      url: `${this.baseUrl}/notifications/${notificationId}/read`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    // This might return 404 if notification doesn't exist, which is expected
    this.assertTrue(response.status === 200 || response.status === 404, 
      "Should return 200 for success or 404 for not found");
  }

  // Test mark all notifications as read
  async testMarkAllNotificationsAsRead() {
    const response = await this.makeRequest({
      method: "PUT",
      url: `${this.baseUrl}/notifications/read-all`,
      headers: {
        Authorization: `Bearer ${this.authToken}`
      }
    });

    this.assertEqual(response.status, 200, "Mark all as read should return 200");
    this.assertFalse(response.data.error, "Response should not have error");
    this.assertTrue(response.data.data, "Response should have data");
    this.assertTrue(typeof response.data.data.updatedCount === 'number', "Should have updatedCount");
  }

  // Test unauthorized access
  async testUnauthorizedAccess() {
    const response = await this.makeRequest({
      method: "GET",
      url: `${this.baseUrl}/stats`
      // No authorization header
    });

    this.assertEqual(response.status, 401, "Should return 401 for unauthorized access");
  }

  async run() {
    await this.setup();
    
    try {
      await this.testDashboardStats();
      await this.testNotifications();
      await this.testNotificationsWithFilters();
      await this.testActivities();
      await this.testActivitiesWithFilters();
      await this.testDashboardSummary();
      await this.testMarkNotificationAsRead();
      await this.testMarkAllNotificationsAsRead();
      await this.testUnauthorizedAccess();
      
      this.printResults();
    } catch (error) {
      console.error("Test execution failed:", error);
    } finally {
      await this.teardown();
    }
  }
}

// Export for use with test runner
module.exports = TrainerDashboardTest;

// Run tests if called directly
if (require.main === module) {
  const test = new TrainerDashboardTest();
  test.run();
}
