const APITestFramework = require("../../../tests/apitesting.base");

// Create a simple test to verify the post feed endpoint exists and has access control
async function testPostFeedReviewAccessControl() {
  const framework = new APITestFramework();
  
  console.log("Testing Post Feed Review Access Control...");
  
  try {
    // Test 1: Verify endpoint exists and requires authentication
    console.log("Test 1: Checking endpoint exists and requires authentication");
    const response1 = await framework.makeRequest("/v2/api/kanglink/custom/trainer/feed", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
        // No Authorization header - should return 401
      },
      body: {
        program_id: 1,
        post_type: "review",
        content: "Test review",
        rating: 5
      }
    });
    
    console.log(`Response 1 status: ${response1.status}`);
    if (response1.status === 401) {
      console.log("✅ Test 1 PASSED: Endpoint requires authentication");
    } else {
      console.log(`❌ Test 1 FAILED: Expected 401, got ${response1.status}`);
    }
    
    // Test 2: Verify review validation works
    console.log("Test 2: Checking review validation");
    const response2 = await framework.makeRequest("/v2/api/kanglink/custom/trainer/feed", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-token"
      },
      body: {
        program_id: 1,
        post_type: "review",
        content: "Test review without rating"
        // Missing rating field
      }
    });
    
    console.log(`Response 2 status: ${response2.status}`);
    if (response2.status === 400 || response2.status === 401) {
      console.log("✅ Test 2 PASSED: Review validation works");
    } else {
      console.log(`❌ Test 2 FAILED: Expected 400 or 401, got ${response2.status}`);
    }
    
    // Test 3: Verify rating validation works
    console.log("Test 3: Checking rating validation");
    const response3 = await framework.makeRequest("/v2/api/kanglink/custom/trainer/feed", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer test-token"
      },
      body: {
        program_id: 1,
        post_type: "review",
        content: "Test review with invalid rating",
        rating: 6 // Invalid rating (should be 1-5)
      }
    });
    
    console.log(`Response 3 status: ${response3.status}`);
    if (response3.status === 400 || response3.status === 401) {
      console.log("✅ Test 3 PASSED: Rating validation works");
    } else {
      console.log(`❌ Test 3 FAILED: Expected 400 or 401, got ${response3.status}`);
    }
    
    // Test 4: Verify GET endpoint exists
    console.log("Test 4: Checking GET reviews endpoint");
    const response4 = await framework.makeRequest("/v2/api/kanglink/custom/trainer/feed", {
      method: "GET",
      headers: {
        "Authorization": "Bearer test-token"
      },
      params: {
        program_id: 1,
        post_type: "review"
      }
    });
    
    console.log(`Response 4 status: ${response4.status}`);
    if (response4.status === 401) {
      console.log("✅ Test 4 PASSED: GET reviews endpoint requires authentication");
    } else {
      console.log(`❌ Test 4 FAILED: Expected 401, got ${response4.status}`);
    }
    
    console.log("🎉 All Post Feed Review Access Control tests completed!");
    
  } catch (error) {
    console.error("❌ Test failed with error:", error.message);
  }
}

// Run the test
testPostFeedReviewAccessControl();

module.exports = testPostFeedReviewAccessControl; 