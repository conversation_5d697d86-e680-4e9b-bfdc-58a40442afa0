-- Migration script to add status column to kanglink_program table if it doesn't exist
-- This script is safe to run multiple times

-- Check if status column exists and add it if it doesn't
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'kanglink_program'
    AND COLUMN_NAME = 'status'
);

SET @sql = IF(@column_exists = 0,
    'ALTER TABLE kanglink_program ADD COLUMN status VARCHAR(50) DEFAULT ''draft'' AFTER image',
    'SELECT ''Column status already exists'' as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update any existing records to have 'published' status if they don't have a status
UPDATE kanglink_program 
SET status = 'published' 
WHERE status IS NULL OR status = '';

-- Show the result
SELECT 'Migration completed successfully' as result;
