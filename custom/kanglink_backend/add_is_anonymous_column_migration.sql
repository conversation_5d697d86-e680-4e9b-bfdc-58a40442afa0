-- Migration: Add is_anonymous column to post_feed table
-- Date: Current migration for anonymous posts feature
-- Description: Adds boolean column to track anonymous posts and modifies user_id to allow NULL values

-- Add the is_anonymous column to the post_feed table
ALTER TABLE kanglink_post_feed 
ADD COLUMN is_anonymous BOOLEAN DEFAULT FALSE NOT NULL;

-- Modify user_id column to allow NULL values for anonymous posts
ALTER TABLE kanglink_post_feed 
MODIFY COLUMN user_id INT NULL;

-- Add index for better query performance on anonymous posts
CREATE INDEX idx_post_feed_is_anonymous ON kanglink_post_feed(is_anonymous);

-- Add composite index for program_id + is_anonymous for efficient filtering
CREATE INDEX idx_post_feed_program_anonymous ON kanglink_post_feed(program_id, is_anonymous);

-- Update existing posts to have is_anonymous = FALSE (default value)
-- This is already handled by the DEFAULT FALSE in the column definition

-- Optional: Add comment to document the column purpose
ALTER TABLE kanglink_post_feed 
MODIFY COLUMN is_anonymous BOOLEAN DEFAULT FALSE NOT NULL 
COMMENT 'Indicates if the post was created anonymously, hiding user identity'; 