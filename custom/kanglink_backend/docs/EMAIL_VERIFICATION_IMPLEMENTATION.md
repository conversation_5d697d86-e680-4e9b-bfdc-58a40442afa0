# Email Verification Implementation

## Overview

This document describes the implementation of email verification handling for unverified users during the login process. When a user attempts to login but their email is not verified, they are shown a verification modal instead of being allowed to proceed.

## Backend Changes

### Modified Files

#### 1. `lambda/member_login.js`

**Changes Made:**
- Updated the email verification check to return additional fields when a user is unverified
- Added `requires_verification`, `user_id`, and `email` fields to the error response

**Before:**
```javascript
if (!user.verify) {
  return res.status(403).json({
    error: true,
    message: "Your email is not verified"
  });
}
```

**After:**
```javascript
if (!user.verify) {
  return res.status(403).json({
    error: true,
    message: "Your email is not verified",
    requires_verification: true,
    user_id: user.id,
    email: user.email
  });
}
```

### API Response Format

**For Unverified Users:**
```json
{
  "error": true,
  "message": "Your email is not verified",
  "requires_verification": true,
  "user_id": 123,
  "email": "<EMAIL>"
}
```

## Frontend Changes

### Modified Files

#### 1. `pages/Common/Auth/LoginPage.tsx`

**Changes Made:**
- Added verification state management
- Updated login flow to handle unverified users
- Added EmailVerificationModal import and usage

**New State Variables:**
```typescript
const [showVerificationModal, setShowVerificationModal] = useState(false);
const [verificationData, setVerificationData] = useState({
  email: "",
  user_id: "",
  role: "",
});
```

**Updated Login Flow:**
```typescript
// Check if user needs verification
if (result.requires_verification) {
  setVerificationData({
    email: result.email,
    user_id: result.user_id,
    role: RoleMap[loginType],
  });
  setShowVerificationModal(true);
  return;
}
```

#### 2. `components/Profile/EmailVerificationModal.tsx`

**New Component:**
- Modal that displays when a user tries to login but is unverified
- Shows the user's email address
- Provides instructions on how to verify their email
- Includes buttons to close the modal and return to login

**Features:**
- Responsive design with theme support
- Clear instructions for email verification
- Displays the user's email address
- Helpful tips about checking spam folder

## Testing

### Updated Test File

#### `tests/lambda/member_login.test.js`

**Added Test Case:**
- Updated "member Login - Unverified Email" test to verify new response fields
- Tests for `requires_verification`, `user_id`, and `email` fields

**Test Assertions:**
```javascript
this.framework.assert(
  response.body.requires_verification === true,
  "Should return requires_verification flag"
);
this.framework.assert(
  response.body.user_id,
  "Should return user_id"
);
this.framework.assert(
  response.body.email,
  "Should return email"
);
```

## User Flow

### 1. User Attempts Login
- User enters email and password
- Clicks login button

### 2. Backend Validates
- Backend checks if user exists
- Validates password
- Checks email verification status

### 3. Unverified User Response
- If email is not verified, backend returns:
  - `error: true`
  - `requires_verification: true`
  - `user_id` and `email` fields

### 4. Frontend Shows Verification Modal
- Login form is hidden
- EmailVerificationModal is displayed
- Shows user's email address
- Provides verification instructions

### 5. User Verifies Email
- User checks their email
- Clicks verification link
- Email is verified in backend

### 6. User Returns to Login
- User clicks "I've Verified My Email" or "Back to Login"
- Modal closes
- User can attempt login again

## Resend Verification Feature

### Backend Implementation

#### `lambda/member_resend_verification.js`

**New Endpoint:** `/v1/api/kanglink/member/lambda/resend_verification`

**Features:**
- Validates user exists and is unverified
- Deletes existing verification tokens
- Generates new verification token
- Sends new verification email
- Handles various error scenarios

**Request Format:**
```json
{
  "email": "<EMAIL>",
  "role": "member"
}
```

**Response Format:**
```json
{
  "error": false,
  "message": "Verification email sent successfully"
}
```

**Error Scenarios:**
- Missing email: 400 Bad Request
- User not found: 404 Not Found
- Already verified: 400 Bad Request
- Email send failure: 500 Internal Server Error

### Frontend Implementation

#### Updated `EmailVerificationModal.tsx`

**New Features:**
- Resend button with loading state
- Success/error toast notifications
- Automatic retry functionality
- Visual feedback during resend process

**UI Elements:**
- Resend button with refresh icon
- Loading spinner during resend
- Disabled state while processing
- Success/error messages

### Testing

#### `tests/lambda/member_resend_verification.test.js`

**Test Cases:**
- Successful resend verification
- Missing email validation
- User not found scenario
- Already verified user
- Invalid role access
- Empty request body

### User Experience

**Resend Flow:**
1. User clicks "Resend Email" button
2. Button shows loading state with spinner
3. Backend generates new verification token
4. New verification email is sent
5. Success toast notification appears
6. Button returns to normal state

**Error Handling:**
- Network errors show error toast
- Invalid email shows validation message
- Already verified users get appropriate message
- Rate limiting considerations for spam prevention

## Security Considerations

1. **Token Expiration**: Verification tokens have a configurable expiration time
2. **Rate Limiting**: Implement rate limiting for verification and resend requests to prevent spam
3. **Email Validation**: Ensure email addresses are properly validated
4. **Token Security**: Verification tokens are stored securely in the database
5. **Resend Protection**: Delete old tokens before generating new ones to prevent token accumulation
6. **User Verification**: Only allow resend for unverified users
7. **Audit Logging**: Log resend attempts for security monitoring

## Configuration

### Environment Variables
- `verification_token_expire`: Token expiration time in seconds (default: 3600)
- `jwt_key`: Secret key for JWT token generation
- `app_url`: Base URL for verification links

### Database Tables
- `user`: Stores user verification status (`verify` field)
- `tokens`: Stores verification tokens with expiration

## Error Handling

### Common Error Scenarios
1. **Invalid Token**: Token is malformed or expired
2. **User Not Found**: User ID doesn't exist
3. **Already Verified**: User is already verified
4. **Email Send Failure**: Verification email fails to send

### Error Responses
All errors follow the standard response format:
```json
{
  "error": true,
  "message": "Error description"
}
```

## Maintenance

### Regular Tasks
1. Clean up expired verification tokens
2. Monitor email delivery rates
3. Review and update verification token expiration times
4. Test verification flow regularly
5. Monitor resend request patterns
6. Review rate limiting effectiveness

### Monitoring
- Track verification success rates
- Monitor email delivery failures
- Log verification attempts for security
- Monitor token expiration patterns
- Track resend request frequency
- Monitor for potential abuse patterns 