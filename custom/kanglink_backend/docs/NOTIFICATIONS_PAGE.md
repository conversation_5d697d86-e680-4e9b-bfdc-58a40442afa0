# Notifications Page Documentation

## Overview
The Notifications Page provides athletes with a comprehensive view of all their notifications, including filtering, pagination, and mark-as-read functionality. This page is accessible from the notification popover in the header.

## Features

### 1. **Notification Display**
- **Real-time Data**: Fetches notifications from the API
- **Visual Indicators**: Unread notifications have special styling and indicators
- **Rich Content**: Shows notification title, message, sender, and timestamp
- **Type Icons**: Different icons for different notification types

### 2. **Filtering Options**
- **Status Filter**: Filter by "All" or "Unread" notifications
- **Category Filter**: Filter by notification category (enrollment, payment, progress, system, trainer)
- **Collapsible Filters**: Filters can be toggled on/off

### 3. **Pagination**
- **Page Navigation**: Navigate through multiple pages of notifications
- **Page Information**: Shows current page and total pages
- **Responsive Design**: Works on all screen sizes

### 4. **Mark as Read Functionality**
- **Individual Mark as Read**: Mark individual notifications as read
- **Mark All as Read**: Mark all notifications as read at once
- **Real-time Updates**: UI updates immediately after marking as read

### 5. **Loading States**
- **Loading Spinner**: Shows while fetching notifications
- **Error Handling**: Displays error messages if API calls fail
- **Empty States**: Shows appropriate messages when no notifications exist

## Page Structure

### Header Section
```
┌─────────────────────────────────────────────────────────┐
│ 🔔 Notifications                    [Filters] [Mark All] │
│    123 total • 5 unread                                │
└─────────────────────────────────────────────────────────┘
```

### Filters Section (Collapsible)
```
┌─────────────────────────────────────────────────────────┐
│ Status: [All] [Unread (5)]                             │
│ Category: [All Categories ▼]                           │
└─────────────────────────────────────────────────────────┘
```

### Notifications List
```
┌─────────────────────────────────────────────────────────┐
│ 🎯 New Program Available                    [✓]        │
│    You have access to a new program...                 │
│    From: John Doe • 2 hours ago                        │
│    ● (unread indicator)                                │
├─────────────────────────────────────────────────────────┤
│ 💰 Payment Received                                    │
│    Your payment of $29.99 has been...                  │
│    From: System • 1 day ago                            │
│    [Read]                                              │
└─────────────────────────────────────────────────────────┘
```

### Pagination Section
```
┌─────────────────────────────────────────────────────────┐
│ Page 1 of 5                    [←] [→]                │
└─────────────────────────────────────────────────────────┘
```

## API Integration

### Endpoints Used
- `GET /v2/api/kanglink/custom/athlete/notifications` - Fetch notifications
- `PUT /v2/api/kanglink/custom/athlete/notifications/:id/read` - Mark as read
- `PUT /v2/api/kanglink/custom/athlete/notifications/read-all` - Mark all as read
- `GET /v2/api/kanglink/custom/athlete/notifications/unread-count` - Get unread count

### Query Parameters
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20)
- `unread_only` - Filter unread notifications (default: false)
- `category` - Filter by category (optional)

## Notification Types

### Icons and Colors
- **🎯 Enrollment**: Green badge - New program enrollments
- **💳 Payment**: Blue badge - Payment confirmations
- **📈 Progress**: Purple badge - Progress updates
- **🔔 System**: Yellow badge - System notifications
- **👨‍🏫 Trainer**: Indigo badge - Messages from trainers
- **📢 Default**: Gray badge - Other notifications

### Categories
- **enrollment**: Program enrollment notifications
- **payment**: Payment and billing notifications
- **progress**: Progress tracking notifications
- **system**: System-wide notifications
- **trainer**: Messages from trainers

## User Experience

### Navigation
- **Access**: Click "View all notifications" from header popover
- **Route**: `/athlete/notifications`
- **Authentication**: Requires member role
- **Back Navigation**: Browser back button or header navigation

### Responsive Design
- **Desktop**: Full layout with sidebar navigation
- **Tablet**: Optimized layout with collapsible filters
- **Mobile**: Stacked layout with touch-friendly buttons

### Performance
- **Lazy Loading**: Notifications load as needed
- **Debounced Filters**: Filter changes are debounced
- **Optimistic Updates**: UI updates immediately for better UX
- **Error Recovery**: Graceful error handling with retry options

## State Management

### Local State
```typescript
interface NotificationsState {
  notifications: Notification[];
  loading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  totalNotifications: number;
  unreadCount: number;
  filter: 'all' | 'unread';
  categoryFilter: string;
  showFilters: boolean;
}
```

### API State
- **Loading States**: Individual loading states for different operations
- **Error States**: Error messages for failed operations
- **Success States**: Confirmation messages for successful operations

## Accessibility

### Keyboard Navigation
- **Tab Order**: Logical tab order through all interactive elements
- **Enter/Space**: Activate buttons and links
- **Escape**: Close modals and popovers

### Screen Reader Support
- **ARIA Labels**: Proper labels for all interactive elements
- **Status Messages**: Announce loading, success, and error states
- **Semantic HTML**: Proper heading hierarchy and landmarks

### Color Contrast
- **WCAG Compliance**: All text meets WCAG AA contrast requirements
- **Theme Support**: Works with both light and dark themes
- **High Contrast**: Support for high contrast mode

## Error Handling

### Network Errors
- **Retry Logic**: Automatic retry for failed requests
- **Offline Support**: Graceful degradation when offline
- **Error Messages**: User-friendly error messages

### Validation Errors
- **Input Validation**: Client-side validation for filters
- **API Validation**: Server-side validation with clear error messages
- **Fallback Values**: Default values when validation fails

## Testing

### Unit Tests
- **Component Tests**: Test individual components
- **Hook Tests**: Test custom hooks for data fetching
- **Utility Tests**: Test utility functions

### Integration Tests
- **API Integration**: Test API calls and responses
- **User Flows**: Test complete user journeys
- **Error Scenarios**: Test error handling

### E2E Tests
- **Navigation**: Test navigation from header to page
- **Filtering**: Test all filter combinations
- **Pagination**: Test pagination functionality
- **Mark as Read**: Test mark as read functionality

## Future Enhancements

### Planned Features
- **Real-time Updates**: WebSocket integration for live notifications
- **Push Notifications**: Browser push notifications
- **Email Integration**: Email notification preferences
- **Advanced Filtering**: Date range and custom filters
- **Bulk Actions**: Select multiple notifications for bulk operations

### Performance Improvements
- **Virtual Scrolling**: For large notification lists
- **Caching**: Client-side caching for better performance
- **Preloading**: Preload next page of notifications
- **Optimistic Updates**: More optimistic UI updates

## Related Files

- **Page Component**: `ViewAthleteNotificationsPage.tsx`
- **API Service**: `NotificationService.ts`
- **Custom Hook**: `useNotifications.ts`
- **Route Definition**: `Routes.tsx`
- **Lazy Load**: `LazyLoad.ts`
- **Test File**: `notifications_page.test.js`
- **Documentation**: `NOTIFICATIONS_PAGE.md` 