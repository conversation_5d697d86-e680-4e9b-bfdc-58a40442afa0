# Post Feed API Documentation

## Overview

The Post Feed API provides endpoints for managing social media-style posts within fitness programs. Users can create posts, view program feeds, and interact with content through a paginated interface.

## Base URL

```
/v2/api/kanglink/custom/trainer/feed
```

## Authentication

All endpoints require authentication via <PERSON><PERSON> token in the Authorization header:

```
Authorization: Bearer <your-token>
```

## Endpoints

### 1. Get Paginated Posts for a Program

**GET** `/v2/api/kanglink/custom/trainer/feed`

Retrieves a paginated list of posts for a specific program with related user, program, and split data.

#### Query Parameters

| Parameter          | Type    | Required | Description                                                                         |
| ------------------ | ------- | -------- | ----------------------------------------------------------------------------------- |
| `program_id`       | integer | Yes      | ID of the program to fetch posts for                                                |
| `page`             | integer | No       | Page number (default: 1)                                                            |
| `limit`            | integer | No       | Number of posts per page (default: 50)                                              |
| `post_type`        | string  | No       | Filter by post type: `review`, `announcement`, `question`, `update`                 |
| `visibility_scope` | string  | No       | Filter by visibility (trainers/admins only): `public`, `program_members`, `private` |
| `user_id`          | integer | No       | Filter posts by specific user                                                       |

#### Response Format

```json
{
  "success": true,
  "data": {
    "posts": [
      {
        "id": 1,
        "user_id": 123,
        "program_id": 456,
        "split_id": 789,
        "post_type": "question",
        "content": "How often should I rest between sets?",
        "rating": null,
        "attachments": ["image1.jpg", "video1.mp4"],
        "is_private": false,
        "visibility_scope": "public",
        "is_pinned": false,
        "pin_expiration": null,
        "is_edited": false,
        "is_flagged": false,
        "flag_reason": null,
        "reaction_count": 5,
        "comment_count": 3,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "user": {
          "id": 123,
          "email": "<EMAIL>",
          "data": { "name": "John Doe" }
        },
        "program": {
          "id": 456,
          "program_name": "Strength Training 101",
          "type_of_program": "strength"
        },
        "split": {
          "id": 789,
          "title": "Upper Body"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 150,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  },
  "message": "Posts retrieved successfully"
}
```

#### Visibility Rules

- **Members**: Can see public posts, program_members posts, and their own private posts. Flagged posts are hidden.
- **Trainers/Super Admins**: Can see all posts including flagged ones.

#### Sorting

Posts are sorted by:

1. Pinned posts first (`is_pinned DESC`)
2. Creation date newest first (`created_at DESC`)

---

### 2. Create a New Post

**POST** `/v2/api/kanglink/custom/trainer/feed`

Creates a new post in the program feed.

#### Request Body

```json
{
  "program_id": 456,
  "split_id": 789,
  "post_type": "question",
  "content": "How often should I train each muscle group?",
  "rating": 5,
  "attachments": ["image1.jpg", "video1.mp4"],
  "is_private": false,
  "visibility_scope": "public"
}
```

#### Field Descriptions

| Field              | Type    | Required    | Description                                                          |
| ------------------ | ------- | ----------- | -------------------------------------------------------------------- |
| `program_id`       | integer | Yes         | ID of the program                                                    |
| `split_id`         | integer | No          | ID of the split (optional)                                           |
| `post_type`        | string  | Yes         | Type: `review`, `announcement`, `question`, `update`                 |
| `content`          | string  | Yes         | Post content/message                                                 |
| `rating`           | integer | Conditional | Required for reviews (1-5)                                           |
| `attachments`      | array   | No          | Array of attachment URLs                                             |
| `is_private`       | boolean | No          | Whether post is private (default: false)                             |
| `visibility_scope` | string  | No          | Visibility: `public`, `program_members`, `private` (default: public) |

#### Validation Rules

- `post_type` must be one of: `review`, `announcement`, `question`, `update`
- `rating` is required for `review` posts and must be 1-5
- Only trainers and super_admins can create `announcement` posts
- Members cannot create announcements

#### Response Format

```json
{
  "success": true,
  "data": {
    "post": {
      "id": 123,
      "user_id": 456,
      "program_id": 789,
      "post_type": "question",
      "content": "How often should I train each muscle group?",
      "attachments": ["image1.jpg"],
      "is_private": false,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  },
  "message": "Post created successfully"
}
```

---

### 3. Get a Single Post

**GET** `/v2/api/kanglink/custom/trainer/feed/:post_id`

Retrieves a specific post with full details including user and program information.

#### URL Parameters

| Parameter | Type    | Required | Description                |
| --------- | ------- | -------- | -------------------------- |
| `post_id` | integer | Yes      | ID of the post to retrieve |

#### Response Format

Same as individual post object in the paginated response.

#### Access Control

- Members cannot access private posts they don't own
- Members cannot see flagged posts
- Trainers and super_admins can access all posts

---

### 4. Get Reactions for a Post

**GET** `/v2/api/kanglink/custom/trainer/feed/:post_id/reactions`

Retrieves paginated reactions for a specific post.

#### URL Parameters

| Parameter | Type    | Required | Description                         |
| --------- | ------- | -------- | ----------------------------------- |
| `post_id` | integer | Yes      | ID of the post to get reactions for |

#### Query Parameters

| Parameter       | Type    | Required | Description                                               |
| --------------- | ------- | -------- | --------------------------------------------------------- |
| `page`          | integer | No       | Page number (default: 1)                                  |
| `limit`         | integer | No       | Number of reactions per page (default: 50)                |
| `reaction_type` | string  | No       | Filter by reaction type: `like`, `love`, `fire`, `strong` |

#### Response Format

```json
{
  "success": true,
  "data": {
    "reactions": [
      {
        "id": 1,
        "user_id": 123,
        "target_type": "post",
        "target_id": 456,
        "reaction_type": "like",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "user": {
          "id": 123,
          "email": "<EMAIL>",
          "data": { "name": "John Doe" }
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 25,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  },
  "message": "Reactions retrieved successfully"
}
```

---

### 5. Get Comments for a Post

**GET** `/v2/api/kanglink/custom/trainer/feed/:post_id/comments`

Retrieves paginated comments for a specific post with optional nested replies.

#### URL Parameters

| Parameter | Type    | Required | Description                        |
| --------- | ------- | -------- | ---------------------------------- |
| `post_id` | integer | Yes      | ID of the post to get comments for |

#### Query Parameters

| Parameter         | Type    | Required | Description                               |
| ----------------- | ------- | -------- | ----------------------------------------- |
| `page`            | integer | No       | Page number (default: 1)                  |
| `limit`           | integer | No       | Number of comments per page (default: 20) |
| `include_replies` | boolean | No       | Include nested replies (default: true)    |

#### Response Format

```json
{
  "success": true,
  "data": {
    "comments": [
      {
        "id": 1,
        "user_id": 123,
        "post_id": 456,
        "parent_comment_id": null,
        "content": "Great workout! Really helped me improve my form.",
        "attachments": ["image1.jpg"],
        "is_private": false,
        "is_edited": false,
        "is_flagged": false,
        "flag_reason": null,
        "mentioned_users": [789],
        "reaction_count": 3,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "user": {
          "id": 123,
          "email": "<EMAIL>",
          "data": { "name": "John Doe" }
        },
        "replies": [
          {
            "id": 2,
            "user_id": 456,
            "post_id": 456,
            "parent_comment_id": 1,
            "content": "Thanks for the feedback!",
            "attachments": null,
            "is_private": false,
            "is_edited": false,
            "is_flagged": false,
            "flag_reason": null,
            "mentioned_users": [123],
            "reaction_count": 1,
            "created_at": "2024-01-15T11:00:00Z",
            "updated_at": "2024-01-15T11:00:00Z",
            "user": {
              "id": 456,
              "email": "<EMAIL>",
              "data": { "name": "Jane Trainer" }
            }
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 15,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  },
  "message": "Comments retrieved successfully"
}
```

#### Comment Features

- **Nested Replies**: Comments can have replies (parent_comment_id references parent comment)
- **Mentions**: Users can be mentioned in comments via mentioned_users array
- **Attachments**: Comments support file attachments
- **Moderation**: Flagged comments are hidden from members
- **Privacy**: Comments can be marked as private

---

## Post Types

### Review Posts

- **Purpose**: User reviews and ratings of programs
- **Required Fields**: `content`, `rating` (1-5)
- **Who Can Create**: All authenticated users
- **Special Features**: Includes star rating system

### Announcement Posts

- **Purpose**: Official announcements from trainers
- **Required Fields**: `content`
- **Who Can Create**: Trainers and super_admins only
- **Special Features**: Can be pinned to top of feed

### Question Posts

- **Purpose**: User questions about programs/workouts
- **Required Fields**: `content`
- **Who Can Create**: All authenticated users
- **Special Features**: Designed for community interaction

### Update Posts

- **Purpose**: Progress updates and general sharing
- **Required Fields**: `content`
- **Who Can Create**: All authenticated users
- **Special Features**: General purpose posting

## Visibility Scopes

### Public

- Visible to all users
- Default visibility setting
- Appears in public program feeds

### Program Members

- Visible only to users enrolled in the program
- Requires program membership verification
- More private than public but not fully private

### Private

- Visible only to the post creator and trainers/admins
- Most restrictive visibility
- Used for personal notes or sensitive content

## Error Responses

### 400 Bad Request

```json
{
  "success": false,
  "message": "program_id is required"
}
```

### 401 Unauthorized

```json
{
  "success": false,
  "message": "Authentication required"
}
```

### 403 Forbidden

```json
{
  "success": false,
  "message": "Only trainers can create announcements"
}
```

### 404 Not Found

```json
{
  "success": false,
  "message": "Post not found"
}
```

### 500 Internal Server Error

```json
{
  "success": false,
  "message": "Internal server error",
  "error": "Detailed error message"
}
```

## Usage Examples

### Frontend Integration

```typescript
// Fetch posts for a program
const fetchPosts = async (programId: number, page: number = 1) => {
  const response = await fetch(
    `/v2/api/kanglink/custom/trainer/feed?program_id=${programId}&page=${page}&limit=20`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
  return response.json();
};

// Create a new question post
const createPost = async (postData: PostFeed) => {
  const response = await fetch("/v2/api/kanglink/custom/trainer/feed", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(postData),
  });
  return response.json();
};
```

## Rate Limiting

- No specific rate limits currently implemented
- Standard API rate limiting applies
- Consider implementing rate limiting for post creation to prevent spam

## Future Enhancements

- Comment system integration
- Reaction system integration
- Real-time updates via WebSocket
- Advanced filtering and search
- Post editing and deletion endpoints
- Moderation tools for flagging/unflagging posts
