# Program Creation Endpoint

## Overview
This endpoint creates a complete fitness program with all its related data including splits, weeks, days, sessions, and exercise instances.

## Endpoint
```
POST /v2/api/kanglink/custom/trainer/programs/create
```

## Authentication
- Requires valid JWT token
- Authorized roles: `trainer`, `super_admin`

## Request Body
The endpoint expects a JSON payload with two main sections:

### stepOneData
Contains the main program information and splits configuration:

```json
{
  "stepOneData": {
    "program_name": "string",
    "type_of_program": "string",
    "program_description": "string",
    "payment_plan": ["array of strings"],
    "track_progress": boolean,
    "allow_comments": boolean,
    "allow_private_messages": boolean,
    "target_levels": ["array of strings"],
    "split_program": number,
    "splits": [
      {
        "title": "string",
        "full_price": number,
        "subscription": number,
        "split_id": "string (UUID)"
      }
    ],
    "currency": "string",
    "days_for_preview": number
  }
}
```

### stepTwoData
Contains the detailed workout structure for each split:

```json
{
  "stepTwoData": {
    "program_split": "string (UUID)",
    "description": "string",
    "splitConfigurations": {
      "split_id_uuid": [
        {
          "id": "string",
          "title": "string",
          "equipment_required": "string",
          "days": [
            {
              "id": "string",
              "title": "string",
              "is_rest_day": boolean,
              "sessions": [
                {
                  "id": "string",
                  "title": "string",
                  "exercises": [
                    {
                      "id": "string",
                      "sets": "string",
                      "reps_or_time": "string",
                      "exercise_details": "string",
                      "rest_duration_minutes": number,
                      "rest_duration_seconds": number,
                      "label": "string",
                      "label_number": "string",
                      "is_linked": boolean,
                      "exercise_order": number,
                      "user_id": number,
                      "session_id": "string",
                      "exercise_id": "string",
                      "video_id": "string (URL or ID)",
                      "reps_time_type": "string"
                    }
                  ],
                  "session_letter": "string",
                  "session_number": number,
                  "linked_session_id": "string or null",
                  "day_id": "string"
                }
              ],
              "week_id": "string"
            }
          ],
          "split_id": "string"
        }
      ]
    }
  }
}
```

## Response

### Success Response (200)
```json
{
  "error": false,
  "message": "Program created successfully",
  "program_id": number,
  "data": {
    "program": { /* program object */ },
    "splits_created": number,
    "weeks_created": number,
    "days_created": number,
    "sessions_created": number,
    "exercises_created": number,
    "videos_created": number
  }
}
```

### Error Responses

#### 400 - Bad Request
```json
{
  "error": true,
  "message": "Both stepOneData and stepTwoData are required"
}
```

#### 401 - Unauthorized
```json
{
  "error": true,
  "message": "UNAUTHORIZED"
}
```

#### 500 - Internal Server Error
```json
{
  "error": true,
  "message": "Error message describing the issue"
}
```

## Features

### Automatic Video Handling
- If `video_id` is a URL (starts with 'http'), the endpoint automatically creates a video record
- The created video ID is then linked to the exercise instance
- If `video_id` is already an ID, it's used directly

### Transaction-like Behavior
- The endpoint implements manual rollback functionality
- If any step fails during creation, all previously created records are deleted
- Rollback happens in reverse order: exercises → videos → sessions → days → weeks → splits → program

### Data Relationships
The endpoint creates records in the following hierarchy:
```
Program
└── Split(s)
    └── Week(s)
        └── Day(s)
            └── Session(s)
                └── Exercise Instance(s)
                    ├── Video (if URL provided)
                    └── Exercise (if exercise_id provided)
```

## Database Tables Affected
- `kanglink_program`
- `kanglink_split`
- `kanglink_week`
- `kanglink_day`
- `kanglink_session`
- `kanglink_exercise_instance`
- `kanglink_video` (conditionally)

## Usage Example

```javascript
const payload = {
  stepOneData: {
    program_name: "Beginner Strength Training",
    type_of_program: "strength",
    program_description: "A comprehensive beginner program",
    payment_plan: ["one_time"],
    track_progress: true,
    allow_comments: true,
    allow_private_messages: false,
    target_levels: ["beginner"],
    split_program: 1,
    splits: [
      {
        title: "Full Body",
        full_price: 99.99,
        subscription: 0,
        split_id: "uuid-here"
      }
    ],
    currency: "USD",
    days_for_preview: 7
  },
  stepTwoData: {
    // ... detailed workout structure
  }
};

fetch('/v2/api/kanglink/custom/trainer/programs/create', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify(payload)
});
```
