# Testing Stripe Integration Cleanup

## Verification Test Plan

This document outlines how to test that the Stripe integration cleanup is working correctly.

## Test Scenarios

### 1. **Happy Path - Complete Flow**

#### Step 1: Create Program with Pricing
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/trainer/programs/published" \
  -H "Authorization: Bearer TRAINER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "stepOneData": {
      "program_name": "Test Cleanup Program",
      "type_of_program": "strength",
      "currency": "USD",
      "splits": [
        {
          "title": "Test Split",
          "full_price": 99.99,
          "subscription": 29.99,
          "split_id": "test-split-1"
        }
      ]
    },
    "stepTwoData": {
      "program_split": "test-split-1",
      "splitConfigurations": {
        "test-split-1": [
          {
            "id": "week-1",
            "title": "Week 1",
            "week_order": 1,
            "days": [
              {
                "id": "day-1",
                "title": "Day 1",
                "day_order": 1,
                "is_rest_day": false,
                "sessions": [
                  {
                    "id": "session-1",
                    "title": "Session 1",
                    "session_order": 1,
                    "exercises": [
                      {
                        "id": "exercise-1",
                        "sets": "3",
                        "reps_or_time": "10",
                        "exercise_details": "Push-ups",
                        "exercise_order": 1,
                        "exercise_id": "1",
                        "reps_time_type": "reps"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  }'
```

**Expected Result:**
- Program created successfully
- Stripe product created: "Test Cleanup Program - Test Split"
- Stripe prices created:
  - "Test Cleanup Program - Test Split - One Time" ($99.99)
  - "Test Cleanup Program - Test Split Monthly" ($29.99/month)

#### Step 2: Verify Stripe Resources in Database
```sql
-- Check Stripe product
SELECT * FROM kanglink_stripe_product 
WHERE name = 'Test Cleanup Program - Test Split';

-- Check Stripe prices
SELECT * FROM kanglink_stripe_price 
WHERE name LIKE 'Test Cleanup Program - Test Split%';
```

#### Step 3: Test Subscription Enrollment
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/athlete/enroll" \
  -H "Authorization: Bearer ATHLETE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "split_id": SPLIT_ID_FROM_STEP_1,
    "payment_type": "subscription",
    "payment_method_id": "pm_card_visa"
  }'
```

**Expected Result:**
```json
{
  "error": false,
  "message": "Enrollment created successfully",
  "data": {
    "enrollment_id": 123,
    "payment_type": "subscription",
    "amount": 29.99,
    "currency": "USD",
    "status": "active",
    "payment_status": "paid",
    "stripe_subscription_id": "sub_xxx"
  }
}
```

#### Step 4: Test One-Time Enrollment
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/athlete/enroll" \
  -H "Authorization: Bearer ATHLETE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "split_id": SPLIT_ID_FROM_STEP_1,
    "payment_type": "one_time",
    "payment_method_id": "pm_card_visa"
  }'
```

**Expected Result:**
```json
{
  "error": false,
  "message": "Enrollment created successfully",
  "data": {
    "enrollment_id": 124,
    "payment_type": "one_time",
    "amount": 99.99,
    "currency": "USD",
    "status": "pending",
    "payment_status": "pending",
    "stripe_payment_intent_id": "pi_xxx"
  }
}
```

### 2. **Error Handling - Missing Pricing**

#### Step 1: Create Program Without Pricing
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/trainer/programs/published" \
  -H "Authorization: Bearer TRAINER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "stepOneData": {
      "program_name": "Free Program",
      "splits": [
        {
          "title": "Free Split",
          "full_price": 0,
          "subscription": 0,
          "split_id": "free-split-1"
        }
      ]
    },
    "stepTwoData": { /* minimal workout data */ }
  }'
```

#### Step 2: Try to Enroll (Should Fail)
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/athlete/enroll" \
  -H "Authorization: Bearer ATHLETE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "split_id": FREE_SPLIT_ID,
    "payment_type": "subscription",
    "payment_method_id": "pm_card_visa"
  }'
```

**Expected Result:**
```json
{
  "error": true,
  "message": "Subscription pricing not configured for this split. Please contact the trainer to republish the program."
}
```

### 3. **Partial Pricing Configuration**

#### Step 1: Create Program with Only One-Time Pricing
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/trainer/programs/published" \
  -d '{
    "stepOneData": {
      "splits": [
        {
          "title": "One-Time Only Split",
          "full_price": 149.99,
          "subscription": 0
        }
      ]
    }
  }'
```

#### Step 2: Test One-Time Enrollment (Should Work)
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/athlete/enroll" \
  -d '{
    "split_id": SPLIT_ID,
    "payment_type": "one_time",
    "payment_method_id": "pm_card_visa"
  }'
```

**Expected**: Success

#### Step 3: Test Subscription Enrollment (Should Fail)
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/athlete/enroll" \
  -d '{
    "split_id": SPLIT_ID,
    "payment_type": "subscription",
    "payment_method_id": "pm_card_visa"
  }'
```

**Expected**: Error message about missing subscription pricing

### 4. **Price Update Flow**

#### Step 1: Update Split Pricing
```bash
curl -X PUT "http://localhost:3000/v2/api/kanglink/custom/trainer/splits/SPLIT_ID" \
  -H "Authorization: Bearer TRAINER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "full_price": 199.99,
    "subscription": 49.99
  }'
```

#### Step 2: Verify Old Prices Deactivated
```sql
-- Check that old prices are marked as inactive
SELECT * FROM kanglink_stripe_price 
WHERE name LIKE 'Test Cleanup Program - Test Split%'
ORDER BY created_at DESC;
```

#### Step 3: Test Enrollment with New Pricing
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/athlete/enroll" \
  -d '{
    "split_id": SPLIT_ID,
    "payment_type": "subscription",
    "payment_method_id": "pm_card_visa"
  }'
```

**Expected**: Enrollment uses new $49.99 pricing

## Verification Checklist

### ✅ **Code Quality Checks**
- [ ] No duplicate Stripe product creation code
- [ ] No duplicate Stripe price creation code
- [ ] Enrollment route only finds existing prices
- [ ] Clear error messages when prices missing
- [ ] Helper function reduces code duplication

### ✅ **Functional Tests**
- [ ] Program creation creates Stripe resources
- [ ] Enrollment finds existing Stripe prices
- [ ] Subscription enrollment works with existing prices
- [ ] One-time enrollment works with existing prices
- [ ] Clear errors when pricing not configured
- [ ] Price updates create new Stripe prices
- [ ] Old prices properly deactivated

### ✅ **Database Verification**
- [ ] Stripe products saved correctly
- [ ] Stripe prices saved correctly
- [ ] Price status updates work (active/inactive)
- [ ] No orphaned Stripe resources

### ✅ **Error Handling**
- [ ] Missing subscription pricing handled gracefully
- [ ] Missing one-time pricing handled gracefully
- [ ] Helpful error messages guide users
- [ ] No Stripe resource creation during enrollment

### ✅ **Performance**
- [ ] Enrollment faster (no Stripe API calls for resource creation)
- [ ] Reduced database queries during enrollment
- [ ] No race conditions between program creation and enrollment

## Console Output Verification

### During Program Creation
```
Created Stripe product: prod_xxx for split 123
Created Stripe one-time price: price_xxx for split 123
Created Stripe subscription price: price_yyy for split 123
```

### During Enrollment (Should NOT see)
```
❌ Creating Stripe product...
❌ Creating Stripe price...
```

### During Enrollment (Should see)
```
✅ Finding existing Stripe price for split 123
✅ Using existing price: price_yyy
```

## Rollback Plan

If issues are found, the cleanup can be rolled back by:

1. **Restore enrollment.js** to previous version with product/price creation
2. **Keep program.js** Stripe integration (it's still beneficial)
3. **Add duplicate detection** to prevent conflicts
4. **Plan gradual migration** to cleaned-up version

## Success Criteria

The cleanup is successful if:

1. **All existing functionality works** as before
2. **No duplicate Stripe resources** are created
3. **Clear error messages** guide users when issues occur
4. **Performance improved** for enrollment process
5. **Code is more maintainable** with clear separation of concerns

This test plan ensures the Stripe integration cleanup maintains functionality while improving code quality and user experience.
