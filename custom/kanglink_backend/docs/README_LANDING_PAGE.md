# KangLink Landing Page Endpoints

## 🚀 Overview

This implementation provides 6 high-performance landing page endpoints for the KangLink fitness platform, designed with performance optimization and user experience in mind.

## 📋 Endpoints Implemented

### Programs
1. **Top Rated Programs** - `/v2/api/kanglink/custom/landing/top-rated-programs`
2. **Programs You May Like** - `/v2/api/kanglink/custom/landing/programs-you-may-like` (Auth Required)
3. **All Programs** - `/v2/api/kanglink/custom/landing/all-programs`

### Trainers
4. **Top Rated Trainers** - `/v2/api/kanglink/custom/landing/top-rated-trainers`
5. **Trainers You May Like** - `/v2/api/kanglink/custom/landing/trainers-you-may-like` (Auth Required)
6. **All Trainers** - `/v2/api/kanglink/custom/landing/all-trainers`

## ⚡ Performance Features

### 🔄 Caching Strategy
- **Redis Caching**: 10-minute cache for public endpoints
- **Rating Calculations**: 5-minute cache for aggregated ratings
- **Smart Cache Keys**: Include filters and pagination in cache keys
- **Authenticated Bypass**: No caching for personalized results

### 🗃️ Database Optimization
- **Efficient Queries**: Uses JOINs and subqueries to minimize database calls
- **Proper Indexing**: Comprehensive index strategy for all query patterns
- **Rating Aggregation**: Single-query rating calculations with subqueries
- **Pagination**: LIMIT/OFFSET implementation with total count optimization

### 🎯 Smart Recommendations
- **Match Scoring Algorithm**: Calculates compatibility based on user preferences
- **Fitness Level Matching**: Matches user level with program target levels
- **Specialization Matching**: Matches user goals with trainer specializations
- **Experience Weighting**: Considers trainer experience for user fitness level

## 📁 File Structure

```
mtpbk/custom/ksl_be/
├── routes/
│   └── landing_page.js                 # Main endpoint implementations
├── docs/
│   └── landing_page_endpoints.md       # Comprehensive API documentation
├── tests/
│   └── landing_page_tests.js          # Test suite with performance monitoring
├── performance_indexes.sql             # Database optimization indexes
└── README_LANDING_PAGE.md             # This file
```

## 🛠️ Installation & Setup

### 1. Database Optimization
Run the performance indexes to optimize query performance:

```bash
mysql -u username -p database_name < performance_indexes.sql
```

### 2. Redis Setup (Optional but Recommended)
Ensure Redis is running for caching:

```bash
# Install Redis (Ubuntu/Debian)
sudo apt-get install redis-server

# Start Redis
sudo systemctl start redis-server

# Verify Redis is running
redis-cli ping
```

### 3. Environment Configuration
Ensure your configuration includes Redis settings:

```javascript
// In your config file
module.exports = {
  is_redis: true,
  redis_host: 'localhost',
  redis_port: 6379,
  redis_password: '', // if required
  // ... other config
};
```

## 🧪 Testing

### Run Basic Tests
```bash
cd mtpbk/custom/ksl_be
node tests/landing_page_tests.js
```

### Run Load Tests
```javascript
const { runLoadTest } = require('./tests/landing_page_tests.js');

// Test with 10 concurrent users, 5 iterations each
runLoadTest(10, 5);
```

### Test Configuration
Update the test configuration in `tests/landing_page_tests.js`:

```javascript
const BASE_URL = 'http://localhost:5172'; // Your server URL
const TEST_TOKEN = 'your_test_token_here'; // Valid JWT token for auth tests
```

## 📊 Performance Benchmarks

### Expected Performance
- **Response Time**: < 500ms for cached requests
- **Response Time**: < 2000ms for uncached requests
- **Throughput**: 100+ requests/second with proper indexing
- **Memory Usage**: < 50MB additional heap per request

### Monitoring
The test suite includes performance monitoring that tracks:
- Response times
- Memory usage
- Data transfer sizes
- Success rates
- Concurrent user handling

## 🔧 Configuration Options

### Query Parameters

#### Common Parameters (All Endpoints)
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20, max: 50): Items per page

#### Search & Filter Parameters
- `search` (string): Search in names, descriptions, etc.
- `min_rating` / `max_rating` (float): Rating range filter
- `sort_by` (string): Sort field (rating, name, created_at, etc.)
- `sort_order` (string): Sort direction (asc, desc)

#### Program-Specific Parameters
- `category` (string): Program type filter
- `level` (string): Target fitness level
- `has_preview` (boolean): Programs with preview days

#### Trainer-Specific Parameters
- `specialization` (string): Trainer specialization filter
- `min_experience` / `max_experience` (integer): Experience range
- `has_programs` (boolean): Trainers with published programs

## 🔐 Authentication

### Optional Authentication
Most endpoints support optional authentication:
- **Without Auth**: Returns general popular content
- **With Auth**: Returns personalized results with match scores

### Required Authentication
These endpoints require authentication:
- Programs You May Like
- Trainers You May Like

### Token Format
```
Authorization: Bearer <your_jwt_token>
```

## 📈 Response Format

All endpoints return consistent response format:

```json
{
  "error": false,
  "message": "Success message",
  "data": [...], // Array of results
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "num_pages": 8,
    "has_next": true,
    "has_prev": false
  },
  "filters": {...}, // Applied filters (where applicable)
  "user_preferences": {...} // User preferences (for personalized endpoints)
}
```

## 🚨 Error Handling

### HTTP Status Codes
- `200`: Success
- `400`: Bad Request (invalid parameters, missing user preferences)
- `401`: Unauthorized (missing/invalid token for protected endpoints)
- `500`: Internal Server Error

### Error Response Format
```json
{
  "error": true,
  "message": "Error description"
}
```

## 🔍 Troubleshooting

### Common Issues

1. **Slow Response Times**
   - Ensure database indexes are created
   - Check Redis connection
   - Monitor database query performance

2. **Authentication Errors**
   - Verify JWT token is valid
   - Check token expiration
   - Ensure user has required role

3. **Empty Results**
   - Check if programs/trainers exist in database
   - Verify status filters (published programs, active users)
   - Check user preferences for personalized endpoints

### Performance Monitoring
```sql
-- Check slow queries
SHOW PROCESSLIST;

-- Analyze query performance
EXPLAIN SELECT ... FROM kanglink_program ...;

-- Check index usage
SHOW INDEX FROM kanglink_program;
```

## 🤝 Contributing

When modifying these endpoints:

1. **Maintain Performance**: Always consider query optimization
2. **Test Thoroughly**: Run the test suite after changes
3. **Update Documentation**: Keep docs in sync with code changes
4. **Monitor Metrics**: Check performance impact of changes

## 📞 Support

For issues or questions regarding these endpoints:

1. Check the comprehensive documentation in `docs/landing_page_endpoints.md`
2. Run the test suite to identify specific issues
3. Review performance metrics and database query plans
4. Check Redis connectivity and caching behavior

---

**Built with performance in mind for the KangLink fitness platform** 🏋️‍♂️
