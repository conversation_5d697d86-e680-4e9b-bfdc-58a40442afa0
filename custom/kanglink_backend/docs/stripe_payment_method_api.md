# Stripe PaymentMethod Management API

## Overview

This API provides endpoints for managing Stripe PaymentMethods for customers in the Kanglink platform. These endpoints handle attaching, detaching, and listing PaymentMethods for authenticated members.

## Base URL

```
http://localhost:5172/v1/api/kanglink/member/lambda/stripe/customer
```

## Authentication

All endpoints require member authentication using JWT tokens:

```
Authorization: Bearer <jwt_token>
```

## Endpoints

### 1. Attach PaymentMethod to Customer

Attaches a PaymentMethod to the authenticated user's Stripe customer account. If the user doesn't have a Stripe customer account, one will be created automatically.

**Endpoint:** `POST /payment-method/attach`

**Request Body:**
```json
{
  "payment_method_id": "pm_1Rgx95BgOlWo0lDUfWEA0vpj"
}
```

**Success Response (200):**
```json
{
  "error": false,
  "message": "PaymentMethod attached successfully",
  "data": {
    "payment_method": {
      "id": "pm_1Rgx95BgOlWo0lDUfWEA0vpj",
      "object": "payment_method",
      "type": "card",
      "card": {
        "brand": "visa",
        "last4": "4242",
        "exp_month": 12,
        "exp_year": 2025
      },
      "customer": "cus_ABC123"
    },
    "customer_id": "cus_ABC123"
  }
}
```

**Error Responses:**
- `400` - Missing payment_method_id
- `401` - Unauthorized (invalid/missing token)
- `404` - User not found
- `500` - Server error

### 2. Detach PaymentMethod from Customer

Detaches a PaymentMethod from its customer, making it available for reuse.

**Endpoint:** `POST /payment-method/detach`

**Request Body:**
```json
{
  "payment_method_id": "pm_1Rgx95BgOlWo0lDUfWEA0vpj"
}
```

**Success Response (200):**
```json
{
  "error": false,
  "message": "PaymentMethod detached successfully",
  "data": {
    "payment_method": {
      "id": "pm_1Rgx95BgOlWo0lDUfWEA0vpj",
      "object": "payment_method",
      "type": "card",
      "card": {
        "brand": "visa",
        "last4": "4242",
        "exp_month": 12,
        "exp_year": 2025
      },
      "customer": null
    }
  }
}
```

### 3. List Customer PaymentMethods

Retrieves all PaymentMethods attached to the authenticated user's customer account.

**Endpoint:** `GET /payment-methods`

**Query Parameters:**
- `type` (optional): Filter by PaymentMethod type (default: "card")
- `limit` (optional): Number of results to return (default: 10, max: 100)

**Example Request:**
```
GET /payment-methods?type=card&limit=5
```

**Success Response (200):**
```json
{
  "error": false,
  "message": "PaymentMethods retrieved successfully",
  "data": {
    "payment_methods": [
      {
        "id": "pm_1Rgx95BgOlWo0lDUfWEA0vpj",
        "object": "payment_method",
        "type": "card",
        "card": {
          "brand": "visa",
          "last4": "4242",
          "exp_month": 12,
          "exp_year": 2025,
          "funding": "credit",
          "country": "US"
        },
        "customer": "cus_ABC123",
        "created": **********
      }
    ],
    "has_more": false
  }
}
```

## Frontend Integration Examples

### JavaScript/TypeScript SDK Methods

```typescript
// Types
interface PaymentMethodAttachRequest {
  payment_method_id: string;
}

interface PaymentMethodDetachRequest {
  payment_method_id: string;
}

interface PaymentMethodListParams {
  type?: 'card' | 'bank_account';
  limit?: number;
}

// API Client Methods
class StripePaymentMethodAPI {
  private baseUrl = '/v1/api/kanglink/member/lambda/stripe/customer';
  
  async attachPaymentMethod(data: PaymentMethodAttachRequest) {
    const response = await fetch(`${this.baseUrl}/payment-method/attach`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}`
      },
      body: JSON.stringify(data)
    });
    return response.json();
  }
  
  async detachPaymentMethod(data: PaymentMethodDetachRequest) {
    const response = await fetch(`${this.baseUrl}/payment-method/detach`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}`
      },
      body: JSON.stringify(data)
    });
    return response.json();
  }
  
  async listPaymentMethods(params: PaymentMethodListParams = {}) {
    const queryString = new URLSearchParams({
      type: params.type || 'card',
      limit: (params.limit || 10).toString()
    }).toString();
    
    const response = await fetch(`${this.baseUrl}/payment-methods?${queryString}`, {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
      }
    });
    return response.json();
  }
}
```

### React Hook Example

```typescript
import { useState, useCallback } from 'react';

export const usePaymentMethods = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const api = new StripePaymentMethodAPI();

  const attachPaymentMethod = useCallback(async (paymentMethodId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await api.attachPaymentMethod({
        payment_method_id: paymentMethodId
      });
      
      if (result.error) {
        throw new Error(result.message);
      }
      
      return result.data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to attach payment method');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const listPaymentMethods = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await api.listPaymentMethods();
      
      if (result.error) {
        throw new Error(result.message);
      }
      
      return result.data.payment_methods;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load payment methods');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    attachPaymentMethod,
    listPaymentMethods,
    loading,
    error
  };
};
```

## Usage Flow for Enrollment

### Recommended Integration Pattern

```typescript
// 1. Before enrollment, check if user has saved payment methods
const existingMethods = await listPaymentMethods();

if (existingMethods.length > 0) {
  // Show card selection modal
  const selectedMethod = await showCardSelectionModal(existingMethods);
  // Use selected payment method for enrollment
  await enrollInProgram(splitId, selectedMethod.id);
} else {
  // Create new payment method with Stripe Elements
  const { paymentMethod } = await stripe.createPaymentMethod({
    type: 'card',
    card: cardElement,
  });
  
  // Attach to customer for future use
  await attachPaymentMethod(paymentMethod.id);
  
  // Use for enrollment
  await enrollInProgram(splitId, paymentMethod.id);
}
```

## Error Handling

```typescript
try {
  await attachPaymentMethod(paymentMethodId);
} catch (error) {
  if (error.message.includes('already attached')) {
    // PaymentMethod is already attached to this or another customer
    console.log('PaymentMethod already in use');
  } else if (error.message.includes('not found')) {
    // PaymentMethod doesn't exist
    console.log('Invalid PaymentMethod ID');
  } else {
    // Other errors
    console.error('Attachment failed:', error.message);
  }
}
```

## Notes

- PaymentMethods can only be attached to one customer at a time
- Detaching a PaymentMethod makes it reusable
- The API automatically creates Stripe customers when needed
- All endpoints require valid member authentication
- PaymentMethod IDs are provided by Stripe Elements or other Stripe APIs
