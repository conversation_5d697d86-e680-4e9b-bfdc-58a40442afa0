# Anonymous Posts Feature

## Overview

The anonymous posts feature allows users to create posts without revealing their identity. When a post is marked as anonymous, the user's personal information is not stored or displayed with the post.

## Backend Implementation

### Database Changes

The `post_feed` table now includes an `is_anonymous` boolean field to track anonymous posts.

### API Endpoints

#### Create Anonymous Post

**Endpoint:** `POST /v2/api/kanglink/custom/trainer/feed`

**Request Body:**
```json
{
  "program_id": 123,
  "split_id": 456,
  "post_type": "update",
  "content": "This is my anonymous post",
  "is_private": false,
  "is_anonymous": true,
  "visibility_scope": "public"
}
```

**Key Changes:**
- Added `is_anonymous` field to request body
- When `is_anonymous` is `true`, `user_id` is set to `null` in the database
- The `is_anonymous` field is stored as a boolean in the database

#### Retrieve Posts

**Endpoint:** `GET /v2/api/kanglink/custom/trainer/feed?program_id=123`

**Response for Anonymous Posts:**
```json
{
  "error": false,
  "data": [
    {
      "id": "post_123",
      "user_id": null,
      "content": "This is an anonymous post",
      "is_anonymous": true,
      "user": {
        "id": null,
        "email": "Anonymous",
        "data": {
          "first_name": "Anonymous",
          "last_name": "User"
        }
      }
      // ... other fields
    }
  ]
}
```

## Frontend Implementation

### PostComposer Component

The `PostComposer` component includes:
- Anonymous toggle checkbox
- Proper state management for `isAnonymous`
- Integration with the post creation API

### Anonymous Post Display

When displaying anonymous posts:
- Author name shows as "Anonymous User"
- Author ID is set to "anonymous" (string) to maintain compatibility
- Generic avatar is used
- No trainer badge is shown for anonymous posts

### Usage Example

```tsx
const handlePost = (content: string, isPrivate: boolean, isAnonymous: boolean) => {
  createPost({
    program_id: selectedEnrollment?.program_id,
    split_id: selectedEnrollment?.split_id,
    post_type: "update",
    content,
    is_private: isPrivate,
    is_anonymous: isAnonymous,
    visibility_scope: isPrivate ? "private" : "public",
  });
};

<PostComposer onPost={handlePost} />
```

## Security Considerations

1. **Authentication Required**: Even anonymous posts require user authentication
2. **No User Data Leakage**: Anonymous posts completely hide user identity
3. **Database Privacy**: `user_id` is set to `null` for anonymous posts
4. **Audit Trail**: The system still tracks which authenticated user created the post (in logs if needed)

## API Response Structure

### Anonymous Post Response
```json
{
  "id": "123",
  "user_id": null,
  "content": "Post content",
  "is_anonymous": true,
  "user": {
    "id": null,
    "email": "Anonymous",
    "data": {
      "first_name": "Anonymous",
      "last_name": "User"
    }
  }
}
```

### Regular Post Response
```json
{
  "id": "123",
  "user_id": "456",
  "content": "Post content",
  "is_anonymous": false,
  "user": {
    "id": "456",
    "email": "<EMAIL>",
    "data": {
      "first_name": "John",
      "last_name": "Doe"
    }
  }
}
```

## Testing

Comprehensive tests are available in `tests/lambda/post_feed_anonymous.test.js` covering:
- Creating anonymous posts
- Creating regular posts
- Retrieving anonymous posts with proper data masking
- Validation and error handling
- Authentication requirements

## Database Schema

```sql
ALTER TABLE kanglink_post_feed 
ADD COLUMN is_anonymous BOOLEAN DEFAULT FALSE;
```

## Migration Notes

- Existing posts will have `is_anonymous` set to `FALSE` by default
- No data migration is needed as the feature is additive
- Backward compatibility is maintained

## Future Enhancements

1. **Anonymous Comments**: Extend the feature to support anonymous comments
2. **Anonymous Reactions**: Allow anonymous reactions to posts
3. **Admin Controls**: Add admin settings to enable/disable anonymous posting per program
4. **Analytics**: Track anonymous vs identified post engagement (while maintaining privacy) 