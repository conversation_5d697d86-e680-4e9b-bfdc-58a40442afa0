# Discount System API Documentation

## Overview

The discount system provides comprehensive discount management for fitness programs, supporting program-level discounts, split-specific discounts, and promotional coupon codes.

## Base URL

```
/v2/api/kanglink/custom/trainer/programs/{programId}/discounts
```

## Authentication

All endpoints require authentication with a valid JWT token for `trainer` or `super_admin` roles.

**Header:**
```
Authorization: Bearer <jwt_token>
```

## Endpoints

### 1. GET - Fetch Program Discount Details

**Endpoint:** `GET /v2/api/kanglink/custom/trainer/programs/{programId}/discounts`

**Description:** Retrieves complete discount configuration for a program.

**Response Structure:**
```json
{
  "success": true,
  "data": {
    "id": "program_discount_id",
    "affiliateLink": "string",
    "saleDiscount": {
      "type": "fixed|percentage",
      "value": "number",
      "applyToAll": "boolean"
    },
    "subscriptionDiscounts": [
      {
        "id": "discount_id",
        "tierId": "split_id",
        "discountType": "fixed|percentage",
        "discountValue": "number"
      }
    ],
    "fullPriceDiscounts": [
      {
        "id": "discount_id",
        "tierId": "split_id",
        "discountType": "fixed|percentage",
        "discountValue": "number"
      }
    ],
    "promoCode": {
      "id": "coupon_id",
      "code": "string",
      "discountType": "fixed|percentage",
      "discountValue": "number",
      "appliesTo": {
        "subscription": "boolean",
        "fullPayment": "boolean"
      },
      "isActive": "boolean",
      "expiryDate": "ISO_date_string",
      "usageLimit": "number",
      "usedCount": "number"
    },
    "lastUpdated": "ISO_date_string"
  }
}
```

### 2. POST - Create Program Discount Configuration

**Endpoint:** `POST /v2/api/kanglink/custom/trainer/programs/{programId}/discounts`

**Description:** Creates a new discount configuration for a program.

**Request Body:**
```json
{
  "affiliateLink": "string",
  "saleDiscount": {
    "type": "fixed|percentage",
    "value": "number",
    "applyToAll": "boolean"
  },
  "subscriptionDiscounts": [
    {
      "tierId": "split_id",
      "discountType": "fixed|percentage",
      "discountValue": "number"
    }
  ],
  "fullPriceDiscounts": [
    {
      "tierId": "split_id",
      "discountType": "fixed|percentage",
      "discountValue": "number"
    }
  ],
  "promoCode": {
    "code": "string",
    "discountType": "fixed|percentage",
    "discountValue": "number",
    "appliesTo": {
      "subscription": "boolean",
      "fullPayment": "boolean"
    },
    "isActive": "boolean",
    "expiryDate": "ISO_date_string",
    "usageLimit": "number"
  }
}
```

**Success Response (201):**
```json
{
  "success": true,
  "message": "Discount configuration created successfully",
  "data": {
    "programId": "number",
    "programDiscountId": "number",
    "subscriptionDiscountsCount": "number",
    "fullPriceDiscountsCount": "number",
    "couponId": "number|null",
    "createdAt": "ISO_date_string"
  }
}
```

**Error Response (409) - Already Exists:**
```json
{
  "success": false,
  "message": "Discount configuration already exists for this program. Use PUT to update."
}
```

### 3. PUT - Update Program Discount Configuration

**Endpoint:** `PUT /v2/api/kanglink/custom/trainer/programs/{programId}/discounts`

**Description:** Updates existing discount configuration for a program.

**Request Body:** Same as POST endpoint

**Success Response (200):**
```json
{
  "success": true,
  "message": "Discount settings updated successfully",
  "data": {
    "programId": "number",
    "lastUpdated": "ISO_date_string"
  }
}
```

### 4. DELETE - Remove Program Discount Configuration

**Endpoint:** `DELETE /v2/api/kanglink/custom/trainer/programs/{programId}/discounts`

**Description:** Completely removes discount configuration and all related data.

**Success Response (200):**
```json
{
  "success": true,
  "message": "Discount configuration deleted successfully",
  "data": {
    "programId": "number",
    "deletionSummary": {
      "programDiscountDeleted": "boolean",
      "discountsDeleted": "number",
      "couponsDeleted": "number",
      "couponUsagesDeleted": "number"
    },
    "deletedAt": "ISO_date_string"
  }
}
```

**Error Response (404) - Not Found:**
```json
{
  "success": false,
  "message": "No discount configuration found for this program"
}
```

## Validation Rules

### Discount Types
- Must be either `"fixed"` or `"percentage"`
- Fixed discounts: positive numbers
- Percentage discounts: 0-100

### Promo Codes
- Length: 3-50 characters
- Characters: alphanumeric, hyphens, underscores only
- Must be unique across all active coupons
- Expiry date must be in the future (if provided)
- Usage limit must be positive (if provided)

### Split IDs
- Must belong to the specified program
- Validated against existing program splits

## Error Responses

### 400 - Bad Request
```json
{
  "success": false,
  "message": "Program ID is required"
}
```

### 401/403 - Unauthorized
```json
{
  "success": false,
  "message": "Unauthorized access"
}
```

### 404 - Not Found
```json
{
  "success": false,
  "message": "Program not found or access denied"
}
```

### 500 - Validation Error
```json
{
  "success": false,
  "message": "Sale discount percentage cannot exceed 100%"
}
```

## Usage Examples

### Creating a Basic Discount Configuration
```javascript
const response = await fetch('/v2/api/kanglink/custom/trainer/programs/123/discounts', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your_jwt_token'
  },
  body: JSON.stringify({
    affiliateLink: 'https://example.com/affiliate',
    saleDiscount: {
      type: 'percentage',
      value: 10,
      applyToAll: true
    },
    promoCode: {
      code: 'SAVE20',
      discountType: 'percentage',
      discountValue: 20,
      appliesTo: {
        subscription: true,
        fullPayment: true
      },
      isActive: true,
      usageLimit: 100
    }
  })
});
```

### Fetching Discount Configuration
```javascript
const response = await fetch('/v2/api/kanglink/custom/trainer/programs/123/discounts', {
  headers: {
    'Authorization': 'Bearer your_jwt_token'
  }
});
const data = await response.json();
```

## Database Tables

The system uses four main tables:
- `kanglink_program_discount` - Program-level settings
- `kanglink_discount` - Split-specific discounts
- `kanglink_coupon` - Promotional codes
- `kanglink_coupon_usage` - Usage tracking
