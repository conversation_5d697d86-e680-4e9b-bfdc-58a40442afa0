# Trainer Transactions and Earnings System

This document describes the trainer transactions and earnings system implementation for the KangaLink platform.

## Overview

The system provides trainers with:
- Real-time transaction statistics (total earnings, pending payouts, available to withdraw, withdrawn)
- Monthly earnings graph visualization
- Automatic withdrawal processing through Stripe Connect
- Detailed transaction history with filtering and pagination
- Commission processing with configurable payout timing

## API Endpoints

### 1. Transaction Statistics
**GET** `/v2/api/kanglink/custom/trainer/transactions/stats`

Returns trainer's financial overview including:
- `total_earnings`: All processed commissions
- `pending_payouts`: Funds waiting for payout time to elapse
- `available_to_withdraw`: Funds ready for withdrawal
- `withdrawn`: Already processed payouts
- `currency`: Currency code
- `payout_time_hours`: Current payout delay setting

**Authentication**: Trainer role required

**Response Example**:
```json
{
  "success": true,
  "data": {
    "total_earnings": 2250.00,
    "pending_payouts": 1500.00,
    "available_to_withdraw": 15.00,
    "withdrawn": 1500.00,
    "currency": "USD",
    "payout_time_hours": 24
  }
}
```

### 2. Earnings Graph Data
**GET** `/v2/api/kanglink/custom/trainer/transactions/earnings-graph`

Returns monthly earnings data for the past 12 months for graph visualization.

**Authentication**: Trainer role required

**Response Example**:
```json
{
  "success": true,
  "data": {
    "earnings_by_month": [
      {
        "month": "2024-01",
        "month_name": "Jan",
        "year": 2024,
        "month_number": 1,
        "earnings": 150.00
      }
    ],
    "total_months": 12,
    "currency": "USD"
  }
}
```

### 3. Withdrawal Request
**POST** `/v2/api/kanglink/custom/trainer/transactions/withdraw`

Processes withdrawal of available funds through Stripe Connect.

**Authentication**: Trainer role required

**Request Body**:
```json
{
  "amount": 15.00,
  "currency": "USD"
}
```

**Response Example**:
```json
{
  "success": true,
  "data": {
    "withdrawal_id": "tr_1234567890",
    "amount": 15.00,
    "currency": "USD",
    "status": "processed",
    "transfer_details": {
      "stripe_transfer_id": "tr_1234567890",
      "destination_account": "acct_1234567890",
      "created": **********
    },
    "message": "Withdrawal processed successfully"
  }
}
```

### 4. Transaction History
**GET** `/v2/api/kanglink/custom/trainer/transactions/history`

Returns paginated transaction history with filtering options.

**Authentication**: Trainer role required

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `type`: Filter by type ('all', 'earnings', 'withdrawals', 'refunds')
- `status`: Filter by payout status
- `start_date`: Filter from date (YYYY-MM-DD)
- `end_date`: Filter to date (YYYY-MM-DD)

**Response Example**:
```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "commission_id": 123,
        "enrollment_id": 456,
        "type": "earning",
        "commission_type": "regular",
        "amount": {
          "total": 100.00,
          "original": 120.00,
          "discount": 20.00,
          "trainer_amount": 70.00,
          "company_amount": 30.00,
          "currency": "USD"
        },
        "status": "pending",
        "dates": {
          "created": "2024-01-15T10:30:00Z",
          "scheduled_payout": "2024-01-16T10:30:00Z",
          "processed_payout": null,
          "enrollment_date": "2024-01-15T10:00:00Z"
        },
        "program": {
          "id": 789,
          "title": "Advanced Fitness Program",
          "description": "Complete fitness transformation"
        },
        "split": {
          "id": 101,
          "title": "Beginner Split",
          "full_price": 120.00,
          "subscription_price": 29.99
        },
        "athlete": {
          "email": "<EMAIL>",
          "name": "John Doe"
        },
        "enrollment": {
          "payment_type": "one_time",
          "status": "active",
          "payment_status": "paid",
          "stripe_subscription_id": null,
          "stripe_payment_intent_id": "pi_1234567890"
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total_records": 45,
      "total_pages": 3,
      "has_next": true,
      "has_prev": false
    },
    "filters": {
      "type": "all",
      "status": null,
      "start_date": null,
      "end_date": null
    }
  }
}
```

### 5. Stripe Connect Setup
**POST** `/v2/api/kanglink/custom/trainer/transactions/setup-stripe-connect`

Creates or retrieves Stripe Connect account for trainer payouts.

**Authentication**: Trainer role required

**Request Body**:
```json
{
  "country": "US",
  "business_type": "individual",
  "return_url": "https://yourapp.com/trainer/transactions?setup=complete",
  "refresh_url": "https://yourapp.com/trainer/transactions?setup=refresh"
}
```

**Response Example**:
```json
{
  "success": true,
  "data": {
    "stripe_connect_account_id": "acct_1234567890",
    "onboarding_url": "https://connect.stripe.com/setup/...",
    "account_status": {
      "onboarded": false,
      "details_submitted": false,
      "charges_enabled": false,
      "payouts_enabled": false,
      "requirements": {...}
    },
    "expires_at": **********,
    "message": "Please complete the Stripe Connect onboarding process"
  }
}
```

### 6. Stripe Connect Status
**GET** `/v2/api/kanglink/custom/trainer/transactions/stripe-connect-status`

Checks the current status of trainer's Stripe Connect account.

**Authentication**: Trainer role required

**Response Example**:
```json
{
  "success": true,
  "data": {
    "has_stripe_connect": true,
    "stripe_connect_account_id": "acct_1234567890",
    "onboarded": true,
    "details_submitted": true,
    "charges_enabled": true,
    "payouts_enabled": true,
    "requirements": null,
    "message": "Stripe Connect account is fully set up and ready for payouts"
  }
}
```

## Database Models

### Commission Model
Tracks trainer earnings from enrollments with payout status and timing.

**Key Fields**:
- `enrollment_id`: Related enrollment
- `trainer_id`: Trainer receiving commission
- `commission_type`: 'regular' or 'affiliate'
- `trainer_amount`: Amount trainer will receive
- `payout_status`: 'pending', 'processed', 'failed', 'cancelled'
- `payout_scheduled_at`: When funds become available
- `payout_processed_at`: When withdrawal was processed

### Payout Settings Model
Admin-configurable settings for commission processing.

**Key Fields**:
- `trainer_payout_time_hours`: Hours to wait before making funds available
- `split_trainer_percentage`: Trainer's share for regular enrollments
- `affiliate_trainer_percentage`: Trainer's share for affiliate enrollments
- `is_active`: Whether these settings are currently active

## Commission Processing Flow

1. **Enrollment Created**: Commission record created with 'pending' status
2. **Payout Timer**: Funds remain pending for configured hours (default 24)
3. **Available for Withdrawal**: After timer expires, funds become available
4. **Withdrawal Request**: Trainer requests withdrawal through Stripe Connect
5. **Transfer Processing**: Stripe transfers funds to trainer's account
6. **Status Update**: Commission marked as 'processed'

## Refund Handling

When an enrollment is refunded:
1. Related commission records are marked as 'cancelled'
2. If funds were already withdrawn, the system tracks this for reconciliation
3. Future commission calculations account for refunded amounts

## Background Processing

The `CommissionProcessingService` handles:
- Moving pending commissions to available status
- Processing refund scenarios
- Creating commissions for new enrollments

**Manual Processing Endpoint** (Admin only):
**POST** `/v2/api/kanglink/custom/admin/transactions/process-commissions`

## Webhook Endpoints

### Enrollment Created
**POST** `/v2/api/kanglink/custom/webhook/enrollment-created`
Creates commission record for new enrollment.

### Enrollment Refunded
**POST** `/v2/api/kanglink/custom/webhook/enrollment-refunded`
Cancels related commission records.

## Integration Notes

1. **Stripe Connect**: Trainers must complete Stripe Connect onboarding before withdrawals
2. **Currency**: System supports multiple currencies but defaults to USD
3. **Precision**: All monetary amounts use proper rounding for Stripe API
4. **Error Handling**: Comprehensive error responses with actionable messages
5. **Security**: All endpoints require proper authentication and role validation

## Frontend Integration

The frontend should:
1. Display transaction stats in dashboard cards
2. Render earnings graph using provided monthly data
3. Handle Stripe Connect onboarding flow
4. Provide withdrawal interface with amount validation
5. Show transaction history with filtering options
6. Handle error states and loading indicators

## Testing

Use the custom test suite framework in `mtpbk/tests/` for comprehensive testing of all endpoints and commission processing logic.
