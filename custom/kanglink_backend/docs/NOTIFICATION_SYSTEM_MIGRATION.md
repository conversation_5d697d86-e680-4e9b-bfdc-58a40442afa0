# Notification System Migration Guide

## Overview

This document outlines the migration from scattered notification endpoints across multiple files to a centralized notification system. The new system provides better organization, maintainability, and consistency.

## New File Structure

### 1. `notification.js` - Common Notification Endpoints
**Purpose:** Handles notifications available to all roles (member, trainer, super_admin)
**Table:** `kanglink_notification`
**Endpoints:**
- `GET /v2/api/kanglink/custom/notifications` - Get user notifications
- `PUT /v2/api/kanglink/custom/notifications/:id/read` - Mark notification as read
- `PUT /v2/api/kanglink/custom/notifications/read-all` - Mark all as read
- `GET /v2/api/kanglink/custom/notifications/unread-count` - Get unread count

### 2. `trainer_notification.js` - Trainer-Specific Notifications
**Purpose:** Handles trainer-specific notifications using dedicated table
**Table:** `kanglink_trainer_notifications`
**Endpoints:**
- `GET /v2/api/kanglink/custom/trainer/notifications` - Get trainer notifications
- `PUT /v2/api/kanglink/custom/trainer/notifications/:notification_id/read` - <PERSON> as read
- `PUT /v2/api/kanglink/custom/trainer/notifications/read-all` - Mark all as read
- `GET /v2/api/kanglink/custom/trainer/notifications/unread-count` - Get unread count

### 3. `super_admin_notification.js` - System Alerts
**Purpose:** Handles system alerts and administrative notifications
**Table:** `kanglink_notification` (filtered by type)
**Endpoints:**
- `POST /v2/api/kanglink/custom/super_admin/notifications/system-alert` - Create system alert
- `GET /v2/api/kanglink/custom/super_admin/notifications/system-alerts` - Get system alerts
- `PUT /v2/api/kanglink/custom/super_admin/notifications/system-alerts/:id/read` - Mark as read

## Migration from Old Endpoints

### Old Endpoints to Remove

#### From `athlete.js`:
```javascript
// Remove these endpoints (lines 607-758)
app.get("/v2/api/kanglink/custom/athlete/notifications", ...)
app.put("/v2/api/kanglink/custom/athlete/notifications/:id/read", ...)
app.put("/v2/api/kanglink/custom/athlete/notifications/read-all", ...)
app.get("/v2/api/kanglink/custom/athlete/notifications/unread-count", ...)
```

#### From `athlete_progress.js`:
```javascript
// Remove these endpoints (lines 550-637)
app.get("/v2/api/kanglink/custom/trainer/notifications", ...)
app.put("/v2/api/kanglink/custom/trainer/notifications/:notification_id/read", ...)
```

#### From `super_admin.js`:
```javascript
// Remove these endpoints (lines 1026-1248)
app.get("/v2/api/kanglink/custom/super_admin/notifications", ...)
app.put("/v2/api/kanglink/custom/super_admin/notifications/:id/read", ...)
app.put("/v2/api/kanglink/custom/super_admin/notifications/read-all", ...)
app.get("/v2/api/kanglink/custom/super_admin/notifications/unread-count", ...)
app.post("/v2/api/kanglink/custom/super_admin/notifications/system-alert", ...)
```

#### From `trainer_dashboard.js`:
```javascript
// Remove these endpoints (lines 150-424)
app.get("/v2/api/kanglink/custom/trainer/dashboard/notifications", ...)
app.put("/v2/api/kanglink/custom/trainer/dashboard/notifications/:id/read", ...)
app.put("/v2/api/kanglink/custom/trainer/dashboard/notifications/read-all", ...)
```

### New Endpoint Mapping

| Old Endpoint | New Endpoint | Notes |
|--------------|--------------|-------|
| `/v2/api/kanglink/custom/athlete/notifications` | `/v2/api/kanglink/custom/notifications` | Common endpoint for all roles |
| `/v2/api/kanglink/custom/athlete/notifications/:id/read` | `/v2/api/kanglink/custom/notifications/:id/read` | Common endpoint for all roles |
| `/v2/api/kanglink/custom/athlete/notifications/read-all` | `/v2/api/kanglink/custom/notifications/read-all` | Common endpoint for all roles |
| `/v2/api/kanglink/custom/athlete/notifications/unread-count` | `/v2/api/kanglink/custom/notifications/unread-count` | Common endpoint for all roles |
| `/v2/api/kanglink/custom/trainer/notifications` | `/v2/api/kanglink/custom/trainer/notifications` | Trainer-specific endpoint |
| `/v2/api/kanglink/custom/trainer/notifications/:id/read` | `/v2/api/kanglink/custom/trainer/notifications/:notification_id/read` | Trainer-specific endpoint |
| `/v2/api/kanglink/custom/trainer/dashboard/notifications` | `/v2/api/kanglink/custom/trainer/notifications` | Consolidated into trainer notifications |
| `/v2/api/kanglink/custom/super_admin/notifications` | `/v2/api/kanglink/custom/notifications` | Common endpoint for all roles |
| `/v2/api/kanglink/custom/super_admin/notifications/system-alert` | `/v2/api/kanglink/custom/super_admin/notifications/system-alert` | Super admin specific |

## Implementation Steps

### Step 1: Add New Files
1. Add `notification.js` to routes directory
2. Add `trainer_notification.js` to routes directory
3. Add `super_admin_notification.js` to routes directory

### Step 2: Update API Registration
Add the new notification routes to your main API registration:

```javascript
// In your main API file (e.g., api.js)
const notificationRoutes = require("./routes/notification");
const trainerNotificationRoutes = require("./routes/trainer_notification");
const superAdminNotificationRoutes = require("./routes/super_admin_notification");

// Register routes
notificationRoutes(app);
trainerNotificationRoutes(app);
superAdminNotificationRoutes(app);
```

### Step 3: Remove Old Endpoints
Remove the notification endpoints from the following files:
- `athlete.js` (lines 607-758)
- `athlete_progress.js` (lines 550-637)
- `super_admin.js` (lines 1026-1248)
- `trainer_dashboard.js` (lines 150-424)

### Step 4: Update Frontend
Update frontend code to use the new endpoints:

```javascript
// Old
const response = await fetch('/v2/api/kanglink/custom/athlete/notifications');

// New
const response = await fetch('/v2/api/kanglink/custom/notifications');
```

### Step 5: Update Tests
1. Remove old notification tests from individual route test files
2. Add comprehensive tests in `notification.test.js`

## Benefits of New System

### 1. **Centralized Management**
- All notification logic is in dedicated files
- Easier to maintain and update
- Consistent error handling and response formats

### 2. **Role-Based Organization**
- Common notifications for all roles
- Trainer-specific notifications with additional context
- Super admin system alerts

### 3. **Better Testing**
- Comprehensive test coverage
- Isolated test scenarios
- Easier to mock and test

### 4. **Improved Documentation**
- Clear endpoint documentation
- Usage examples
- Error handling guidelines

### 5. **Consistent API Design**
- Standardized response formats
- Consistent error handling
- Proper HTTP status codes

## Database Considerations

### Existing Tables
- `kanglink_notification` - General notifications for all users
- `kanglink_trainer_notifications` - Trainer-specific notifications

### Data Migration
No data migration is required as the existing tables remain unchanged. The new system uses the same database structure.

## Testing Strategy

### 1. Unit Tests
- Test each endpoint individually
- Verify role-based access control
- Test error scenarios

### 2. Integration Tests
- Test notification creation and retrieval
- Test pagination and filtering
- Test read/unread functionality

### 3. End-to-End Tests
- Test complete notification workflows
- Test cross-role interactions
- Test real-world usage scenarios

## Rollback Plan

If issues arise during migration:

1. **Immediate Rollback**: Keep old endpoints temporarily
2. **Gradual Migration**: Migrate one endpoint at a time
3. **Feature Flag**: Use feature flags to switch between old and new endpoints
4. **Monitoring**: Monitor error rates and performance

## Performance Considerations

### 1. **Database Indexing**
Ensure proper indexes on:
- `user_id` in `kanglink_notification`
- `trainer_id` in `kanglink_trainer_notifications`
- `is_read` for filtering unread notifications
- `created_at` for sorting

### 2. **Caching Strategy**
- Cache unread counts
- Cache recent notifications
- Use Redis for real-time updates

### 3. **Pagination**
- All list endpoints support pagination
- Default limits to prevent performance issues
- Efficient query optimization

## Security Considerations

### 1. **Authentication**
- All endpoints require valid JWT tokens
- Token validation on every request

### 2. **Authorization**
- Role-based access control
- Users can only access their own notifications
- Super admins can create system alerts

### 3. **Input Validation**
- Validate all input parameters
- Sanitize user data
- Prevent SQL injection

## Monitoring and Logging

### 1. **Error Tracking**
- Log all notification errors
- Monitor failed notification creation
- Track read/unread operations

### 2. **Performance Monitoring**
- Monitor response times
- Track database query performance
- Monitor memory usage

### 3. **Usage Analytics**
- Track notification types
- Monitor user engagement
- Analyze notification effectiveness

## Future Enhancements

### 1. **Real-time Notifications**
- WebSocket implementation
- Push notifications
- Email notifications

### 2. **Advanced Filtering**
- Filter by notification type
- Filter by date range
- Search functionality

### 3. **Bulk Operations**
- Bulk mark as read
- Bulk delete notifications
- Export notifications

### 4. **Notification Templates**
- Customizable notification templates
- Multi-language support
- Rich text formatting

## Conclusion

The new centralized notification system provides better organization, maintainability, and consistency. The migration should be done gradually with proper testing and monitoring to ensure a smooth transition.

For questions or issues during migration, refer to the comprehensive documentation in `NOTIFICATION_ENDPOINTS.md` and the test suite in `notification.test.js`. 