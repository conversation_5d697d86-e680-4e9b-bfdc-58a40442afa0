# Model Mapping: TypeScript Interfaces to Backend Models

This document shows the mapping between the frontend TypeScript interfaces and the backend BaseModel implementations.

## ✅ Created Models

### 1. Program Interface → program.js
**TypeScript Interface:**
```typescript
export interface Program {
  id?: number | string;
  user_id?: number | string;
  program_name?: string;
  type_of_program?: string;
  program_description?: string;
  payment_plan?: string[];
  track_progress?: boolean;
  allow_comments?: boolean;
  allow_private_messages?: boolean;
  target_levels?: string[];
  split_program?: number;
  currency?: string;
  days_for_preview?: number;
  created_at?: string;
  updated_at?: string;
  status: "draft" | "pending_approval" | "live" | "published" | "rejected" | "archived";
}
```

**Backend Model:** `mtpbk/custom/ksl_be/models/program.js`
- ✅ All fields mapped correctly
- ✅ Status enum implemented as mapping
- ✅ JSON fields for arrays (payment_plan, target_levels)
- ✅ Boolean fields for flags
- ✅ Transform method for status mapping

### 2. Split Interface → split.js
**TypeScript Interface:**
```typescript
export interface Split {
  id?: string | number;
  program_id?: string | number;
  equipment_required?: string;
  title?: string;
  full_price?: number;
  subscription?: number;
  created_at?: string;
  updated_at?: string;
}
```

**Backend Model:** `mtpbk/custom/ksl_be/models/split.js`
- ✅ All fields mapped correctly
- ✅ Foreign key relationship to program
- ✅ Float type for price fields

### 3. Week Interface → week.js
**TypeScript Interface:**
```typescript
export interface Week {
  id?: string | number;
  split_id?: string | number;
  title?: string;
  equipment_required?: string;
  created_at?: string;
  updated_at?: string;
}
```

**Backend Model:** `mtpbk/custom/ksl_be/models/week.js`
- ✅ All fields mapped correctly
- ✅ Foreign key relationship to split

### 4. Day Interface → day.js
**TypeScript Interface:**
```typescript
export interface Day {
  id?: string | number;
  week_id?: string | number;
  title?: string;
  is_rest_day?: boolean;
  created_at?: string;
  updated_at?: string;
}
```

**Backend Model:** `mtpbk/custom/ksl_be/models/day.js`
- ✅ All fields mapped correctly
- ✅ Foreign key relationship to week
- ✅ Boolean field for rest day flag

### 5. Session Interface → session.js
**TypeScript Interface:**
```typescript
export interface Session {
  id?: string | number;
  day_id?: string | number;
  title?: string;
  session_letter?: string;
  session_number?: number;
  linked_session_id?: string | null;
  created_at?: string;
  updated_at?: string;
}
```

**Backend Model:** `mtpbk/custom/ksl_be/models/session.js`
- ✅ All fields mapped correctly
- ✅ Foreign key relationships to day and linked session
- ✅ Integer type for session number

### 6. ExerciseInstance Interface → exercise_instance.js
**TypeScript Interface:**
```typescript
export interface ExerciseInstance {
  id?: number | string;
  user_id?: number;
  session_id?: number | string;
  exercise_id?: number | string;
  video_id?: number | null;
  sets?: string | null;
  reps_or_time?: string | null;
  reps_time_type?: "reps" | "time";
  exercise_details?: string | null;
  rest_duration_minutes?: number;
  rest_duration_seconds?: number;
  label?: string | null;
  label_number?: string;
  is_linked?: boolean;
  exercise_order?: number;
  created_at?: string;
  updated_at?: string;
}
```

**Backend Model:** `mtpbk/custom/ksl_be/models/exercise_instance.js`
- ✅ All fields mapped correctly
- ✅ Multiple foreign key relationships
- ✅ Enum mapping for reps_time_type
- ✅ Boolean field for is_linked
- ✅ Transform method for reps_time_type

### 7. Exercise Interface → exercise.js
**TypeScript Interface:**
```typescript
export interface Exercise {
  id?: number | string;
  name?: string;
  type?: number;
  user_id?: number | null;
  created_at?: string;
  updated_at?: string;
}
```

**Backend Model:** `mtpbk/custom/ksl_be/models/exercise.js`
- ✅ All fields mapped correctly
- ✅ Foreign key relationship to user

### 8. Video Interface → video.js
**TypeScript Interface:**
```typescript
export interface Video {
  id?: number | string;
  name?: string;
  type?: number;
  url?: string;
  user_id?: number | null;
  created_at?: string;
  updated_at?: string;
}
```

**Backend Model:** `mtpbk/custom/ksl_be/models/video.js`
- ✅ All fields mapped correctly
- ✅ Foreign key relationship to user

## 🔗 Relationship Hierarchy

```
Program (program.js)
└── Split (split.js)
    └── Week (week.js)
        └── Day (day.js)
            └── Session (session.js)
                └── ExerciseInstance (exercise_instance.js)
                    ├── Exercise (exercise.js)
                    ├── Video (video.js)
                    └── User (user.js - existing)
```

## 📋 Field Type Mappings

| TypeScript Type | Backend Model Type | Notes |
|-----------------|-------------------|-------|
| `string` | `"string"` | Standard text fields |
| `number` | `"integer"` or `"float"` | Depends on decimal requirement |
| `boolean` | `"boolean"` | True/false values |
| `string[]` | `"json"` | Arrays stored as JSON |
| `enum` | `"mapping"` | Predefined value sets |
| `Date/string` | `"datetime"` | Timestamp fields |
| Foreign keys | `"foreign key"` | Relationships |
| Long text | `"long text"` | Extended text content |

## 🎯 Key Features Implemented

1. **Proper Relationships**: All foreign key relationships maintained
2. **Data Types**: Correct mapping of all TypeScript types to backend types
3. **Enums**: Status and type enums implemented as mappings
4. **Arrays**: JSON storage for array fields (payment_plan, target_levels)
5. **Nullable Fields**: Proper handling of optional/nullable fields
6. **Transform Methods**: Added for enum mappings where needed
7. **Validation**: Basic validation structure in place
8. **Timestamps**: Created_at and updated_at fields for all models

## 🚀 Usage Example

```javascript
const Program = require('./mtpbk/custom/ksl_be/models/program');

// Create a new program
const programData = {
  user_id: 1,
  program_name: "Beginner Strength Training",
  type_of_program: "strength",
  program_description: "A comprehensive beginner program",
  payment_plan: ["monthly", "yearly"],
  track_progress: true,
  allow_comments: true,
  allow_private_messages: false,
  target_levels: ["beginner", "intermediate"],
  split_program: 3,
  currency: "USD",
  days_for_preview: 7,
  status: "draft"
};

const program = new Program(programData);
if (program.isValid()) {
  console.log("Program is valid!");
} else {
  console.log("Validation errors:", program.getErrors());
}
```

All models are now ready for use with the BackendSDK and support the complete fitness program structure defined in the TypeScript interfaces.
