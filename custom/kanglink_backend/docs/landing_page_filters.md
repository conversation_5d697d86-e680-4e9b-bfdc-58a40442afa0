# Landing Page Filters Documentation

This document describes the comprehensive filtering system implemented for the landing page endpoints.

## Overview

The landing page now supports advanced filtering capabilities for both programs and trainers, allowing users to filter by gender, experience, rating, and other criteria.

## Supported Endpoints

### 1. All Programs Endpoint
**GET** `/v2/api/kanglink/custom/landing/all-programs`

### 2. All Trainers Endpoint  
**GET** `/v2/api/kanglink/custom/landing/all-trainers`

## Filter Parameters

### Gender Filter
Filter by trainer gender (applies to both programs and trainers).

**Parameter:** `gender`
**Type:** String (comma-separated values)
**Values:** `man`, `woman`, `non_binary`, `transgender_woman`, `transgender_man`, `other`

**Example:**
```
GET /v2/api/kanglink/custom/landing/all-programs?gender=man,woman
```

### Experience Filter
Filter by trainer experience level.

**Parameter:** `experience`
**Type:** String (comma-separated values)
**Values:** `less_than_1_year`, `1_2_years`, `3_5_years`, `5_7_years`, `6_10_years`, `10_plus_years`

**Example:**
```
GET /v2/api/kanglink/custom/landing/all-programs?experience=1_2_years,5_7_years
```

### Rating Filter
Filter by minimum and maximum rating.

**Parameters:**
- `min_rating` (number): Minimum rating (0-5)
- `max_rating` (number): Maximum rating (0-5)

**Example:**
```
GET /v2/api/kanglink/custom/landing/all-programs?min_rating=3.5&max_rating=4.5
```

### Experience Range Filter (Trainers Only)
Filter by experience range in years.

**Parameters:**
- `min_experience` (number): Minimum years of experience
- `max_experience` (number): Maximum years of experience

**Example:**
```
GET /v2/api/kanglink/custom/landing/all-trainers?min_experience=5&max_experience=15
```

### Sorting Options
Sort results by various criteria.

**Parameters:**
- `sort_by` (string): Sort field
- `sort_order` (string): Sort direction (`asc` or `desc`)

**Available sort options:**
- `created_at` - Recently Added
- `rating` - Highest Rated
- `name` - Name (A-Z or Z-A)
- `popularity` - Most Popular
- `price` - Price (Low to High or High to Low)
- `experience` - Experience (Trainers only)

**Example:**
```
GET /v2/api/kanglink/custom/landing/all-programs?sort_by=price&sort_order=asc
```

### Boolean Filters

#### Has Preview (Programs Only)
Filter programs that have preview days available.

**Parameter:** `has_preview`
**Type:** Boolean string
**Values:** `true`, `false`

**Example:**
```
GET /v2/api/kanglink/custom/landing/all-programs?has_preview=true
```

#### Has Programs (Trainers Only)
Filter trainers who have published programs.

**Parameter:** `has_programs`
**Type:** Boolean string
**Values:** `true`, `false`

**Example:**
```
GET /v2/api/kanglink/custom/landing/all-trainers?has_programs=true
```

## Response Format

All filtered endpoints return the same response format with additional filter information:

```json
{
  "error": false,
  "message": "Data retrieved successfully",
  "filters": {
    "search": "search term",
    "category": "category name",
    "gender": ["man", "woman"],
    "experience": ["1-2", "5-7"],
    "min_rating": 3.5,
    "max_rating": 4.5,
    "sort_by": "rating",
    "sort_order": "desc",
    "has_preview": true,
    "has_programs": true
  },
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "num_pages": 5,
    "has_next": true,
    "has_prev": false
  }
}
```

## Frontend Integration

### Filter Panel Component
The frontend includes a comprehensive filter panel that allows users to:
- Select multiple gender options
- Choose experience levels
- Set rating ranges using sliders
- Sort by various criteria
- Apply boolean filters

### Filter Indicators
Active filters are displayed as removable chips, allowing users to:
- See all applied filters at a glance
- Remove individual filters
- Clear all filters at once

### URL State Management
All filter states are managed through URL parameters, enabling:
- Shareable filtered results
- Browser back/forward navigation
- Bookmarkable filtered views

## Database Implementation

### Gender Filtering
Gender filtering is implemented by querying the `gender` field in the user's JSON data:
```sql
JSON_EXTRACT(u.data, '$.gender') = 'man'
```

### Experience Filtering
Experience filtering uses the `years_of_experience` field with string matching:
```sql
JSON_EXTRACT(u.data, '$.years_of_experience') LIKE '%3-5 years%'
```

### Price Sorting
Price sorting is implemented by aggregating the minimum price from program splits:
```sql
MIN(s.full_price) ASC
```

## Testing

Comprehensive tests are included to verify:
- Individual filter functionality
- Combined filter behavior
- Response format consistency
- Edge cases and error handling

Run tests with:
```bash
npm test landing_page_filters.test.js
```

## Migration Notes

The new filter functionality is backward compatible. Existing endpoints will continue to work without any changes to the API contract.

## Performance Considerations

- All filters are implemented at the database level for optimal performance
- Complex queries use proper indexing on JSON fields
- Rating aggregations are cached where possible
- Pagination is maintained for large result sets 