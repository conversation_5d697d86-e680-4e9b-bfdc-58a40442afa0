# Public Program Endpoints - Technical Documentation

This document provides comprehensive technical documentation for the public program endpoints.

## 🎯 Overview

These endpoints provide public access to program information and reviews without requiring authentication. They are designed for frontend applications to display program details and user reviews.

### Key Features

- ✅ No authentication required
- ✅ Only published programs accessible
- ✅ Optimized queries with joins
- ✅ Comprehensive error handling
- ✅ Pagination support for reviews
- ✅ Flexible sorting options

## 📋 Endpoints Summary

| Endpoint                                             | Method | Purpose             | Auth Required |
| ---------------------------------------------------- | ------ | ------------------- | ------------- |
| `/v2/api/kanglink/custom/public/program/:id`         | GET    | Get program details | ❌            |
| `/v2/api/kanglink/custom/public/program/:id/reviews` | GET    | Get program reviews | ❌            |

---

## 🔍 1. Get Program Details

### Endpoint

```
GET /v2/api/kanglink/custom/public/program/:program_id
```

### Description

Retrieves comprehensive program information including trainer details, pricing from splits, ratings, and program configuration.

### Parameters

- `program_id` (path, required): Integer - The ID of the program to retrieve

### Database Query

The endpoint executes a complex JOIN query to gather:

- Program details from `kanglink_program`
- Trainer information from `kanglink_user`
- Average ratings from `kanglink_post_feed` (reviews only)
- Split pricing from `kanglink_split`

### Response Schema

```json
{
  "error": false,
  "message": "Program details retrieved successfully",
  "data": {
    "id": 1,
    "user_id": 123,
    "program_name": "Advanced Strength Training",
    "type_of_program": "Strength",
    "program_description": "A comprehensive strength training program...",
    "target_levels": ["Intermediate", "Advanced"],
    "currency": "USD",
    "days_for_preview": 7,
    "image": "https://example.com/program-image.jpg",
    "track_progress": true,
    "allow_comments": true,
    "allow_private_messages": false,
    "rating": 4.5,
    "review_count": 25,
    "price": 99.99,
    "splits": [
      {
        "id": 1,
        "program_id": 1,
        "equipment_required": "Barbell, Dumbbells",
        "title": "Split Level 1",
        "full_price": 99.99,
        "subscription": 19.99,
        "created_at": "2024-01-01T00:00:00.000Z",
        "updated_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "trainer": {
      "id": 123,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "full_name": "John Doe",
      "photo": "https://example.com/trainer-photo.jpg"
    },
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### Business Logic

1. **Program Validation**: Only programs with `status = 'published'` are returned
2. **Price Calculation**: The `price` field contains the minimum value from all split prices (full_price and subscription), excluding zero values
3. **Trainer Data**: Trainer information is parsed from the `user.data` JSON field
4. **Rating Calculation**: Average rating calculated from reviews in `kanglink_post_feed` where `post_type = 'review'`

### Error Responses

- `400 Bad Request`: Invalid program ID (non-numeric)
- `404 Not Found`: Program doesn't exist or not published
- `500 Internal Server Error`: Database or server error

### 2. Get Program Reviews

**Endpoint:** `GET /v2/api/kanglink/custom/public/program/:program_id/reviews`

**Description:** Retrieves public reviews for a specific program with pagination and sorting options.

**Parameters:**

- `program_id` (path parameter): The ID of the program
- `page` (query parameter, optional): Page number (default: 1)
- `limit` (query parameter, optional): Number of reviews per page (default: 20, max: 50)
- `sort_by` (query parameter, optional): Sort field - `created_at` or `rating` (default: `created_at`)
- `sort_order` (query parameter, optional): Sort order - `asc` or `desc` (default: `desc`)

**Example Requests:**

```
GET /v2/api/kanglink/custom/public/program/1/reviews
GET /v2/api/kanglink/custom/public/program/1/reviews?page=2&limit=10
GET /v2/api/kanglink/custom/public/program/1/reviews?sort_by=rating&sort_order=desc
```

**Response Format:**

```json
{
  "error": false,
  "message": "Program reviews retrieved successfully",
  "data": [
    {
      "id": 1,
      "user_id": 456,
      "program_id": 1,
      "content": "Great program! Really helped me build strength.",
      "rating": 5,
      "attachments": [],
      "is_edited": false,
      "user": {
        "id": 456,
        "email": "<EMAIL>",
        "first_name": "Jane",
        "last_name": "Smith",
        "full_name": "Jane Smith",
        "photo": "https://example.com/user-photo.jpg"
      },
      "created_at": "2024-01-15T10:30:00.000Z",
      "updated_at": "2024-01-15T10:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 25,
    "num_pages": 2,
    "has_next": true,
    "has_prev": false
  }
}
```

**Error Responses:**

- `400`: Invalid program ID or sort parameters
- `404`: Program not found or not published
- `500`: Internal server error

## Features

### Program Details Endpoint

- Returns only published programs
- Includes trainer information from user.data field
- Calculates minimum price from all splits
- Includes program ratings and review count
- Shows all splits with pricing information

### Reviews Endpoint

- Returns only public reviews (visibility_scope = 'public')
- Only includes reviews with ratings
- Supports pagination with configurable page size
- Supports sorting by creation date or rating
- Includes reviewer information (anonymized)
- Shows review content, rating, and attachments

## Security Considerations

- No authentication required (public endpoints)
- Only published programs are accessible
- Only public reviews are returned
- User email addresses are included but this is acceptable for public reviews
- No sensitive program data is exposed

## Usage Examples

### Frontend Integration

```javascript
// Get program details
const programResponse = await fetch("/v2/api/kanglink/custom/public/program/1");
const programData = await programResponse.json();

// Get program reviews with pagination
const reviewsResponse = await fetch(
  "/v2/api/kanglink/custom/public/program/1/reviews?page=1&limit=10&sort_by=rating&sort_order=desc"
);
const reviewsData = await reviewsResponse.json();
```

### Display Program Card

The program details endpoint provides all necessary information to display a program card similar to the one shown in the UI mockup:

- Program name and description
- Trainer information
- Rating and review count
- Pricing information (minimum price from splits)
- Program image

### Display Reviews Section

The reviews endpoint provides paginated reviews that can be displayed in a reviews section:

- Individual review cards with user info
- Star ratings
- Review content
- Pagination controls

---

## 🔧 Implementation Details

### Database Tables Used

- `kanglink_program`: Main program data
- `kanglink_user`: Trainer and reviewer information
- `kanglink_post_feed`: Reviews and ratings
- `kanglink_split`: Program pricing splits

### Performance Optimizations

- Single query for program details with JOINs
- Separate optimized query for splits
- Pagination with LIMIT/OFFSET
- Indexed queries on program_id and status

### Security Features

- Input validation for all parameters
- SQL injection prevention with parameterized queries
- Only published content accessible
- No sensitive data exposure

### Error Handling

- Consistent error response format
- Detailed error messages for debugging
- Proper HTTP status codes
- Graceful handling of edge cases

## 🧪 Testing

### Test Coverage

- Valid program requests
- Invalid program IDs (non-numeric, non-existent)
- Unpublished programs (should return 404)
- Pagination edge cases
- Sorting validation
- Error scenarios

### Test Files

- `mtpbk/custom/ksl_be/tests/routes/public_program_api.test.js`

### Running Tests

```bash
# Run all tests in the project
node mtpbk/test_runner.js

# Run only the public program API tests
node mtpbk/test_runner.js --pattern public_program_api

# Run all tests in the ksl_be routes directory
node mtpbk/test_runner.js --path mtpbk/custom/ksl_be/tests/routes

# Run the specific test file directly
node mtpbk/custom/ksl_be/tests/routes/public_program_api.test.js
```

## 📚 Related Documentation

- [Frontend API Guide](./frontend_public_program_api.md) - Frontend integration guide
- [Program Creation Endpoint](./program_creation_endpoint.md) - Creating programs
- [Post Feed API](./post_feed_api.md) - Review system details
