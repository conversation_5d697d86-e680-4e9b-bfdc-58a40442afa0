# Notification Endpoints Documentation

This document describes the centralized notification system for the KangLink platform. The notification system is organized into three main categories:

1. **Common Notifications** - Available to all roles (member, trainer, super_admin)
2. **Trainer Notifications** - Specific to trainers using `kanglink_trainer_notifications` table
3. **Super Admin Notifications** - System alerts and administrative notifications

## Common Notification Endpoints

These endpoints use the `kanglink_notification` table and are available to all authenticated users.

### Get User Notifications

**Endpoint:** `GET /v2/api/kanglink/custom/notifications`

**Description:** Retrieve notifications for the authenticated user with pagination and filtering options.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Query Parameters:**
- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of notifications per page (default: 20)
- `unread_only` (optional): Filter to show only unread notifications (default: false)

**Response:**
```json
{
  "error": false,
  "message": "Notifications retrieved successfully",
  "data": {
    "notifications": [
      {
        "id": 123,
        "notification_type": "new_enrollment",
        "category": "enrollment",
        "title": "New Enrollment",
        "message": "<PERSON> enrolled in 'Strength Training - Beginner'",
        "data": {
          "enrollment_id": 456,
          "program_name": "Strength Training",
          "split_name": "Beginner",
          "athlete_name": "John Doe"
        },
        "is_read": false,
        "read_at": null,
        "created_at": "2024-01-15T10:30:00Z",
        "sender_id": 789,
        "sender_name": "John Doe",
        "sender_email": "<EMAIL>",
        "related_id": 456,
        "related_type": "enrollment"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 5
    }
  }
}
```

### Mark Notification as Read

**Endpoint:** `PUT /v2/api/kanglink/custom/notifications/:id/read`

**Description:** Mark a specific notification as read.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Path Parameters:**
- `id`: Notification ID to mark as read

**Response:**
```json
{
  "error": false,
  "message": "Notification marked as read"
}
```

### Mark All Notifications as Read

**Endpoint:** `PUT /v2/api/kanglink/custom/notifications/read-all`

**Description:** Mark all unread notifications for the user as read.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Response:**
```json
{
  "error": false,
  "message": "All notifications marked as read"
}
```

### Get Unread Notification Count

**Endpoint:** `GET /v2/api/kanglink/custom/notifications/unread-count`

**Description:** Get the count of unread notifications for the user.

**Headers:**
- `Authorization: Bearer <token>` (required)

**Response:**
```json
{
  "error": false,
  "message": "Unread count retrieved successfully",
  "data": {
    "unread_count": 5
  }
}
```

## Trainer Notification Endpoints

These endpoints use the `kanglink_trainer_notifications` table and are specific to trainers.

### Get Trainer Notifications

**Endpoint:** `GET /v2/api/kanglink/custom/trainer/notifications`

**Description:** Retrieve trainer-specific notifications with athlete and program information.

**Headers:**
- `Authorization: Bearer <token>` (required - trainer role)

**Query Parameters:**
- `limit` (optional): Number of notifications per page (default: 20)
- `offset` (optional): Offset for pagination (default: 0)
- `unread_only` (optional): Filter to show only unread notifications (default: false)

**Response:**
```json
{
  "error": false,
  "data": {
    "notifications": [
      {
        "id": 123,
        "trainer_id": 456,
        "athlete_id": 789,
        "enrollment_id": 101,
        "notification_type": "exercise_completed",
        "title": "Exercise Completed",
        "message": "John Doe completed Bench Press",
        "data": {
          "enrollment_id": 101,
          "program_name": "Strength Training",
          "exercise_name": "Bench Press"
        },
        "is_read": false,
        "read_at": null,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "athlete_first_name": "John",
        "athlete_last_name": "Doe",
        "athlete_photo": "https://example.com/photo.jpg",
        "program_name": "Strength Training",
        "split_title": "Beginner"
      }
    ],
    "unread_count": 3,
    "pagination": {
      "limit": 20,
      "offset": 0,
      "total": 5
    }
  }
}
```

### Mark Trainer Notification as Read

**Endpoint:** `PUT /v2/api/kanglink/custom/trainer/notifications/:notification_id/read`

**Description:** Mark a specific trainer notification as read.

**Headers:**
- `Authorization: Bearer <token>` (required - trainer role)

**Path Parameters:**
- `notification_id`: Trainer notification ID to mark as read

**Response:**
```json
{
  "error": false,
  "message": "Notification marked as read"
}
```

### Mark All Trainer Notifications as Read

**Endpoint:** `PUT /v2/api/kanglink/custom/trainer/notifications/read-all`

**Description:** Mark all unread trainer notifications as read.

**Headers:**
- `Authorization: Bearer <token>` (required - trainer role)

**Response:**
```json
{
  "error": false,
  "message": "All notifications marked as read"
}
```

### Get Trainer Unread Count

**Endpoint:** `GET /v2/api/kanglink/custom/trainer/notifications/unread-count`

**Description:** Get the count of unread trainer notifications.

**Headers:**
- `Authorization: Bearer <token>` (required - trainer role)

**Response:**
```json
{
  "error": false,
  "message": "Unread count retrieved successfully",
  "data": {
    "unread_count": 2
  }
}
```

## Super Admin Notification Endpoints

These endpoints are specific to super admins and handle system alerts.

### Create System Alert

**Endpoint:** `POST /v2/api/kanglink/custom/super_admin/notifications/system-alert`

**Description:** Create a system alert notification that will be sent to all super admins.

**Headers:**
- `Authorization: Bearer <token>` (required - super_admin role)

**Request Body:**
```json
{
  "title": "System Maintenance",
  "message": "Scheduled maintenance will occur tonight at 2 AM",
  "type": "warning",
  "severity": "medium",
  "details": {
    "maintenance_window": "2:00 AM - 4:00 AM",
    "affected_services": ["database", "api"]
  },
  "target_roles": ["super_admin"]
}
```

**Response:**
```json
{
  "error": false,
  "message": "System alert notification created successfully",
  "data": {
    "alert_id": "alert_1705312200000",
    "recipients_count": 3
  }
}
```

### Get System Alerts

**Endpoint:** `GET /v2/api/kanglink/custom/super_admin/notifications/system-alerts`

**Description:** Retrieve system alerts for the authenticated super admin.

**Headers:**
- `Authorization: Bearer <token>` (required - super_admin role)

**Query Parameters:**
- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of alerts per page (default: 20)
- `severity` (optional): Filter by alert severity (low, medium, high, critical)

**Response:**
```json
{
  "error": false,
  "message": "System alerts retrieved successfully",
  "data": {
    "alerts": [
      {
        "id": 123,
        "title": "System Maintenance",
        "message": "Scheduled maintenance will occur tonight at 2 AM",
        "alert_type": "warning",
        "alert_severity": "medium",
        "alert_details": {
          "maintenance_window": "2:00 AM - 4:00 AM",
          "affected_services": ["database", "api"]
        },
        "is_read": false,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 5
    }
  }
}
```

### Mark System Alert as Read

**Endpoint:** `PUT /v2/api/kanglink/custom/super_admin/notifications/system-alerts/:id/read`

**Description:** Mark a specific system alert as read.

**Headers:**
- `Authorization: Bearer <token>` (required - super_admin role)

**Path Parameters:**
- `id`: System alert ID to mark as read

**Response:**
```json
{
  "error": false,
  "message": "System alert marked as read"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "error": true,
  "message": "Title and message are required"
}
```

### 401 Unauthorized
```json
{
  "error": true,
  "message": "Unauthorized"
}
```

### 403 Forbidden
```json
{
  "error": true,
  "message": "Access denied"
}
```

### 404 Not Found
```json
{
  "error": true,
  "message": "Notification not found"
}
```

### 500 Internal Server Error
```json
{
  "error": true,
  "message": "Failed to fetch notifications"
}
```

## Notification Types

### Common Notification Types
- `new_enrollment`: When a user enrolls in a program
- `payment_received`: When a payment is processed
- `progress_update`: When an athlete makes progress
- `system_alert`: System-wide alerts (super admin only)

### Trainer Notification Types
- `exercise_completed`: When an athlete completes an exercise
- `day_completed`: When an athlete completes a day
- `week_completed`: When an athlete completes a week
- `program_completed`: When an athlete completes a program

### Categories
- `enrollment`: Enrollment-related notifications
- `payment`: Payment-related notifications
- `progress`: Progress-related notifications
- `system`: System-related notifications
- `communication`: Communication-related notifications

## Database Tables

### kanglink_notification
Stores general notifications for all users.

**Key Fields:**
- `id`: Primary key
- `user_id`: Recipient user ID
- `sender_id`: Sender user ID (nullable for system notifications)
- `notification_type`: Type of notification
- `category`: Notification category
- `title`: Notification title
- `message`: Notification message
- `data`: JSON data with additional information
- `is_read`: Boolean indicating if notification is read
- `read_at`: Timestamp when notification was read
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### kanglink_trainer_notifications
Stores trainer-specific notifications with additional context.

**Key Fields:**
- `id`: Primary key
- `trainer_id`: Trainer user ID
- `athlete_id`: Athlete user ID
- `enrollment_id`: Enrollment ID
- `notification_type`: Type of notification
- `title`: Notification title
- `message`: Notification message
- `data`: JSON data with additional information
- `is_read`: Boolean indicating if notification is read
- `read_at`: Timestamp when notification was read
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

## Usage Examples

### Frontend Integration

```javascript
// Get user notifications
const getNotifications = async (page = 1, limit = 20) => {
  const response = await fetch('/v2/api/kanglink/custom/notifications?page=' + page + '&limit=' + limit, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// Mark notification as read
const markAsRead = async (notificationId) => {
  const response = await fetch(`/v2/api/kanglink/custom/notifications/${notificationId}/read`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// Get unread count for badge
const getUnreadCount = async () => {
  const response = await fetch('/v2/api/kanglink/custom/notifications/unread-count', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};
```

### Real-time Updates

For real-time notification updates, consider implementing WebSocket connections or server-sent events to push notifications to connected clients.

## Security Considerations

1. **Authentication**: All endpoints require valid JWT tokens
2. **Authorization**: Role-based access control ensures users can only access appropriate notifications
3. **Data Validation**: Input validation prevents malicious data injection
4. **Rate Limiting**: Consider implementing rate limiting for notification endpoints
5. **Audit Logging**: Log notification creation and access for security monitoring

## Performance Considerations

1. **Pagination**: All notification lists support pagination to handle large datasets
2. **Indexing**: Ensure proper database indexing on frequently queried fields
3. **Caching**: Consider caching unread counts and recent notifications
4. **Batch Operations**: Use batch operations for marking multiple notifications as read 