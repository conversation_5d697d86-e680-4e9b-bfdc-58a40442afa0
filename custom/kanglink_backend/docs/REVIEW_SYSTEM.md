# Enhanced Review System

## Overview

The review system has been enhanced to ensure that users can only write one review per program, with the ability to update or delete their existing review. This prevents spam and ensures review quality.

## Key Features

### 1. Single Review Per User Per Program
- Users can only write one review per program
- If a user tries to create a second review, they receive an error message
- The system automatically loads existing review data when opening the review modal

### 2. Update and Delete Functionality
- Users can update their existing review content and rating
- Users can delete their review entirely
- All changes are tracked with an `is_edited` flag

### 3. Access Control
- Only enrolled users can write reviews
- Users must be authenticated to access review functionality
- Proper error messages guide users on requirements

## Frontend Implementation

### Components

#### WriteReviewModal
- **Location**: `frontend/src/components/WriteReviewModal/WriteReviewModal.tsx`
- **Features**:
  - Detects existing reviews and loads data
  - Provides update/delete functionality
  - Uses MkdInputV2 for review content
  - Star rating (1-5 stars)
  - Character count validation
  - Loading states for all operations

#### ProgramHeader
- **Location**: `frontend/src/components/ProgramHeader/ProgramHeader.tsx`
- **Features**:
  - Shows "Write a Review" or "Edit Review" based on existing review
  - Access control based on enrollment status
  - Proper button states and tooltips

### Hooks

#### useUserReview
- **Location**: `frontend/src/hooks/useUserReview/useUserReview.tsx`
- **Features**:
  - Fetches user's existing review for a program
  - Provides update and delete functions
  - Handles query invalidation
  - Loading states for all operations

## Backend Implementation

### Access Control
- **Location**: `routes/post_feed.js`
- **Features**:
  - Enrollment verification before allowing reviews
  - Prevents multiple reviews per user per program
  - Proper error messages for different scenarios

### Database Schema
- **Table**: `post_feed`
- **Key Fields**:
  - `user_id`: User who wrote the review
  - `program_id`: Program being reviewed
  - `post_type`: Set to "review"
  - `content`: Review text
  - `rating`: Star rating (1-5)
  - `is_edited`: Boolean flag for updated reviews

## User Flow

### New User Writing First Review
1. User clicks "Write a Review" button
2. System checks enrollment status
3. If enrolled, modal opens with empty form
4. User fills in rating and content
5. User submits review
6. Review is saved to database

### User with Existing Review
1. User clicks "Edit Review" button
2. Modal opens with existing review data loaded
3. User can modify rating and content
4. User can choose to:
   - Update the review (changes content/rating)
   - Delete the review entirely
5. Changes are saved to database

### Access Control Scenarios
1. **Not Logged In**: Button shows "Write a Review" (disabled) with tooltip
2. **Logged In but Not Enrolled**: Button shows "Write a Review" (disabled) with tooltip
3. **Logged In and Enrolled**: Button shows "Write a Review" or "Edit Review" (enabled)

## API Endpoints

### Create Review
- **Method**: POST
- **Endpoint**: `/v2/api/kanglink/custom/trainer/feed`
- **Required Fields**:
  - `program_id`: Program ID
  - `post_type`: "review"
  - `content`: Review text (10-1000 characters)
  - `rating`: Star rating (1-5)

### Update Review
- **Method**: PUT
- **Endpoint**: `/v2/api/kanglink/custom/trainer/feed/:id`
- **Required Fields**:
  - `content`: Updated review text
  - `rating`: Updated star rating
  - `is_edited`: true

### Delete Review
- **Method**: DELETE
- **Endpoint**: `/v2/api/kanglink/custom/trainer/feed/:id`

## Error Handling

### Common Error Messages
- "You must be enrolled in this program to write a review"
- "You have already written a review for this program. You can update your existing review instead."
- "Rating must be between 1 and 5 stars"
- "Review must be at least 10 characters long"
- "Review cannot exceed 1000 characters"

### Frontend Error States
- Loading states for all operations
- Disabled buttons during operations
- Clear error messages in modal
- Toast notifications for success/error

## Testing

### Test File
- **Location**: `tests/review_access_control.test.js`
- **Coverage**:
  - Authentication requirements
  - Enrollment verification
  - Single review per user enforcement
  - Update and delete functionality

## Benefits

1. **Prevents Spam**: Users can only write one review per program
2. **Maintains Quality**: Users can update their reviews if their opinion changes
3. **User Control**: Users can delete their reviews if needed
4. **Clear UX**: Different button text and states based on user situation
5. **Data Integrity**: Proper validation and access control at both frontend and backend

## Future Enhancements

1. **Review History**: Track all changes to reviews
2. **Review Moderation**: Allow trainers to moderate reviews
3. **Review Analytics**: Track review patterns and trends
4. **Review Templates**: Pre-defined review questions
5. **Review Photos**: Allow users to attach photos to reviews 