# Kanglink Enrollment API Documentation

## Overview

The Kanglink enrollment system allows athletes to enroll in trainer programs with two payment models:

- **Subscription**: Monthly recurring payment with live updates to program content
- **One-time**: Single payment for lifetime access to a frozen snapshot of the program

## Authentication

All endpoints require authentication via Bear<PERSON> token in the Authorization header:

```
Authorization: Bearer <token>
```

## Base URL

```
https://your-domain.com/v2/api/kanglink/custom
```

---

## Endpoints

### 1. Create Enrollment

**Endpoint:** `POST /athlete/enroll`

**Description:** Enroll an athlete in a program split with Stripe payment processing.

**Authentication:** Required (member, trainer, or super_admin)

**Request Body:**

```json
{
  "split_id": 123,
  "payment_type": "subscription", // "subscription" or "one_time"
  "payment_method_id": "pm_1234567890" // Stripe payment method ID
}
```

**Response (Success - 200):**

```json
{
  "error": false,
  "message": "Enrollment created successfully",
  "data": {
    "enrollment_id": 456,
    "payment_type": "subscription",
    "amount": 29.99,
    "currency": "USD",
    "status": "active",
    "payment_status": "paid",
    "stripe_subscription_id": "sub_1234567890",
    "stripe_payment_intent_id": null
  }
}
```

**Response (Error - 400):**

```json
{
  "error": true,
  "message": "split_id, payment_type, and payment_method_id are required"
}
```

**Error Cases:**

- `400`: Missing required fields, invalid payment type, already enrolled, pricing not available
- `404`: Split not found, athlete not found
- `500`: Payment processing failed, server error

---

### 2. Get Athlete Enrollments

**Endpoint:** `GET /athlete/enrollments`

**Description:** Get all enrollments for the authenticated athlete.

**Authentication:** Required (member, trainer, or super_admin)

**Response (Success - 200):**

```json
{
  "error": false,
  "data": [
    {
      "id": 456,
      "trainer_id": 123,
      "athlete_id": 789,
      "program_id": 101,
      "split_id": 202,
      "payment_type": "subscription",
      "amount": 29.99,
      "currency": "USD",
      "enrollment_date": "2025-01-02 10:30:00",
      "expiry_date": null,
      "status": "active",
      "payment_status": "paid",
      "stripe_subscription_id": "sub_1234567890",
      "stripe_payment_intent_id": null,
      "access_type": "live",
      "program_name": "Strength Training Program",
      "type_of_program": "strength",
      "split_title": "Beginner Split",
      "trainer_email": "<EMAIL>",
      "created_at": "2025-01-02 10:30:00",
      "updated_at": "2025-01-02 10:30:00"
    }
  ]
}
```

---

### 3. Get Trainer Enrollments

**Endpoint:** `GET /trainer/enrollments`

**Description:** Get all enrollments for programs owned by the authenticated trainer.

**Authentication:** Required (trainer or super_admin)

**Response (Success - 200):**

```json
{
  "error": false,
  "data": [
    {
      "id": 456,
      "trainer_id": 123,
      "athlete_id": 789,
      "program_id": 101,
      "split_id": 202,
      "payment_type": "subscription",
      "amount": 29.99,
      "currency": "USD",
      "enrollment_date": "2025-01-02 10:30:00",
      "status": "active",
      "payment_status": "paid",
      "program_name": "Strength Training Program",
      "type_of_program": "strength",
      "split_title": "Beginner Split",
      "athlete_email": "<EMAIL>",
      "created_at": "2025-01-02 10:30:00"
    }
  ]
}
```

---

### 4. Cancel Enrollment

**Endpoint:** `POST /enrollment/{enrollment_id}/cancel`

**Description:** Cancel an enrollment and associated Stripe subscription.

**Authentication:** Required (member can cancel own, trainer can cancel their students)

**Path Parameters:**

- `enrollment_id`: The ID of the enrollment to cancel

**Response (Success - 200):**

```json
{
  "error": false,
  "message": "Enrollment cancelled successfully"
}
```

**Response (Warning - 200):**

```json
{
  "error": false,
  "message": "Enrollment cancelled (with Stripe error)",
  "warning": "Stripe cancellation failed but local status updated"
}
```

**Error Cases:**

- `403`: Permission denied (can only cancel own enrollments or your students)
- `404`: Enrollment not found
- `400`: Enrollment already cancelled

---

### 5. Get Split Details

**Endpoint:** `GET /splits/{split_id}`

**Description:** Get detailed information about a specific split.

**Authentication:** Required (member, trainer, or super_admin)

**Path Parameters:**

- `split_id`: The ID of the split

**Response (Success - 200):**

```json
{
  "error": false,
  "data": {
    "id": 202,
    "program_id": 101,
    "title": "Beginner Split",
    "description": "Perfect for beginners starting their fitness journey",
    "full_price": 99.99,
    "subscription": 29.99,
    "duration_weeks": 12,
    "difficulty_level": "beginner",
    "program_name": "Strength Training Program",
    "type_of_program": "strength",
    "program_description": "Complete strength training program",
    "currency": "USD",
    "trainer_id": 123,
    "trainer_email": "<EMAIL>",
    "created_at": "2025-01-01 12:00:00"
  }
}
```

---

### 6. Get Available Splits

**Endpoint:** `GET /splits`

**Description:** Get all available splits for enrollment (published programs only).

**Authentication:** Required (member, trainer, or super_admin)

**Query Parameters:**

- `trainer_id` (optional): Filter by specific trainer
- `program_id` (optional): Filter by specific program

**Example:** `GET /splits?trainer_id=123&program_id=101`

**Response (Success - 200):**

```json
{
  "error": false,
  "data": [
    {
      "id": 202,
      "program_id": 101,
      "title": "Beginner Split",
      "description": "Perfect for beginners",
      "full_price": 99.99,
      "subscription": 29.99,
      "duration_weeks": 12,
      "difficulty_level": "beginner",
      "program_name": "Strength Training Program",
      "type_of_program": "strength",
      "currency": "USD",
      "trainer_id": 123,
      "trainer_email": "<EMAIL>"
    }
  ]
}
```

---

### 7. Check Enrollment Eligibility

**Endpoint:** `GET /splits/{split_id}/eligibility`

**Description:** Check if the authenticated user can enroll in a specific split.

**Authentication:** Required (member, trainer, or super_admin)

**Response (Success - 200):**

```json
{
  "error": false,
  "data": {
    "split_id": 202,
    "program_name": "Strength Training Program",
    "eligibility": {
      "can_enroll": true,
      "reasons": [],
      "payment_options": {
        "one_time": {
          "available": true,
          "reason": null
        },
        "subscription": {
          "available": true,
          "reason": null
        }
      }
    },
    "existing_enrollment": null
  }
}
```

**Response (Not Eligible):**

```json
{
  "error": false,
  "data": {
    "split_id": 202,
    "program_name": "Strength Training Program",
    "eligibility": {
      "can_enroll": false,
      "reasons": ["Already enrolled in this split"],
      "payment_options": {
        "one_time": {
          "available": true,
          "reason": null
        },
        "subscription": {
          "available": true,
          "reason": null
        }
      }
    },
    "existing_enrollment": {
      "id": 456,
      "payment_type": "subscription",
      "status": "active",
      "enrollment_date": "2025-01-02 10:30:00"
    }
  }
}
```

---

### 8. Get Split Pricing

**Endpoint:** `GET /splits/{split_id}/pricing`

**Description:** Get detailed pricing information and recommendations for a split.

**Authentication:** Required (member, trainer, or super_admin)

**Response (Success - 200):**

```json
{
  "error": false,
  "data": {
    "split_id": 202,
    "program_name": "Strength Training Program",
    "split_title": "Beginner Split",
    "currency": "USD",
    "pricing": {
      "one_time": {
        "amount": 99.99,
        "available": true,
        "stripe_configured": true,
        "description": "Lifetime access, no updates when trainer modifies split"
      },
      "subscription": {
        "amount": 29.99,
        "available": true,
        "stripe_configured": true,
        "description": "Monthly billing, access with automatic updates"
      },
      "minimum": 29.99
    },
    "recommendations": {
      "best_value": "one_time",
      "savings_months": 4
    }
  }
}
```

---

### 9. Get Enrollment Content

**Endpoint:** `GET /athlete/enrollment/{enrollment_id}/content`

**Description:** Get the program content based on enrollment access type (live or snapshot).

**Authentication:** Required (member, trainer, or super_admin)

**Path Parameters:**

- `enrollment_id`: The ID of the enrollment

**Response (Success - 200):**

```json
{
  "error": false,
  "data": {
    "enrollment_id": 456,
    "access_type": "live",
    "content": {
      "split": {
        "id": 202,
        "title": "Beginner Split",
        "description": "Perfect for beginners",
        "program_name": "Strength Training Program"
      },
      "weeks": [
        {
          "id": 301,
          "week_number": 1,
          "title": "Week 1 - Foundation",
          "days": [
            {
              "id": 401,
              "day_number": 1,
              "title": "Day 1 - Upper Body",
              "sessions": [
                {
                  "id": 501,
                  "title": "Main Workout",
                  "session_order": 1,
                  "exercises": [
                    {
                      "id": 601,
                      "exercise_id": 701,
                      "exercise_name": "Push-ups",
                      "sets": 3,
                      "reps_or_time": "10-15",
                      "rest_time": "60 seconds",
                      "exercise_order": 1,
                      "videos": [
                        {
                          "id": 801,
                          "video_url": "https://example.com/video.mp4",
                          "video_type": "demonstration"
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      "access_info": {
        "type": "live",
        "message": "This is your subscription access. You'll automatically receive all updates to this split.",
        "enrollment_date": "2025-01-02 10:30:00",
        "payment_type": "subscription"
      },
      "snapshot_date": "2025-01-02T15:30:00.000Z",
      "snapshot_version": "1.0"
    }
  }
}
```

**Response (Snapshot Access):**

```json
{
  "error": false,
  "data": {
    "enrollment_id": 456,
    "access_type": "snapshot",
    "content": {
      "access_info": {
        "type": "snapshot",
        "message": "This is your lifetime access version. Content is frozen at purchase time and won't receive updates.",
        "purchase_date": "2025-01-02 10:30:00",
        "payment_type": "one_time"
      }
    }
  }
}
```

**Error Cases:**

- `403`: Access denied to this enrollment
- `404`: Enrollment not found
- `400`: Enrollment is not active

---

## Data Models

### Enrollment Object

```typescript
interface Enrollment {
  id: number;
  trainer_id: number;
  athlete_id: number;
  program_id: number;
  split_id: number;
  payment_type: "subscription" | "one_time";
  amount: number;
  currency: string;
  enrollment_date: string; // ISO datetime
  expiry_date: string | null; // ISO datetime
  status: "active" | "cancelled" | "pending" | "expired";
  payment_status: "paid" | "pending" | "failed" | "refunded";
  stripe_subscription_id: string | null;
  stripe_payment_intent_id: string | null;
  stripe_customer_id: string;
  access_type: "live" | "snapshot";
  split_snapshot: string | null; // JSON string
  created_at: string; // ISO datetime
  updated_at: string; // ISO datetime
}
```

### Split Object

```typescript
interface Split {
  id: number;
  program_id: number;
  title: string;
  description: string;
  full_price: number;
  subscription: number;
  duration_weeks: number;
  difficulty_level: "beginner" | "intermediate" | "advanced";
  currency: string;
  created_at: string;
  updated_at: string;
}
```

### Eligibility Object

```typescript
interface Eligibility {
  can_enroll: boolean;
  reasons: string[];
  payment_options: {
    one_time: {
      available: boolean;
      reason: string | null;
    };
    subscription: {
      available: boolean;
      reason: string | null;
    };
  };
}
```

### Pricing Object

```typescript
interface Pricing {
  split_id: number;
  program_name: string;
  split_title: string;
  currency: string;
  pricing: {
    one_time: {
      amount: number;
      available: boolean;
      stripe_configured: boolean;
      description: string;
    };
    subscription: {
      amount: number;
      available: boolean;
      stripe_configured: boolean;
      description: string;
    };
    minimum: number;
  };
  recommendations: {
    best_value: "one_time" | "subscription" | null;
    savings_months: number | null;
  };
}
```

---

## Frontend Implementation Examples

### 1. Check Enrollment Eligibility

```typescript
async function checkEnrollmentEligibility(
  splitId: number
): Promise<EligibilityResponse> {
  const response = await fetch(
    `/v2/api/kanglink/custom/splits/${splitId}/eligibility`,
    {
      headers: {
        Authorization: `Bearer ${userToken}`,
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to check eligibility");
  }

  return response.json();
}

// Usage
try {
  const eligibility = await checkEnrollmentEligibility(202);
  if (eligibility.data.eligibility.can_enroll) {
    // Show enrollment options
    console.log(
      "Available payment options:",
      eligibility.data.eligibility.payment_options
    );
  } else {
    // Show reasons why enrollment is not possible
    console.log("Cannot enroll:", eligibility.data.eligibility.reasons);
  }
} catch (error) {
  console.error("Error checking eligibility:", error);
}
```

### 2. Get Pricing Information

```typescript
async function getSplitPricing(splitId: number): Promise<PricingResponse> {
  const response = await fetch(
    `/v2/api/kanglink/custom/splits/${splitId}/pricing`,
    {
      headers: {
        Authorization: `Bearer ${userToken}`,
        "Content-Type": "application/json",
      },
    }
  );

  return response.json();
}

// Usage
const pricing = await getSplitPricing(202);
const { one_time, subscription, minimum } = pricing.data.pricing;

// Display pricing options
if (one_time.available) {
  console.log(`One-time: $${one_time.amount} - ${one_time.description}`);
}
if (subscription.available) {
  console.log(`Monthly: $${subscription.amount} - ${subscription.description}`);
}

// Show recommendation
if (pricing.data.recommendations.best_value) {
  console.log(`Best value: ${pricing.data.recommendations.best_value}`);
  console.log(
    `Break-even: ${pricing.data.recommendations.savings_months} months`
  );
}
```

### 3. Create Enrollment with Stripe

```typescript
import { loadStripe } from "@stripe/stripe-js";

async function createEnrollment(
  splitId: number,
  paymentType: "subscription" | "one_time",
  paymentMethodId: string
): Promise<EnrollmentResponse> {
  const response = await fetch("/v2/api/kanglink/custom/athlete/enroll", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${userToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      split_id: splitId,
      payment_type: paymentType,
      payment_method_id: paymentMethodId,
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message);
  }

  return response.json();
}

// Complete enrollment flow with Stripe
async function handleEnrollment(
  splitId: number,
  paymentType: "subscription" | "one_time"
) {
  const stripe = await loadStripe("pk_test_your_stripe_key");

  try {
    // Create payment method
    const { error: pmError, paymentMethod } = await stripe.createPaymentMethod({
      type: "card",
      card: cardElement, // Your Stripe card element
    });

    if (pmError) {
      throw new Error(pmError.message);
    }

    // Create enrollment
    const enrollment = await createEnrollment(
      splitId,
      paymentType,
      paymentMethod.id
    );

    console.log("Enrollment created:", enrollment.data);

    // Redirect to success page or show success message
    window.location.href = `/enrollment-success/${enrollment.data.enrollment_id}`;
  } catch (error) {
    console.error("Enrollment failed:", error);
    // Show error message to user
  }
}
```

### 4. Get User Enrollments

```typescript
async function getUserEnrollments(): Promise<EnrollmentListResponse> {
  const response = await fetch("/v2/api/kanglink/custom/athlete/enrollments", {
    headers: {
      Authorization: `Bearer ${userToken}`,
      "Content-Type": "application/json",
    },
  });

  return response.json();
}

// Usage
const enrollments = await getUserEnrollments();
enrollments.data.forEach((enrollment) => {
  console.log(`${enrollment.program_name} - ${enrollment.split_title}`);
  console.log(
    `Status: ${enrollment.status}, Payment: ${enrollment.payment_type}`
  );
  console.log(`Amount: ${enrollment.currency} ${enrollment.amount}`);
});
```

### 5. Cancel Enrollment

```typescript
async function cancelEnrollment(enrollmentId: number): Promise<CancelResponse> {
  const response = await fetch(
    `/v2/api/kanglink/custom/enrollment/${enrollmentId}/cancel`,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${userToken}`,
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message);
  }

  return response.json();
}

// Usage with confirmation
async function handleCancelEnrollment(enrollmentId: number) {
  const confirmed = confirm("Are you sure you want to cancel this enrollment?");
  if (!confirmed) return;

  try {
    const result = await cancelEnrollment(enrollmentId);
    console.log("Enrollment cancelled:", result.message);

    // Refresh enrollments list
    await getUserEnrollments();
  } catch (error) {
    console.error("Failed to cancel enrollment:", error);
  }
}
```

### 6. Get Enrollment Content

```typescript
async function getEnrollmentContent(
  enrollmentId: number
): Promise<ContentResponse> {
  const response = await fetch(
    `/v2/api/kanglink/custom/athlete/enrollment/${enrollmentId}/content`,
    {
      headers: {
        Authorization: `Bearer ${userToken}`,
        "Content-Type": "application/json",
      },
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message);
  }

  return response.json();
}

// Usage
const content = await getEnrollmentContent(456);
const { access_type, content: programContent } = content.data;

console.log(`Access type: ${access_type}`);
console.log(`Program: ${programContent.split.program_name}`);
console.log(`Weeks: ${programContent.weeks.length}`);

// Display access info
console.log(programContent.access_info.message);
```

---

## Error Handling

### Common Error Responses

```typescript
interface ErrorResponse {
  error: true;
  message: string;
  details?: string;
}

// Handle API errors consistently
async function handleApiCall<T>(apiCall: () => Promise<T>): Promise<T> {
  try {
    return await apiCall();
  } catch (error) {
    if (error instanceof Response) {
      const errorData: ErrorResponse = await error.json();
      throw new Error(errorData.message);
    }
    throw error;
  }
}
```

### Stripe Error Handling

```typescript
// Handle Stripe-specific errors
function handleStripeError(error: any): string {
  switch (error.code) {
    case "card_declined":
      return "Your card was declined. Please try a different payment method.";
    case "insufficient_funds":
      return "Insufficient funds. Please check your account balance.";
    case "expired_card":
      return "Your card has expired. Please use a different card.";
    case "incorrect_cvc":
      return "Your card's security code is incorrect.";
    case "processing_error":
      return "An error occurred while processing your card. Please try again.";
    default:
      return error.message || "An unexpected error occurred.";
  }
}
```

---

## Best Practices

### 1. Always Check Eligibility First

```typescript
// Before showing enrollment UI, check if user can enroll
const eligibility = await checkEnrollmentEligibility(splitId);
if (!eligibility.data.eligibility.can_enroll) {
  // Show reasons and alternative actions
  showIneligibilityMessage(eligibility.data.eligibility.reasons);
  return;
}
```

### 2. Show Pricing Comparison

```typescript
// Help users make informed decisions
const pricing = await getSplitPricing(splitId);
const { one_time, subscription, recommendations } = pricing.data.pricing;

if (one_time.available && subscription.available) {
  // Show comparison table
  showPricingComparison({
    oneTime: one_time,
    subscription: subscription,
    recommendation: recommendations.best_value,
    breakEvenMonths: recommendations.savings_months,
  });
}
```

### 3. Handle Payment States

```typescript
// Monitor enrollment status after creation
async function monitorEnrollmentStatus(enrollmentId: number) {
  const enrollment = await getEnrollmentDetails(enrollmentId);

  switch (enrollment.payment_status) {
    case "paid":
      showSuccessMessage(
        "Payment successful! You now have access to the program."
      );
      redirectToProgram(enrollmentId);
      break;
    case "pending":
      showPendingMessage("Payment is being processed...");
      // Poll for status updates
      setTimeout(() => monitorEnrollmentStatus(enrollmentId), 5000);
      break;
    case "failed":
      showErrorMessage("Payment failed. Please try again or contact support.");
      break;
  }
}
```

### 4. Implement Proper Loading States

```typescript
// Show loading states during API calls
async function enrollWithLoading(
  splitId: number,
  paymentType: string,
  paymentMethodId: string
) {
  setLoading(true);
  setError(null);

  try {
    const enrollment = await createEnrollment(
      splitId,
      paymentType,
      paymentMethodId
    );
    setSuccess(true);
    return enrollment;
  } catch (error) {
    setError(error.message);
    throw error;
  } finally {
    setLoading(false);
  }
}
```

---

## Testing

### Test Stripe Integration

Use Stripe's test card numbers for testing:

```typescript
// Test cards for different scenarios
const testCards = {
  success: "****************",
  declined: "****************",
  insufficientFunds: "****************",
  expiredCard: "****************",
  incorrectCvc: "****************",
};
```

### Test Enrollment Flow

```typescript
// Test complete enrollment flow
describe("Enrollment Flow", () => {
  test("should complete subscription enrollment", async () => {
    // 1. Check eligibility
    const eligibility = await checkEnrollmentEligibility(testSplitId);
    expect(eligibility.data.eligibility.can_enroll).toBe(true);

    // 2. Get pricing
    const pricing = await getSplitPricing(testSplitId);
    expect(pricing.data.pricing.subscription.available).toBe(true);

    // 3. Create enrollment
    const enrollment = await createEnrollment(
      testSplitId,
      "subscription",
      testPaymentMethodId
    );
    expect(enrollment.data.status).toBe("active");

    // 4. Verify content access
    const content = await getEnrollmentContent(enrollment.data.enrollment_id);
    expect(content.data.access_type).toBe("live");
  });
});
```

This comprehensive API documentation provides everything needed for frontend implementation of the enrollment system, including detailed examples, error handling, and best practices.
