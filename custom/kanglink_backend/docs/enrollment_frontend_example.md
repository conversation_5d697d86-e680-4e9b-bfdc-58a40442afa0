# Frontend Integration Example for Enrollment System

## TypeScript Interfaces

```typescript
// Enrollment interface matching backend
export interface Enrollment {
  id?: number | string;
  trainer_id?: number | string;
  athlete_id?: number | string;
  program_id?: number | string;
  split_id?: number | string;
  payment_type?: "subscription" | "one_time";
  amount?: number;
  currency?: string;
  enrollment_date?: string;
  expiry_date?: string | null;
  status?: "active" | "expired" | "cancelled" | "pending";
  payment_status?: "paid" | "pending" | "failed" | "refunded";
  stripe_subscription_id?: string | null;
  stripe_payment_intent_id?: string | null;
  stripe_customer_id?: string | null;
  created_at?: string;
  updated_at?: string;

  // Related entities from API responses
  program_name?: string;
  type_of_program?: string;
  split_title?: string;
  trainer_email?: string;
  athlete_email?: string;
}

// Enrollment request payload
export interface EnrollmentRequest {
  split_id: number;
  payment_type: "subscription" | "one_time";
  payment_method_id: string; // Stripe payment method ID
}

// API response format
export interface EnrollmentResponse {
  error: boolean;
  message?: string;
  data?: {
    enrollment_id: number;
    payment_type: string;
    amount: number;
    currency: string;
    status: string;
    payment_status: string;
    stripe_subscription_id?: string;
    stripe_payment_intent_id?: string;
  };
}
```

## React Component Example

```tsx
import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';

const stripePromise = loadStripe('pk_test_your_publishable_key');

interface Split {
  id: number;
  title: string;
  full_price: number;
  subscription: number;
  program_name: string;
  currency: string;
}

interface EnrollmentFormProps {
  split: Split;
  onSuccess: (enrollment: any) => void;
  onError: (error: string) => void;
}

const EnrollmentForm: React.FC<EnrollmentFormProps> = ({
  split,
  onSuccess,
  onError
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [paymentType, setPaymentType] = useState<'one_time' | 'subscription'>('one_time');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setLoading(true);

    try {
      const cardElement = elements.getElement(CardElement);
      if (!cardElement) {
        throw new Error('Card element not found');
      }

      // Create payment method
      const { error: paymentMethodError, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
      });

      if (paymentMethodError) {
        throw new Error(paymentMethodError.message);
      }

      // Create enrollment
      const response = await fetch('/v2/api/kanglink/custom/athlete/enroll', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          split_id: split.id,
          payment_type: paymentType,
          payment_method_id: paymentMethod.id,
        }),
      });

      const result = await response.json();

      if (result.error) {
        throw new Error(result.message);
      }

      onSuccess(result.data);
    } catch (error) {
      onError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const getPrice = () => {
    return paymentType === 'one_time' ? split.full_price : split.subscription;
  };

  const getPriceDisplay = () => {
    const price = getPrice();
    const currency = split.currency || 'USD';
    
    if (paymentType === 'one_time') {
      return `$${price} (One-time payment)`;
    } else {
      return `$${price}/month (Monthly subscription)`;
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold">{split.program_name}</h3>
        <p className="text-gray-600">{split.title}</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Payment Type
        </label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              value="one_time"
              checked={paymentType === 'one_time'}
              onChange={(e) => setPaymentType(e.target.value as 'one_time')}
              className="mr-2"
            />
            <span>One-time purchase - ${split.full_price} (Lifetime access, no updates)</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              value="subscription"
              checked={paymentType === 'subscription'}
              onChange={(e) => setPaymentType(e.target.value as 'subscription')}
              className="mr-2"
            />
            <span>Monthly subscription - ${split.subscription}/month (Access with updates)</span>
          </label>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Payment Information
        </label>
        <div className="border rounded-md p-3">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
              },
            }}
          />
        </div>
      </div>

      <div className="bg-gray-50 p-3 rounded-md">
        <p className="text-sm text-gray-600">
          Total: <span className="font-semibold">{getPriceDisplay()}</span>
        </p>
      </div>

      <button
        type="submit"
        disabled={!stripe || loading}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
      >
        {loading ? 'Processing...' : `Enroll for ${getPriceDisplay()}`}
      </button>
    </form>
  );
};

// Main enrollment component
const EnrollmentPage: React.FC<{ splitId: number }> = ({ splitId }) => {
  const [split, setSplit] = useState<Split | null>(null);
  const [enrollments, setEnrollments] = useState<Enrollment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchSplitDetails();
    fetchEnrollments();
  }, [splitId]);

  const fetchSplitDetails = async () => {
    try {
      // Fetch split details (you'll need to implement this endpoint)
      const response = await fetch(`/v2/api/kanglink/custom/splits/${splitId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.message);
      }
      
      setSplit(result.data);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load split details');
    } finally {
      setLoading(false);
    }
  };

  const fetchEnrollments = async () => {
    try {
      const response = await fetch('/v2/api/kanglink/custom/athlete/enrollments', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });
      const result = await response.json();
      
      if (!result.error) {
        setEnrollments(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch enrollments:', error);
    }
  };

  const handleEnrollmentSuccess = (enrollmentData: any) => {
    setSuccess('Enrollment successful! You now have access to this program.');
    setError(null);
    fetchEnrollments(); // Refresh enrollments
  };

  const handleEnrollmentError = (errorMessage: string) => {
    setError(errorMessage);
    setSuccess(null);
  };

  const isAlreadyEnrolled = () => {
    return enrollments.some(
      enrollment => 
        enrollment.split_id === splitId && 
        enrollment.status === 'active'
    );
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!split) {
    return <div>Split not found</div>;
  }

  return (
    <div className="max-w-md mx-auto p-6">
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      {isAlreadyEnrolled() ? (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
          You are already enrolled in this program.
        </div>
      ) : (
        <Elements stripe={stripePromise}>
          <EnrollmentForm
            split={split}
            onSuccess={handleEnrollmentSuccess}
            onError={handleEnrollmentError}
          />
        </Elements>
      )}
    </div>
  );
};

export default EnrollmentPage;
```

## Usage Example

```tsx
// In your app
import EnrollmentPage from './components/EnrollmentPage';

function App() {
  return (
    <div>
      <EnrollmentPage splitId={123} />
    </div>
  );
}
```

## API Service Example

```typescript
// services/enrollmentService.ts
export class EnrollmentService {
  private baseUrl = '/v2/api/kanglink/custom';
  
  async createEnrollment(request: EnrollmentRequest): Promise<EnrollmentResponse> {
    const response = await fetch(`${this.baseUrl}/athlete/enroll`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify(request),
    });
    
    return response.json();
  }
  
  async getAthleteEnrollments(): Promise<{ error: boolean; data: Enrollment[] }> {
    const response = await fetch(`${this.baseUrl}/athlete/enrollments`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });
    
    return response.json();
  }
  
  async getTrainerEnrollments(): Promise<{ error: boolean; data: Enrollment[] }> {
    const response = await fetch(`${this.baseUrl}/trainer/enrollments`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });
    
    return response.json();
  }
  
  async cancelEnrollment(enrollmentId: number): Promise<{ error: boolean; message: string }> {
    const response = await fetch(`${this.baseUrl}/enrollment/${enrollmentId}/cancel`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });
    
    return response.json();
  }
}
```
