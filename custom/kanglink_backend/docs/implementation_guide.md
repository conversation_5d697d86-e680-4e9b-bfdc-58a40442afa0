# Payout & Discount System Implementation Guide

## Quick Start

This guide provides step-by-step instructions for implementing and using the Kanglink payout and discount system.

## Prerequisites

1. **Database Setup**: Run the SQL scripts to create required tables
2. **Payout Settings**: Configure initial payout settings via super admin
3. **Program Setup**: Ensure programs are approved and have pricing configured

## Database Setup

### 1. Create Tables

Run the SQL script to create all required tables:

```bash
mysql -u username -p database_name < create_payout_settings_table.sql
```

### 2. Verify Tables

Confirm all tables are created:
- `kanglink_payout_settings`
- `kanglink_commission`
- `kanglink_program_discount` (existing)
- `kanglink_discount` (existing)
- `kanglink_coupon` (existing)
- `kanglink_coupon_usage` (existing)

## Initial Configuration

### 1. Configure Payout Settings (Super Admin)

```javascript
// Set initial payout configuration
const response = await fetch('/v2/api/kanglink/custom/super_admin/payout-settings', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + superAdminToken,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    trainer_payout_time_hours: 24,        // 24 hours before payout
    split_company_percentage: 30.00,      // 30% company commission
    split_trainer_percentage: 70.00,      // 70% trainer commission
    affiliate_company_percentage: 20.00,  // 20% company for affiliate
    affiliate_trainer_percentage: 80.00   // 80% trainer for affiliate
  })
});
```

### 2. Verify Program Approval Process

Ensure programs cannot be approved without payout settings:

```javascript
// This will fail if no payout settings exist
const approvalResponse = await fetch('/v2/api/kanglink/custom/super_admin/programs/1/approve', {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer ' + superAdminToken
  }
});
// Expected: 400 error if no payout settings configured
```

## Frontend Integration

### 1. Enrollment with Discounts

Update your enrollment form to include discount fields:

```html
<form id="enrollmentForm">
  <input type="hidden" name="split_id" value="1">
  <input type="hidden" name="payment_type" value="one_time">
  <input type="hidden" name="payment_method_id" value="">
  
  <!-- Optional discount fields -->
  <input type="text" name="affiliate_code" placeholder="Affiliate Code (optional)">
  <input type="text" name="coupon_code" placeholder="Coupon Code (optional)">
  
  <button type="submit">Enroll Now</button>
</form>
```

### 2. JavaScript Implementation

```javascript
async function enrollWithDiscounts(formData) {
  try {
    const response = await fetch('/v2/api/kanglink/custom/athlete/enroll', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer ' + athleteToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        split_id: formData.split_id,
        payment_type: formData.payment_type,
        payment_method_id: formData.payment_method_id,
        affiliate_code: formData.affiliate_code || null,
        coupon_code: formData.coupon_code || null
      })
    });

    const result = await response.json();
    
    if (result.error) {
      // Handle discount errors (invalid coupon, etc.)
      showError(result.message);
      return;
    }

    // Success - show discount details
    const { enrollment, discount_details, commission } = result.data;
    
    showSuccessMessage({
      originalAmount: discount_details.original_amount,
      finalAmount: discount_details.final_amount,
      totalDiscount: discount_details.total_discount_amount,
      appliedDiscounts: discount_details.applied_discounts
    });

  } catch (error) {
    console.error('Enrollment error:', error);
    showError('Enrollment failed. Please try again.');
  }
}
```

### 3. Display Discount Information

```javascript
function showDiscountBreakdown(discountDetails) {
  const breakdown = document.getElementById('discount-breakdown');
  
  let html = `
    <div class="pricing-summary">
      <div class="original-price">Original Price: $${discountDetails.original_amount}</div>
  `;
  
  discountDetails.applied_discounts.forEach(discount => {
    html += `
      <div class="discount-item">
        <span class="discount-type">${discount.type} (${discount.discount_type})</span>
        <span class="discount-amount">-$${discount.discount_amount}</span>
      </div>
    `;
  });
  
  html += `
      <div class="final-price">Final Price: $${discountDetails.final_amount}</div>
      <div class="total-savings">Total Savings: $${discountDetails.total_discount_amount}</div>
    </div>
  `;
  
  breakdown.innerHTML = html;
}
```

## Trainer Dashboard Integration

### 1. Commission Summary

```javascript
async function loadCommissionSummary() {
  const response = await fetch('/v2/api/kanglink/custom/trainer/commissions/summary', {
    headers: {
      'Authorization': 'Bearer ' + trainerToken
    }
  });
  
  const result = await response.json();
  
  if (!result.error) {
    displayCommissionSummary(result.data);
  }
}

function displayCommissionSummary(summaryData) {
  const container = document.getElementById('commission-summary');
  
  let html = '<div class="commission-summary">';
  
  summaryData.forEach(item => {
    html += `
      <div class="commission-item">
        <h3>${item.payout_status.charAt(0).toUpperCase() + item.payout_status.slice(1)}</h3>
        <p>Count: ${item.count}</p>
        <p>Total: $${item.total_amount} ${item.currency}</p>
      </div>
    `;
  });
  
  html += '</div>';
  container.innerHTML = html;
}
```

### 2. Commission History

```javascript
async function loadCommissionHistory(page = 1, status = null) {
  const params = new URLSearchParams({
    page: page,
    limit: 20
  });
  
  if (status) {
    params.append('status', status);
  }
  
  const response = await fetch(`/v2/api/kanglink/custom/trainer/commissions/history?${params}`, {
    headers: {
      'Authorization': 'Bearer ' + trainerToken
    }
  });
  
  const result = await response.json();
  
  if (!result.error) {
    displayCommissionHistory(result.data, result.pagination);
  }
}
```

## Super Admin Dashboard Integration

### 1. Payout Management

```javascript
async function loadPendingPayouts() {
  const response = await fetch('/v2/api/kanglink/custom/super_admin/payouts/pending', {
    headers: {
      'Authorization': 'Bearer ' + superAdminToken
    }
  });
  
  const result = await response.json();
  
  if (!result.error) {
    displayPendingPayouts(result.data);
  }
}

async function processCommissionPayout(commissionId) {
  const response = await fetch(`/v2/api/kanglink/custom/super_admin/payouts/${commissionId}/process`, {
    method: 'PUT',
    headers: {
      'Authorization': 'Bearer ' + superAdminToken
    }
  });
  
  const result = await response.json();
  
  if (!result.error) {
    showSuccess('Commission payout processed successfully');
    loadPendingPayouts(); // Refresh the list
  } else {
    showError(result.message);
  }
}
```

### 2. Payout Settings Management

```javascript
async function updatePayoutSettings(settings) {
  const response = await fetch('/v2/api/kanglink/custom/super_admin/payout-settings', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer ' + superAdminToken,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(settings)
  });
  
  const result = await response.json();
  
  if (!result.error) {
    showSuccess('Payout settings updated successfully');
  } else {
    showError(result.message);
  }
}
```

## Testing

### 1. Run Unit Tests

```bash
# Test discount system
node test_runner.js discount_system.test.js

# Test payout system
node test_runner.js payout_system.test.js

# Test enrollment integration
node test_runner.js enrollment_commission.test.js
```

### 2. Manual Testing Scenarios

#### Test Discount Stacking
1. Create a program with sale discount (10% off)
2. Create a general discount ($5 off)
3. Create a coupon (20% off)
4. Enroll with coupon code
5. Verify final amount: $100 → $90 → $85 → $68

#### Test Commission Calculation
1. Enroll without affiliate code (regular commission)
2. Verify: 30% company, 70% trainer
3. Enroll with affiliate code (affiliate commission)
4. Verify: 20% company, 80% trainer

#### Test Coupon Validation
1. Try expired coupon → Should fail
2. Try used coupon → Should fail
3. Try invalid coupon → Should fail
4. Try valid coupon → Should succeed

## Troubleshooting

### Common Issues

1. **Commission not created**: Check webhook configuration and error logs
2. **Discount not applied**: Verify discount is active and applies to payment type
3. **Coupon validation fails**: Check expiry date, usage limits, and previous usage
4. **Payout settings error**: Ensure percentages add up to 100%

### Debug Mode

Enable detailed logging in development:

```javascript
// In DiscountService.js
console.log('Discount calculation:', {
  original_amount,
  final_amount,
  applied_discounts
});

// In CommissionService.js
console.log('Commission calculation:', {
  total_amount,
  company_amount,
  trainer_amount,
  commission_type
});
```

## Production Deployment

### 1. Database Migration

```sql
-- Run in production database
SOURCE create_payout_settings_table.sql;

-- Verify tables created
SHOW TABLES LIKE 'kanglink_%';
```

### 2. Configuration

1. Set production payout settings
2. Configure Stripe webhook endpoints
3. Set up monitoring for commission processing
4. Configure automated payout processing (optional)

### 3. Monitoring

Monitor key metrics:
- Commission creation success rate
- Discount application accuracy
- Payout processing times
- Error rates in enrollment process

This implementation guide provides everything needed to deploy and use the payout and discount system effectively.
