# Program Approval Notification System

## Overview

The program approval notification system automatically sends notifications and emails to trainers when their programs are approved or rejected by super administrators. This ensures trainers are immediately informed of the status of their program submissions.

## Features

- **In-App Notifications**: Real-time notifications appear in the trainer's notification center
- **Email Notifications**: Automated emails sent to trainers with program approval/rejection details
- **Comprehensive Data**: Notifications include program details, status, and relevant metadata
- **Error Handling**: Notification failures don't prevent program approval/rejection

## Endpoints

### 1. Approve Program

**Endpoint**: `PUT /v2/api/kanglink/custom/super_admin/programs/:program_id/approve`

**Description**: Approves a program and sends notification/email to the trainer

**Authentication**: Requires super_admin role

**Request Body**: Empty (no body required)

**Response**:
```json
{
  "error": false,
  "message": "Program approved successfully and affiliate link created"
}
```

**Notification Created**:
- Type: `program_approved`
- Title: "Program Approved"
- Message: "Your program '[Program Name]' has been approved and is now live!"
- Category: "program"

**Email Sent**:
- Subject: "Program Approved - [Program Name]"
- Content: Professional email with program details and next steps

### 2. Update Program Status

**Endpoint**: `PUT /v2/api/kanglink/custom/super_admin/programs/:program_id/status`

**Description**: Updates program status and sends appropriate notifications

**Authentication**: Requires super_admin role

**Request Body**:
```json
{
  "status": "rejected|archived|deleted|live|published",
  "rejection_reason": "Optional reason for rejection"
}
```

**Response**:
```json
{
  "error": false,
  "message": "Program status updated to [status] successfully"
}
```

## Notification Types

### Program Approved (`program_approved`)
- **Trigger**: When a program is approved by super admin
- **Recipient**: Program creator (trainer)
- **Data Included**:
  - Program ID
  - Program name
  - Status (published)
  - Affiliate link
  - Action by (admin ID)

### Program Rejected (`program_rejected`)
- **Trigger**: When a program is rejected by super admin
- **Recipient**: Program creator (trainer)
- **Data Included**:
  - Program ID
  - Program name
  - Status (rejected)
  - Rejection reason
  - Action by (admin ID)

## Email Templates

### Program Approval Email

**Subject**: `Program Approved - [Program Name]`

**Content**:
```html
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2>🎉 Program Approved!</h2>
  <p>Dear [Trainer Name],</p>
  <p>Great news! Your program "<strong>[Program Name]</strong>" has been approved and is now live on our platform.</p>
  <p><strong>Program Details:</strong></p>
  <ul>
    <li><strong>Program Name:</strong> [Program Name]</li>
    <li><strong>Status:</strong> Published</li>
    <li><strong>Approval Date:</strong> [Date]</li>
  </ul>
  <p>Your program is now available for athletes to discover and enroll in. You can start promoting your program and earning from enrollments.</p>
  <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
  <p>Best regards,<br>The KangLink Team</p>
</div>
```

### Program Rejection Email

**Subject**: `Program Rejected - [Program Name]`

**Content**:
```html
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2>Program Status Update</h2>
  <p>Dear [Trainer Name],</p>
  <p>Your program "<strong>[Program Name]</strong>" has been rejected.</p>
  <p><strong>Reason:</strong> [Rejection Reason]</p>
  <p>Please review the feedback and make necessary adjustments before resubmitting your program.</p>
  <p>If you have any questions, please contact our support team.</p>
  <p>Best regards,<br>The KangLink Team</p>
</div>
```

## Database Schema

### Notification Table
```sql
CREATE TABLE notification (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  sender_id INT,
  related_id INT,
  related_type ENUM('enrollment', 'program', 'split', 'exercise', 'payment', 'user', 'general'),
  notification_type ENUM('exercise_completed', 'day_completed', 'week_completed', 'program_completed', 'milestone_reached', 'new_enrollment', 'payment_received', 'program_updated', 'athlete_message', 'system_alert', 'refund_requested', 'refund_approved', 'refund_rejected', 'refund_processed', 'program_approved', 'program_rejected'),
  category ENUM('progress', 'enrollment', 'payment', 'communication', 'system', 'general', 'refund'),
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSON,
  is_read BOOLEAN DEFAULT FALSE,
  read_at DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Implementation Details

### Notification Creation
```javascript
const notificationData = {
  user_id: program.user_id,
  sender_id: req.user_id, // Admin who performed the action
  related_id: programId,
  related_type: "program",
  notification_type: "program_approved", // or "program_rejected"
  category: "program",
  title: "Program Approved", // or "Program Rejected"
  message: `Your program "${program.program_name}" has been approved and is now live!`,
  data: JSON.stringify({
    program_id: programId,
    program_name: program.program_name,
    status: "published", // or "rejected"
    reason: rejection_reason, // for rejections
    action_by: req.user_id,
  }),
  is_read: false,
  created_at: UtilService.sqlDateTimeFormat(new Date()),
  updated_at: UtilService.sqlDateTimeFormat(new Date()),
};
```

### Email Sending
```javascript
const mailService = new MailService(config);
await mailService.send(
  mailService.from,
  trainer.email,
  emailSubject,
  emailBody
);
```

## Error Handling

- Notification creation failures don't prevent program approval/rejection
- Email sending failures are logged but don't affect the main operation
- Graceful degradation ensures the core functionality works even if notifications fail

## Testing

The notification system is thoroughly tested with the following test cases:

1. **Program Approval Tests**:
   - Successful program approval with notification
   - Notification data structure validation
   - Email sending verification
   - Error handling for non-existent programs
   - Authorization checks

2. **Program Rejection Tests**:
   - Successful program rejection with notification
   - Rejection reason validation
   - Notification type verification
   - Email content validation

3. **Notification System Tests**:
   - Notification creation with correct data
   - JSON data parsing and validation
   - User relationship verification

## Configuration

### Email Configuration
```javascript
// In config.js
mail: {
  mail_host: process.env.MAIL_HOST || "smtp.zoho.com",
  mail_port: process.env.MAIL_PORT || 465,
  mail_user: process.env.MAIL_USER || "<EMAIL>",
  mail_pass: process.env.MAIL_PASS || "dJwg7uw2uxQX",
  from_mail: process.env.FROM_MAIL || "<EMAIL>",
}
```

### Notification Icons
```javascript
const icons = {
  program_approved: "✅",
  program_rejected: "❌",
  // ... other notification types
};
```

## Security Considerations

- Only super administrators can approve/reject programs
- Notifications are only sent to program creators
- Email addresses are validated before sending
- Sensitive data is not included in notifications
- All actions are logged for audit purposes

## Monitoring

- Email delivery status is logged
- Notification creation success/failure is tracked
- Failed operations are logged with error details
- Performance metrics are collected for optimization

## Future Enhancements

1. **Push Notifications**: Add mobile push notifications
2. **SMS Notifications**: Send SMS for critical updates
3. **Custom Templates**: Allow trainers to customize email templates
4. **Bulk Operations**: Support for bulk program approval/rejection
5. **Analytics**: Track notification engagement and effectiveness 