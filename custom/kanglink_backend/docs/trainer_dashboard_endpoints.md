# Trainer Dashboard API Endpoints

This document describes the API endpoints for the trainer dashboard functionality.

## Authentication

All endpoints require authentication via the `TokenMiddleware`. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## Base URL

All endpoints are prefixed with: `/v2/api/kanglink/custom/trainer/dashboard`

---

## 1. Dashboard Stats

**GET** `/stats`

Returns key statistics for the trainer's dashboard.

### Response

```json
{
  "error": false,
  "data": {
    "active_programs_count": 11,
    "active_athletes_count": 25,
    "monthly_revenue": 2450.0,
    "total_lifetime_earnings": 15750.0,
    "available_to_withdraw": 1200.0,
    "pending_payouts": 800.0,
    "total_programs_count": 15,
    "total_enrollments_count": 87,
    "currency": "USD",
    "period": {
      "month": 1,
      "year": 2025
    }
  }
}
```

### Fields Description

- `active_programs_count`: Number of programs with status 'published'
- `active_athletes_count`: Number of unique athletes currently enrolled with 'active' status and 'paid' payment status
- `monthly_revenue`: Total trainer commission earned this month from the commission table
- `total_lifetime_earnings`: Total trainer commission earned across all time (pending + processed)
- `available_to_withdraw`: Commission amount ready for payout (past payout hold period)
- `pending_payouts`: Commission amount still in hold period before payout
- `total_programs_count`: Total number of programs created by the trainer
- `total_enrollments_count`: Total number of paid enrollments across all programs (from commission table)
- `currency`: Currency code from commission records (defaults to USD)
- `period`: Current month and year for the revenue calculation

---

## 2. Notifications

**GET** `/notifications`

Returns paginated notifications for the trainer.

### Query Parameters

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `unread_only` (optional): Filter to unread notifications only (true/false)
- `category` (optional): Filter by category (progress, enrollment, payment, communication, system, general)

### Response

```json
{
  "error": false,
  "data": {
    "notifications": [
      {
        "id": 123,
        "notification_type": "new_enrollment",
        "category": "enrollment",
        "title": "New athlete enrolled",
        "message": "John Doe enrolled in your program 'Advanced Strength Training'",
        "data": {
          "program_id": 45,
          "enrollment_id": 789
        },
        "is_read": false,
        "read_at": null,
        "created_at": "2025-01-08T10:30:00Z",
        "sender_id": 456,
        "sender_name": "John Doe",
        "related_id": 789,
        "related_type": "enrollment"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "total_pages": 3,
      "has_next": true,
      "has_prev": false
    },
    "unread_count": 8
  }
}
```

---

## 3. Activities

**GET** `/activities`

Returns paginated activities related to the trainer and their athletes.

### Query Parameters

- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `activity_type` (optional): Filter by specific activity type
- `visibility` (optional): Filter by visibility (all, public, private, trainer_only, default: all)

### Response

```json
{
  "error": false,
  "data": {
    "activities": [
      {
        "id": 456,
        "user_id": 789,
        "user_name": "Sarah Smith",
        "actor_id": 789,
        "actor_name": "Sarah Smith",
        "activity_type": "workout_completed",
        "title": "Workout session completed",
        "description": "Completed Day 1 of Week 3",
        "metadata": {
          "duration_minutes": 45,
          "exercises_completed": 8
        },
        "visibility": "trainer_only",
        "related_id": 123,
        "related_type": "day",
        "related_name": null,
        "created_at": "2025-01-08T09:15:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "total_pages": 8,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

---

## 4. Mark Notification as Read

**PUT** `/notifications/:id/read`

Marks a specific notification as read.

### URL Parameters

- `id`: Notification ID

### Response

```json
{
  "error": false,
  "message": "Notification marked as read"
}
```

### Error Responses

- `404`: Notification not found or doesn't belong to the trainer
- `500`: Server error

---

## 5. Mark All Notifications as Read

**PUT** `/notifications/read-all`

Marks all unread notifications as read for the trainer.

### Response

```json
{
  "error": false,
  "message": "All notifications marked as read",
  "data": {
    "updated_count": 8
  }
}
```

---

## 6. Dashboard Summary

**GET** `/summary`

Returns a quick overview combining key stats and recent items for dashboard display.

### Response

```json
{
  "error": false,
  "data": {
    "stats": {
      "active_programs_count": 11,
      "active_athletes_count": 25,
      "unread_notifications_count": 8
    },
    "recent_notifications": [
      {
        "id": 123,
        "type": "new_enrollment",
        "title": "New athlete enrolled",
        "message": "John Doe enrolled in your program",
        "created_at": "2025-01-08T10:30:00Z",
        "is_read": false
      }
    ],
    "recent_activities": [
      {
        "id": 456,
        "type": "workout_completed",
        "title": "Workout session completed",
        "user_name": "Sarah Smith",
        "created_at": "2025-01-08T09:15:00Z"
      }
    ]
  }
}
```

---

## Activity Types

The following activity types are supported:

- `workout_started`: Athlete started a workout session
- `workout_completed`: Athlete completed a workout session
- `day_completed`: Athlete completed a full day
- `week_completed`: Athlete completed a full week
- `program_completed`: Athlete completed the entire program
- `new_enrollment`: New athlete enrolled in a program
- `payment_made`: Payment was processed
- `program_created`: New program was created
- `program_updated`: Program was updated
- `session_scheduled`: Training session was scheduled
- `milestone_reached`: Athlete reached a milestone
- `refund_requested`: Refund was requested
- `subscription_cancelled`: Subscription was cancelled
- `profile_updated`: User profile was updated

## Notification Types

The following notification types are supported:

- `exercise_completed`: Exercise was completed
- `day_completed`: Day was completed
- `week_completed`: Week was completed
- `program_completed`: Program was completed
- `milestone_reached`: Milestone was reached
- `new_enrollment`: New enrollment occurred
- `payment_received`: Payment was received
- `program_updated`: Program was updated
- `athlete_message`: Message from athlete
- `system_alert`: System alert

## Error Handling

All endpoints return consistent error responses:

```json
{
  "error": true,
  "message": "Error description"
}
```

Common HTTP status codes:

- `200`: Success
- `400`: Bad request
- `401`: Unauthorized
- `404`: Not found
- `500`: Internal server error
