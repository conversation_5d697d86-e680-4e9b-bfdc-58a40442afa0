# Enrollment with Comments API Endpoint

## Overview
This endpoint retrieves athlete enrollments where the trainer has enabled comments for their programs. This is specifically designed for the athlete feed functionality where athletes can view and interact with programs they're enrolled in that allow comments.

## Endpoint Details

### GET `/v2/api/kanglink/custom/athlete/enrollments/with-comments`

**Description:** Fetch athlete enrollments where trainer has allowed comments

**Authentication:** Required (Bearer token)
**Roles:** `member|trainer|super_admin`

## Request Parameters

### Query Parameters
- `page` (optional, default: 1): Page number for pagination
- `limit` (optional, default: 50): Number of items per page
- `search` (optional): Search term to filter programs by name, split title, or program type

### Example Request
```bash
GET /v2/api/kanglink/custom/athlete/enrollments/with-comments?page=1&limit=10&search=strength
Authorization: Bearer <token>
```

## Response Format

### Success Response (200)
```json
{
  "error": false,
  "data": [
    {
      "id": "123",
      "athlete_id": "456",
      "program_id": "789",
      "split_id": "101",
      "payment_type": "subscription",
      "amount": 99.99,
      "currency": "USD",
      "enrollment_date": "2024-01-15T10:30:00Z",
      "expiry_date": "2024-12-15T10:30:00Z",
      "status": "active",
      "payment_status": "paid",
      "access_type": "live",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "program": {
        "id": "789",
        "program_name": "Strength Training Program",
        "type_of_program": "strength",
        "program_description": "Comprehensive strength training program",
        "allow_comments": true,
        "allow_private_messages": true,
        "image": "https://example.com/image.jpg",
        "status": "live"
      },
      "split": {
        "id": "101",
        "title": "Upper Body Focus",
        "description": "Upper body strength training split"
      },
      "trainer": {
        "id": "202",
        "email": "<EMAIL>",
        "data": {
          "first_name": "John",
          "last_name": "Doe",
          "avatar": "https://example.com/avatar.jpg"
        }
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  },
  "message": "Enrollments retrieved successfully"
}
```

### Error Response (400)
```json
{
  "error": true,
  "message": "Invalid parameters"
}
```

### Error Response (401)
```json
{
  "error": true,
  "message": "Unauthorized"
}
```

### Error Response (500)
```json
{
  "error": true,
  "message": "Internal server error",
  "error": "Error details"
}
```

## Business Logic

### Filtering Criteria
The endpoint only returns enrollments that meet the following criteria:
1. **Active Status**: Only enrollments with `status = 'active'`
2. **Comments Allowed**: Only programs where `allow_comments = 1`
3. **Program Status**: Only programs with status in `['live', 'published', 'active']`
4. **Athlete Ownership**: Only enrollments belonging to the authenticated athlete

### Search Functionality
The search parameter filters results by:
- Program name (`program_name`)
- Split title (`split.title`)
- Program type (`type_of_program`)

Search is case-insensitive and uses SQL LIKE operator.

### Data Transformation
- JSON fields (like `trainer_data`) are parsed and included in the response
- Boolean fields are properly converted from database format
- All dates are returned in ISO format

## Security Considerations

1. **Authentication Required**: All requests must include a valid Bearer token
2. **Role-Based Access**: Only authenticated users with appropriate roles can access
3. **Data Isolation**: Athletes can only see their own enrollments
4. **Input Validation**: All query parameters are validated and sanitized

## Usage Examples

### Frontend Integration
```javascript
// Using the SDK
const response = await sdk.request({
  endpoint: "/v2/api/kanglink/custom/athlete/enrollments/with-comments",
  method: "GET",
  params: {
    page: 1,
    limit: 10,
    search: "strength"
  }
});

if (!response.error) {
  const enrollments = response.data;
  // Process enrollments for dropdown selection
}
```

### Search Implementation
```javascript
// Search for specific programs
const searchEnrollments = async (searchTerm) => {
  const response = await sdk.request({
    endpoint: "/v2/api/kanglink/custom/athlete/enrollments/with-comments",
    method: "GET",
    params: {
      page: 1,
      limit: 50,
      search: searchTerm
    }
  });
  
  return response.data;
};
```

## Related Endpoints

- `GET /v2/api/kanglink/custom/trainer/feed` - Get posts for a specific program
- `POST /v2/api/kanglink/custom/trainer/feed` - Create a new post
- `GET /v2/api/kanglink/custom/trainer/feed/:post_id/comments` - Get comments for a post

## Database Schema

### Tables Involved
- `kanglink_enrollment` - Main enrollment data
- `kanglink_program` - Program information and settings
- `kanglink_split` - Split information
- `kanglink_user` - User/trainer information

### Key Fields
- `enrollment.athlete_id` - Links to authenticated athlete
- `program.allow_comments` - Boolean flag for comment permissions
- `program.status` - Program publication status
- `enrollment.status` - Enrollment status

## Testing

See `baas_v5/mtpbk/tests/lambda/enrollment_with_comments.test.js` for comprehensive test cases covering:
- Successful data retrieval
- Search functionality
- Pagination
- Authentication
- Data isolation
- Error handling 