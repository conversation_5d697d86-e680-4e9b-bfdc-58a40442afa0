# Trainer Public Endpoints

## Overview
These endpoints provide public access to trainer information and their published programs for the public trainer profile pages.

## Endpoints

### 1. Get Trainer Details
**Endpoint**: `GET /v2/api/kanglink/custom/public/trainer/:trainer_id`

**Description**: Returns detailed information about a specific trainer including their ratings and program statistics.

**Authentication**: None required (public endpoint)

**Parameters**:
- `trainer_id` (path parameter): The ID of the trainer

**Response Format**:
```json
{
  "error": false,
  "message": "Trainer details retrieved successfully",
  "data": {
    "id": 5,
    "email": "<EMAIL>",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "full_name": "<PERSON>",
    "photo": "https://example.com/photo.jpg",
    "bio": "Experienced fitness trainer with 10+ years...",
    "specialization": ["strength", "cardio", "nutrition"],
    "experience_years": 10,
    "certifications": ["NASM-CPT", "ACSM-CEP"],
    "average_rating": 4.8,
    "review_count": 25,
    "program_count": 8,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

**Error Responses**:
- `400`: Invalid trainer ID
- `404`: Trainer not found
- `500`: Internal server error

### 2. Get Trainer Programs
**Endpoint**: `GET /v2/api/kanglink/custom/public/trainer/:trainer_id/programs`

**Description**: Returns paginated list of a trainer's published programs with ratings and pricing information.

**Authentication**: None required (public endpoint)

**Parameters**:
- `trainer_id` (path parameter): The ID of the trainer
- `page` (query parameter, optional): Page number (default: 1)
- `limit` (query parameter, optional): Items per page (default: 20, max: 50)
- `sort_by` (query parameter, optional): Sort field - `created_at`, `rating`, `program_name` (default: `created_at`)
- `sort_order` (query parameter, optional): Sort order - `asc`, `desc` (default: `desc`)

**Response Format**:
```json
{
  "error": false,
  "message": "Trainer programs retrieved successfully",
  "data": [
    {
      "id": 1,
      "user_id": 5,
      "program_name": "Advanced Strength Training",
      "type_of_program": "strength",
      "program_description": "A comprehensive strength training program...",
      "target_levels": ["intermediate", "advanced"],
      "currency": "USD",
      "days_for_preview": 7,
      "image": "https://example.com/program-image.jpg",
      "rating": 4.8,
      "review_count": 25,
      "price": 99.99,
      "splits": [
        {
          "id": 1,
          "title": "Upper Body",
          "full_price": 149.99,
          "subscription": 29.99
        },
        {
          "id": 2,
          "title": "Lower Body",
          "full_price": 99.99,
          "subscription": 19.99
        }
      ],
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 8,
    "num_pages": 1,
    "has_next": false,
    "has_prev": false
  }
}
```

**Error Responses**:
- `400`: Invalid trainer ID or sort parameters
- `404`: Trainer not found
- `500`: Internal server error

## Features

### Trainer Details Endpoint
- **Public Access**: No authentication required
- **Comprehensive Data**: Includes trainer bio, specializations, certifications, and experience
- **Rating Statistics**: Shows average rating, review count, and total program count
- **Data Parsing**: Safely parses JSON data from user.data field
- **Error Handling**: Graceful handling of missing or invalid data

### Trainer Programs Endpoint
- **Pagination**: Supports page-based pagination with configurable limits
- **Sorting**: Multiple sort options (date, rating, name) with ascending/descending order
- **Price Calculation**: Automatically calculates minimum price from all splits
- **Rating Integration**: Shows program ratings from review posts
- **Split Information**: Includes detailed pricing for each program split
- **Performance Optimized**: Uses single query with JOINs for efficient data retrieval

## Database Tables Used
- `kanglink_user`: Trainer information
- `kanglink_program`: Program details
- `kanglink_split`: Program pricing splits
- `kanglink_post_feed`: Reviews and ratings

## Usage Examples

### Get trainer details:
```bash
curl "https://api.example.com/v2/api/kanglink/custom/public/trainer/5"
```

### Get trainer programs with sorting:
```bash
curl "https://api.example.com/v2/api/kanglink/custom/public/trainer/5/programs?page=1&limit=10&sort_by=rating&sort_order=desc"
```

### Get trainer programs by creation date:
```bash
curl "https://api.example.com/v2/api/kanglink/custom/public/trainer/5/programs?sort_by=created_at&sort_order=asc"
```

## Performance Considerations
- Uses raw SQL queries with JOINs for optimal performance
- Implements proper pagination to limit data transfer
- Includes rating calculations in single query to avoid N+1 problems
- Validates input parameters to prevent invalid queries
- Uses COALESCE for handling NULL values in aggregations
