# Payout & Discount System API Reference

## Overview

Complete API reference for the Kanglink payout and discount system, including enrollment with discounts, commission tracking, and payout management.

## Authentication

All endpoints require appropriate role-based authentication:
- **Super Admin**: Full access to payout settings and commission data
- **Trainer**: Access to own commission information
- **Athlete**: Access to enrollment with discount codes

## Enrollment API (Enhanced with Discounts)

### Enroll in Split with Discounts

**Endpoint:** `POST /v2/api/kanglink/custom/athlete/enroll`

**Headers:**
```
Authorization: Bearer {athlete_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "split_id": 1,
  "payment_type": "one_time",           // or "subscription"
  "payment_method_id": "pm_1234567890",
  "affiliate_code": "ABC123",           // optional - for affiliate commissions
  "coupon_code": "SAVE20"               // optional - for coupon discounts
}
```

**Response (Success):**
```json
{
  "error": false,
  "message": "Enrollment successful",
  "data": {
    "enrollment": {
      "id": 1,
      "trainer_id": 1,
      "athlete_id": 123,
      "program_id": 1,
      "split_id": 1,
      "payment_type": "one_time",
      "amount": 68.00,                  // Final amount charged (after discounts)
      "original_amount": 100.00,        // Original price
      "discount_amount": 32.00,         // Total discount applied
      "currency": "USD",
      "status": "active",
      "payment_status": "paid",
      "affiliate_code": "ABC123",
      "commission_calculated": true,
      "created_at": "2024-07-04T17:30:00Z"
    },
    "discount_details": {
      "original_amount": 100.00,
      "final_amount": 68.00,
      "total_discount_amount": 32.00,
      "applied_discounts": [
        {
          "type": "sale",
          "discount_type": "percentage",
          "discount_value": 10.00,
          "discount_amount": 10.00,
          "source": "program_sale"
        },
        {
          "type": "general",
          "discount_type": "fixed",
          "discount_value": 5.00,
          "discount_amount": 5.00,
          "source": "general_discount"
        },
        {
          "type": "coupon",
          "coupon_id": 1,
          "coupon_code": "SAVE20",
          "discount_type": "percentage",
          "discount_value": 20.00,
          "discount_amount": 17.00,
          "source": "coupon"
        }
      ]
    },
    "commission": {
      "id": 1,
      "commission_type": "affiliate",     // "regular" or "affiliate"
      "total_amount": 68.00,              // Commission calculated on discounted amount
      "company_amount": 13.60,            // 20% for affiliate enrollment
      "trainer_amount": 54.40,            // 80% for affiliate enrollment
      "payout_scheduled_at": "2024-07-05T17:30:00Z"
    }
  }
}
```

**Response (Error - Invalid Coupon):**
```json
{
  "error": true,
  "message": "Invalid coupon code"
}
```

**Response (Error - Coupon Expired):**
```json
{
  "error": true,
  "message": "Coupon has expired"
}
```

## Payout Settings API (Super Admin)

### Get Current Payout Settings

**Endpoint:** `GET /v2/api/kanglink/custom/super_admin/payout-settings`

**Headers:**
```
Authorization: Bearer {super_admin_token}
```

**Response:**
```json
{
  "error": false,
  "data": {
    "id": 1,
    "trainer_payout_time_hours": 24,
    "split_company_percentage": 30.00,
    "split_trainer_percentage": 70.00,
    "affiliate_company_percentage": 20.00,
    "affiliate_trainer_percentage": 80.00,
    "is_active": true,
    "created_at": "2024-07-04T10:00:00Z",
    "updated_at": "2024-07-04T15:30:00Z"
  }
}
```

### Update Payout Settings

**Endpoint:** `POST /v2/api/kanglink/custom/super_admin/payout-settings`

**Headers:**
```
Authorization: Bearer {super_admin_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "trainer_payout_time_hours": 48,
  "split_company_percentage": 25.00,
  "split_trainer_percentage": 75.00,
  "affiliate_company_percentage": 15.00,
  "affiliate_trainer_percentage": 85.00
}
```

**Response:**
```json
{
  "error": false,
  "message": "Payout settings created successfully",
  "data": {
    "id": 2,
    "trainer_payout_time_hours": 48,
    "split_company_percentage": 25.00,
    "split_trainer_percentage": 75.00,
    "affiliate_company_percentage": 15.00,
    "affiliate_trainer_percentage": 85.00,
    "is_active": true,
    "created_at": "2024-07-04T18:00:00Z"
  }
}
```

## Commission Management API (Super Admin)

### Get Pending Payouts

**Endpoint:** `GET /v2/api/kanglink/custom/super_admin/payouts/pending`

**Headers:**
```
Authorization: Bearer {super_admin_token}
```

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "id": 1,
      "enrollment_id": 1,
      "trainer_id": 1,
      "athlete_id": 123,
      "program_id": 1,
      "split_id": 1,
      "commission_type": "affiliate",
      "total_amount": 68.00,
      "original_amount": 100.00,
      "discount_amount": 32.00,
      "company_amount": 13.60,
      "trainer_amount": 54.40,
      "affiliate_code": "ABC123",
      "payout_status": "pending",
      "payout_scheduled_at": "2024-07-05T17:30:00Z",
      "currency": "USD",
      "created_at": "2024-07-04T17:30:00Z"
    }
  ],
  "count": 1
}
```

### Process Commission Payout

**Endpoint:** `PUT /v2/api/kanglink/custom/super_admin/payouts/{commission_id}/process`

**Headers:**
```
Authorization: Bearer {super_admin_token}
```

**Response:**
```json
{
  "error": false,
  "message": "Commission payout processed successfully",
  "data": {
    "id": 1,
    "payout_status": "processed",
    "payout_processed_at": "2024-07-04T18:00:00Z",
    "updated_at": "2024-07-04T18:00:00Z"
  }
}
```

### Get Trainer Commission Summary

**Endpoint:** `GET /v2/api/kanglink/custom/super_admin/commissions/trainer/{trainer_id}`

**Query Parameters:**
- `status` (optional): Filter by payout status (`pending`, `processed`, `failed`, `cancelled`)

**Headers:**
```
Authorization: Bearer {super_admin_token}
```

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "payout_status": "pending",
      "count": 5,
      "total_amount": 350.00,
      "currency": "USD"
    },
    {
      "payout_status": "processed",
      "count": 10,
      "total_amount": 700.00,
      "currency": "USD"
    }
  ]
}
```

### Get Affiliate Commission Summary

**Endpoint:** `GET /v2/api/kanglink/custom/super_admin/commissions/affiliate/{trainer_id}`

**Query Parameters:**
- `status` (optional): Filter by payout status

**Headers:**
```
Authorization: Bearer {super_admin_token}
```

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "payout_status": "pending",
      "count": 3,
      "total_amount": 240.00,
      "currency": "USD"
    },
    {
      "payout_status": "processed",
      "count": 7,
      "total_amount": 560.00,
      "currency": "USD"
    }
  ]
}
```

## Trainer Commission API

### Get Own Commission Summary

**Endpoint:** `GET /v2/api/kanglink/custom/trainer/commissions/summary`

**Query Parameters:**
- `status` (optional): Filter by payout status

**Headers:**
```
Authorization: Bearer {trainer_token}
```

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "payout_status": "pending",
      "count": 8,
      "total_amount": 590.00,
      "currency": "USD"
    },
    {
      "payout_status": "processed",
      "count": 17,
      "total_amount": 1260.00,
      "currency": "USD"
    }
  ]
}
```

### Get Own Affiliate Earnings

**Endpoint:** `GET /v2/api/kanglink/custom/trainer/commissions/affiliate`

**Query Parameters:**
- `status` (optional): Filter by payout status

**Headers:**
```
Authorization: Bearer {trainer_token}
```

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "payout_status": "pending",
      "count": 3,
      "total_amount": 240.00,
      "currency": "USD"
    },
    {
      "payout_status": "processed",
      "count": 7,
      "total_amount": 560.00,
      "currency": "USD"
    }
  ]
}
```

### Get Commission History

**Endpoint:** `GET /v2/api/kanglink/custom/trainer/commissions/history`

**Query Parameters:**
- `status` (optional): Filter by payout status
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)

**Headers:**
```
Authorization: Bearer {trainer_token}
```

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "id": 1,
      "enrollment_id": 1,
      "program_name": "Advanced Fitness Program",
      "split_name": "Beginner Split",
      "athlete_email": "<EMAIL>",
      "commission_type": "affiliate",
      "total_amount": 68.00,
      "original_amount": 100.00,
      "discount_amount": 32.00,
      "trainer_amount": 54.40,
      "affiliate_code": "ABC123",
      "payout_status": "processed",
      "payout_processed_at": "2024-07-04T18:00:00Z",
      "currency": "USD",
      "created_at": "2024-07-04T17:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 25,
    "pages": 2
  }
}
```

## Error Responses

### Common Error Codes

**400 Bad Request:**
```json
{
  "error": true,
  "message": "Invalid request parameters"
}
```

**401 Unauthorized:**
```json
{
  "error": true,
  "message": "Authentication required"
}
```

**403 Forbidden:**
```json
{
  "error": true,
  "message": "Insufficient permissions"
}
```

**404 Not Found:**
```json
{
  "error": true,
  "message": "Resource not found"
}
```

**500 Internal Server Error:**
```json
{
  "error": true,
  "message": "Internal server error"
}
```

## Rate Limiting

- **Enrollment endpoints**: 10 requests per minute per user
- **Commission endpoints**: 100 requests per minute per user
- **Payout settings**: 20 requests per minute per admin

## Webhook Integration

The system integrates with Stripe webhooks to automatically create commission records:

- `payment_intent.succeeded`: Creates commission for one-time payments
- `customer.subscription.created`: Creates commission for subscription payments

Commission records are created with the actual charged amount (after discounts) to ensure accurate commission calculations.
