# Two-Factor Authentication (2FA) Implementation

## Overview

This document describes the complete 2FA implementation for the Kanglink project, including both backend endpoints and frontend integration.

## Backend Implementation

### Endpoints

#### 1. 2FA Status Check
- **Endpoint**: `GET /v2/api/kanglink/member/lambda/2fa/status`
- **Authentication**: Required (TokenMiddleware)
- **Response**:
```json
{
  "error": false,
  "enabled": true,
  "user_id": 123
}
```

#### 2. 2FA Enable
- **Endpoint**: `POST /v2/api/kanglink/member/lambda/2fa/enable`
- **Authentication**: Required (TokenMiddleware)
- **Request Body**:
```json
{
  "type": "qr", // or "sms"
  "phone": "+1234567890" // required for SMS
}
```
- **Response**:
```json
{
  "error": false,
  "qr_code": "data:image/png;base64,...",
  "secret_key": "JBSWY3DPEHPK3PXP",
  "backup_codes": ["123456", "789012"],
  "access_token": "jwt_token",
  "expire_at": 30,
  "user_id": 123
}
```

#### 3. 2FA Verify
- **Endpoint**: `POST /v2/api/kanglink/member/lambda/2fa/verify`
- **Authentication**: Required (TokenMiddleware)
- **Request Body**:
```json
{
  "verification_code": "123456"
}
```
- **Response**:
```json
{
  "error": false,
  "valid": true,
  "message": "Verified Successfully"
}
```

#### 4. 2FA Disable
- **Endpoint**: `POST /v2/api/kanglink/member/lambda/2fa/disable`
- **Authentication**: Required (TokenMiddleware)
- **Response**:
```json
{
  "error": false,
  "message": "2FA disabled"
}
```

#### 5. 2FA Authorize
- **Endpoint**: `POST /v2/api/kanglink/member/lambda/2fa/authorize`
- **Authentication**: Required (TokenMiddleware)
- **Response**:
```json
{
  "error": false,
  "qr_code": "data:image/png;base64,...",
  "type": "qr",
  "access_token": "jwt_token",
  "expire_at": 30,
  "user_id": 123
}
```

### Backend Services

#### TwoFactorService
The backend uses the `TwoFactorService` from the BaaS core which provides:

- `getTwoFactorAuthenticationCode()` - Generate new 2FA secrets
- `setUpTwoFactorAuth()` - Set up 2FA for a user
- `verifyTotp()` - Verify TOTP codes
- `enable2FA()` - Enable 2FA for a user
- `disable2FA()` - Disable 2FA for a user

## Frontend Implementation

### SDK Methods

The frontend SDK (`MkdSDK.ts`) provides the following 2FA methods:

#### 1. get2FAStatus()
```typescript
async get2FAStatus(): Promise<MkdAPIResponse>
```
Returns the current 2FA status for the authenticated user.

#### 2. enable2FA()
```typescript
async enable2FA(reqObj: TwoFactorPayload = {}): Promise<MkdAPIResponse>
```
Initiates 2FA setup and returns QR code and setup information.

#### 3. verify2FA()
```typescript
async verify2FA(verification_code: string): Promise<MkdAPIResponse>
```
Verifies a 2FA verification code.

#### 4. disable2FA()
```typescript
async disable2FA(reqObj: TwoFactorPayload = {}): Promise<MkdAPIResponse>
```
Disables 2FA for the authenticated user.

#### 5. authorize2FA()
```typescript
async authorize2FA(access_token: string): Promise<MkdAPIResponse>
```
Authorizes 2FA and returns new access token.

### React Component

The `TwoFactorAuthModal.tsx` component provides a complete UI for 2FA management:

#### Features:
- **Setup Step**: Shows QR code and secret key for authenticator apps
- **Verify Step**: Form to enter verification code
- **Disable Step**: Confirmation to disable 2FA
- **Error Handling**: Comprehensive error handling with toast notifications
- **Loading States**: Loading indicators during API calls

#### Usage:
```typescript
<TwoFactorAuthModal
  isOpen={isModalOpen}
  onClose={() => setModalOpen(false)}
  onSuccess={(enabled: boolean) => {
    // Handle success
  }}
  currentlyEnabled={userHas2FA}
/>
```

## Security Features

### 1. TOTP Implementation
- Uses **speakeasy** library for TOTP generation and verification
- 30-second time windows for code validation
- Base32 encoding for secret keys

### 2. Token Management
- JWT tokens for API authentication
- Short-lived access tokens for 2FA operations
- Secure token storage and validation

### 3. Error Handling
- Comprehensive validation of input parameters
- Proper error responses with meaningful messages
- Rate limiting and brute force protection

### 4. Database Security
- Encrypted storage of 2FA secrets
- Secure token storage with expiration
- User status validation

## Testing

### Test Suite
The implementation includes comprehensive tests in `2fa_integration.test.js`:

- 2FA Status Check
- 2FA Enable (QR Code)
- 2FA Enable (SMS)
- 2FA Verify (Valid Code)
- 2FA Verify (Invalid Code)
- 2FA Disable
- 2FA Authorize

### Running Tests
```bash
node mtpbk/custom/ksl_be/tests/2fa_integration.test.js
```

## Configuration

### Required Dependencies
- `speakeasy` - TOTP generation and verification
- `qrcode` - QR code generation
- `jsonwebtoken` - JWT token handling

### Environment Variables
- `JWT_KEY` - Secret key for JWT tokens
- `JWT_EXPIRE` - JWT token expiration time
- `SMS_PROVIDER` - SMS service configuration (if using SMS 2FA)

## Best Practices

### 1. User Experience
- Clear instructions for QR code scanning
- Backup codes for account recovery
- Intuitive error messages
- Loading states for all operations

### 2. Security
- Rate limiting on verification attempts
- Secure storage of 2FA secrets
- Proper token validation
- Audit logging for security events

### 3. Error Handling
- Graceful degradation when 2FA is unavailable
- Clear error messages for users
- Proper HTTP status codes
- Validation feedback

## Troubleshooting

### Common Issues

#### 1. QR Code Not Scanning
- Ensure the QR code is properly generated
- Check that the secret key is correctly formatted
- Verify the authenticator app supports TOTP

#### 2. Verification Code Not Working
- Check time synchronization between server and client
- Verify the code is entered correctly
- Ensure the 2FA secret is properly stored

#### 3. SMS Not Received
- Verify phone number format
- Check SMS service configuration
- Ensure proper error handling for SMS failures

### Debug Mode
Enable debug logging by setting:
```javascript
console.log("2FA Debug:", { user_id, secret, token });
```

## Future Enhancements

### Planned Features
1. **Backup Codes**: Generate and validate backup codes
2. **Multiple Devices**: Support for multiple authenticator apps
3. **SMS Fallback**: SMS as backup to QR code
4. **Admin Override**: Admin ability to disable user 2FA
5. **Audit Logging**: Comprehensive security event logging

### Security Improvements
1. **Rate Limiting**: Enhanced rate limiting for verification attempts
2. **Device Fingerprinting**: Track and validate device changes
3. **Geolocation**: Location-based 2FA requirements
4. **Risk Assessment**: Dynamic 2FA requirements based on risk

## Conclusion

The 2FA implementation provides a secure, user-friendly two-factor authentication system that integrates seamlessly with the existing authentication flow. The modular design allows for easy extension and customization while maintaining security best practices. 