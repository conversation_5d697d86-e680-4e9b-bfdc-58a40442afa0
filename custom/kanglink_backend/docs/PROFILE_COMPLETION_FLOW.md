# Profile Completion Flow for OAuth Users

## Overview

When users sign up or log in using OAuth providers (Google, Facebook, LinkedIn, etc.), only basic information like email and name is stored. To ensure we have complete user profiles, we've implemented a profile completion flow that redirects OAuth users to complete their profile information.

## How It Works

### 1. OAuth Login Process

When a user logs in via OAuth:
- The system creates a user account with `profile_update: false`
- Only basic information (email, first_name, last_name) is stored
- The user is redirected to a profile completion page

### 2. Profile Completion Pages

#### For Athletes (role: member)
- **Route**: `/athlete/profile-completion`
- **Fields**: Full Name, Date of Birth, Fitness Level, Fitness Goals
- **Validation**: Age validation (minimum 18 years old)
- **No password fields** (since OAuth users don't have passwords)

#### For Trainers (role: trainer)
- **Route**: `/trainer/profile-completion`
- **Fields**: Full Name, Phone Number, Years of Experience, Gender, Qualifications, Specializations, Bio
- **Validation**: Phone number validation, required fields
- **No password fields** (since OAuth users don't have passwords)

### 3. Profile Update Process

When users complete their profile:
- All form data is sent to the respective profile update endpoint
- `profile_update` is set to `true` in the database
- User is redirected to their appropriate dashboard
- Profile data is refreshed to reflect the new information

## Implementation Details

### Backend Changes

#### AuthService.js
Updated all OAuth login methods to set `profile_update: 0` for new users:
- `googleLogin()`
- `appleLogin()`
- `facebookLogin()`
- `microsoftLogin()`
- `linkedinLogin()`

#### Profile Endpoints
- **Athlete**: `/v1/api/kanglink/member/lambda/profile`
- **Trainer**: `/v1/api/kanglink/trainer/lambda/profile`

### Frontend Changes

#### OAuthCallbackPage.tsx
- Added profile check after successful OAuth login
- Redirects to appropriate profile completion page if `profile_update` is false
- Falls back to default redirects if profile check fails

#### New Components
- `AthleteProfileCompletion.tsx` - Profile completion form for athletes
- `TrainerProfileCompletion.tsx` - Profile completion form for trainers

#### Routes
- Added routes for profile completion pages
- Protected routes requiring authentication
- Proper role-based access control

## Database Schema

### User Table
```sql
ALTER TABLE user ADD COLUMN profile_update BOOLEAN DEFAULT 1;
```

- `profile_update: 1` - Profile is complete (regular signup users)
- `profile_update: 0` - Profile needs completion (OAuth users)

## User Experience

### Flow for OAuth Users
1. User clicks "Sign in with Google/Facebook/etc."
2. OAuth provider authenticates user
3. User is redirected to OAuth callback page
4. System checks if profile is complete
5. If incomplete, redirect to profile completion page
6. User fills out required information
7. Profile is marked as complete
8. User is redirected to dashboard

### Flow for Regular Users
1. User signs up with email/password
2. Profile is marked as complete immediately
3. User is redirected to dashboard

## Testing

### Test Cases
- OAuth user with incomplete profile → redirected to completion page
- OAuth user with complete profile → redirected to dashboard
- Regular signup user → no profile completion required
- Profile completion form validation
- Age validation for athletes
- Phone validation for trainers

### Test File
`tests/profile_completion_flow.test.js`

## Security Considerations

- Profile completion pages are protected routes
- Role-based access control ensures users can only access their appropriate completion page
- Form validation prevents invalid data submission
- Age validation prevents underage users from registering

## Future Enhancements

- Add profile picture upload during completion
- Add more detailed validation rules
- Add progress indicators for multi-step completion
- Add ability to skip optional fields
- Add profile completion reminders for users who abandon the process 