# Program Preview API Documentation

## Overview
The Program Preview API allows users to fetch detailed information about a program split for preview purposes. This includes program details, trainer information, weeks, days, sessions, exercises, and videos.

## Endpoint

### GET `/v2/api/kanglink/custom/preview/program/:split_id`

Fetches complete program split details for preview.

#### Parameters
- `split_id` (path parameter): The ID of the split to preview

#### Headers
- `Authorization`: Bearer token required
- `Content-Type`: application/json

#### Authentication
- Requires authentication with role: `member`, `trainer`, or `super_admin`

#### Response Format

**Success Response (200)**
```json
{
  "error": false,
  "message": "Program preview retrieved successfully",
  "data": {
    "split": {
      "id": 1,
      "title": "Split Title",
      "equipment_required": "Dumbbells, Bench",
      "full_price": 99.99,
      "subscription": 19.99,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "program": {
        "id": 1,
        "name": "Program Name",
        "description": "Program description...",
        "type": "strength",
        "currency": "USD",
        "image_url": "https://example.com/image.jpg",
        "status": "active",
        "approval_date": "2024-01-01T00:00:00Z",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "trainer": {
          "id": 1,
          "full_name": "John Doe",
          "first_name": "John",
          "last_name": "Doe",
          "photo": "https://example.com/photo.jpg",
          "email": "<EMAIL>"
        }
      },
      "weeks": [
        {
          "id": 1,
          "title": "Week 1",
          "week_order": 1,
          "days_count": 5,
          "created_at": "2024-01-01T00:00:00Z",
          "updated_at": "2024-01-01T00:00:00Z",
          "days": [
            {
              "id": 1,
              "title": "Day 1",
              "is_rest_day": false,
              "day_order": 1,
              "sessions_count": 1,
              "created_at": "2024-01-01T00:00:00Z",
              "updated_at": "2024-01-01T00:00:00Z",
              "sessions": [
                {
                  "id": 1,
                  "title": "Session 1",
                  "session_order": 1,
                  "exercises_count": 3,
                  "created_at": "2024-01-01T00:00:00Z",
                  "updated_at": "2024-01-01T00:00:00Z",
                  "exercises": [
                    {
                      "id": 1,
                      "exercise_name": "Bench Press",
                      "exercise_type": 1,
                      "exercise_category": 1,
                      "sets": "3",
                      "reps_or_time": "12",
                      "reps_time_type": "reps",
                      "exercise_details": "Detailed exercise instructions...",
                      "rest_duration_minutes": 2,
                      "rest_duration_seconds": 0,
                      "label": "A",
                      "label_number": "1",
                      "is_linked": false,
                      "exercise_order": 1,
                      "video": {
                        "id": 1,
                        "name": "Bench Press Tutorial",
                        "url": "https://example.com/video.mp4",
                        "video_type": "tutorial",
                        "video_category": 1
                      }
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  }
}
```

**Error Responses**

**400 Bad Request - Invalid Split ID**
```json
{
  "error": true,
  "message": "Invalid split ID provided"
}
```

**401 Unauthorized**
```json
{
  "error": true,
  "message": "Unauthorized"
}
```

**404 Not Found**
```json
{
  "error": true,
  "message": "Split not found"
}
```

**500 Internal Server Error**
```json
{
  "error": true,
  "message": "Failed to get program preview"
}
```

## Data Structure

### Split Object
- `id`: Split unique identifier
- `title`: Split title
- `equipment_required`: Required equipment for the split
- `full_price`: One-time purchase price
- `subscription`: Subscription price
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### Program Object
- `id`: Program unique identifier
- `name`: Program name
- `description`: Program description
- `type`: Program type (e.g., "strength", "cardio")
- `currency`: Price currency
- `image_url`: Program image URL
- `status`: Program status
- `approval_date`: Program approval date
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### Trainer Object
- `id`: Trainer unique identifier
- `full_name`: Trainer's full name
- `first_name`: Trainer's first name
- `last_name`: Trainer's last name
- `photo`: Trainer's photo URL
- `email`: Trainer's email

### Week Object
- `id`: Week unique identifier
- `title`: Week title
- `week_order`: Week order number
- `days_count`: Number of days in the week
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp
- `days`: Array of day objects

### Day Object
- `id`: Day unique identifier
- `title`: Day title
- `is_rest_day`: Whether it's a rest day
- `day_order`: Day order number
- `sessions_count`: Number of sessions in the day
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp
- `sessions`: Array of session objects

### Session Object
- `id`: Session unique identifier
- `title`: Session title
- `session_order`: Session order number
- `exercises_count`: Number of exercises in the session
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp
- `exercises`: Array of exercise objects

### Exercise Object
- `id`: Exercise unique identifier
- `exercise_name`: Exercise name
- `exercise_type`: Exercise type ID
- `exercise_category`: Exercise category ID
- `sets`: Number of sets
- `reps_or_time`: Reps or time value
- `reps_time_type`: Type of measurement ("reps" or "time")
- `exercise_details`: Detailed exercise instructions
- `rest_duration_minutes`: Rest duration in minutes
- `rest_duration_seconds`: Rest duration in seconds
- `label`: Exercise label (A, B, C, etc.)
- `label_number`: Exercise label number
- `is_linked`: Whether exercise is linked to another
- `exercise_order`: Exercise order number
- `video`: Video object (if available)

### Video Object
- `id`: Video unique identifier
- `name`: Video name
- `url`: Video URL
- `video_type`: Video type
- `video_category`: Video category ID

## Usage Examples

### Frontend Implementation
```typescript
import { useProgramPreview } from '@/hooks/useProgramPreview';

const ProgramPreviewPage = () => {
  const { splitId } = useParams<{ splitId: string }>();
  const { data, loading, error } = useProgramPreview(splitId || '');

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!data?.split) return <div>No data found</div>;

  return (
    <div>
      <h1>{data.split.program.name}</h1>
      <p>{data.split.program.description}</p>
      {/* Render weeks, days, exercises */}
    </div>
  );
};
```

### API Call Example
```javascript
const response = await fetch('/v2/api/kanglink/custom/preview/program/123', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer your-token-here',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
```

## Notes

1. **Authentication Required**: All requests must include a valid authentication token
2. **Role-based Access**: Users must have `member`, `trainer`, or `super_admin` role
3. **Data Hierarchy**: The API returns a complete hierarchy: Split → Program → Trainer → Weeks → Days → Sessions → Exercises → Videos
4. **Video Support**: Exercise videos are included when available
5. **Preview Limitation**: This endpoint is designed for preview purposes and may not include all program data
6. **Performance**: The endpoint performs multiple joins to fetch complete data in a single request

## Related Files

- **Backend Route**: `mtpbk/custom/ksl_be/routes/preview_program.js`
- **Frontend Hook**: `mtpbk/custom/ksl_be/frontend/src/hooks/useProgramPreview.ts`
- **Frontend Page**: `mtpbk/custom/ksl_be/frontend/src/pages/Athlete/View/ViewAthleteProgramPreviewPage.tsx`
- **Test File**: `mtpbk/custom/ksl_be/tests/preview_program.test.js` 