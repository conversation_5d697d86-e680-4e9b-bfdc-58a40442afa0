# Notification System Documentation

## Overview

The notification system provides real-time notifications for athletes (members) about various events in the platform. It supports multiple notification types, categories, and provides a comprehensive API for managing notifications.

## Database Schema

### Table: `kanglink_notification`

```sql
CREATE TABLE kanglink_notification (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  sender_id INT NULL,
  related_id INT NULL,
  related_type ENUM('enrollment', 'program', 'split', 'exercise', 'payment', 'user', 'general', 'refund_request', 'post_feed') NULL,
  notification_type ENUM(
    'exercise_completed', 
    'day_completed', 
    'week_completed', 
    'program_completed', 
    'milestone_reached',
    'new_enrollment',
    'payment_received',
    'program_updated',
    'athlete_message',
    'system_alert',
    'refund_requested',
    'refund_approved',
    'refund_rejected',
    'post_feed_created',
    'post_feed_comment'
  ) NOT NULL,
  category ENUM('progress', 'enrollment', 'payment', 'communication', 'system', 'general', 'refund', 'social') NOT NULL DEFAULT 'general',
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSON NULL,
  is_read BOOLEAN NOT NULL DEFAULT FALSE,
  read_at DATETIME NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id),
  INDEX idx_sender_id (sender_id),
  INDEX idx_notification_type (notification_type),
  INDEX idx_category (category),
  INDEX idx_is_read (is_read),
  INDEX idx_created_at (created_at),
  INDEX idx_related (related_type, related_id)
);
```

## Notification Types

### Progress Notifications
- **`exercise_completed`** - When an athlete completes an exercise
- **`day_completed`** - When an athlete completes a day of training
- **`week_completed`** - When an athlete completes a week of training
- **`program_completed`** - When an athlete completes an entire program
- **`milestone_reached`** - When an athlete reaches a milestone (e.g., 50% completion)

### Enrollment Notifications
- **`new_enrollment`** - When an athlete enrolls in a program

### Payment Notifications
- **`payment_received`** - When a payment is processed

### Program Notifications
- **`program_updated`** - When a trainer updates a program

### Communication Notifications
- **`athlete_message`** - When an athlete receives a message

### System Notifications
- **`system_alert`** - System-wide alerts for admins

### Refund Notifications
- **`refund_requested`** - When an athlete requests a refund
- **`refund_approved`** - When a refund is approved
- **`refund_rejected`** - When a refund is rejected

### Social Notifications
- **`post_feed_created`** - When a trainer creates a post
- **`post_feed_comment`** - When someone comments on a post

## API Endpoints

### Athlete Notifications

#### Get Notifications
```http
GET /v2/api/kanglink/custom/athlete/notifications
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of notifications per page (default: 20)
- `unread_only` (optional): Filter unread notifications only (default: false)

**Response:**
```json
{
  "error": false,
  "message": "Notifications retrieved successfully",
  "data": {
    "notifications": [
      {
        "id": 1,
        "notification_type": "new_enrollment",
        "category": "enrollment",
        "title": "Enrollment Successful",
        "message": "You have successfully enrolled in \"Test Program - Test Split\"",
        "data": {
          "enrollment_id": 1,
          "program_name": "Test Program",
          "split_name": "Test Split",
          "trainer_name": "John Trainer",
          "payment_amount": 99.99,
          "payment_currency": "USD",
          "payment_type": "stripe"
        },
        "is_read": false,
        "read_at": null,
        "created_at": "2025-01-08T10:30:00Z",
        "sender_id": 2,
        "sender_name": "John Trainer",
        "sender_email": "<EMAIL>",
        "related_id": 1,
        "related_type": "enrollment"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1
    }
  }
}
```

#### Get Unread Count
```http
GET /v2/api/kanglink/custom/athlete/notifications/unread-count
```

**Response:**
```json
{
  "error": false,
  "message": "Unread count retrieved successfully",
  "data": {
    "count": 5
  }
}
```

#### Mark Notification as Read
```http
PUT /v2/api/kanglink/custom/athlete/notifications/:id/read
```

**Response:**
```json
{
  "error": false,
  "message": "Notification marked as read"
}
```

#### Mark All Notifications as Read
```http
PUT /v2/api/kanglink/custom/athlete/notifications/read-all
```

**Response:**
```json
{
  "error": false,
  "message": "All notifications marked as read"
}
```

## Frontend Integration

### Notification Popover Component

The `NotificationPopover` component provides a dropdown interface for viewing and managing notifications.

**Features:**
- Real-time unread count badge
- Mark individual notifications as read
- Mark all notifications as read
- Notification icons based on type
- Time formatting (e.g., "2h ago")
- Loading states
- Error handling

**Usage:**
```tsx
import { NotificationPopover } from "@/components/AthleteHeader/NotificationPopover";

<NotificationPopover
  isOpen={notificationPopoverOpen}
  onClose={handleNotificationClose}
  onToggle={handleNotificationToggle}
  unreadCount={unreadCount}
/>
```

### useNotifications Hook

The `useNotifications` hook provides a comprehensive interface for managing notifications.

**Features:**
- Automatic unread count polling (every 30 seconds)
- Fetch notifications
- Mark notifications as read
- Mark all notifications as read
- Error handling
- Loading states

**Usage:**
```tsx
import { useNotifications } from "@/hooks/useNotifications";

const { 
  unreadCount, 
  notifications, 
  loading, 
  error,
  fetchNotifications,
  markAsRead,
  markAllAsRead,
  refetchUnreadCount 
} = useNotifications();
```

## Backend Service

### NotificationService

The `NotificationService` class provides methods for creating and managing notifications.

**Key Methods:**

#### createEnrollmentNotification
Creates notifications for new enrollments.

```javascript
await notificationService.createEnrollmentNotification(
  enrollmentData,
  athleteData,
  trainerData
);
```

#### createPaymentNotification
Creates notifications for payments.

```javascript
await notificationService.createPaymentNotification(
  paymentData,
  enrollmentData,
  athleteData,
  trainerData
);
```

#### createProgressNotification
Creates notifications for progress updates.

```javascript
await notificationService.createProgressNotification(
  progressData,
  athleteData,
  trainerData,
  notificationType
);
```

#### createRefundRequestNotification
Creates notifications for refund requests.

```javascript
await notificationService.createRefundRequestNotification(
  refundData,
  athleteData,
  trainerData,
  notificationType
);
```

#### createPostFeedNotification
Creates notifications for post feeds.

```javascript
await notificationService.createPostFeedNotification(
  postData,
  trainerData,
  enrolledAthletes,
  notificationType
);
```

#### getUserNotifications
Retrieves user notifications with pagination.

```javascript
const result = await notificationService.getUserNotifications(
  userId,
  page,
  limit,
  unreadOnly
);
```

#### markNotificationAsRead
Marks a specific notification as read.

```javascript
const success = await notificationService.markNotificationAsRead(
  notificationId,
  userId
);
```

#### markAllNotificationsAsRead
Marks all notifications as read for a user.

```javascript
const success = await notificationService.markAllNotificationsAsRead(userId);
```

#### getUnreadNotificationCount
Gets the unread notification count for a user.

```javascript
const count = await notificationService.getUnreadNotificationCount(userId);
```

## Integration Examples

### Enrollment Flow
When an athlete enrolls in a program, notifications are automatically created:

```javascript
// In enrollment.js route
const notificationService = new NotificationService(sdk);

// After successful enrollment
await notificationService.createEnrollmentNotification(
  enrollmentData,
  athleteData,
  trainerData
);
```

### Refund Request Flow
When an athlete requests a refund:

```javascript
// In refund_request.js route
const notificationService = new NotificationService(sdk);

// After creating refund request
await notificationService.createRefundRequestNotification(
  refundData,
  athleteData,
  trainerData,
  "refund_requested"
);
```

### Post Feed Flow
When a trainer creates a post:

```javascript
// In post_feed.js route
const notificationService = new NotificationService(sdk);

// After creating post
await notificationService.createPostFeedNotification(
  postData,
  trainerData,
  enrolledAthletes,
  "post_feed_created"
);
```

## Testing

The notification system includes comprehensive tests covering:

- Notification creation for all types
- API endpoint functionality
- Database schema validation
- Integration with other systems

Run tests with:
```bash
npm test notification_system.test.js
```

## Migration

To update the notification system, run the migration:

```bash
mysql -u username -p database_name < migrations/002_add_missing_notification_types.sql
```

## Best Practices

1. **Always handle errors gracefully** - Notification failures should not break the main functionality
2. **Use appropriate notification types** - Choose the most specific type for better filtering
3. **Include relevant data** - Store important information in the `data` field for context
4. **Test notification creation** - Verify notifications are created in integration tests
5. **Monitor performance** - Large numbers of notifications can impact performance
6. **Clean up old notifications** - Consider archiving or deleting old notifications periodically

## Troubleshooting

### Common Issues

1. **Notifications not appearing**
   - Check if the user_id is correct
   - Verify the notification was actually created in the database
   - Check for JavaScript errors in the frontend

2. **Unread count not updating**
   - Verify the polling interval is working
   - Check if the API endpoint is returning correct data
   - Ensure the frontend is properly handling the response

3. **Performance issues**
   - Consider pagination for large notification lists
   - Add database indexes for frequently queried fields
   - Implement caching for unread counts

### Debug Commands

```sql
-- Check notification types in database
SELECT DISTINCT notification_type FROM kanglink_notification;

-- Check unread notifications for a user
SELECT COUNT(*) FROM kanglink_notification 
WHERE user_id = ? AND is_read = false;

-- Check recent notifications
SELECT * FROM kanglink_notification 
WHERE user_id = ? 
ORDER BY created_at DESC 
LIMIT 10;
``` 