# Athlete Enrollments Endpoint Documentation

## Overview

The athlete enrollments endpoint provides a comprehensive view of all enrollments for a logged-in athlete, categorized by their status and payment type. This endpoint is designed to support the athlete dashboard UI with all necessary data for displaying enrollment cards with proper refund logic and subscription billing status.

## Endpoint Details

```
GET /v2/api/kanglink/custom/athlete/library
```

### Authentication

- **Required**: Yes
- **Roles**: `member`, `trainer`, `super_admin`
- **Token**: Bearer token in Authorization header

### Request Headers

```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Response Structure

The endpoint returns enrollments categorized into four main categories based on business logic:

### Categories

1. **Owned** - One-time payments that are active and paid (lifetime access)
2. **Subscribed** - Subscription payments (active or with billing issues)
3. **Pending Refund** - Enrollments with `status = 'refund'` and `payment_status != 'refunded'`
4. **Refunded** - Enrollments with `status = 'refund'` and `payment_status = 'refunded'`

### Response Format

```json
{
  "error": false,
  "data": {
    "owned": [
      {
        "id": 1,
        "trainer_id": 2,
        "athlete_id": 3,
        "program_id": 4,
        "split_id": 5,
        "payment_type": "one_time",
        "amount": 100.0,
        "currency": "USD",
        "enrollment_date": "2024-01-15T10:30:00Z",
        "status": "active",
        "payment_status": "paid",
        "category": "owned",
        "pricing": {
          "full_price": 100.0,
          "subscription_price": 20.0,
          "price": 20.0,
          "currency": "USD"
        },
        "trainer": {
          "id": 2,
          "email": "<EMAIL>",
          "full_name": "John Trainer",
          "first_name": "John",
          "last_name": "Trainer",
          "photo": "https://example.com/photo.jpg"
        },
        "program": {
          "id": 4,
          "name": "Strength Training Program",
          "type": "strength",
          "description": "Complete strength training program",
          "image_url": "https://example.com/program.jpg"
        },
        "split": {
          "id": 5,
          "title": "Beginner Split",
          "description": "Perfect for beginners",
          "duration_weeks": 8
        },
        "refund_info": {
          "can_request": true,
          "hours_remaining": 18,
          "time_limit_hours": 24
        },
        "subscription_info": {
          "billing_failed": false,
          "stripe_subscription_id": null
        }
      }
    ],
    "subscribed": [],
    "pending_refund": [],
    "refunded": []
  },
  "meta": {
    "total_enrollments": 1,
    "refund_time_limit_hours": 24,
    "categories": {
      "owned": 1,
      "subscribed": 0,
      "pending_refund": 0,
      "refunded": 0
    }
  }
}
```

## Business Logic

### Refund System

- **Eligibility**: Only one-time payments can be refunded
- **Time Limit**: Based on `trainer_payout_time_hours` from payout settings (default: 24 hours)
- **Status Logic**:
  - `status = 'refund'` AND `payment_status != 'refunded'` = Pending refund
  - `status = 'refund'` AND `payment_status = 'refunded'` = Complete refund
- **Calculation**: Hours remaining = `time_limit - hours_since_enrollment`

### Subscription Billing

- **Billing Failed**: Detected when `payment_type = 'subscription'` and `payment_status = 'failed'`
- **Pay Button**: Shown when subscription billing fails
- **Expiry**: Athletes see "expires in X days" for failed billing

### Categorization Logic

```javascript
function getCategoryFromStatus(enrollment) {
  const { status, payment_status, payment_type } = enrollment;

  // Pending refund: status = 'refund' AND payment_status != 'refunded'
  if (status === "refund" && payment_status !== "refunded") {
    return "pending_refund";
  }

  // Complete refund: status = 'refund' AND payment_status = 'refunded'
  if (status === "refund" && payment_status === "refunded") {
    return "refunded";
  }

  // Owned: one_time payment that is active and paid
  if (
    payment_type === "one_time" &&
    status === "active" &&
    payment_status === "paid"
  ) {
    return "owned";
  }

  // Subscribed: subscription payment (active or with billing issues)
  if (payment_type === "subscription") {
    return "subscribed";
  }

  // Default fallback
  return payment_type === "subscription" ? "subscribed" : "owned";
}
```

## Data Sources

The endpoint performs comprehensive joins across multiple tables:

### Primary Tables

- `kanglink_enrollment` - Core enrollment data
- `kanglink_split` - Split details and pricing
- `kanglink_program` - Program information
- `kanglink_user` - Trainer information (from JSON `data` field)
- `kanglink_payout_settings` - Refund time limits

### Key SQL Logic

```sql
-- Calculate minimum price (excluding zero values)
CASE
  WHEN s.full_price > 0 AND s.subscription > 0 THEN LEAST(s.full_price, s.subscription)
  WHEN s.full_price > 0 THEN s.full_price
  WHEN s.subscription > 0 THEN s.subscription
  ELSE 0
END as price

-- Calculate refund eligibility
CASE
  WHEN e.payment_type = 'one_time' AND e.status != 'refund' AND e.payment_status = 'paid'
    AND TIMESTAMPDIFF(HOUR, e.created_at, NOW()) <= ${refundTimeHours}
  THEN 1
  ELSE 0
END as can_request_refund

-- Extract trainer info from JSON data field
JSON_UNQUOTE(JSON_EXTRACT(trainer.data, '$.full_name')) as trainer_full_name
```

## UI Integration Guidelines

### Owned Programs

- Show **"Refund"** button if `refund_info.can_request` is true
- Display **"X hours remaining"** with `refund_info.hours_remaining`
- Show **"View"** button for accessing content
- Badge: "Lifetime Access"

### Subscribed Programs

- Show **"Pay"** button if `subscription_info.billing_failed` is true
- Display **"Billing Failed - Expires Soon"** for failed billing
- Show **"View"** button for accessing content
- Badge: "Subscription"

### Pending Refund

- Show **"Refund Status: Pending"** message
- Display refund request date
- Disable other actions
- Badge: "Refund Pending"

### Refunded

- Show **"Refunded"** status
- Display refund completion date
- No action buttons available
- Badge: "Refunded"

## Error Responses

### 401 Unauthorized

```json
{
  "error": true,
  "message": "Authentication required"
}
```

### 500 Internal Server Error

```json
{
  "error": true,
  "message": "Failed to get enrollments"
}
```

## Performance Considerations

- **Single Query**: Uses one optimized SQL query with joins
- **Computed Fields**: Calculations done in SQL for better performance
- **Indexed Lookups**: Uses indexed fields for athlete filtering
- **Clean Response**: Removes redundant fields to reduce payload size

## Database Requirements

### Model Updates

The `enrollment` model includes the `refund` status:

```javascript
mapping: "active:Active,expired:Expired,cancelled:Cancelled,pending:Pending,refund:Refund";
```

### Required Indexes

- `kanglink_enrollment.athlete_id` (for filtering)
- `kanglink_enrollment.status` (for categorization)
- `kanglink_enrollment.payment_status` (for refund logic)
- `kanglink_enrollment.created_at` (for refund time calculations)

## Testing

The endpoint includes comprehensive test coverage for:

- Empty enrollments response
- Owned enrollment categorization
- Subscribed enrollment categorization
- Pending refund categorization
- Completed refund categorization
- Authentication requirements

## Related Endpoints

- `POST /v2/api/kanglink/custom/athlete/enrollment` - Create new enrollment
- `GET /v2/api/kanglink/custom/athlete/enrollment/:id/content` - Access enrollment content
- `POST /v2/api/kanglink/custom/enrollment/:id/cancel` - Cancel enrollment/subscription
