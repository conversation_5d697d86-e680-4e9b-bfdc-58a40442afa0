# Program Status Update with Notifications

## Overview

The program status update endpoint allows super admins to update program statuses and automatically sends notifications to trainers when their programs are rejected, archived, or deleted.

## Endpoint

```
PUT /v2/api/kanglink/custom/super_admin/programs/:program_id/status
```

## Authentication

Requires super_admin role authentication via TokenMiddleware.

## Request Parameters

### Path Parameters
- `program_id` (number, required): The ID of the program to update

### Request Body
```json
{
  "status": "string",           // Required: New status for the program
  "rejection_reason": "string"  // Required for rejected/archived/deleted status
}
```

### Valid Status Values
- `draft`
- `pending_approval`
- `live`
- `published`
- `rejected`
- `archived`
- `deleted`

## Response Format

### Success Response (200)
```json
{
  "error": false,
  "message": "Program status updated to [status] successfully",
  "data": {
    "program_id": 123,
    "status": "rejected",
    "trainer": {
      "id": 456,
      "email": "<EMAIL>",
      "name": "<PERSON>"
    },
    "updated_at": "2024-01-01 12:00:00",
    "rejection_reason": "Program does not meet quality standards"
  }
}
```

### Error Responses

#### Invalid Program ID (400)
```json
{
  "error": true,
  "message": "Invalid program ID provided"
}
```

#### Invalid Status (400)
```json
{
  "error": true,
  "message": "Invalid status. Must be one of: draft, pending_approval, live, published, rejected, archived, deleted"
}
```

#### Missing Rejection Reason (400)
```json
{
  "error": true,
  "message": "Rejection reason is required when rejecting a program"
}
```

#### Program Not Found (404)
```json
{
  "error": true,
  "message": "Program not found"
}
```

#### Server Error (500)
```json
{
  "error": true,
  "message": "Failed to update program status"
}
```

## Notification System

When a program is updated to `rejected`, `archived`, or `deleted` status, the system automatically:

1. **Creates an in-app notification** for the trainer
2. **Sends an email notification** to the trainer (if email is available)

### Notification Details

#### In-App Notification
- **Type**: `program_rejected`, `program_archived`, or `program_deleted`
- **Category**: `program`
- **Title**: "Program [Status]" (e.g., "Program Rejected")
- **Message**: Includes program name and reason
- **Data**: Contains program details and action information

#### Email Notification
- **Subject**: "Program [Status] - [Program Name]"
- **Content**: Professional email with program details and reason
- **Sender**: System email address

### Notification Data Structure
```json
{
  "user_id": 456,
  "sender_id": 789,
  "related_id": 123,
  "related_type": "program",
  "notification_type": "program_rejected",
  "category": "program",
  "title": "Program Rejected",
  "message": "Your program \"Program Name\" has been rejected: Reason provided",
  "data": {
    "program_id": 123,
    "program_name": "Program Name",
    "status": "rejected",
    "reason": "Reason provided",
    "action_by": 789
  },
  "is_read": false,
  "created_at": "2024-01-01 12:00:00",
  "updated_at": "2024-01-01 12:00:00"
}
```

## Usage Examples

### Reject a Program
```javascript
const response = await fetch('/v2/api/kanglink/custom/super_admin/programs/123/status', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer [super_admin_token]'
  },
  body: JSON.stringify({
    status: 'rejected',
    rejection_reason: 'Program does not meet quality standards'
  })
});
```

### Delete a Program
```javascript
const response = await fetch('/v2/api/kanglink/custom/super_admin/programs/123/status', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer [super_admin_token]'
  },
  body: JSON.stringify({
    status: 'deleted',
    rejection_reason: 'Program violates community guidelines'
  })
});
```

### Archive a Program
```javascript
const response = await fetch('/v2/api/kanglink/custom/super_admin/programs/123/status', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer [super_admin_token]'
  },
  body: JSON.stringify({
    status: 'archived',
    rejection_reason: 'Program is no longer relevant'
  })
});
```

## Frontend Integration

The frontend includes a delete button for all programs and updated modals using the consistent Modal component:

### ProgramTable Component
- Added `onDeleteProgram` prop
- Added delete button with trash icon
- Updated all modals to use the Modal component
- Added delete reason input field

### ListAdminProgramPage Component
- Added `handleDeleteProgram` function
- Passes delete handler to ProgramTable

## Testing

A comprehensive test suite is available at `tests/routes/program_status_update.test.js` that covers:

- Status updates with notifications
- Validation of required fields
- Error handling
- Notification creation verification

## Security Considerations

1. **Role-based access**: Only super_admin users can update program statuses
2. **Input validation**: All inputs are validated before processing
3. **Error handling**: Graceful error handling prevents information leakage
4. **Audit trail**: All actions are logged with user information

## Dependencies

- `NotificationService`: For creating in-app notifications
- `MailService`: For sending email notifications
- `TokenMiddleware`: For authentication
- `UtilService`: For date formatting 