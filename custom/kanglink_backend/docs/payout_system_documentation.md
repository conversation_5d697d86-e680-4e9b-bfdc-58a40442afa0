# Payout System Documentation

## Overview

The Kanglink payout system manages trainer commissions and affiliate earnings with configurable payout settings and automatic commission calculations based on actual revenue received (after discounts).

## System Architecture

### Core Components

1. **Payout Settings** - Configurable commission percentages and payout timing
2. **Commission Calculation** - Automatic commission calculation on enrollment
3. **Commission Tracking** - Track pending and processed payouts
4. **Affiliate System** - Higher commission rates for trainer referrals

### Database Tables

#### `kanglink_payout_settings`
Stores global payout configuration settings.

```sql
CREATE TABLE kanglink_payout_settings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  trainer_payout_time_hours INT NOT NULL DEFAULT 24,
  
  -- Regular enrollment commissions
  split_company_percentage DECIMAL(5,2) NOT NULL DEFAULT 30.00,
  split_trainer_percentage DECIMAL(5,2) NOT NULL DEFAULT 70.00,
  
  -- Affiliate enrollment commissions (higher trainer rate)
  affiliate_company_percentage DECIMAL(5,2) NOT NULL DEFAULT 20.00,
  affiliate_trainer_percentage DECIMAL(5,2) NOT NULL DEFAULT 80.00,
  
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### `kanglink_commission`
Tracks individual commission records for each enrollment.

```sql
CREATE TABLE kanglink_commission (
  id INT AUTO_INCREMENT PRIMARY KEY,
  enrollment_id INT NOT NULL,
  program_id INT NOT NULL,
  split_id INT NOT NULL,
  trainer_id INT NOT NULL,
  athlete_id INT NOT NULL,
  
  -- Commission details
  commission_type ENUM('regular', 'affiliate') NOT NULL,
  total_amount DECIMAL(10,2) NOT NULL, -- Amount after discounts
  original_amount DECIMAL(10,2) DEFAULT NULL, -- Original amount before discounts
  discount_amount DECIMAL(10,2) DEFAULT 0.00, -- Total discount applied
  
  -- Commission breakdown
  company_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  trainer_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  
  -- Affiliate information
  affiliate_code VARCHAR(255) DEFAULT NULL,
  affiliate_user_id INT DEFAULT NULL,
  
  -- Payout tracking
  payout_status ENUM('pending', 'processed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending',
  payout_scheduled_at TIMESTAMP NULL,
  payout_processed_at TIMESTAMP NULL,
  
  currency VARCHAR(3) NOT NULL DEFAULT 'USD',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Commission Structure

### Regular Enrollments
- **Company**: 30%
- **Trainer**: 70%

### Affiliate Enrollments (Trainer Referrals)
- **Company**: 20%
- **Trainer**: 80% (higher rate for bringing referrals)

**Note**: Trainers themselves are the affiliates - there's no separate affiliate referrer percentage.

## API Endpoints

### Super Admin Endpoints

#### Get Payout Settings
```http
GET /v2/api/kanglink/custom/super_admin/payout-settings
Authorization: Bearer {super_admin_token}
```

**Response:**
```json
{
  "error": false,
  "data": {
    "id": 1,
    "trainer_payout_time_hours": 24,
    "split_company_percentage": 30.00,
    "split_trainer_percentage": 70.00,
    "affiliate_company_percentage": 20.00,
    "affiliate_trainer_percentage": 80.00,
    "is_active": true
  }
}
```

#### Update Payout Settings
```http
POST /v2/api/kanglink/custom/super_admin/payout-settings
Authorization: Bearer {super_admin_token}
Content-Type: application/json

{
  "trainer_payout_time_hours": 24,
  "split_company_percentage": 30.00,
  "split_trainer_percentage": 70.00,
  "affiliate_company_percentage": 20.00,
  "affiliate_trainer_percentage": 80.00
}
```

#### Get Pending Payouts
```http
GET /v2/api/kanglink/custom/super_admin/payouts/pending
Authorization: Bearer {super_admin_token}
```

#### Process Commission Payout
```http
PUT /v2/api/kanglink/custom/super_admin/payouts/{commission_id}/process
Authorization: Bearer {super_admin_token}
```

#### Get Trainer Commission Summary
```http
GET /v2/api/kanglink/custom/super_admin/commissions/trainer/{trainer_id}?status=pending
Authorization: Bearer {super_admin_token}
```

#### Get Affiliate Commission Summary
```http
GET /v2/api/kanglink/custom/super_admin/commissions/affiliate/{trainer_id}?status=pending
Authorization: Bearer {super_admin_token}
```

### Trainer Endpoints

#### Get Own Commission Summary
```http
GET /v2/api/kanglink/custom/trainer/commissions/summary?status=pending
Authorization: Bearer {trainer_token}
```

#### Get Own Affiliate Earnings
```http
GET /v2/api/kanglink/custom/trainer/commissions/affiliate?status=processed
Authorization: Bearer {trainer_token}
```

#### Get Commission History
```http
GET /v2/api/kanglink/custom/trainer/commissions/history?page=1&limit=20&status=processed
Authorization: Bearer {trainer_token}
```

## Commission Calculation Process

### 1. Enrollment Creation
When an athlete enrolls in a split:

1. **Discount calculation** applied first (see Discount System Documentation)
2. **Final amount** determined after all discounts
3. **Stripe charged** the final discounted amount
4. **Commission calculated** on the final amount (not original price)

### 2. Commission Calculation Logic

```javascript
// Example: $100 original, $15 discount = $85 final amount

// Regular enrollment commission on $85:
const companyAmount = 85 * 0.30; // $25.50
const trainerAmount = 85 * 0.70; // $59.50

// Affiliate enrollment commission on $85:
const companyAmount = 85 * 0.20; // $17.00
const trainerAmount = 85 * 0.80; // $68.00
```

### 3. Payout Scheduling
- Commissions are scheduled for payout after the configured time (default: 24 hours)
- `payout_scheduled_at` is set to `enrollment_time + payout_hours`
- Super admin can process payouts manually or via automated system

## Integration Points

### Enrollment Process
The enrollment system automatically:
1. Calculates discounts using `DiscountService`
2. Charges customer the final discounted amount via Stripe
3. Creates commission record using `CommissionService`
4. Records coupon usage if applicable

### Stripe Webhooks
Webhook handlers create commission records for:
- Successful one-time payments (`payment_intent.succeeded`)
- Successful subscription payments (`customer.subscription.created`)

### Affiliate System
- Affiliate codes are generated during program approval
- Affiliate validation ensures codes match the correct program
- Higher commission rates automatically applied for affiliate enrollments

## Error Handling

### Commission Creation Failures
- Enrollment succeeds even if commission creation fails
- Failed commissions can be processed via background jobs
- All errors logged for manual review

### Payout Processing Failures
- Failed payouts marked with `payout_status = 'failed'`
- Can be retried manually by super admin
- Detailed error logging for troubleshooting

## Security Considerations

### Access Control
- Super admin: Full access to all payout settings and commission data
- Trainers: Access only to their own commission information
- Athletes: No access to commission data

### Data Validation
- Percentage validation ensures splits add up to 100%
- Amount validation prevents negative commissions
- Affiliate code validation prevents unauthorized usage

## Monitoring and Reporting

### Key Metrics
- Total pending payouts
- Commission amounts by trainer
- Affiliate conversion rates
- Average commission per enrollment

### Audit Trail
- All commission calculations logged
- Payout processing history maintained
- Discount application details stored

## Testing

Comprehensive test suite available in:
- `tests/payout_system.test.js`
- `tests/enrollment_commission.test.js`

Run tests with:
```bash
node test_runner.js payout_system.test.js
```
