# Discount API Quick Reference

## Base URL

```
/v2/api/kanglink/custom/trainer/programs/{programId}/discounts
```

## Authentication

```
Authorization: Bearer <jwt_token>
```

## Endpoints Summary

| Method | Endpoint     | Purpose                | Status Codes            |
| ------ | ------------ | ---------------------- | ----------------------- |
| GET    | `/discounts` | Fetch discount config  | 200, 404, 401/403       |
| POST   | `/discounts` | Create discount config | 201, 409, 400, 404, 500 |
| PUT    | `/discounts` | Update discount config | 200, 404, 400, 500      |
| DELETE | `/discounts` | Delete discount config | 200, 404, 401/403, 500  |

## Request/Response Examples

### GET - Fetch Configuration

```bash
curl -X GET \
  "http://localhost:5172/v2/api/kanglink/custom/trainer/programs/123/discounts" \
  -H "Authorization: Bearer your_jwt_token"
```

**Response:**

```json
{
  "error": false,
  "data": {
    "id": 1,
    "affiliateLink": "https://example.com/affiliate",
    "saleDiscount": {
      "type": "percentage",
      "value": 10,
      "applyToAll": true
    },
    "subscriptionDiscounts": [
      {
        "id": 1,
        "tierId": 1,
        "discountType": "percentage",
        "discountValue": 15
      }
    ],
    "fullPriceDiscounts": [
      {
        "id": 2,
        "tierId": 1,
        "discountType": "fixed",
        "discountValue": 50
      }
    ],
    "promoCode": {
      "id": 1,
      "code": "SAVE20",
      "discountType": "percentage",
      "discountValue": 20,
      "appliesTo": {
        "subscription": true,
        "fullPayment": true
      },
      "isActive": true,
      "expiryDate": "2024-12-31T23:59:59.000Z",
      "usageLimit": 100,
      "usedCount": 5
    },
    "lastUpdated": "2024-01-15T10:30:00.000Z"
  }
}
```

### POST - Create Configuration

```bash
curl -X POST \
  "http://localhost:5172/v2/api/kanglink/custom/trainer/programs/123/discounts" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "affiliateLink": "https://example.com/affiliate",
    "saleDiscount": {
      "type": "percentage",
      "value": 10,
      "applyToAll": true
    },
    "subscriptionDiscounts": [
      {
        "tierId": 1,
        "discountType": "percentage",
        "discountValue": 15
      }
    ],
    "fullPriceDiscounts": [
      {
        "tierId": 1,
        "discountType": "fixed",
        "discountValue": 50
      }
    ],
    "promoCode": {
      "code": "SAVE20",
      "discountType": "percentage",
      "discountValue": 20,
      "appliesTo": {
        "subscription": true,
        "fullPayment": true
      },
      "isActive": true,
      "expiryDate": "2024-12-31T23:59:59.000Z",
      "usageLimit": 100
    }
  }'
```

**Response (201):**

```json
{
  "error": false,
  "message": "Discount configuration created successfully",
  "data": {
    "programId": 123,
    "programDiscountId": 1,
    "subscriptionDiscountsCount": 1,
    "fullPriceDiscountsCount": 1,
    "couponId": 1,
    "createdAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### PUT - Update Configuration

```bash
curl -X PUT \
  "http://localhost:5172/v2/api/kanglink/custom/trainer/programs/123/discounts" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "affiliateLink": "https://example.com/new-affiliate",
    "saleDiscount": {
      "type": "percentage",
      "value": 15,
      "applyToAll": false
    }
  }'
```

**Response (200):**

```json
{
  "error": false,
  "message": "Discount settings updated successfully",
  "data": {
    "programId": 123,
    "lastUpdated": "2024-01-15T11:00:00.000Z"
  }
}
```

### DELETE - Remove Configuration

```bash
curl -X DELETE \
  "http://localhost:5172/v2/api/kanglink/custom/trainer/programs/123/discounts" \
  -H "Authorization: Bearer your_jwt_token"
```

**Response (200):**

```json
{
  "error": false,
  "message": "Discount configuration deleted successfully",
  "data": {
    "programId": 123,
    "deletionSummary": {
      "programDiscountDeleted": true,
      "discountsDeleted": 2,
      "couponsDeleted": 1,
      "couponUsagesDeleted": 5
    },
    "deletedAt": "2024-01-15T11:30:00.000Z"
  }
}
```

## Error Responses

### 400 - Bad Request

```json
{
  "error": true,
  "message": "Program ID is required"
}
```

### 401/403 - Unauthorized

```json
{
  "error": true,
  "message": "Program not found or access denied"
}
```

### 404 - Not Found

```json
{
  "error": true,
  "message": "No discount configuration found for this program"
}
```

### 409 - Conflict (POST only)

```json
{
  "error": true,
  "message": "Discount configuration already exists for this program. Use PUT to update."
}
```

### 500 - Validation Error

```json
{
  "error": true,
  "message": "Sale discount percentage cannot exceed 100%"
}
```

## Validation Rules

### Discount Types

- **Type**: Must be `"fixed"` or `"percentage"`
- **Fixed**: Positive numbers only
- **Percentage**: 0-100 range

### Promo Codes

- **Length**: 3-50 characters
- **Characters**: Alphanumeric, hyphens, underscores only (`/^[A-Za-z0-9_-]+$/`)
- **Uniqueness**: Must be unique across all active coupons
- **Expiry**: Must be future date (if provided)
- **Usage Limit**: Positive integer (if provided)

### Split IDs

- Must exist in the program's splits
- Validated against `kanglink_split` table

## Database Tables

| Table                       | Purpose                  |
| --------------------------- | ------------------------ |
| `kanglink_program_discount` | Program-level settings   |
| `kanglink_discount`         | Split-specific discounts |
| `kanglink_coupon`           | Promotional codes        |
| `kanglink_coupon_usage`     | Usage tracking           |

## Frontend Integration

### TypeScript Interface

```typescript
interface ProgramDiscountConfig {
  id?: number;
  affiliateLink: string;
  saleDiscount?: {
    type: "fixed" | "percentage";
    value: number;
    applyToAll: boolean;
  };
  subscriptionDiscounts: Array<{
    id?: number;
    tierId: number;
    discountType: "fixed" | "percentage";
    discountValue: number;
  }>;
  fullPriceDiscounts: Array<{
    id?: number;
    tierId: number;
    discountType: "fixed" | "percentage";
    discountValue: number;
  }>;
  promoCode?: {
    id?: number;
    code: string;
    discountType: "fixed" | "percentage";
    discountValue: number;
    appliesTo: {
      subscription: boolean;
      fullPayment: boolean;
    };
    isActive: boolean;
    expiryDate?: string;
    usageLimit?: number;
    usedCount?: number;
  };
  lastUpdated?: string;
}
```

### React Hook Usage

```typescript
const {
  discountConfig,
  loading,
  error,
  fetchDiscountConfig,
  createDiscountConfig,
  updateDiscountConfig,
  deleteDiscountConfig,
} = useDiscountManager({ programId: 123, authToken: "your_token" });
```

## Testing

### Test Server

```bash
# Start the server
npm start

# Run tests
node test_runner.js --path custom/ksl_be/tests/routes --pattern discount
```

### Test Data

- **Program ID**: Use existing program ID from your database
- **Split IDs**: Use valid split IDs that belong to the program
- **Auth Token**: Get from trainer login endpoint

## Common Use Cases

1. **Create Basic Discount**: POST with affiliate link only
2. **Add Sale Discount**: POST/PUT with saleDiscount object
3. **Set Split Discounts**: POST/PUT with subscription/fullPrice arrays
4. **Create Promo Code**: POST/PUT with promoCode object
5. **Update Existing**: PUT with partial data
6. **Remove All**: DELETE to clear configuration

For detailed implementation examples, see `frontend_discount_implementation.md`.
