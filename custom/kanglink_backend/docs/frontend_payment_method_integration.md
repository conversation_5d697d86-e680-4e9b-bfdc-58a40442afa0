# Frontend PaymentMethod Integration Guide

## Quick Start

### 1. Install Required Dependencies

```bash
npm install @stripe/stripe-js @stripe/react-stripe-js
```

### 2. API Endpoints Summary

| Method | Endpoint | Purpose |
|--------|----------|---------|
| POST | `/v1/api/kanglink/member/lambda/stripe/customer/payment-method/attach` | Attach PaymentMethod to customer |
| POST | `/v1/api/kanglink/member/lambda/stripe/customer/payment-method/detach` | Detach PaymentMethod from customer |
| GET | `/v1/api/kanglink/member/lambda/stripe/customer/payment-methods` | List customer's PaymentMethods |

### 3. Basic Usage

```typescript
// Attach a PaymentMethod
const response = await fetch('/v1/api/kanglink/member/lambda/stripe/customer/payment-method/attach', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authToken}`
  },
  body: JSON.stringify({
    payment_method_id: 'pm_1Rgx95BgOlWo0lDUfWEA0vpj'
  })
});

const result = await response.json();
```

## Complete Integration Example

### SDK Service Class

```typescript
// services/paymentMethodService.ts
export class PaymentMethodService {
  private baseUrl = '/v1/api/kanglink/member/lambda/stripe/customer';

  private async request(endpoint: string, options: RequestInit = {}) {
    const token = localStorage.getItem('authToken'); // Adjust based on your auth system
    
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'Request failed');
    }
    
    return data;
  }

  async attachPaymentMethod(paymentMethodId: string) {
    return this.request('/payment-method/attach', {
      method: 'POST',
      body: JSON.stringify({ payment_method_id: paymentMethodId }),
    });
  }

  async detachPaymentMethod(paymentMethodId: string) {
    return this.request('/payment-method/detach', {
      method: 'POST',
      body: JSON.stringify({ payment_method_id: paymentMethodId }),
    });
  }

  async listPaymentMethods(type = 'card', limit = 10) {
    return this.request(`/payment-methods?type=${type}&limit=${limit}`);
  }
}
```

### React Hook

```typescript
// hooks/usePaymentMethods.ts
import { useState, useCallback, useEffect } from 'react';
import { PaymentMethodService } from '../services/paymentMethodService';

export const usePaymentMethods = () => {
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const service = new PaymentMethodService();

  const loadPaymentMethods = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await service.listPaymentMethods();
      setPaymentMethods(result.data.payment_methods);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load payment methods');
    } finally {
      setLoading(false);
    }
  }, []);

  const attachPaymentMethod = useCallback(async (paymentMethodId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      await service.attachPaymentMethod(paymentMethodId);
      await loadPaymentMethods(); // Refresh the list
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to attach payment method');
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadPaymentMethods]);

  const detachPaymentMethod = useCallback(async (paymentMethodId: string) => {
    setLoading(true);
    setError(null);
    
    try {
      await service.detachPaymentMethod(paymentMethodId);
      await loadPaymentMethods(); // Refresh the list
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to detach payment method');
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadPaymentMethods]);

  useEffect(() => {
    loadPaymentMethods();
  }, [loadPaymentMethods]);

  return {
    paymentMethods,
    loading,
    error,
    attachPaymentMethod,
    detachPaymentMethod,
    refreshPaymentMethods: loadPaymentMethods,
  };
};
```

### Card Management Component

```typescript
// components/PaymentMethodManager.tsx
import React from 'react';
import { usePaymentMethods } from '../hooks/usePaymentMethods';

export const PaymentMethodManager: React.FC = () => {
  const { 
    paymentMethods, 
    loading, 
    error, 
    detachPaymentMethod 
  } = usePaymentMethods();

  const handleDeleteCard = async (paymentMethodId: string) => {
    if (confirm('Are you sure you want to remove this payment method?')) {
      await detachPaymentMethod(paymentMethodId);
    }
  };

  if (loading) return <div>Loading payment methods...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="payment-methods">
      <h3>Saved Payment Methods</h3>
      
      {paymentMethods.length === 0 ? (
        <p>No saved payment methods</p>
      ) : (
        <div className="payment-method-list">
          {paymentMethods.map((pm) => (
            <div key={pm.id} className="payment-method-card">
              <div className="card-info">
                <span className="brand">{pm.card.brand.toUpperCase()}</span>
                <span className="last4">•••• {pm.card.last4}</span>
                <span className="expiry">{pm.card.exp_month}/{pm.card.exp_year}</span>
              </div>
              <button 
                onClick={() => handleDeleteCard(pm.id)}
                className="delete-button"
              >
                Remove
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
```

### Enrollment Flow Integration

```typescript
// components/EnrollmentFlow.tsx
import React, { useState } from 'react';
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { usePaymentMethods } from '../hooks/usePaymentMethods';

export const EnrollmentFlow: React.FC<{ splitId: number }> = ({ splitId }) => {
  const stripe = useStripe();
  const elements = useElements();
  const { paymentMethods, attachPaymentMethod } = usePaymentMethods();
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string | null>(null);
  const [useNewCard, setUseNewCard] = useState(false);

  const handleEnrollment = async () => {
    let paymentMethodId = selectedPaymentMethod;

    // If using a new card, create and attach it
    if (useNewCard && stripe && elements) {
      const cardElement = elements.getElement(CardElement);
      
      if (!cardElement) return;

      const { paymentMethod, error } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
      });

      if (error) {
        console.error('Error creating payment method:', error);
        return;
      }

      // Attach the new payment method to customer
      const attached = await attachPaymentMethod(paymentMethod.id);
      if (!attached) return;

      paymentMethodId = paymentMethod.id;
    }

    // Proceed with enrollment using paymentMethodId
    await enrollInProgram(splitId, paymentMethodId);
  };

  return (
    <div className="enrollment-flow">
      <h3>Choose Payment Method</h3>
      
      {paymentMethods.length > 0 && (
        <div className="existing-cards">
          <h4>Use Saved Card</h4>
          {paymentMethods.map((pm) => (
            <label key={pm.id} className="payment-method-option">
              <input
                type="radio"
                name="paymentMethod"
                value={pm.id}
                checked={selectedPaymentMethod === pm.id}
                onChange={(e) => {
                  setSelectedPaymentMethod(e.target.value);
                  setUseNewCard(false);
                }}
              />
              <span>{pm.card.brand.toUpperCase()} •••• {pm.card.last4}</span>
            </label>
          ))}
        </div>
      )}
      
      <div className="new-card-option">
        <label className="payment-method-option">
          <input
            type="radio"
            name="paymentMethod"
            checked={useNewCard}
            onChange={() => {
              setUseNewCard(true);
              setSelectedPaymentMethod(null);
            }}
          />
          <span>Use New Card</span>
        </label>
        
        {useNewCard && (
          <div className="card-element-container">
            <CardElement />
          </div>
        )}
      </div>
      
      <button 
        onClick={handleEnrollment}
        disabled={!selectedPaymentMethod && !useNewCard}
        className="enroll-button"
      >
        Enroll Now
      </button>
    </div>
  );
};
```

## Error Handling Best Practices

```typescript
// utils/errorHandling.ts
export const handlePaymentMethodError = (error: any) => {
  if (error.message.includes('already attached')) {
    return 'This payment method is already saved to your account.';
  }
  
  if (error.message.includes('not found')) {
    return 'Payment method not found. Please try again.';
  }
  
  if (error.message.includes('Unauthorized')) {
    return 'Please log in to manage payment methods.';
  }
  
  return 'Something went wrong. Please try again.';
};
```

## Testing

```typescript
// __tests__/paymentMethods.test.ts
import { PaymentMethodService } from '../services/paymentMethodService';

// Mock the fetch function
global.fetch = jest.fn();

describe('PaymentMethodService', () => {
  const service = new PaymentMethodService();
  
  beforeEach(() => {
    (fetch as jest.Mock).mockClear();
  });

  it('should attach payment method successfully', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        error: false,
        message: 'PaymentMethod attached successfully',
        data: { payment_method: { id: 'pm_test' } }
      })
    });

    const result = await service.attachPaymentMethod('pm_test');
    expect(result.error).toBe(false);
    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('/payment-method/attach'),
      expect.objectContaining({
        method: 'POST',
        body: JSON.stringify({ payment_method_id: 'pm_test' })
      })
    );
  });
});
```

## Environment Configuration

```typescript
// config/stripe.ts
export const stripeConfig = {
  publishableKey: process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY!,
  apiBaseUrl: process.env.REACT_APP_API_BASE_URL || 'http://localhost:5172',
};
```

This documentation provides everything the frontend team needs to integrate the PaymentMethod management endpoints into their application.
