# Landing Page Endpoints Documentation

## Overview
This document describes the 6 high-performance landing page endpoints designed for the KangLink fitness platform. These endpoints provide optimized access to programs and trainers with advanced filtering, search, and personalized recommendations.

## Performance Features
- **Redis Caching**: Frequently accessed data is cached for faster response times
- **Optimized SQL Queries**: Uses JOINs and subqueries for efficient data retrieval
- **Pagination**: All endpoints support pagination to limit data transfer
- **Optional Authentication**: Provides personalized results when authenticated
- **Smart Filtering**: Advanced filtering capabilities with multiple criteria
- **Match Scoring**: AI-powered recommendation algorithm for personalized content

## Endpoints

### 1. Top Rated Programs
**Endpoint**: `GET /v2/api/kanglink/custom/landing/top-rated-programs`

**Description**: Returns the highest-rated programs based on user reviews.

**Authentication**: Optional (Bearer token)

**Query Parameters**:
- `page` (integer, default: 1): Page number for pagination
- `limit` (integer, default: 20, max: 50): Number of items per page
- `category` (string, optional): Filter by program type/category
- `min_rating` (float, default: 0): Minimum rating filter (0-5)

**Response Format**:
```json
{
  "error": false,
  "message": "Top rated programs retrieved successfully",
  "data": [
    {
      "id": 1,
      "user_id": 5,
      "program_name": "Advanced Strength Training",
      "type_of_program": "strength",
      "program_description": "...",
      "target_levels": ["intermediate", "advanced"],
      "currency": "USD",
      "days_for_preview": 7,
      "image": "...",
      "rating": "4.8",
      "review_count": 25,
      "favorite": [123, 456, 789], // Array of user IDs who favorited this program
      "match_score": 85.5, // Only for authenticated users
      "trainer": {
        "id": 5,
        "email": "<EMAIL>",
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe",
        "photo": "..."
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "num_pages": 8,
    "has_next": true,
    "has_prev": false
  }
}
```

**Performance Notes**:
- Cached for 10 minutes for non-authenticated requests
- Requires minimum 3 reviews to be considered "top rated"
- Uses efficient rating calculation with subqueries

**Favorite Information**:
- Each program includes a `favorite` array containing user IDs who have favorited the program
- Empty array `[]` if no users have favorited the program
- Uses efficient GROUP_CONCAT with LEFT JOIN to gather favorite data

### 2. Programs You May Like
**Endpoint**: `GET /v2/api/kanglink/custom/landing/programs-you-may-like`

**Description**: Returns personalized program recommendations based on user preferences.

**Authentication**: Required (Bearer token)

**Query Parameters**:
- `page` (integer, default: 1): Page number for pagination
- `limit` (integer, default: 20, max: 50): Number of items per page
- `min_rating` (float, default: 3.0): Minimum rating filter

**Response Format**: Same as Top Rated Programs, but includes:
- `user_preferences`: Object showing user's fitness goals and level
- `match_score`: Percentage match based on user preferences (0-100)
- `favorite`: Array of user IDs who favorited the program

**Algorithm**: 
- Matches user fitness goals with program types
- Considers user fitness level with program target levels
- Excludes user's own programs
- Sorts by match score, then by rating

### 3. All Programs
**Endpoint**: `GET /v2/api/kanglink/custom/landing/all-programs`

**Description**: Returns all published programs with comprehensive filtering and search.

**Authentication**: Optional (Bearer token)

**Query Parameters**:
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20, max: 50): Items per page
- `search` (string, optional): Search in name, description, type, or trainer name
- `category` (string, optional): Filter by program type
- `level` (string, optional): Filter by target level (beginner/intermediate/expert)
- `min_rating` (float, default: 0): Minimum rating
- `max_rating` (float, default: 5): Maximum rating
- `sort_by` (string, default: 'created_at'): Sort field (created_at/rating/name/popularity)
- `sort_order` (string, default: 'desc'): Sort direction (asc/desc)
- `has_preview` (boolean, optional): Filter programs with preview days

**Response Format**: Same as Top Rated Programs, plus:
- `filters`: Object showing applied filters
- `favorite`: Array of user IDs who favorited the program

### 4. Top Rated Trainers
**Endpoint**: `GET /v2/api/kanglink/custom/landing/top-rated-trainers`

**Description**: Returns the highest-rated trainers based on their program reviews.

**Authentication**: Optional (Bearer token)

**Query Parameters**:
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20, max: 50): Items per page
- `specialization` (string, optional): Filter by trainer specialization
- `min_rating` (float, default: 0): Minimum rating
- `min_experience` (integer, default: 0): Minimum years of experience

**Response Format**:
```json
{
  "error": false,
  "message": "Top rated trainers retrieved successfully",
  "data": [
    {
      "id": 5,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "full_name": "John Doe",
      "photo": "...",
      "bio": "Certified fitness trainer...",
      "specializations": ["strength", "weight_loss"],
      "qualifications": ["NASM-CPT", "CSCS"],
      "years_of_experience": "8",
      "rating": "4.9",
      "review_count": 45,
      "program_count": 12,
      "favorite": [123, 456, 789], // Array of user IDs who favorited this trainer
      "pricing": {
        "full_price": {
          "min": 29.99,
          "max": 199.99
        },
        "subscription_price": {
          "min": 19.99,
          "max": 99.99
        },
        "currency": "USD"
      },
      "price": 19.99, // Minimum price across all trainer's programs
      "match_score": 92.3, // Only for authenticated users
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": { ... }
}
```

**Performance Notes**:
- Cached for 10 minutes for non-authenticated requests
- Requires minimum 1 review to be considered "top rated"
- Uses efficient rating calculation with subqueries

**Favorite Information**:
- Each trainer includes a `favorite` array containing user IDs who have favorited the trainer
- Empty array `[]` if no users have favorited the trainer
- Uses efficient GROUP_CONCAT with LEFT JOIN to gather favorite data

**Pricing Information**:
- Each trainer includes `pricing` object with min/max prices for full and subscription options
- `price` field shows the minimum price across all trainer's published programs
- Only includes pricing for trainers with published programs
- Uses efficient aggregation with LEFT JOIN to gather pricing data

### 5. Trainers You May Like
**Endpoint**: `GET /v2/api/kanglink/custom/landing/trainers-you-may-like`

**Description**: Returns personalized trainer recommendations based on user goals and preferences.

**Authentication**: Required (Bearer token)

**Query Parameters**:
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20, max: 50): Items per page
- `min_rating` (float, default: 3.0): Minimum rating

**Response Format**: Same as Top Rated Trainers, plus:
- `user_preferences`: User's fitness goals and level
- `match_score`: Compatibility score based on specializations and experience
- `favorite`: Array of user IDs who favorited the trainer
- `pricing`: Pricing information for trainer's programs
- `price`: Minimum price across all trainer's programs

### 6. All Trainers
**Endpoint**: `GET /v2/api/kanglink/custom/landing/all-trainers`

**Description**: Returns all active trainers with comprehensive filtering and search.

**Authentication**: Optional (Bearer token)

**Query Parameters**:
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20, max: 50): Items per page
- `search` (string, optional): Search in name, email, bio, or specializations
- `specialization` (string, optional): Filter by specialization
- `min_rating` (float, default: 0): Minimum rating
- `max_rating` (float, default: 5): Maximum rating
- `min_experience` (integer, default: 0): Minimum years of experience
- `max_experience` (integer, default: 50): Maximum years of experience
- `sort_by` (string, default: 'created_at'): Sort field (created_at/rating/name/experience)
- `sort_order` (string, default: 'desc'): Sort direction (asc/desc)
- `has_programs` (boolean, optional): Filter trainers with published programs

**Response Format**: Same as Top Rated Trainers, plus:
- `filters`: Object showing applied filters
- `favorite`: Array of user IDs who favorited the trainer
- `pricing`: Pricing information for trainer's programs
- `price`: Minimum price across all trainer's programs

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": true,
  "message": "Error description"
}
```

Common HTTP status codes:
- `200`: Success
- `400`: Bad Request (missing required parameters, invalid user preferences)
- `401`: Unauthorized (invalid or missing token for protected endpoints)
- `500`: Internal Server Error

## Performance Optimizations

### Database Indexes
Recommended indexes for optimal performance:

```sql
-- Programs table indexes
CREATE INDEX idx_program_status ON kanglink_program(status);
CREATE INDEX idx_program_type ON kanglink_program(type_of_program);
CREATE INDEX idx_program_user_id ON kanglink_program(user_id);
CREATE INDEX idx_program_created_at ON kanglink_program(created_at);

-- Post feed indexes for ratings
CREATE INDEX idx_post_feed_type_rating ON kanglink_post_feed(post_type, rating);
CREATE INDEX idx_post_feed_program_id ON kanglink_post_feed(program_id);

-- User table indexes
CREATE INDEX idx_user_role_status ON kanglink_user(role_id, status);
CREATE INDEX idx_user_created_at ON kanglink_user(created_at);

-- Preference table indexes
CREATE INDEX idx_preference_user_id ON kanglink_preference(user_id);
```

### Caching Strategy
- **Top Rated Programs**: 10-minute cache for non-authenticated requests
- **Top Rated Trainers**: 10-minute cache for non-authenticated requests
- **Rating Calculations**: 5-minute cache for aggregated ratings
- **User Preferences**: No caching (always fresh for personalization)

### Query Optimization
- Uses subqueries for rating calculations to avoid N+1 problems
- Implements LIMIT with OFFSET for efficient pagination
- Uses LEFT JOINs to include trainers/programs without ratings
- Applies filters at the database level to reduce data transfer

## Usage Examples

### Get top rated programs with authentication:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "https://api.example.com/v2/api/kanglink/custom/landing/top-rated-programs?page=1&limit=10&category=strength"
```

### Search all programs:
```bash
curl "https://api.example.com/v2/api/kanglink/custom/landing/all-programs?search=weight%20loss&min_rating=4.0&sort_by=rating"
```

### Get personalized trainer recommendations:
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "https://api.example.com/v2/api/kanglink/custom/landing/trainers-you-may-like?page=1&limit=5"
```
