# Trainer Forgot Password Implementation

## Overview

This document describes the implementation of the forgot password functionality for trainers in the KSL backend system. The implementation follows a secure OTP-based flow with JWT tokens for session management.

## Flow Description

The forgot password process consists of three main steps:

1. **Email Submission**: User submits email and receives OTP
2. **OTP Verification**: User enters OTP and receives reset token
3. **Password Reset**: User sets new password using reset token

## API Endpoints

### 1. Forgot Password Request

**Endpoint**: `POST /v2/api/kanglink/custom/trainer/forgot-password`

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

**Response** (Success):
```json
{
  "error": false,
  "message": "O<PERSON> sent to your email address",
  "data": {
    "temp_token": "jwt_temporary_token",
    "expires_in": 300
  }
}
```

**Response** (Error):
```json
{
  "error": true,
  "message": "User not found with this email address"
}
```

### 2. OTP Verification

**Endpoint**: `POST /v2/api/kanglink/custom/trainer/verify-otp`

**Request Body**:
```json
{
  "otp": "123456",
  "temp_token": "jwt_temporary_token"
}
```

**Response** (Success):
```json
{
  "error": false,
  "message": "OTP verified successfully",
  "data": {
    "reset_token": "jwt_reset_token",
    "expires_in": 900
  }
}
```

### 3. Password Reset

**Endpoint**: `POST /v2/api/kanglink/custom/trainer/reset-password`

**Request Body**:
```json
{
  "password": "NewPassword123!",
  "confirm_password": "NewPassword123!",
  "reset_token": "jwt_reset_token"
}
```

**Response** (Success):
```json
{
  "error": false,
  "message": "Password reset successfully"
}
```

## Security Features

### Token Management
- **Temporary Token**: 5-minute expiry for OTP verification
- **Reset Token**: 15-minute expiry for password reset
- **JWT Verification**: All tokens are signed and verified

### Validation
- **Email Format**: Validates proper email format
- **Password Strength**: Requires uppercase, lowercase, number, and special character
- **Password Confirmation**: Ensures passwords match
- **OTP Format**: 6-digit numeric code validation

### Database Security
- **Token Storage**: OTP and tokens stored in database with expiration
- **Password Hashing**: Uses secure password hashing service
- **Token Cleanup**: Used tokens are invalidated after successful operations

## Frontend Integration

### ForgotPassword Component
- Handles email submission and OTP entry
- Manages step transitions (email → OTP)
- Stores temporary token for OTP verification
- Navigates to change password on success

### ChangePassword Component
- Validates reset token from localStorage
- Handles password reset with confirmation
- Redirects to login on successful reset
- Cleans up tokens after use

## File Structure

```
mtpbk/custom/ksl_be/
├── lambda/
│   ├── trainer_forgot_password.js    # Email submission endpoint
│   ├── trainer_verify_otp.js         # OTP verification endpoint
│   └── trainer_reset_password.js     # Password reset endpoint
├── roles/
│   └── trainer.js                    # Updated with forgot/reset permissions
├── frontend/src/pages/Trainer/Auth/
│   ├── ForgotPassword.tsx            # Updated frontend component
│   └── ChangePassword.tsx            # Updated frontend component
└── tests/lambda/
    └── trainer_forgot_password_flow.test.js  # Comprehensive tests
```

## Configuration Requirements

### Role Permissions
The trainer role must have the following permissions enabled:
```javascript
canForgot: true,
canReset: true,
```

### Email Service
The system requires a configured email service for sending OTP codes. The email template includes:
- 6-digit OTP code
- 5-minute expiration notice
- Security warning for unauthorized requests

### Database Tables
The implementation uses the existing `tokens` table with:
- `type: 2` for reset tokens
- `code` field for OTP storage
- `expired_at` for token expiration
- `data` field for additional metadata

## Testing

### Automated Tests
Run the comprehensive test suite:
```bash
node mtpbk/custom/ksl_be/tests/lambda/trainer_forgot_password_flow.test.js
```

### Manual Testing
Run the basic API validation:
```bash
node mtpbk/custom/ksl_be/test_forgot_password.js
```

### Test Coverage
- Email validation (format, existence)
- OTP generation and verification
- Token management and expiration
- Password strength validation
- Error handling and edge cases

## Error Handling

### Common Error Responses
- `400`: Invalid input (email format, password strength, etc.)
- `403`: Authentication/authorization errors (invalid tokens, expired OTP)
- `404`: User not found
- `500`: Internal server errors

### Frontend Error Display
- Toast notifications for user feedback
- Form validation messages
- Automatic redirects for invalid states

## Security Considerations

### Rate Limiting
Consider implementing rate limiting for:
- Forgot password requests per email
- OTP verification attempts
- Password reset attempts

### Audit Logging
Log security events:
- Password reset requests
- Failed OTP attempts
- Successful password changes

### Token Security
- Short expiration times
- Secure JWT signing
- Token invalidation after use

## Deployment Notes

1. Ensure email service is properly configured
2. Verify database permissions for token operations
3. Test email delivery in production environment
4. Monitor for failed password reset attempts
5. Set up appropriate logging and alerting

## Future Enhancements

- SMS-based OTP as alternative to email
- Account lockout after multiple failed attempts
- Password history to prevent reuse
- Enhanced email templates with branding
- Multi-language support for error messages
