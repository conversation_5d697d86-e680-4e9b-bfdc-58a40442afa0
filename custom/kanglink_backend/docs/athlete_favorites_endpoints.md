# Athlete Favorites Endpoints

## Overview

The athlete favorites endpoints allow athletes to manage their favorite programs and trainers. The system uses a single polymorphic table (`kanglink_favorite`) to store both program and trainer favorites, with a `favorite_type` field to distinguish between them.

## Database Schema

### Favorites Table

```sql
CREATE TABLE kanglink_favorite (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  favorite_type ENUM('program', 'trainer') NOT NULL,
  favorite_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  CONSTRAINT fk_favorite_user FOREIGN KEY (user_id) REFERENCES kanglink_user(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_favorite (user_id, favorite_type, favorite_id)
);
```

## Endpoints

### Favorite Programs

#### Get Favorite Programs

```
GET /v2/api/kanglink/custom/athlete/favorite/programs
```

**Authentication**: Required (Bearer token)
**Roles**: `member`, `trainer`, `super_admin`

**Response**:

```json
{
  "error": false,
  "data": [
    {
      "favorite_id": 1,
      "favorited_at": "2024-01-15T10:30:00Z",
      "id": 4,
      "name": "Strength Training Program",
      "description": "Complete strength training program",
      "type": "strength",
      "image_url": "https://example.com/program.jpg",
      "status": "active",
      "created_at": "2024-01-10T08:00:00Z",
      "updated_at": "2024-01-12T14:30:00Z",
      "price": 20.0,
      "average_rating": 4.5,
      "review_count": 12,
      "trainer": {
        "id": 2,
        "full_name": "John Trainer",
        "first_name": "John",
        "last_name": "Trainer",
        "photo": "https://example.com/trainer.jpg"
      }
    }
  ]
}
```

#### Add Program to Favorites

```
POST /v2/api/kanglink/custom/athlete/favorite/programs/:programId
```

**Authentication**: Required (Bearer token)
**Roles**: `member`, `trainer`, `super_admin`

**Response**:

```json
{
  "error": false,
  "data": {
    "id": 1,
    "message": "Program added to favorites successfully"
  }
}
```

**Error Responses**:

- `400`: Invalid program ID
- `404`: Program not found or inactive
- `409`: Program already in favorites

#### Remove Program from Favorites

```
DELETE /v2/api/kanglink/custom/athlete/favorite/programs/:programId
```

**Authentication**: Required (Bearer token)
**Roles**: `member`, `trainer`, `super_admin`

**Response**:

```json
{
  "error": false,
  "data": {
    "message": "Program removed from favorites successfully"
  }
}
```

**Error Responses**:

- `400`: Invalid program ID
- `404`: Favorite not found

### Favorite Trainers

#### Get Favorite Trainers

```
GET /v2/api/kanglink/custom/athlete/favorite/trainers
```

**Authentication**: Required (Bearer token)
**Roles**: `member`, `trainer`, `super_admin`

**Response**:

```json
{
  "error": false,
  "data": [
    {
      "favorite_id": 2,
      "favorited_at": "2024-01-16T11:45:00Z",
      "id": 3,
      "email": "<EMAIL>",
      "full_name": "Jane Trainer",
      "first_name": "Jane",
      "last_name": "Trainer",
      "photo": "https://example.com/jane.jpg",
      "bio": "Certified personal trainer with 10 years experience",
      "location": "Los Angeles, CA",
      "average_rating": 4.8,
      "review_count": 25,
      "program_count": 8
    }
  ]
}
```

#### Add Trainer to Favorites

```
POST /v2/api/kanglink/custom/athlete/favorite/trainers/:trainerId
```

**Authentication**: Required (Bearer token)
**Roles**: `member`, `trainer`, `super_admin`

**Response**:

```json
{
  "error": false,
  "data": {
    "id": 2,
    "message": "Trainer added to favorites successfully"
  }
}
```

**Error Responses**:

- `400`: Invalid trainer ID
- `404`: Trainer not found or inactive
- `409`: Trainer already in favorites

#### Remove Trainer from Favorites

```
DELETE /v2/api/kanglink/custom/athlete/favorite/trainers/:trainerId
```

**Authentication**: Required (Bearer token)
**Roles**: `member`, `trainer`, `super_admin`

**Response**:

```json
{
  "error": false,
  "data": {
    "message": "Trainer removed from favorites successfully"
  }
}
```

**Error Responses**:

- `400`: Invalid trainer ID
- `404`: Favorite not found

## Features

### Program Favorites

- **Rich Program Data**: Includes program details, pricing, ratings, and trainer information
- **Price Calculation**: Shows minimum price from all active splits
- **Rating Aggregation**: Displays average rating and review count from post_feed reviews (post_type='review')
- **Trainer Information**: Includes trainer details from user.data JSON field

### Trainer Favorites

- **Comprehensive Trainer Profile**: Full trainer information including bio and location
- **Performance Metrics**: Average rating across all trainer's programs from post_feed reviews
- **Program Statistics**: Count of active programs by the trainer
- **Review Aggregation**: Total review count across all trainer's programs from post_feed

### Data Integrity

- **Unique Constraints**: Prevents duplicate favorites for same user/item combination
- **Foreign Key Constraints**: Ensures data consistency with cascade delete
- **Status Filtering**: Only shows active programs and trainers
- **Polymorphic Design**: Single table handles both program and trainer favorites

## Testing

Run the test suite:

```bash
cd mtpbk/custom/ksl_be/tests
node ../../../test_runner.js athlete_favorites.test.js
```

The test covers:

- Getting empty favorites lists
- Adding programs and trainers to favorites
- Preventing duplicate favorites
- Retrieving favorite lists with proper data structure
- Removing favorites
- Error handling for invalid IDs and non-existent items

## Performance Considerations

- **Single Query Design**: Each endpoint uses one optimized SQL query with joins
- **Indexed Fields**: Database indexes on user_id, favorite_type, and favorite_id
- **Computed Fields**: Ratings and counts calculated in SQL for efficiency
- **JSON Extraction**: Trainer data extracted from user.data JSON field in SQL

## Security

- **Authentication Required**: All endpoints require valid Bearer token
- **User Scoping**: Users can only manage their own favorites
- **Input Validation**: Parameter validation for IDs and types
- **SQL Injection Protection**: Parameterized queries used throughout
