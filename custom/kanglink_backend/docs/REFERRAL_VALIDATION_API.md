# Referral Validation API

This document describes the referral validation endpoint that validates referral codes against program discount settings.

## Endpoint

### Validate Referral Code

**URL:** `GET /v2/api/kanglink/custom/validate-referral`

**Description:** Validates a referral code against a program's discount settings.

**Query Parameters:**
- `program_id` (required): The ID of the program to validate against
- `ref` (required): The referral code to validate

**Authentication:** None required (public endpoint)

## Request Example

```bash
GET /v2/api/kanglink/custom/validate-referral?program_id=123&ref=ABC123
```

## Response Format

### Success Response (Valid Referral)

**Status:** 200 OK

```json
{
  "error": false,
  "message": "Referral code is valid",
  "data": {
    "is_valid": true,
    "program_discount": {
      "id": 1,
      "sale_discount_type": "percentage",
      "sale_discount_value": 10.00,
      "sale_apply_to_all": true
    }
  }
}
```

### Success Response (Invalid Referral)

**Status:** 200 OK

```json
{
  "error": false,
  "message": "Invalid referral code",
  "data": {
    "is_valid": false,
    "reason": "Referral code does not match"
  }
}
```

### Success Response (No Discount Settings)

**Status:** 200 OK

```json
{
  "error": false,
  "message": "No discount settings found for this program",
  "data": {
    "is_valid": false,
    "reason": "No discount settings available for this program"
  }
}
```

### Error Response (Missing Parameters)

**Status:** 400 Bad Request

```json
{
  "error": true,
  "message": "Program ID and referral code are required"
}
```

### Error Response (Program Not Found)

**Status:** 404 Not Found

```json
{
  "error": true,
  "message": "Program not found",
  "data": {
    "is_valid": false,
    "reason": "Program not found"
  }
}
```

### Error Response (Server Error)

**Status:** 500 Internal Server Error

```json
{
  "error": true,
  "message": "Failed to validate referral code"
}
```

## Validation Logic

1. **Program Existence Check:** Verifies that the program exists in the database
2. **Discount Settings Check:** Checks if the program has discount settings in the `program_discount` table
3. **Referral Code Match:** Compares the provided `ref` parameter with the `affiliate_link` field in the program's discount settings

## Possible Reasons for Invalid Referral

- Program does not exist
- Program has no discount settings configured
- Referral code does not match the program's affiliate link
- Missing required parameters

## Usage in Frontend

The frontend uses this endpoint to:

1. **Show validation status** on program pages when a referral code is present in the URL
2. **Display appropriate messages** to users about the validity of their referral code
3. **Apply discounts** when valid referral codes are used during enrollment

## Integration with UI Components

The referral validation is integrated into:

- `SplitCard` component - Shows validation status on split cards
- `ViewAthleteProgramPage` - Shows validation status on program detail pages  
- `ViewAthleteProgramPreviewPage` - Shows validation status on program preview pages

## Testing

See `tests/routes/validate_referral.test.js` for comprehensive test coverage including:

- Missing parameter validation
- Non-existent program handling
- Programs without discount settings
- Invalid referral codes
- Valid referral codes 