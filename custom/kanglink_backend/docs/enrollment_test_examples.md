# Enrollment System Test Examples

## API Testing with cURL

### 1. Get Available Splits
```bash
curl -X GET "http://localhost:3000/v2/api/kanglink/custom/splits" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 2. Get Specific Split Details
```bash
curl -X GET "http://localhost:3000/v2/api/kanglink/custom/splits/123" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 3. Create One-Time Enrollment
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/athlete/enroll" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "split_id": 123,
    "payment_type": "one_time",
    "payment_method_id": "pm_1234567890abcdef"
  }'
```

### 4. Create Subscription Enrollment
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/athlete/enroll" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "split_id": 123,
    "payment_type": "subscription",
    "payment_method_id": "pm_1234567890abcdef"
  }'
```

### 5. Get Athlete Enrollments
```bash
curl -X GET "http://localhost:3000/v2/api/kanglink/custom/athlete/enrollments" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 6. Get Trainer Enrollments
```bash
curl -X GET "http://localhost:3000/v2/api/kanglink/custom/trainer/enrollments" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

### 7. Cancel Enrollment
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/enrollment/456/cancel" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

## JavaScript Test Examples

### Frontend Integration Test
```javascript
// Test enrollment flow
class EnrollmentTester {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  async getAvailableSplits() {
    const response = await fetch(`${this.baseUrl}/v2/api/kanglink/custom/splits`, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.json();
  }

  async enrollInSplit(splitId, paymentType, paymentMethodId) {
    const response = await fetch(`${this.baseUrl}/v2/api/kanglink/custom/athlete/enroll`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        split_id: splitId,
        payment_type: paymentType,
        payment_method_id: paymentMethodId
      })
    });
    return response.json();
  }

  async getMyEnrollments() {
    const response = await fetch(`${this.baseUrl}/v2/api/kanglink/custom/athlete/enrollments`, {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.json();
  }

  async cancelEnrollment(enrollmentId) {
    const response = await fetch(`${this.baseUrl}/v2/api/kanglink/custom/enrollment/${enrollmentId}/cancel`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    return response.json();
  }
}

// Usage example
const tester = new EnrollmentTester('http://localhost:3000', 'your_jwt_token');

// Test complete flow
async function testEnrollmentFlow() {
  try {
    // 1. Get available splits
    console.log('Getting available splits...');
    const splits = await tester.getAvailableSplits();
    console.log('Available splits:', splits);

    if (splits.data && splits.data.length > 0) {
      const firstSplit = splits.data[0];
      
      // 2. Enroll in first split (one-time)
      console.log('Enrolling in split (one-time)...');
      const enrollment = await tester.enrollInSplit(
        firstSplit.id, 
        'one_time', 
        'pm_card_visa' // Stripe test payment method
      );
      console.log('Enrollment result:', enrollment);

      // 3. Get my enrollments
      console.log('Getting my enrollments...');
      const myEnrollments = await tester.getMyEnrollments();
      console.log('My enrollments:', myEnrollments);

      // 4. Cancel enrollment (if successful)
      if (enrollment.data && enrollment.data.enrollment_id) {
        console.log('Cancelling enrollment...');
        const cancellation = await tester.cancelEnrollment(enrollment.data.enrollment_id);
        console.log('Cancellation result:', cancellation);
      }
    }
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testEnrollmentFlow();
```

### Stripe Payment Method Creation Test
```javascript
// Test with Stripe Elements (frontend)
import { loadStripe } from '@stripe/stripe-js';

const stripe = await loadStripe('pk_test_your_publishable_key');

// Create payment method for testing
async function createTestPaymentMethod() {
  const { error, paymentMethod } = await stripe.createPaymentMethod({
    type: 'card',
    card: {
      number: '****************',
      exp_month: 12,
      exp_year: 2025,
      cvc: '123',
    },
  });

  if (error) {
    console.error('Payment method creation failed:', error);
    return null;
  }

  console.log('Payment method created:', paymentMethod.id);
  return paymentMethod.id;
}

// Use in enrollment
async function testEnrollmentWithStripe() {
  const paymentMethodId = await createTestPaymentMethod();
  
  if (paymentMethodId) {
    const enrollment = await tester.enrollInSplit(123, 'subscription', paymentMethodId);
    console.log('Enrollment with Stripe:', enrollment);
  }
}
```

## Database Test Queries

### Check Enrollment Data
```sql
-- View all enrollments with related data
SELECT 
  e.*,
  p.program_name,
  s.title as split_title,
  u1.email as athlete_email,
  u2.email as trainer_email
FROM kanglink_enrollment e
JOIN kanglink_split s ON e.split_id = s.id
JOIN kanglink_program p ON e.program_id = p.id
JOIN kanglink_user u1 ON e.athlete_id = u1.id
JOIN kanglink_user u2 ON e.trainer_id = u2.id
ORDER BY e.created_at DESC;
```

### Check Split Pricing
```sql
-- View splits with pricing information
SELECT 
  s.*,
  p.program_name,
  p.currency,
  u.email as trainer_email
FROM kanglink_split s
JOIN kanglink_program p ON s.program_id = p.id
JOIN kanglink_user u ON p.user_id = u.id
WHERE s.full_price > 0 OR s.subscription > 0;
```

### Check Stripe Integration Data
```sql
-- View Stripe products and prices
SELECT * FROM kanglink_stripe_product WHERE name LIKE '%-%';
SELECT * FROM kanglink_stripe_price WHERE name LIKE '%Monthly%';
```

## Expected Test Results

### Successful One-Time Enrollment
```json
{
  "error": false,
  "message": "Enrollment created successfully",
  "data": {
    "enrollment_id": 123,
    "payment_type": "one_time",
    "amount": 99.99,
    "currency": "USD",
    "status": "pending", // Will become "active" after webhook
    "payment_status": "pending", // Will become "paid" after webhook
    "stripe_subscription_id": null,
    "stripe_payment_intent_id": "pi_1234567890"
  }
}
```

### Successful Subscription Enrollment
```json
{
  "error": false,
  "message": "Enrollment created successfully",
  "data": {
    "enrollment_id": 124,
    "payment_type": "subscription",
    "amount": 29.99,
    "currency": "USD",
    "status": "active", // Active immediately for subscriptions
    "payment_status": "paid", // Paid immediately for subscriptions
    "stripe_subscription_id": "sub_1234567890",
    "stripe_payment_intent_id": null
  }
}
```

### Error Cases
```json
// Already enrolled
{
  "error": true,
  "message": "Already enrolled in this split"
}

// Invalid payment method
{
  "error": true,
  "message": "Payment processing failed: Your card was declined."
}

// Split not found
{
  "error": true,
  "message": "Split not found"
}
```

## Webhook Testing

### Test Webhook Locally
```bash
# Install Stripe CLI
stripe listen --forward-to localhost:3000/v1/api/lambda/stripe/mtp/webhook

# Trigger test events
stripe trigger payment_intent.succeeded
stripe trigger customer.subscription.created
```

### Webhook Payload Examples
```json
// payment_intent.succeeded
{
  "type": "payment_intent.succeeded",
  "data": {
    "object": {
      "id": "pi_1234567890",
      "customer": "cus_1234567890",
      "metadata": {
        "projectId": "kanglink",
        "split_id": "123",
        "athlete_id": "456",
        "trainer_id": "789",
        "payment_type": "one_time"
      }
    }
  }
}
```

## Performance Testing

### Load Testing Script
```javascript
// Test multiple concurrent enrollments
async function loadTest() {
  const promises = [];
  
  for (let i = 0; i < 10; i++) {
    promises.push(
      tester.enrollInSplit(123, 'subscription', 'pm_card_visa')
    );
  }
  
  const results = await Promise.allSettled(promises);
  console.log('Load test results:', results);
}
```

This comprehensive test suite covers all aspects of the enrollment system and can be used to verify functionality during development and deployment.
