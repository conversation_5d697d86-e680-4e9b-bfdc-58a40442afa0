# Discount Preview Endpoint

## Overview

The discount preview endpoint allows users to calculate the final price of a split after applying all applicable discounts and coupons without actually making a purchase.

## Endpoint Details

**URL:** `GET /v2/api/kanglink/custom/discount/preview`

**Authentication:** None required (public endpoint)

## Query Parameters

| Parameter      | Type    | Required | Description                                |
| -------------- | ------- | -------- | ------------------------------------------ |
| `split_id`     | integer | Yes      | The ID of the split to preview pricing for |
| `payment_type` | string  | Yes      | Either "subscription" or "one_time"        |
| `coupon_code`  | string  | No       | Optional coupon code to apply              |

## Response Format

### Success Response (200)

```json
{
  "success": true,
  "error": false,
  "data": {
    "split_id": 1,
    "program_id": 1,
    "program_name": "Test Program",
    "split_title": "Test Split",
    "payment_type": "subscription",
    "original_amount": 50.0,
    "final_amount": 40.0,
    "total_discount_amount": 10.0,
    "savings_percentage": 20,
    "has_discounts": true,
    "applied_discounts": [
      {
        "type": "general",
        "discount_type": "fixed",
        "discount_value": 5.0,
        "discount_amount": 5.0,
        "source": "general_discount"
      },
      {
        "type": "coupon",
        "coupon_id": 1,
        "coupon_code": "SAVE10",
        "discount_type": "fixed",
        "discount_value": 5.0,
        "discount_amount": 5.0,
        "source": "coupon"
      }
    ]
  }
}
```

### Error Responses

#### 400 - Bad Request

```json
{
  "success": false,
  "error": true,
  "message": "split_id is required"
}
```

#### 400 - Invalid Coupon

```json
{
  "success": false,
  "error": true,
  "message": "Invalid coupon code"
}
```

#### 404 - Split Not Found

```json
{
  "success": false,
  "error": true,
  "message": "Split not found"
}
```

#### 500 - Server Error

```json
{
  "success": false,
  "error": true,
  "message": "Failed to calculate discount preview"
}
```

## Usage Examples

### Basic Preview (No Coupon)

```
GET /v2/api/kanglink/custom/discount/preview?split_id=1&payment_type=subscription
```

### Preview with Coupon

```
GET /v2/api/kanglink/custom/discount/preview?split_id=1&payment_type=one_time&coupon_code=SAVE20
```

## Frontend Integration

The frontend can use this endpoint to:

1. Show real-time pricing updates as users select different splits
2. Validate coupon codes before checkout
3. Display savings information to encourage purchases
4. Calculate final amounts for payment processing

## Discount Application Order

The endpoint applies discounts in the following order:

1. **Program Sale Discount** - Applied first to the original amount
2. **General/Split Discount** - Applied to the amount after sale discount
3. **Coupon Discount** - Applied last to the remaining amount

## Notes

- The endpoint does not validate user-specific coupon usage for preview purposes
- Coupon expiry dates and usage limits are still validated
- **Invalid coupons will return a 400 error response instead of discount data**
- The final amount will never go below 0
- Percentage discounts are calculated on the current amount at each step
- Fixed discounts are capped at the current amount to prevent negative values

## Related Services

- `DiscountService` - Handles all discount calculations
- `discount.js` routes - Manages discount configuration
- Split pricing from `kanglink_split` table
- Coupon validation from `kanglink_coupon` table
