# Admin Alerts API Documentation

## Overview

The Admin Alerts API provides endpoints for managing and viewing administrative alerts in the KangLink system. These alerts are designed to notify super administrators about important system events, user activities, and administrative tasks that require attention.

## Base URL

```
/v2/api/kanglink/custom/admin/alerts
```

## Authentication

All endpoints require authentication with a `super_admin` role token.

**Header Required:**
```
Authorization: Bearer <super_admin_token>
```

## Endpoints

### 1. Get Admin Alerts

**Endpoint:** `GET /v2/api/kanglink/custom/admin/alerts`

**Description:** Retrieves a paginated list of administrative alerts with optional filtering.

**Query Parameters:**
- `page` (optional, default: 1): Page number for pagination
- `limit` (optional, default: 20): Number of alerts per page
- `activity_type` (optional): Filter by activity type
- `date_from` (optional): Filter alerts created from this date (YYYY-MM-DD format)
- `date_to` (optional): Filter alerts created until this date (YYYY-MM-DD format)

**Response:**
```json
{
  "error": false,
  "message": "Admin alerts retrieved successfully",
  "data": {
    "alerts": [
      {
        "id": 1,
        "activity_type": "program_approval_pending",
        "title": "Program Approval Pending",
        "description": "Program \"Fitness Program\" by John Trainer is pending approval",
        "metadata": {
          "program_id": 123,
          "program_name": "Fitness Program",
          "trainer_id": 456,
          "trainer_name": "John Trainer",
          "trainer_email": "<EMAIL>",
          "created_at": "2024-01-15T10:30:00Z"
        },
        "actor_name": "John Trainer",
        "actor_email": "<EMAIL>",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 45,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

**Activity Types:**
- `program_approval_pending`: New programs awaiting admin approval
- `new_athlete_signup`: New athlete registrations
- `new_trainer_signup`: New trainer registrations
- `new_transaction`: New payment transactions
- `refund_requested`: Refund requests from athletes
- `refund_approved`: Approved refunds
- `refund_rejected`: Rejected refunds
- `low_rated_trainer`: Trainers with low ratings
- `system_alert`: System-generated alerts

### 2. Get Admin Alert Statistics

**Endpoint:** `GET /v2/api/kanglink/custom/admin/alerts/stats`

**Description:** Retrieves statistics about admin alerts including counts by type and recent activity.

**Response:**
```json
{
  "error": false,
  "message": "Admin alert statistics retrieved successfully",
  "data": {
    "alert_type_stats": [
      {
        "activity_type": "new_athlete_signup",
        "count": 15
      },
      {
        "activity_type": "program_approval_pending",
        "count": 8
      },
      {
        "activity_type": "new_transaction",
        "count": 25
      }
    ],
    "recent_alerts_count": 48,
    "unread_alerts_count": 12
  }
}
```

### 3. Create System Alert

**Endpoint:** `POST /v2/api/kanglink/custom/admin/alerts/system`

**Description:** Creates a new system alert that will be visible to all super administrators.

**Request Body:**
```json
{
  "title": "System Maintenance",
  "description": "Scheduled maintenance will occur on Sunday at 2 AM",
  "metadata": {
    "priority": "medium",
    "category": "maintenance",
    "scheduled_date": "2024-01-21T02:00:00Z"
  }
}
```

**Required Fields:**
- `title`: Alert title (string)
- `description`: Alert description (string)

**Optional Fields:**
- `metadata`: Additional data as JSON object

**Response:**
```json
{
  "error": false,
  "message": "System alert created successfully"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "error": true,
  "message": "Title and description are required"
}
```

### 401 Unauthorized
```json
{
  "error": true,
  "message": "Unauthorized access"
}
```

### 403 Forbidden
```json
{
  "error": true,
  "message": "Forbidden: Insufficient permissions"
}
```

### 500 Internal Server Error
```json
{
  "error": true,
  "message": "Failed to fetch admin alerts"
}
```

## Usage Examples

### Get Recent Alerts
```bash
curl -X GET "https://api.kanglink.com/v2/api/kanglink/custom/admin/alerts?limit=10" \
  -H "Authorization: Bearer <super_admin_token>"
```

### Get Alerts by Type
```bash
curl -X GET "https://api.kanglink.com/v2/api/kanglink/custom/admin/alerts?activity_type=program_approval_pending" \
  -H "Authorization: Bearer <super_admin_token>"
```

### Get Alerts in Date Range
```bash
curl -X GET "https://api.kanglink.com/v2/api/kanglink/custom/admin/alerts?date_from=2024-01-01&date_to=2024-01-31" \
  -H "Authorization: Bearer <super_admin_token>"
```

### Create System Alert
```bash
curl -X POST "https://api.kanglink.com/v2/api/kanglink/custom/admin/alerts/system" \
  -H "Authorization: Bearer <super_admin_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "New Feature Available",
    "description": "The new analytics dashboard is now available for all trainers",
    "metadata": {
      "priority": "high",
      "category": "feature_update"
    }
  }'
```

## Service Integration

The AdminAlertService provides methods for creating alerts programmatically:

```javascript
const AdminAlertService = require('../services/AdminAlertService');

// Create program approval alert
await adminAlertService.createProgramApprovalAlert(programData);

// Create new athlete signup alert
await adminAlertService.createNewAthleteSignupAlert(athleteData);

// Create new trainer signup alert
await adminAlertService.createNewTrainerSignupAlert(trainerData);

// Create transaction alert
await adminAlertService.createNewTransactionAlert(transactionData);

// Create refund request alert
await adminAlertService.createRefundRequestAlert(refundData);

// Create refund decision alert
await adminAlertService.createRefundDecisionAlert(refundData, 'approve');

// Create low rated trainer alert
await adminAlertService.createLowRatedTrainerAlert(trainerData);

// Create system alert
await adminAlertService.createSystemAlert(title, description, metadata);
```

## Security Considerations

1. **Role-based Access**: Only users with `super_admin` role can access these endpoints
2. **Token Validation**: All requests must include a valid JWT token
3. **Input Validation**: All input parameters are validated and sanitized
4. **SQL Injection Protection**: All database queries use parameterized statements
5. **Rate Limiting**: Consider implementing rate limiting for these endpoints

## Monitoring and Logging

- All alert creation events are logged for audit purposes
- Failed operations are logged with error details
- Database queries are monitored for performance
- Alert statistics are tracked for system health monitoring

## Testing

Comprehensive test coverage is available in `tests/admin_alerts.test.js` including:
- Authentication and authorization tests
- Input validation tests
- Error handling tests
- Service integration tests
- Pagination and filtering tests 