# Trainer Dashboard Notifications System

## Overview

The trainer dashboard notifications system provides real-time updates to trainers about various activities related to their programs, athletes, and business operations.

## Backend Implementation

### API Endpoints

#### 1. Get Trainer Notifications
```
GET /v2/api/kanglink/custom/trainer/dashboard/notifications
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of notifications per page (default: 10)
- `unread_only` (optional): Filter only unread notifications (default: false)
- `category` (optional): Filter by category (progress, enrollment, payment, communication, system, general)

**Response:**
```json
{
  "error": false,
  "message": "Notifications retrieved successfully",
  "data": {
    "notifications": [
      {
        "id": 1,
        "notification_type": "new_enrollment",
        "category": "enrollment",
        "title": "New Enrollment",
        "message": "<PERSON> enrolled in \"Strength Training - Beginner\"",
        "data": {
          "enrollment_id": 123,
          "program_name": "Strength Training",
          "split_name": "<PERSON><PERSON><PERSON>",
          "athlete_name": "<PERSON>",
          "payment_amount": 99.99,
          "payment_currency": "USD",
          "payment_type": "one_time"
        },
        "is_read": false,
        "read_at": null,
        "created_at": "2024-01-15T10:30:00Z",
        "sender_id": 456,
        "sender_name": "John Doe",
        "related_id": 123,
        "related_type": "enrollment"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25
    }
  }
}
```

#### 2. Mark Notification as Read
```
PUT /v2/api/kanglink/custom/trainer/dashboard/notifications/:id/read
```

**Response:**
```json
{
  "error": false,
  "message": "Notification marked as read"
}
```

#### 3. Mark All Notifications as Read
```
PUT /v2/api/kanglink/custom/trainer/dashboard/notifications/read-all
```

**Response:**
```json
{
  "error": false,
  "message": "All notifications marked as read"
}
```

### Notification Categories

1. **enrollment**: New athlete enrollments, enrollment updates
2. **payment**: Payment received, payment failed, refund requests
3. **progress**: Athlete progress updates, milestone achievements
4. **communication**: Messages from athletes, system announcements
5. **system**: System updates, maintenance notifications
6. **general**: Other notifications

### Notification Types

- `new_enrollment`: New athlete enrollment
- `payment_received`: Payment received from athlete
- `payment_failed`: Payment processing failed
- `milestone_reached`: Athlete reached a milestone
- `program_updated`: Program content updated
- `refund_requested`: Athlete requested refund
- `system_alert`: System maintenance or alerts

## Frontend Implementation

### Components

#### DashboardNotifications.tsx
The main notification component that displays notifications in the trainer dashboard.

**Features:**
- Displays notifications with icons based on category
- Shows unread count and "Mark all as read" button
- Click handlers for individual notifications
- Loading states and error handling
- Dark mode support

**Key Functions:**
- `handleNotificationClick()`: Marks notification as read and handles navigation
- `handleMarkAllAsRead()`: Marks all notifications as read
- `getNotificationIcon()`: Returns appropriate icon based on notification type/category

#### useTrainerDashboard Hook
Manages data fetching and state for the trainer dashboard.

**Features:**
- Fetches notifications from backend API
- Handles notification marking as read
- Provides real-time updates
- Error handling and loading states

### Data Flow

1. **Backend Notification Creation**: Notifications are created by the NotificationService when events occur (enrollments, payments, progress updates)
2. **Frontend Data Fetching**: The useTrainerDashboard hook fetches notifications from the API
3. **UI Rendering**: DashboardNotifications component renders the notifications with proper styling and interactions
4. **User Interactions**: Users can click notifications to mark them as read and navigate to related content

### Notification Icons

- **Enrollment**: UserPlus icon
- **Payment**: CreditCard icon  
- **Progress**: Star icon
- **Communication**: Bell icon
- **System**: Edit icon
- **Default**: Bell icon

### Styling

The component uses Tailwind CSS classes with dark mode support:
- Unread notifications have blue background
- Read notifications have gray background
- Hover effects for better UX
- Loading spinners for async operations
- Responsive design for mobile and desktop

## Testing

### Backend Tests
Located in `tests/trainer_dashboard.test.js`:
- Tests notification endpoints
- Tests filtering and pagination
- Tests mark as read functionality
- Tests authorization

### Frontend Tests
The component includes:
- Loading state handling
- Error state handling
- Empty state handling
- User interaction testing

## Usage Examples

### Creating a Notification (Backend)
```javascript
const notificationService = new NotificationService(sdk);
await notificationService.createEnrollmentNotification(
  enrollmentData,
  athleteData,
  trainerData
);
```

### Using Notifications in Frontend
```javascript
const { notifications, markNotificationAsRead } = useTrainerDashboard();

// Mark a notification as read
await markNotificationAsRead(notificationId);

// Display notifications
<DashboardNotifications 
  notifications={notifications}
  isLoading={isNotificationsLoading}
/>
```

## Future Enhancements

1. **Real-time Updates**: Implement WebSocket connections for live notifications
2. **Push Notifications**: Add browser push notifications for important updates
3. **Notification Preferences**: Allow trainers to customize notification settings
4. **Advanced Filtering**: Add more filtering options (date range, specific athletes)
5. **Bulk Actions**: Add bulk mark as read/delete functionality
6. **Notification Templates**: Create reusable notification templates for different scenarios 