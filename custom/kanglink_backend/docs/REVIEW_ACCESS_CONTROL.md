# Review Access Control Implementation

## Overview

This document describes the implementation of access control for the review system, ensuring that only users who have purchased or subscribed to a program can write reviews for that program.

## Frontend Implementation

### ProgramHeader Component

The `ProgramHeader` component has been updated to include access control for the "Write a Review" button.

**Key Features:**
- Checks if user is authenticated
- Verifies if user is enrolled in the program
- Shows appropriate button states based on access level

**Button States:**
1. **Enabled**: User is logged in and enrolled in the program
2. **Disabled (Not Logged In)**: User is not authenticated
3. **Disabled (Not Enrolled)**: User is logged in but not enrolled in the program

**Code Location:** `frontend/src/components/ProgramHeader/ProgramHeader.tsx`

### WriteReviewModal Component

The existing `WriteReviewModal` component already includes comprehensive access control:

**Access Control Features:**
- Validates user authentication status
- Checks program subscription status
- Shows appropriate error messages
- Prevents form submission for unauthorized users

**Error Messages:**
- "You must be logged in to write a review"
- "You must be subscribed to this program to write a review"
- "Checking subscription status..."

**Code Location:** `frontend/src/components/WriteReviewModal/WriteReviewModal.tsx`

## Backend Implementation

### Post Feed Route

The post feed creation endpoint has been enhanced with enrollment access control.

**Endpoint:** `POST /v2/api/kanglink/custom/trainer/feed`

**Access Control Logic:**
```javascript
// Check enrollment for reviews - users must be enrolled in the program to write reviews
if (post_type === "review") {
  sdk.setProjectId("kanglink");
  sdk.setTable("enrollment");
  
  // Check if user has an active enrollment for this program
  const enrollment = await sdk.findOne("enrollment", {
    athlete_id: userId,
    program_id: parseInt(program_id),
    status: "active",
  });

  if (!enrollment) {
    return res.status(403).json({
      error: true,
      message: "You must be enrolled in this program to write a review",
    });
  }
}
```

**Validation Rules:**
1. **Review Posts**: Require active enrollment in the program
2. **Announcement Posts**: Only trainers and super admins can create
3. **Other Post Types**: No enrollment requirement

**Code Location:** `routes/post_feed.js`

## Database Schema

### Enrollment Table

The enrollment check uses the existing `enrollment` table with the following key fields:

- `athlete_id`: User ID of the enrolled athlete
- `program_id`: ID of the program
- `status`: Enrollment status (must be "active")

### Post Feed Table

The `post_feed` table stores reviews with the following key fields:

- `user_id`: ID of the user who wrote the review
- `program_id`: ID of the program being reviewed
- `split_id`: ID of the specific split (optional)
- `post_type`: Type of post ("review")
- `content`: Review text content
- `rating`: Star rating (1-5)

## Testing

### Test File

A comprehensive test suite has been created to verify the access control functionality.

**Test File:** `tests/post_feed_review_access_control.test.js`

**Test Cases:**
1. ✅ Allow enrolled user to create a review
2. ✅ Prevent non-enrolled user from creating a review
3. ✅ Allow trainer to create non-review posts without enrollment
4. ✅ Prevent member from creating announcements
5. ✅ Validate review rating requirements
6. ✅ Validate review rating range (1-5)
7. ✅ Allow super admin to create reviews without enrollment
8. ✅ Retrieve reviews with proper access control

### Running Tests

```bash
# Run the specific test file
npm test -- post_feed_review_access_control.test.js

# Run all tests
npm test
```

## User Experience

### Frontend User Flow

1. **User visits program page**
   - ProgramHeader component loads
   - Checks user authentication and enrollment status

2. **Write Review button states:**
   - **Green/Enabled**: User can write review
   - **Gray/Disabled**: User cannot write review (with tooltip)

3. **User clicks Write Review button:**
   - If enabled: Modal opens with review form
   - If disabled: Button shows tooltip with reason

4. **Review submission:**
   - Frontend validates form data
   - Backend validates enrollment status
   - Success/error feedback provided

### Error Handling

**Frontend Errors:**
- Authentication required
- Enrollment required
- Form validation errors

**Backend Errors:**
- 403 Forbidden: Not enrolled in program
- 400 Bad Request: Invalid review data
- 500 Internal Server Error: System errors

## Security Considerations

### Access Control Layers

1. **Frontend Layer**: UI state management and user experience
2. **Backend Layer**: Server-side validation and enforcement
3. **Database Layer**: Data integrity and constraints

### Validation Points

- **Authentication**: User must be logged in
- **Authorization**: User must have appropriate role/permissions
- **Enrollment**: User must be enrolled in the program
- **Data Validation**: Review content and rating validation

## API Documentation

### Create Review

**Endpoint:** `POST /v2/api/kanglink/custom/trainer/feed`

**Request Body:**
```json
{
  "program_id": 123,
  "split_id": 456,
  "post_type": "review",
  "content": "Great program!",
  "rating": 5,
  "visibility_scope": "public"
}
```

**Success Response (200):**
```json
{
  "error": false,
  "data": {
    "post": {
      "id": 789,
      "post_type": "review",
      "content": "Great program!",
      "rating": 5,
      "user_id": 123,
      "program_id": 123,
      "created_at": "2024-01-01T00:00:00Z"
    }
  },
  "message": "Post created successfully"
}
```

**Error Response (403):**
```json
{
  "error": true,
  "message": "You must be enrolled in this program to write a review"
}
```

## Future Enhancements

### Potential Improvements

1. **Review Moderation**: Add moderation system for reviews
2. **Review Editing**: Allow users to edit their reviews
3. **Review Replies**: Allow trainers to reply to reviews
4. **Review Analytics**: Track review metrics and insights
5. **Review Incentives**: Reward system for helpful reviews

### Technical Debt

1. **Caching**: Implement caching for enrollment status checks
2. **Rate Limiting**: Add rate limiting for review submissions
3. **Audit Logging**: Track review creation and modifications
4. **Performance**: Optimize database queries for enrollment checks

## Conclusion

The review access control implementation ensures that only legitimate users who have purchased or subscribed to a program can write reviews, maintaining the integrity and trustworthiness of the review system while providing a smooth user experience. 