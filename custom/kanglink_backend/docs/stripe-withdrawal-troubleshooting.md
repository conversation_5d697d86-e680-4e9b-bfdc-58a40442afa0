# Stripe Withdrawal Troubleshooting Guide

## Problem: "Insufficient Available Funds" Error

When trainers try to withdraw their earnings, you might encounter this error:

```json
{
  "error": true,
  "message": "Failed to process withdrawal through <PERSON>e",
  "details": "You have insufficient available funds in your Stripe account. Try adding funds directly to your available balance by creating Charges using the **************** test card. See: https://stripe.com/docs/testing#available-balance"
}
```

## Root Cause

In Stripe's test environment, the **platform account** (your main Stripe account) needs to have sufficient available balance before it can transfer funds to connected accounts (trainers). This is different from production where real payments automatically add to your platform balance.

## The Flow

1. Athletes pay for programs → Funds go to platform account
2. Platform calculates trainer commissions → Stored in database
3. Trainer requests withdrawal → Platform transfers from platform account to trainer's connected account

**The issue**: In test mode, step 1 doesn't automatically add "real" funds to the platform balance that can be transferred.

## Solutions

### Option 1: Use the Admin Endpoints (Recommended)

We've added admin endpoints to handle this:

#### Add Test Funds to Platform
```bash
POST /v2/api/kanglink/custom/admin/transactions/add-test-funds
Authorization: Bearer <super_admin_token>
Content-Type: application/json

{
  "amount": 1000,
  "currency": "USD"
}
```

#### Check Platform Balance
```bash
GET /v2/api/kanglink/custom/admin/transactions/platform-balance
Authorization: Bearer <super_admin_token>
```

### Option 2: Use the Script

Run the provided script:

```bash
# Add 1000 USD to platform balance
node mtpbk/custom/ksl_be/scripts/fix-stripe-balance.js 1000 USD

# Check current balance
node mtpbk/custom/ksl_be/scripts/fix-stripe-balance.js check
```

### Option 3: Manual Stripe Dashboard

1. Go to your Stripe Dashboard
2. Navigate to Payments → Create payment
3. Use test card: `****************`
4. Create charges to add funds to your available balance

### Option 4: Use Test Cards in Your App

Create test payments using the special test card `****************` which adds funds to your available balance.

## Enhanced Error Handling

The withdrawal endpoint now provides better error messages:

```json
{
  "error": true,
  "message": "Insufficient platform funds for withdrawal",
  "details": "Platform has 0 USD available, but 250.29 USD is needed. In test mode, create charges using test card **************** to add funds to platform balance.",
  "platform_balance": {
    "available": 0,
    "currency": "USD",
    "required": 250.29
  },
  "action_required": "add_platform_funds"
}
```

## Prevention

To avoid this issue in the future:

1. **For Testing**: Regularly add test funds using the script or admin endpoints
2. **For Production**: This won't be an issue as real payments automatically add to platform balance
3. **Monitoring**: Use the platform balance endpoint to monitor available funds

## Technical Details

### New StripeService Methods

- `getPlatformBalance()`: Retrieves current platform balance
- `createTestCharge()`: Creates test charges to add funds

### Enhanced Withdrawal Logic

The withdrawal endpoint now:
1. Checks platform balance before attempting transfer
2. Provides detailed error messages with suggested actions
3. Shows exactly how much is available vs. required

### Test Cards Reference

- `****************`: Adds funds to available balance (use this one)
- `tok_bypassPending`: Token that bypasses pending status
- Other test cards: See [Stripe Testing Documentation](https://stripe.com/docs/testing)

## FAQ

**Q: Why does this only happen in test mode?**
A: In production, real customer payments automatically add to your platform's available balance. In test mode, test payments don't add "transferable" funds by default.

**Q: How much should I add?**
A: Add at least the total amount of pending trainer withdrawals. You can check this via the transaction stats endpoint.

**Q: Will this affect production?**
A: No, this is only relevant for test environments. Production handles this automatically.

**Q: Can I automate this?**
A: Yes, you could set up a cron job to periodically add test funds, or trigger it when platform balance gets low.
