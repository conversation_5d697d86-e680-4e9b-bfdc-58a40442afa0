# Resend Verification Email Functionality

## Overview

The resend verification email functionality allows users to request a new verification email if they didn't receive the original one or if the verification link has expired.

## Implementation

### Backend Endpoints

#### Member Resend Verification
- **Endpoint**: `/v1/api/kanglink/member/lambda/resend_verification`
- **Method**: `POST`
- **File**: `lambda/member_resend_verification.js`

#### Trainer Resend Verification
- **Endpoint**: `/v1/api/kanglink/trainer/lambda/resend_verification`
- **Method**: `POST`
- **File**: `lambda/trainer_resend_verification.js`

### Request Body
```json
{
  "email": "<EMAIL>",
  "role": "member" // or "trainer"
}
```

### Response
```json
{
  "error": false,
  "message": "Verification email sent successfully"
}
```

### Error Responses

#### 400 - Bad Request
```json
{
  "error": true,
  "message": "Email is required"
}
```

#### 404 - User Not Found
```json
{
  "error": true,
  "message": "User not found"
}
```

#### 400 - Already Verified
```json
{
  "error": true,
  "message": "User is already verified"
}
```

#### 500 - Server Error
```json
{
  "error": true,
  "message": "Failed to send verification email"
}
```

## Frontend Implementation

### EmailVerificationModal Component

The `EmailVerificationModal.tsx` component provides a modal interface for resending verification emails.

**Features:**
- Displays verification status
- Shows email address
- Provides resend button with loading state
- Handles success and error states
- Includes helpful instructions

**Usage:**
```tsx
<EmailVerificationModal
  isOpen={showVerificationModal}
  onClose={() => setShowVerificationModal(false)}
  email="<EMAIL>"
  user_id="123"
  role="member"
/>
```

### VerifyEmail Page

The `VerifyEmail.tsx` page includes resend functionality for users who land on the verification page without a valid token.

**Features:**
- Automatic email verification when token is provided
- Manual resend option when verification fails
- Handles missing email parameters
- Provides clear error messages

## Backend Process

### 1. Validation
- Check if email is provided
- Verify user exists in database
- Check if user is already verified
- Validate role permissions

### 2. Token Management
- Delete any existing verification tokens for the user
- Generate new JWT verification token
- Save token to database with expiry date

### 3. Email Sending
- Create verification URL with token
- Send verification email using MailService
- Handle email sending errors

### 4. Security Features
- Token expiration (7 days by default)
- Role-based access control
- Rate limiting (implemented in middleware)
- Secure token generation

## Configuration

### Environment Variables
```env
VERIFICATION_TOKEN_EXPIRE=604800  # 7 days in seconds
JWT_KEY=your_jwt_secret_key
APP_URL=https://your-app-domain.com
```

### Mail Service Configuration
```javascript
const mailService = new MailService(config);
await mailService.sendVerificationEmail(
  user.email,
  verificationToken,
  verificationUrl
);
```

## User Experience Flow

### 1. User Registration
1. User signs up with email
2. Verification email is sent automatically
3. User receives email with verification link

### 2. Email Verification
1. User clicks verification link
2. Email is verified and account is activated
3. User can now log in

### 3. Resend Process
1. User doesn't receive email or link expires
2. User clicks "Resend Verification" button
3. New verification email is sent
4. User receives new email with fresh link

### 4. Error Handling
1. If email is not found, show helpful message
2. If user is already verified, redirect to login
3. If server error occurs, show retry option

## Testing

### Test Cases
- Resend verification for member users
- Resend verification for trainer users
- Handle missing email parameter
- Handle user not found error
- Handle already verified user
- Handle server errors
- Test token expiration
- Test email delivery

### Test Files
- `tests/resend_verification.test.js` - Frontend tests
- Backend integration tests (to be implemented)

## Security Considerations

### Rate Limiting
- Implement rate limiting for resend requests
- Prevent abuse of resend functionality
- Track resend attempts per email

### Token Security
- Use secure JWT tokens
- Implement token expiration
- Validate token integrity
- Prevent token reuse

### Email Security
- Use secure email templates
- Include user verification in email
- Implement email validation
- Handle email delivery failures

## Future Enhancements

### Planned Features
- Email template customization
- Multiple email providers support
- SMS verification as backup
- Two-factor authentication integration
- Email verification reminders
- Bulk verification for admin users

### Monitoring
- Email delivery tracking
- Verification success rates
- Resend request analytics
- Error rate monitoring
- User engagement metrics 