# Discount System Documentation

## Overview

The Kanglink discount system provides comprehensive discount management with multiple discount types that can be stacked together. The system ensures customers are charged the correct discounted amount via Stripe, and commissions are calculated on actual revenue received.

## System Architecture

### Discount Types

1. **Program Sale Discounts** - Program-wide sale discounts set by trainers
2. **General Discounts** - Flexible discounts that can apply to programs or specific splits
3. **Coupon Discounts** - Code-based discounts with usage limits and expiry dates

### Database Tables

#### `kanglink_program_discount`
Program-level sale discounts and affiliate links.

```sql
CREATE TABLE kanglink_program_discount (
  id INT AUTO_INCREMENT PRIMARY KEY,
  program_id INT NOT NULL,
  affiliate_link TEXT,
  sale_discount_type ENUM('fixed', 'percentage'),
  sale_discount_value DECIMAL(10,2),
  sale_apply_to_all BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### `kanglink_discount`
General discounts that can apply to programs or splits.

```sql
CREATE TABLE kanglink_discount (
  id INT AUTO_INCREMENT PRIMARY KEY,
  program_id INT NOT NULL,
  split_id INT NULL, -- NULL = applies to entire program
  discount_type ENUM('fixed', 'percentage') NOT NULL,
  discount_value DECIMAL(10,2) NOT NULL,
  applies_to ENUM('subscription', 'full_payment', 'both') NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### `kanglink_coupon`
Coupon codes with usage tracking and expiry dates.

```sql
CREATE TABLE kanglink_coupon (
  id INT AUTO_INCREMENT PRIMARY KEY,
  program_id INT NOT NULL,
  code VARCHAR(50) NOT NULL UNIQUE,
  discount_type ENUM('fixed', 'percentage') NOT NULL,
  discount_value DECIMAL(10,2) NOT NULL,
  applies_to ENUM('subscription', 'full_payment', 'both') NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  expiry_date TIMESTAMP NULL,
  usage_limit INT NULL,
  used_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### `kanglink_coupon_usage`
Tracks individual coupon usage to prevent duplicate usage.

```sql
CREATE TABLE kanglink_coupon_usage (
  id INT AUTO_INCREMENT PRIMARY KEY,
  coupon_id INT NOT NULL,
  user_id INT NOT NULL,
  program_id INT NOT NULL,
  split_id INT,
  discount_amount DECIMAL(10,2) NOT NULL,
  used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Discount Calculation Flow

### Order of Application
Discounts are applied in the following order:

1. **Program Sale Discount** (from `program_discount`)
2. **General Discount** (from `discount`)
3. **Coupon Discount** (from `coupon`)

### Example Calculation

```javascript
// Original amount: $100
let amount = 100.00;

// 1. Program sale discount: 10% off
amount = 100.00 - (100.00 * 0.10); // $90.00

// 2. General discount: $5 off
amount = 90.00 - 5.00; // $85.00

// 3. Coupon discount: 20% off current amount
amount = 85.00 - (85.00 * 0.20); // $68.00

// Final amount charged to customer: $68.00
// Commission calculated on: $68.00 (not $100.00)
```

## DiscountService API

### Main Method: calculateDiscountedAmount()

```javascript
const discountService = new DiscountService(sdk);

const result = await discountService.calculateDiscountedAmount({
  program_id: 1,
  split_id: 1,
  payment_type: "one_time", // or "subscription"
  original_amount: 100.00,
  coupon_code: "SAVE20", // optional
  user_id: 123
});

// Result structure:
{
  success: true,
  original_amount: 100.00,
  final_amount: 68.00,
  total_discount_amount: 32.00,
  applied_discounts: [
    {
      type: 'sale',
      discount_type: 'percentage',
      discount_value: 10.00,
      discount_amount: 10.00,
      source: 'program_sale'
    },
    {
      type: 'general',
      discount_type: 'fixed',
      discount_value: 5.00,
      discount_amount: 5.00,
      source: 'general_discount'
    },
    {
      type: 'coupon',
      coupon_id: 1,
      coupon_code: 'SAVE20',
      discount_type: 'percentage',
      discount_value: 20.00,
      discount_amount: 17.00,
      source: 'coupon'
    }
  ],
  has_discounts: true
}
```

### Coupon Validation

```javascript
const couponResult = await discountService.validateAndApplyCoupon(
  "SAVE20",     // coupon_code
  1,            // program_id
  1,            // split_id
  "one_time",   // payment_type
  85.00,        // current_amount
  123           // user_id
);

// Result:
{
  valid: true,
  coupon: { /* coupon object */ },
  discount_amount: 17.00
}
```

### Coupon Usage Recording

```javascript
await discountService.recordCouponUsage(
  1,      // coupon_id
  123,    // user_id
  1,      // program_id
  1,      // split_id
  17.00   // discount_amount
);
```

## Enrollment Integration

### Request Format

```http
POST /v2/api/kanglink/custom/athlete/enroll
Authorization: Bearer {athlete_token}
Content-Type: application/json

{
  "split_id": 1,
  "payment_type": "one_time",
  "payment_method_id": "pm_1234567890",
  "affiliate_code": "ABC123", // optional
  "coupon_code": "SAVE20"     // optional
}
```

### Processing Flow

1. **Validate split and payment type**
2. **Calculate discounts** using `DiscountService`
3. **Validate affiliate code** if provided
4. **Create Stripe payment** with final discounted amount
5. **Create enrollment record** with discount details
6. **Record coupon usage** if coupon was applied
7. **Calculate commission** on final amount

### Enrollment Record

The enrollment record stores complete discount information:

```javascript
{
  id: 1,
  trainer_id: 1,
  athlete_id: 123,
  program_id: 1,
  split_id: 1,
  payment_type: "one_time",
  amount: 68.00,                    // Final amount charged
  original_amount: 100.00,          // Original price
  discount_amount: 32.00,           // Total discount
  discount_details: "[{...}]",      // JSON array of applied discounts
  currency: "USD",
  // ... other fields
}
```

## Discount Type Specifications

### Payment Type Applicability

- **`subscription`**: Applies only to subscription payments
- **`full_payment`**: Applies only to one-time payments
- **`both`**: Applies to both payment types

### Discount Value Types

#### Percentage Discounts
- Value represents percentage (e.g., 20.00 = 20%)
- Applied as: `amount * (discount_value / 100)`
- Example: 20% off $100 = $20 discount

#### Fixed Discounts
- Value represents fixed amount (e.g., 5.00 = $5)
- Applied as: `Math.min(discount_value, amount)`
- Cannot exceed the current amount

## Coupon Management

### Coupon Validation Rules

1. **Active Status**: `is_active = true`
2. **Program Match**: `program_id` must match enrollment program
3. **Expiry Date**: Must not be expired if `expiry_date` is set
4. **Usage Limit**: `used_count < usage_limit` if limit is set
5. **Duplicate Usage**: User cannot use same coupon twice for same program
6. **Payment Type**: Must apply to the enrollment payment type

### Coupon Usage Tracking

- Each coupon usage creates a record in `coupon_usage`
- Coupon `used_count` is automatically incremented
- Prevents duplicate usage by same user for same program

## Error Handling

### Discount Calculation Errors
- If discount calculation fails, enrollment continues with original amount
- Error logged for manual review
- Customer charged original price as fallback

### Coupon Validation Errors
- Invalid coupon codes return specific error messages
- Expired coupons: "Coupon has expired"
- Usage limit exceeded: "Coupon usage limit exceeded"
- Already used: "You have already used this coupon"

### Amount Validation
- Final amount cannot be negative (minimum $0)
- Discount amounts cannot exceed current amount
- All calculations use proper decimal precision

## Integration with Commission System

### Commission Calculation
- Commissions calculated on `final_amount` (after discounts)
- Original amount stored for reference
- Discount details preserved for audit trail

### Stripe Integration
- Stripe charged the `final_amount` (discounted price)
- Payment metadata includes discount information
- Webhook processing uses actual charged amount

## Testing

### Test Coverage
- Discount calculation logic
- Coupon validation rules
- Usage tracking and limits
- Integration with enrollment process
- Error handling scenarios

### Test Files
- `tests/discount_system.test.js`
- `tests/enrollment_commission.test.js`

### Running Tests
```bash
node test_runner.js discount_system.test.js
```

## Performance Considerations

### Database Queries
- Efficient queries with proper indexing
- Minimal database calls per discount calculation
- Batch operations for coupon usage recording

### Caching
- Consider caching active discounts for high-traffic programs
- Coupon validation can be cached with short TTL

### Error Recovery
- Graceful degradation if discount service fails
- Enrollment continues with original pricing
- Background jobs can retry failed discount applications
