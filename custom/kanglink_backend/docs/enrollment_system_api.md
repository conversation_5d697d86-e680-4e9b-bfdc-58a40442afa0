# Athlete Enrollment System with Stripe Integration

## Overview

The enrollment system allows athletes to purchase access to trainer programs through either one-time payments or monthly subscriptions. The system integrates with Stripe for payment processing and automatically handles subscription management.

## Key Features

- **Dual Payment Types**: Support for both one-time purchases and monthly subscriptions
- **Stripe Integration**: Automatic payment processing and subscription management
- **Webhook Handling**: Real-time payment status updates via Stripe webhooks
- **Access Control**: Different access patterns for one-time vs subscription purchases
- **Update Management**: Subscription users get program updates, one-time users don't

## Database Schema

### Enrollment Model

```javascript
{
  id: "primary key",
  trainer_id: "foreign key", // ID of the trainer who owns the program
  athlete_id: "foreign key", // ID of the athlete purchasing access
  program_id: "foreign key", // ID of the program being purchased
  split_id: "foreign key", // ID of the specific split being purchased
  payment_type: "subscription|one_time", // Type of payment
  amount: "float", // Amount paid
  currency: "string", // Currency (default: USD)
  enrollment_date: "datetime", // When enrollment was created
  expiry_date: "datetime", // When access expires (null for lifetime/subscription)
  status: "active|expired|cancelled|pending", // Enrollment status
  payment_status: "paid|pending|failed|refunded", // Payment status
  stripe_subscription_id: "string", // Stripe subscription ID (for subscriptions)
  stripe_payment_intent_id: "string", // Stripe payment intent ID (for one-time)
  stripe_customer_id: "string", // Stripe customer ID
  created_at: "datetime",
  updated_at: "datetime"
}
```

## API Endpoints

### 1. Create Enrollment

**Endpoint:** `POST /v2/api/kanglink/custom/athlete/enroll`

**Authentication:** Required (member, trainer, or super_admin)

**Request Body:**
```json
{
  "split_id": "number", // ID of the split to enroll in
  "payment_type": "subscription|one_time", // Payment type
  "payment_method_id": "string" // Stripe payment method ID
}
```

**Response:**
```json
{
  "error": false,
  "message": "Enrollment created successfully",
  "data": {
    "enrollment_id": "number",
    "payment_type": "subscription",
    "amount": 29.99,
    "currency": "USD",
    "status": "active",
    "payment_status": "paid",
    "stripe_subscription_id": "sub_xxx",
    "stripe_payment_intent_id": null
  }
}
```

### 2. Get Athlete Enrollments

**Endpoint:** `GET /v2/api/kanglink/custom/athlete/enrollments`

**Authentication:** Required (member, trainer, or super_admin)

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "id": 1,
      "trainer_id": 2,
      "athlete_id": 3,
      "program_id": 1,
      "split_id": 1,
      "payment_type": "subscription",
      "amount": 29.99,
      "currency": "USD",
      "status": "active",
      "payment_status": "paid",
      "program_name": "Beginner Strength Training",
      "type_of_program": "strength",
      "split_title": "Full Body Workout",
      "trainer_email": "<EMAIL>",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 3. Get Trainer Enrollments

**Endpoint:** `GET /v2/api/kanglink/custom/trainer/enrollments`

**Authentication:** Required (trainer or super_admin)

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "id": 1,
      "trainer_id": 2,
      "athlete_id": 3,
      "program_id": 1,
      "split_id": 1,
      "payment_type": "subscription",
      "amount": 29.99,
      "currency": "USD",
      "status": "active",
      "payment_status": "paid",
      "program_name": "Beginner Strength Training",
      "type_of_program": "strength",
      "split_title": "Full Body Workout",
      "athlete_email": "<EMAIL>",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 4. Cancel Enrollment

**Endpoint:** `POST /v2/api/kanglink/custom/enrollment/:enrollment_id/cancel`

**Authentication:** Required (member, trainer, or super_admin)

**Permissions:**
- Athletes can cancel their own enrollments
- Trainers can cancel enrollments for their programs
- Super admins can cancel any enrollment

**Response:**
```json
{
  "error": false,
  "message": "Enrollment cancelled successfully"
}
```

## Payment Flow

### One-Time Payment Flow

1. **Enrollment Creation**: Athlete submits enrollment request with `payment_type: "one_time"`
2. **Stripe Payment**: System creates Stripe PaymentIntent with the split's `full_price`
3. **Payment Processing**: Stripe processes the payment using provided payment method
4. **Webhook Confirmation**: Stripe webhook confirms payment success
5. **Status Update**: Enrollment status updated to "active" and payment_status to "paid"
6. **Access Granted**: Athlete gains lifetime access to the split (no updates)

### Subscription Payment Flow

1. **Product/Price Creation**: System creates or retrieves Stripe Product and Price for the split
2. **Enrollment Creation**: Athlete submits enrollment request with `payment_type: "subscription"`
3. **Stripe Subscription**: System creates Stripe Subscription with the split's `subscription` price
4. **Immediate Activation**: Subscription is active immediately
5. **Webhook Confirmation**: Stripe webhook confirms subscription creation
6. **Status Update**: Enrollment status updated to "active" and payment_status to "paid"
7. **Ongoing Access**: Athlete gets access with automatic updates and monthly billing

## Stripe Integration Details

### Product Creation
- Products are created per split: `{program_name} - {split_title}`
- Products include metadata: `projectId: "kanglink"`, `split_id`

### Price Creation
- Subscription prices: `{program_name} - {split_title} Monthly`
- Recurring interval: monthly
- Amount from split's `subscription` field

### Customer Management
- Automatic Stripe customer creation if not exists
- Customer ID stored in user's `stripe_uid` field

### Metadata Usage
All Stripe objects include metadata for tracking:
- `projectId: "kanglink"`
- `split_id`: Split being purchased
- `athlete_id`: Purchasing athlete
- `trainer_id`: Program owner
- `payment_type`: "subscription" or "one_time"

## Webhook Handling

### Payment Intent Succeeded
- Updates one-time enrollment payment_status to "paid"
- Updates enrollment status to "active"
- Triggered when one-time payment completes

### Subscription Created
- Updates subscription enrollment payment_status to "paid"
- Updates enrollment status to "active"
- Links Stripe subscription ID to enrollment

### Subscription Updated/Cancelled
- Updates enrollment status based on subscription status
- Handles subscription cancellations automatically

## Business Rules

### Access Patterns
- **One-time purchases**: Lifetime access, no updates when trainer modifies split
- **Subscriptions**: Access while active, receives updates when trainer modifies split
- **Cancellations**: Immediate access revocation

### Pricing
- One-time price from split's `full_price` field
- Subscription price from split's `subscription` field
- Currency from program's `currency` field (default: USD)

### Validation
- Athletes cannot enroll in same split twice (active enrollments)
- Split must have valid pricing for requested payment type
- Payment method must be valid Stripe payment method

## Error Handling

### Common Errors
- `400`: Invalid payment type, missing required fields, already enrolled
- `404`: Split not found, athlete not found
- `403`: Insufficient permissions
- `500`: Stripe errors, database errors

### Stripe Error Handling
- Payment failures update enrollment payment_status to "failed"
- Subscription creation failures prevent enrollment creation
- Webhook failures are logged but don't affect user experience

## Testing

### Test Scenarios
1. **One-time enrollment**: Create enrollment with valid payment method
2. **Subscription enrollment**: Create subscription with valid payment method
3. **Duplicate enrollment**: Attempt to enroll in same split twice
4. **Invalid split**: Attempt to enroll in non-existent split
5. **Payment failure**: Test with invalid payment method
6. **Cancellation**: Cancel active enrollment/subscription
7. **Webhook processing**: Test payment and subscription webhooks

### Test Data Requirements
- Valid Stripe test payment methods
- Test splits with both one-time and subscription pricing
- Test athletes and trainers
- Stripe webhook endpoint configuration
