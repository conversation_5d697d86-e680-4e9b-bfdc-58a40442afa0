# Complete Athlete Enrollment System with Stripe Integration

## Overview

The enrollment system allows athletes to purchase access to trainer programs through either **one-time payments** or **monthly subscriptions**. The system is fully integrated with <PERSON><PERSON> for secure payment processing and includes automatic subscription management.

## Key Business Rules

### Payment Types
- **One-time purchase**: Athletes pay the `full_price` and get **lifetime access** with **no updates** when trainers modify the split
- **Monthly subscription**: Athletes pay the `subscription` price monthly and get **access with automatic updates** when trainers modify the split

### Access Control
- Athletes can only enroll in published programs
- Athletes cannot enroll in the same split twice (if they have an active enrollment)
- Trainers can view and manage enrollments for their programs
- Athletes can view and cancel their own enrollments

## Database Schema

### Enrollment Table (`kanglink_enrollment`)
```sql
CREATE TABLE kanglink_enrollment (
  id INT AUTO_INCREMENT PRIMARY KEY,
  trainer_id INT NOT NULL,           -- Program owner
  athlete_id INT NOT NULL,           -- Purchasing athlete  
  program_id INT NOT NULL,           -- Program being purchased
  split_id INT NOT NULL,             -- Specific split being purchased
  payment_type ENUM('subscription', 'one_time') NOT NULL,
  amount DECIMAL(10,2) NOT NULL,     -- Amount paid
  currency VARCHAR(3) DEFAULT 'USD',
  enrollment_date DATETIME,
  expiry_date DATETIME NULL,         -- For future use
  status ENUM('active', 'expired', 'cancelled', 'pending') DEFAULT 'pending',
  payment_status ENUM('paid', 'pending', 'failed', 'refunded') DEFAULT 'pending',
  stripe_subscription_id VARCHAR(255) NULL,    -- For subscriptions
  stripe_payment_intent_id VARCHAR(255) NULL,  -- For one-time payments
  stripe_customer_id VARCHAR(255) NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Split Table (Existing)
```sql
-- Splits have both pricing options
full_price DECIMAL(10,2),      -- One-time purchase price
subscription DECIMAL(10,2),    -- Monthly subscription price
```

## API Endpoints

### 1. Create Enrollment
**POST** `/v2/api/kanglink/custom/athlete/enroll`

Creates a new enrollment with Stripe payment processing.

**Request:**
```json
{
  "split_id": 123,
  "payment_type": "subscription", // or "one_time"
  "payment_method_id": "pm_1234567890" // Stripe payment method ID
}
```

**Response:**
```json
{
  "error": false,
  "message": "Enrollment created successfully",
  "data": {
    "enrollment_id": 456,
    "payment_type": "subscription",
    "amount": 29.99,
    "currency": "USD",
    "status": "active",
    "payment_status": "paid",
    "stripe_subscription_id": "sub_1234567890",
    "stripe_payment_intent_id": null
  }
}
```

### 2. Get Athlete Enrollments
**GET** `/v2/api/kanglink/custom/athlete/enrollments`

Returns all enrollments for the authenticated athlete.

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "id": 456,
      "trainer_id": 789,
      "athlete_id": 123,
      "program_id": 101,
      "split_id": 202,
      "payment_type": "subscription",
      "amount": 29.99,
      "currency": "USD",
      "status": "active",
      "payment_status": "paid",
      "program_name": "Beginner Strength Training",
      "type_of_program": "strength",
      "split_title": "Full Body Workout",
      "trainer_email": "<EMAIL>",
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 3. Get Trainer Enrollments
**GET** `/v2/api/kanglink/custom/trainer/enrollments`

Returns all enrollments for the authenticated trainer's programs.

### 4. Cancel Enrollment
**POST** `/v2/api/kanglink/custom/enrollment/:enrollment_id/cancel`

Cancels an enrollment and associated Stripe subscription.

### 5. Get Split Details
**GET** `/v2/api/kanglink/custom/splits/:split_id`

Returns detailed information about a specific split for enrollment.

### 6. Get Available Splits
**GET** `/v2/api/kanglink/custom/splits?trainer_id=123&program_id=456`

Returns available splits for enrollment (published programs only).

## Stripe Integration Flow

### One-Time Payment Flow
1. **Frontend**: Collect payment method using Stripe Elements
2. **Backend**: Create Stripe PaymentIntent with `confirm: true`
3. **Backend**: Create enrollment record with `status: "pending"`
4. **Stripe**: Processes payment and sends webhook
5. **Webhook**: Updates enrollment to `status: "active"` and `payment_status: "paid"`

### Subscription Flow
1. **Frontend**: Collect payment method using Stripe Elements
2. **Backend**: Create/retrieve Stripe Product for the split
3. **Backend**: Create/retrieve Stripe Price for monthly billing
4. **Backend**: Create Stripe Subscription
5. **Backend**: Create enrollment record with `status: "active"`
6. **Stripe**: Sends subscription.created webhook
7. **Webhook**: Confirms enrollment activation

### Automatic Stripe Resource Management
- **Products**: Created per split as `"{program_name} - {split_title}"`
- **Prices**: Created per split subscription as `"{program_name} - {split_title} Monthly"`
- **Customers**: Automatically created and linked to user accounts
- **Metadata**: All Stripe objects include tracking metadata

## Webhook Handling

The system handles these Stripe webhook events:

### `payment_intent.succeeded`
- Updates one-time enrollment payment status
- Activates enrollment access

### `customer.subscription.created`
- Confirms subscription enrollment
- Links Stripe subscription ID to enrollment

### `customer.subscription.updated`
- Handles subscription status changes
- Updates enrollment status accordingly

### `customer.subscription.deleted`
- Handles subscription cancellations
- Updates enrollment status to cancelled

## Frontend Integration Example

```typescript
// TypeScript interfaces
interface EnrollmentRequest {
  split_id: number;
  payment_type: 'subscription' | 'one_time';
  payment_method_id: string;
}

interface Split {
  id: number;
  title: string;
  full_price: number;
  subscription: number;
  program_name: string;
  currency: string;
}

// React component example
const EnrollmentForm = ({ split }: { split: Split }) => {
  const [paymentType, setPaymentType] = useState<'one_time' | 'subscription'>('one_time');
  
  const handleEnroll = async (paymentMethodId: string) => {
    const response = await fetch('/v2/api/kanglink/custom/athlete/enroll', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        split_id: split.id,
        payment_type: paymentType,
        payment_method_id: paymentMethodId,
      }),
    });
    
    const result = await response.json();
    if (!result.error) {
      // Enrollment successful
      console.log('Enrolled successfully:', result.data);
    }
  };
  
  return (
    <div>
      <h3>{split.program_name} - {split.title}</h3>
      
      <div>
        <label>
          <input 
            type="radio" 
            value="one_time"
            checked={paymentType === 'one_time'}
            onChange={(e) => setPaymentType('one_time')}
          />
          One-time: ${split.full_price} (Lifetime access, no updates)
        </label>
        
        <label>
          <input 
            type="radio" 
            value="subscription"
            checked={paymentType === 'subscription'}
            onChange={(e) => setPaymentType('subscription')}
          />
          Monthly: ${split.subscription}/month (Access with updates)
        </label>
      </div>
      
      {/* Stripe Elements CardElement here */}
      <StripePaymentForm onSubmit={handleEnroll} />
    </div>
  );
};
```

## Testing

### Test Scenarios
1. **One-time enrollment**: Test with Stripe test card `****************`
2. **Subscription enrollment**: Test monthly billing cycle
3. **Duplicate enrollment**: Verify prevention of double enrollment
4. **Payment failure**: Test with declined card `****************`
5. **Subscription cancellation**: Test cancellation flow
6. **Webhook processing**: Verify payment confirmations

### Required Setup
1. **Stripe Configuration**: Set up webhook endpoint at `/v1/api/lambda/stripe/mtp/webhook`
2. **Database Migration**: Create `kanglink_enrollment` table
3. **Environment Variables**: Configure Stripe keys
4. **Frontend**: Implement Stripe Elements for payment collection

## Security Considerations

- All payment processing handled by Stripe (PCI compliant)
- JWT authentication required for all endpoints
- Role-based access control (athletes, trainers, super_admin)
- Webhook signature verification for security
- Sensitive data (payment methods) never stored locally

## Error Handling

The system includes comprehensive error handling for:
- Invalid payment methods
- Insufficient funds
- Network failures
- Stripe API errors
- Database constraints
- Permission violations

All errors return consistent JSON format:
```json
{
  "error": true,
  "message": "Descriptive error message"
}
```
