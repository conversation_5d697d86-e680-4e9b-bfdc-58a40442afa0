# Rest Day Inclusion Fix

## Problem
Rest days were being excluded from the program data when fetching athlete enrollment programs. This was happening because the SQL query used `JOIN` clauses that only returned days with sessions and exercises, excluding rest days which typically don't have any sessions or exercises.

## Root Cause
The query in `athlete_progress.js` was using:
```sql
FROM kanglink_week w
JOIN kanglink_day d ON w.id = d.week_id
JOIN kanglink_session s ON d.id = s.day_id
JOIN kanglink_exercise_instance ei ON s.id = ei.session_id
```

This meant that only days with sessions AND exercises were returned, excluding rest days.

## Solution
Changed the query to use `LEFT JOIN` for sessions and exercise instances:
```sql
FROM kanglink_week w
JOIN kanglink_day d ON w.id = d.week_id
LEFT JOIN kanglink_session s ON d.id = s.day_id
LEFT JOIN kanglink_exercise_instance ei ON s.id = ei.session_id
```

This ensures that:
1. All days are included (including rest days)
2. Rest days will have NULL values for session and exercise data
3. The data processing logic handles NULL values properly

## Code Changes
1. **SQL Query Fix**: Changed `JOIN` to `LEFT JOIN` for sessions and exercise instances
2. **Data Processing Fix**: Added null checks when processing session and exercise data
3. **Sorting Logic Fix**: Added safety checks for empty sessions and exercise arrays
4. **Progress Counting Fix**: Separated day and exercise counting to include rest days in progress calculations

## Files Modified
- `mtpbk/custom/kanglink_backend/routes/athlete_progress.js`

## Testing
Created test files to verify:
- `rest_day_inclusion.test.js`: Rest days are included in program data
- `rest_day_progress_counting.test.js`: Rest days are properly counted in progress calculations
- Rest days have proper structure (empty sessions array)
- Progress data is handled correctly for rest days
- Workout days continue to work as expected

## Impact
- Rest days will now appear in the program data
- Athletes can see and mark rest days as complete
- Progress tracking includes rest days properly
- Rest days are correctly counted in `total_days_completed`
- Progress percentage calculation includes rest days
- No breaking changes to existing functionality 