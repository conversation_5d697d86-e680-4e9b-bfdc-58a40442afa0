# Stripe Integration for Program and Split Management

## Overview

The system automatically creates and manages Stripe products and prices when trainers create or modify programs and splits. This ensures that every split with pricing is immediately available for enrollment without manual Stripe configuration.

## How It Works

### During Program Creation

When a trainer creates a program using the endpoint:
```
POST /v2/api/kanglink/custom/trainer/programs/:program_status
```

The system automatically:

1. **Creates the program** in the database
2. **Creates each split** in the database
3. **For each split with pricing**, automatically creates:
   - **Stripe Product**: Named `"{program_name} - {split_title}"`
   - **Stripe One-time Price**: If `full_price > 0`, creates a one-time payment price
   - **Stripe Subscription Price**: If `subscription > 0`, creates a monthly recurring price

### During Split Updates

When a trainer updates split pricing using:
```
PUT /v2/api/kanglink/custom/trainer/splits/:split_id
```

The system automatically:

1. **Updates the split** in the database
2. **If pricing changed**:
   - **Deactivates old Stripe prices** (Stripe doesn't allow price modifications)
   - **Creates new Stripe prices** with updated amounts
   - **Updates database records** to reflect the changes

## Stripe Resource Naming Convention

### Products
- **Format**: `"{program_name} - {split_title}"`
- **Example**: `"Beginner Strength Training - Full Body Workout"`
- **Metadata**:
  - `projectId: "kanglink"`
  - `split_id: "123"`
  - `program_name: "Beginner Strength Training"`

### Prices
- **One-time Price**: `"{program_name} - {split_title} - One Time"`
- **Subscription Price**: `"{program_name} - {split_title} - Monthly"`
- **Metadata**:
  - `projectId: "kanglink"`
  - `split_id: "123"`
  - `payment_type: "one_time"` or `"subscription"`

## Database Integration

### Stripe Product Table (`kanglink_stripe_product`)
```sql
CREATE TABLE kanglink_stripe_product (
  id INT AUTO_INCREMENT PRIMARY KEY,
  stripe_id VARCHAR(255) NOT NULL,     -- Stripe product ID
  name VARCHAR(255) NOT NULL,          -- Product name
  object TEXT,                         -- Full Stripe product object
  status INT DEFAULT 1,                -- 1=active, 0=inactive
  created_at DATETIME,
  updated_at DATETIME
);
```

### Stripe Price Table (`kanglink_stripe_price`)
```sql
CREATE TABLE kanglink_stripe_price (
  id INT AUTO_INCREMENT PRIMARY KEY,
  stripe_id VARCHAR(255) NOT NULL,     -- Stripe price ID
  name VARCHAR(255) NOT NULL,          -- Price name
  object TEXT,                         -- Full Stripe price object
  status INT DEFAULT 1,                -- 1=active, 0=inactive
  created_at DATETIME,
  updated_at DATETIME
);
```

## API Examples

### Creating a Program with Splits
```json
POST /v2/api/kanglink/custom/trainer/programs/draft

{
  "stepOneData": {
    "program_name": "Beginner Strength Training",
    "type_of_program": "strength",
    "program_description": "A comprehensive beginner program",
    "payment_plan": ["one_time", "subscription"],
    "currency": "USD",
    "splits": [
      {
        "title": "Full Body Workout",
        "full_price": 99.99,      // Creates one-time Stripe price
        "subscription": 29.99,    // Creates monthly Stripe price
        "split_id": "uuid-here"
      },
      {
        "title": "Upper/Lower Split",
        "full_price": 149.99,
        "subscription": 39.99,
        "split_id": "uuid-here-2"
      }
    ]
  },
  "stepTwoData": {
    // ... workout structure
  }
}
```

**Result**: Automatically creates in Stripe:
- Product: "Beginner Strength Training - Full Body Workout"
- Price: "Beginner Strength Training - Full Body Workout - One Time" ($99.99)
- Price: "Beginner Strength Training - Full Body Workout - Monthly" ($29.99/month)
- Product: "Beginner Strength Training - Upper/Lower Split"
- Price: "Beginner Strength Training - Upper/Lower Split - One Time" ($149.99)
- Price: "Beginner Strength Training - Upper/Lower Split - Monthly" ($39.99/month)

### Updating Split Pricing
```json
PUT /v2/api/kanglink/custom/trainer/splits/123

{
  "title": "Full Body Workout Pro",
  "full_price": 119.99,        // Updated price
  "subscription": 34.99        // Updated price
}
```

**Result**: 
- Deactivates old Stripe prices
- Creates new Stripe prices with updated amounts
- Updates database records

## Error Handling

### Graceful Degradation
- If Stripe product/price creation fails, the program/split creation still succeeds
- Errors are logged but don't break the main workflow
- Missing Stripe resources are created on-demand during enrollment

### Duplicate Prevention
- System checks for existing products/prices before creating new ones
- Uses product/price names as unique identifiers
- Prevents duplicate Stripe resources

### Price Update Strategy
Since Stripe doesn't allow price modifications:
1. **Deactivate** old price in Stripe
2. **Mark as inactive** in database (`status = 0`)
3. **Create new price** with updated amount
4. **Save new price** to database (`status = 1`)

## Monitoring and Debugging

### Console Logging
The system logs all Stripe operations:
```
Created Stripe product: prod_1234567890 for split 123
Created Stripe one-time price: price_1234567890 for split 123
Created Stripe subscription price: price_0987654321 for split 123
Updated Stripe subscription price: price_1111111111 for split 123
```

### Database Queries for Debugging
```sql
-- View all Stripe products for splits
SELECT sp.*, s.title, p.program_name 
FROM kanglink_stripe_product sp
JOIN kanglink_split s ON JSON_EXTRACT(sp.object, '$.metadata.split_id') = s.id
JOIN kanglink_program p ON s.program_id = p.id;

-- View all active Stripe prices
SELECT * FROM kanglink_stripe_price WHERE status = 1;

-- View pricing history for a split
SELECT * FROM kanglink_stripe_price 
WHERE JSON_EXTRACT(object, '$.metadata.split_id') = '123'
ORDER BY created_at DESC;
```

## Benefits

### For Trainers
- **Automatic Setup**: No manual Stripe configuration required
- **Immediate Availability**: Splits are ready for enrollment as soon as created
- **Price Updates**: Easy pricing changes through the API
- **Consistency**: Guaranteed sync between database and Stripe

### For Athletes
- **Seamless Enrollment**: All pricing options immediately available
- **Reliable Payments**: Proper Stripe integration ensures payment success
- **Transparent Pricing**: Clear distinction between one-time and subscription options

### For Developers
- **Automated Workflow**: No manual intervention required
- **Error Resilience**: Graceful handling of Stripe API failures
- **Audit Trail**: Complete history of price changes
- **Scalability**: Handles unlimited programs and splits

## Security Considerations

- **Metadata Validation**: All Stripe objects include project and split identifiers
- **Permission Checks**: Only program owners can update split pricing
- **API Key Security**: Stripe keys managed through environment variables
- **Webhook Verification**: Stripe webhooks verified for authenticity

## Testing

### Test Scenarios
1. **Create program with pricing**: Verify Stripe products/prices created
2. **Create program without pricing**: Verify no Stripe resources created
3. **Update split pricing**: Verify old prices deactivated, new ones created
4. **Stripe API failure**: Verify program creation still succeeds
5. **Duplicate prevention**: Verify no duplicate products created

### Test Commands
```bash
# Create test program
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/trainer/programs/draft" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"stepOneData": {...}, "stepTwoData": {...}}'

# Update split pricing
curl -X PUT "http://localhost:3000/v2/api/kanglink/custom/trainer/splits/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"full_price": 199.99, "subscription": 49.99}'
```

This integration ensures that the enrollment system has immediate access to properly configured Stripe products and prices, enabling seamless payment processing for all program splits.
