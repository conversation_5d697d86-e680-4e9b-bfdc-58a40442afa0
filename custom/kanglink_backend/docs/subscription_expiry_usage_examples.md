# Subscription Expiry Usage Examples

## Frontend Implementation Examples

### 1. Display Subscription Status

```javascript
function getSubscriptionStatusBadge(subscriptionInfo) {
  const { days_until_expiry, stripe_status, billing_failed } = subscriptionInfo;
  
  // Handle billing failures first
  if (billing_failed) {
    return {
      text: "Payment Failed",
      color: "red",
      action: "update_payment"
    };
  }
  
  // Handle subscription status
  if (days_until_expiry === null) {
    return {
      text: "One-time Purchase",
      color: "green",
      action: null
    };
  }
  
  if (days_until_expiry < 0) {
    return {
      text: "Expired",
      color: "red",
      action: "reactivate"
    };
  }
  
  if (days_until_expiry === 0) {
    return {
      text: "Expires Today",
      color: "orange",
      action: "renew_now"
    };
  }
  
  if (days_until_expiry <= 1) {
    return {
      text: `Expires in ${days_until_expiry} day`,
      color: "orange",
      action: "renew_soon"
    };
  }
  
  if (days_until_expiry <= 7) {
    return {
      text: `Expires in ${days_until_expiry} days`,
      color: "yellow",
      action: "renew_reminder"
    };
  }
  
  return {
    text: `${days_until_expiry} days remaining`,
    color: "green",
    action: null
  };
}
```

### 2. Subscription Card Component

```javascript
function SubscriptionCard({ enrollment }) {
  const { subscription_info, program, split } = enrollment;
  const statusBadge = getSubscriptionStatusBadge(subscription_info);
  
  return (
    <div className="subscription-card">
      <div className="program-info">
        <h3>{program.name}</h3>
        <p>{split.title}</p>
      </div>
      
      <div className="subscription-status">
        <span className={`badge badge-${statusBadge.color}`}>
          {statusBadge.text}
        </span>
        
        {subscription_info.will_cancel_at_period_end && (
          <span className="badge badge-warning">
            Will Cancel at Period End
          </span>
        )}
      </div>
      
      <div className="actions">
        {statusBadge.action === "update_payment" && (
          <button onClick={() => updatePaymentMethod(subscription_info.stripe_subscription_id)}>
            Update Payment Method
          </button>
        )}
        
        {statusBadge.action === "renew_now" && (
          <button onClick={() => renewSubscription(subscription_info.stripe_subscription_id)}>
            Renew Now
          </button>
        )}
        
        {statusBadge.action === "reactivate" && (
          <button onClick={() => reactivateSubscription(enrollment.id)}>
            Reactivate Subscription
          </button>
        )}
      </div>
    </div>
  );
}
```

### 3. Subscription Management Dashboard

```javascript
function SubscriptionDashboard({ enrollments }) {
  const subscriptions = enrollments.subscribed;
  
  // Categorize subscriptions by urgency
  const categorized = {
    expired: [],
    expiring_today: [],
    expiring_soon: [],
    healthy: [],
    billing_issues: []
  };
  
  subscriptions.forEach(sub => {
    const { subscription_info } = sub;
    
    if (subscription_info.billing_failed) {
      categorized.billing_issues.push(sub);
    } else if (subscription_info.days_until_expiry < 0) {
      categorized.expired.push(sub);
    } else if (subscription_info.days_until_expiry === 0) {
      categorized.expiring_today.push(sub);
    } else if (subscription_info.days_until_expiry <= 7) {
      categorized.expiring_soon.push(sub);
    } else {
      categorized.healthy.push(sub);
    }
  });
  
  return (
    <div className="subscription-dashboard">
      {categorized.billing_issues.length > 0 && (
        <section className="urgent-section">
          <h2>⚠️ Payment Issues ({categorized.billing_issues.length})</h2>
          {categorized.billing_issues.map(sub => (
            <SubscriptionCard key={sub.id} enrollment={sub} />
          ))}
        </section>
      )}
      
      {categorized.expired.length > 0 && (
        <section className="urgent-section">
          <h2>🔴 Expired ({categorized.expired.length})</h2>
          {categorized.expired.map(sub => (
            <SubscriptionCard key={sub.id} enrollment={sub} />
          ))}
        </section>
      )}
      
      {categorized.expiring_today.length > 0 && (
        <section className="warning-section">
          <h2>🟠 Expiring Today ({categorized.expiring_today.length})</h2>
          {categorized.expiring_today.map(sub => (
            <SubscriptionCard key={sub.id} enrollment={sub} />
          ))}
        </section>
      )}
      
      {categorized.expiring_soon.length > 0 && (
        <section className="info-section">
          <h2>🟡 Expiring Soon ({categorized.expiring_soon.length})</h2>
          {categorized.expiring_soon.map(sub => (
            <SubscriptionCard key={sub.id} enrollment={sub} />
          ))}
        </section>
      )}
      
      {categorized.healthy.length > 0 && (
        <section className="healthy-section">
          <h2>🟢 Active ({categorized.healthy.length})</h2>
          {categorized.healthy.map(sub => (
            <SubscriptionCard key={sub.id} enrollment={sub} />
          ))}
        </section>
      )}
    </div>
  );
}
```

## Backend Usage Examples

### 1. Send Expiry Notifications

```javascript
// In a cron job or scheduled task
async function sendExpiryNotifications() {
  const sdk = app.get("sdk");
  sdk.setProjectId("kanglink");
  
  // Get subscriptions expiring in 7 days
  const expiringSoon = await sdk.rawQuery(`
    SELECT e.*, u.email, p.program_name, s.title as split_title
    FROM kanglink_enrollment e
    JOIN kanglink_user u ON e.athlete_id = u.id
    JOIN kanglink_split s ON e.split_id = s.id
    JOIN kanglink_program p ON s.program_id = p.id
    WHERE e.payment_type = 'subscription'
      AND e.status = 'active'
      AND DATEDIFF(e.expiry_date, NOW()) = 7
  `);
  
  for (const enrollment of expiringSoon) {
    await sendEmail({
      to: enrollment.email,
      subject: `Your ${enrollment.program_name} subscription expires in 7 days`,
      template: 'subscription_expiry_reminder',
      data: {
        program_name: enrollment.program_name,
        split_title: enrollment.split_title,
        expiry_date: enrollment.expiry_date,
        renewal_link: `${process.env.APP_URL}/renew/${enrollment.id}`
      }
    });
  }
}
```

### 2. Auto-expire Subscriptions

```javascript
// In a cron job to handle expired subscriptions
async function handleExpiredSubscriptions() {
  const sdk = app.get("sdk");
  sdk.setProjectId("kanglink");
  
  // Find expired subscriptions
  const expired = await sdk.rawQuery(`
    SELECT e.*
    FROM kanglink_enrollment e
    WHERE e.payment_type = 'subscription'
      AND e.status = 'active'
      AND e.expiry_date < NOW()
  `);
  
  for (const enrollment of expired) {
    // Update enrollment status
    await sdk.update("enrollment", enrollment.id, {
      status: "expired"
    });
    
    // Cancel Stripe subscription if exists
    if (enrollment.stripe_subscription_id) {
      try {
        await stripeService.cancelStripeSubscription({
          subscriptionId: enrollment.stripe_subscription_id
        });
      } catch (error) {
        console.error(`Failed to cancel Stripe subscription ${enrollment.stripe_subscription_id}:`, error);
      }
    }
    
    // Send expiry notification
    const user = await sdk.findOne("user", { id: enrollment.athlete_id });
    await sendEmail({
      to: user.email,
      subject: "Your subscription has expired",
      template: 'subscription_expired',
      data: {
        enrollment_id: enrollment.id,
        reactivation_link: `${process.env.APP_URL}/reactivate/${enrollment.id}`
      }
    });
  }
}
```

## API Response Example

```json
{
  "error": false,
  "data": {
    "subscribed": [
      {
        "id": 123,
        "athlete_id": 456,
        "program_id": 789,
        "split_id": 101,
        "payment_type": "subscription",
        "amount": 29.99,
        "currency": "USD",
        "status": "active",
        "payment_status": "paid",
        "subscription_info": {
          "billing_failed": false,
          "stripe_subscription_id": "sub_1234567890",
          "days_until_expiry": 15,
          "expiry_date": "2024-02-15T10:30:00Z",
          "stripe_period_end": "2024-02-15T10:30:00Z",
          "stripe_status": "active",
          "will_cancel_at_period_end": false
        },
        "program": {
          "id": 789,
          "name": "Advanced Strength Training",
          "type": "strength",
          "description": "Complete strength program",
          "image_url": "https://example.com/program.jpg"
        },
        "split": {
          "id": 101,
          "title": "Upper/Lower Split",
          "equipment_required": "Gym equipment",
          "duration_weeks": 12
        }
      }
    ]
  }
}
```
