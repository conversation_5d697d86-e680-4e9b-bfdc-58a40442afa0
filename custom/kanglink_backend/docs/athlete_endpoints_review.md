# Athlete Endpoints Review and Fixes

## Overview

This document reviews all athlete endpoints and the fixes applied to resolve database field issues.

## Endpoints Summary

### 1. GET `/v2/api/kanglink/custom/athlete/library`

**Purpose**: Get athlete's enrollments categorized by status (owned, subscribed, pending refund, refunded)

**Key Features**:

- Comprehensive data joining across enrollment, split, program, and user tables
- Dynamic duration calculation by counting weeks for each split
- Refund eligibility and time remaining calculations
- **Subscription expiry tracking** with days until expiry calculation
- Categorization based on enrollment status and payment type

**Fixes Applied**:

- Removed references to non-existent `s.description` field
- Replaced `s.duration_weeks` with dynamic calculation: `(SELECT COUNT(*) FROM kanglink_week w WHERE w.split_id = s.id)`
- Removed references to non-existent `s.status` field
- Fixed JOIN clause: `p.user_id = trainer.id` (program table uses `user_id` for trainer reference)
- Added `trainer.id as trainer_id` to SELECT clause for proper trainer ID
- **Added subscription expiry tracking**: `days_until_expiry`, `stripe_period_end`, `stripe_status`
- **Added LEFT JOIN with Stripe subscription table** for accurate expiration data

### 2. GET `/v2/api/kanglink/custom/athlete/favorite/programs`

**Purpose**: Get athlete's favorite programs with trainer and pricing information

**Key Features**:

- Program details with trainer information from user.data JSON field
- Average rating and review count from post_feed table
- Minimum price calculation across all splits for each program

**Fixes Applied**:

- Removed reference to non-existent `s.status = 'active'` filter
- Confirmed JOIN clause: `p.user_id = u.id` (correct - program table uses `user_id` for trainer reference)

### 3. POST `/v2/api/kanglink/custom/athlete/favorite/programs/:programId`

**Purpose**: Add a program to athlete's favorites

**Status**: ✅ No database field issues

### 4. DELETE `/v2/api/kanglink/custom/athlete/favorite/programs/:programId`

**Purpose**: Remove a program from athlete's favorites

**Status**: ✅ No database field issues

### 5. GET `/v2/api/kanglink/custom/athlete/favorite/trainers`

**Purpose**: Get athlete's favorite trainers with their program information

**Status**: ✅ No database field issues (uses user.data JSON field correctly)

### 6. POST `/v2/api/kanglink/custom/athlete/favorite/trainers/:trainerId`

**Purpose**: Add a trainer to athlete's favorites

**Status**: ✅ No database field issues

### 7. DELETE `/v2/api/kanglink/custom/athlete/favorite/trainers/:trainerId`

**Purpose**: Remove a trainer from athlete's favorites

**Status**: ✅ No database field issues

## Model Fixes

### Split Model (`models/split.js`)

**Removed Non-Existent Fields**:

- `description` (type: "long text")
- `duration_weeks` (type: "integer")
- `status` (type: "mapping")

**Removed Methods**:

- `transformStatus()` method (no longer needed)

**Remaining Fields**:

- `id` (primary key)
- `program_id` (foreign key)
- `equipment_required` (string)
- `title` (string)
- `full_price` (float)
- `subscription` (float)
- `created_at` (datetime)
- `updated_at` (datetime)

## Database Schema Assumptions

Based on the fixes, the actual database schema appears to be:

### kanglink_split table

```sql
CREATE TABLE kanglink_split (
  id INT AUTO_INCREMENT PRIMARY KEY,
  program_id INT NOT NULL,
  equipment_required VARCHAR(255),
  title VARCHAR(255),
  full_price DECIMAL(10,2),
  subscription DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### kanglink_week table

```sql
CREATE TABLE kanglink_week (
  id INT AUTO_INCREMENT PRIMARY KEY,
  split_id INT NOT NULL,
  title VARCHAR(255),
  week_order INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Key Relationships

1. **Enrollment → Split**: `kanglink_enrollment.split_id = kanglink_split.id`
2. **Split → Program**: `kanglink_split.program_id = kanglink_program.id`
3. **Program → Trainer**: `kanglink_program.user_id = kanglink_user.id` (program table uses `user_id` for trainer)
4. **Split → Weeks**: `kanglink_week.split_id = kanglink_split.id`

## Duration Calculation

Instead of storing duration in the split table, duration is calculated dynamically:

```sql
(SELECT COUNT(*) FROM kanglink_week w WHERE w.split_id = s.id) as duration_weeks
```

This approach:

- ✅ Uses actual database structure
- ✅ Provides accurate week counts
- ✅ Automatically updates when weeks are added/removed
- ✅ Eliminates data inconsistency issues

## Subscription Expiry Tracking

### How We Calculate Days Until Expiry

The system uses a **priority-based approach** to determine the most accurate subscription expiration:

#### Priority 1: Stripe Subscription Data (Most Accurate)

```sql
ss.current_period_end as stripe_period_end
```

- Uses `kanglink_stripe_subscription.current_period_end`
- Most accurate as it reflects actual Stripe billing cycles
- Updated automatically via Stripe webhooks

#### Priority 2: Enrollment Expiry Date (Fallback)

```sql
DATEDIFF(e.expiry_date, NOW()) as days_until_expiry
```

- Uses `kanglink_enrollment.expiry_date`
- Fallback when Stripe data is unavailable

#### Priority 3: SQL Calculation (Last Resort)

- Direct SQL calculation from enrollment table
- Used when other methods are unavailable

### Subscription Information Returned

```json
{
  "subscription_info": {
    "billing_failed": false,
    "stripe_subscription_id": "sub_1234567890",
    "days_until_expiry": 15,
    "expiry_date": "2024-02-15T10:30:00Z",
    "stripe_period_end": "2024-02-15T10:30:00Z",
    "stripe_status": "active",
    "will_cancel_at_period_end": false
  }
}
```

### Status Interpretation

- **`days_until_expiry > 7`**: Healthy subscription
- **`days_until_expiry <= 7`**: Expiring soon (show renewal reminder)
- **`days_until_expiry <= 1`**: Expiring very soon (urgent renewal)
- **`days_until_expiry = 0`**: Expires today
- **`days_until_expiry < 0`**: Expired (show reactivation option)
- **`days_until_expiry = null`**: One-time purchase or unknown

## Testing

Run the test suite to verify all endpoints work correctly:

```bash
cd mtpbk
node test_runner.js custom/ksl_be/tests/athlete_endpoints_fixed.test.js
```

## Next Steps

1. **Database Migration**: If the missing fields are needed, create proper migration scripts
2. **Documentation**: Update API documentation to reflect actual response structure
3. **Frontend Updates**: Ensure frontend code handles the corrected field names and structure
4. **Performance**: Consider adding indexes on frequently queried fields like `split_id` in the week table
