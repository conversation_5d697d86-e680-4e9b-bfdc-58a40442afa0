# Frontend Discount System Implementation Guide

## Overview

This guide provides everything the frontend team needs to implement the discount management system for fitness programs. The system supports program-level discounts, split-specific discounts, and promotional coupon codes.

## API Base URL

```
/v2/api/kanglink/custom/trainer/programs/{programId}/discounts
```

## TypeScript Interfaces

### Core Discount Types

```typescript
// Discount configuration for a program
interface ProgramDiscountConfig {
  id?: number;
  affiliateLink: string;
  saleDiscount?: SaleDiscount;
  subscriptionDiscounts: SplitDiscount[];
  fullPriceDiscounts: SplitDiscount[];
  promoCode?: PromoCode;
  lastUpdated?: string;
}

// Program-level sale discount
interface SaleDiscount {
  type: "fixed" | "percentage";
  value: number;
  applyToAll: boolean;
}

// Split-specific discount
interface SplitDiscount {
  id?: number;
  tierId: number; // split ID
  discountType: "fixed" | "percentage";
  discountValue: number;
}

// Promotional coupon code
interface PromoCode {
  id?: number;
  code: string;
  discountType: "fixed" | "percentage";
  discountValue: number;
  appliesTo: {
    subscription: boolean;
    fullPayment: boolean;
  };
  isActive: boolean;
  expiryDate?: string; // ISO date string
  usageLimit?: number;
  usedCount?: number;
}

// API Response wrapper
interface DiscountApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
}

// Creation response
interface DiscountCreationResponse {
  programId: number;
  programDiscountId: number;
  subscriptionDiscountsCount: number;
  fullPriceDiscountsCount: number;
  couponId: number | null;
  createdAt: string;
}

// Deletion response
interface DiscountDeletionResponse {
  programId: number;
  deletionSummary: {
    programDiscountDeleted: boolean;
    discountsDeleted: number;
    couponsDeleted: number;
    couponUsagesDeleted: number;
  };
  deletedAt: string;
}
```

## API Service Implementation

### React Hook for Discount Management

```typescript
import { useState, useCallback } from "react";

interface UseDiscountManagerProps {
  programId: number;
  authToken: string;
}

export const useDiscountManager = ({
  programId,
  authToken,
}: UseDiscountManagerProps) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [discountConfig, setDiscountConfig] =
    useState<ProgramDiscountConfig | null>(null);

  const baseUrl = `/v2/api/kanglink/custom/trainer/programs/${programId}/discounts`;

  const headers = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${authToken}`,
  };

  // Fetch discount configuration
  const fetchDiscountConfig = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(baseUrl, { headers });
      const result: DiscountApiResponse<ProgramDiscountConfig> =
        await response.json();

      if (result.success) {
        setDiscountConfig(result.data || null);
      } else {
        setError(result.message || "Failed to fetch discount configuration");
      }
    } catch (err) {
      setError("Network error occurred");
    } finally {
      setLoading(false);
    }
  }, [baseUrl]);

  // Create discount configuration
  const createDiscountConfig = useCallback(
    async (config: Omit<ProgramDiscountConfig, "id" | "lastUpdated">) => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(baseUrl, {
          method: "POST",
          headers,
          body: JSON.stringify(config),
        });

        const result: DiscountApiResponse<DiscountCreationResponse> =
          await response.json();

        if (result.success) {
          await fetchDiscountConfig(); // Refresh data
          return result.data;
        } else {
          setError(result.message || "Failed to create discount configuration");
          return null;
        }
      } catch (err) {
        setError("Network error occurred");
        return null;
      } finally {
        setLoading(false);
      }
    },
    [baseUrl, fetchDiscountConfig]
  );

  // Update discount configuration
  const updateDiscountConfig = useCallback(
    async (config: Omit<ProgramDiscountConfig, "id" | "lastUpdated">) => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(baseUrl, {
          method: "PUT",
          headers,
          body: JSON.stringify(config),
        });

        const result: DiscountApiResponse = await response.json();

        if (result.success) {
          await fetchDiscountConfig(); // Refresh data
          return true;
        } else {
          setError(result.message || "Failed to update discount configuration");
          return false;
        }
      } catch (err) {
        setError("Network error occurred");
        return false;
      } finally {
        setLoading(false);
      }
    },
    [baseUrl, fetchDiscountConfig]
  );

  // Delete discount configuration
  const deleteDiscountConfig = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(baseUrl, {
        method: "DELETE",
        headers,
      });

      const result: DiscountApiResponse<DiscountDeletionResponse> =
        await response.json();

      if (result.success) {
        setDiscountConfig(null);
        return result.data;
      } else {
        setError(result.message || "Failed to delete discount configuration");
        return null;
      }
    } catch (err) {
      setError("Network error occurred");
      return null;
    } finally {
      setLoading(false);
    }
  }, [baseUrl]);

  return {
    discountConfig,
    loading,
    error,
    fetchDiscountConfig,
    createDiscountConfig,
    updateDiscountConfig,
    deleteDiscountConfig,
  };
};
```

## Form Validation Utilities

```typescript
// Validation rules
export const discountValidation = {
  // Validate discount type and value
  validateDiscount: (type: string, value: number): string | null => {
    if (!["fixed", "percentage"].includes(type)) {
      return "Discount type must be fixed or percentage";
    }

    if (value < 0) {
      return "Discount value must be positive";
    }

    if (type === "percentage" && value > 100) {
      return "Percentage discount cannot exceed 100%";
    }

    return null;
  },

  // Validate promo code
  validatePromoCode: (code: string): string | null => {
    if (code.length < 3) {
      return "Promo code must be at least 3 characters long";
    }

    if (code.length > 50) {
      return "Promo code cannot exceed 50 characters";
    }

    if (!/^[A-Za-z0-9_-]+$/.test(code)) {
      return "Promo code can only contain letters, numbers, hyphens, and underscores";
    }

    return null;
  },

  // Validate expiry date
  validateExpiryDate: (date: string): string | null => {
    const expiryDate = new Date(date);

    if (isNaN(expiryDate.getTime())) {
      return "Invalid date format";
    }

    if (expiryDate <= new Date()) {
      return "Expiry date must be in the future";
    }

    return null;
  },

  // Validate usage limit
  validateUsageLimit: (limit: number): string | null => {
    if (limit < 1) {
      return "Usage limit must be positive";
    }

    return null;
  },
};
```

### Split-Specific Discount Component

```typescript
interface SplitDiscountManagerProps {
  splits: Array<{ id: number; title: string }>;
  discounts: SplitDiscount[];
  discountType: "subscription" | "fullPrice";
  onChange: (discounts: SplitDiscount[]) => void;
}

export const SplitDiscountManager: React.FC<SplitDiscountManagerProps> = ({
  splits,
  discounts,
  discountType,
  onChange,
}) => {
  const addDiscount = () => {
    const newDiscount: SplitDiscount = {
      tierId: splits[0]?.id || 0,
      discountType: "percentage",
      discountValue: 0,
    };
    onChange([...discounts, newDiscount]);
  };

  const updateDiscount = (index: number, updates: Partial<SplitDiscount>) => {
    const updated = discounts.map((discount, i) =>
      i === index ? { ...discount, ...updates } : discount
    );
    onChange(updated);
  };

  const removeDiscount = (index: number) => {
    onChange(discounts.filter((_, i) => i !== index));
  };

  return (
    <div className="split-discount-manager">
      <h4>
        {discountType === "subscription" ? "Subscription" : "Full Price"}{" "}
        Discounts
      </h4>

      {discounts.map((discount, index) => (
        <div key={index} className="discount-item">
          <div className="form-group">
            <label>Split</label>
            <select
              value={discount.tierId}
              onChange={(e) =>
                updateDiscount(index, { tierId: parseInt(e.target.value) })
              }
            >
              {splits.map((split) => (
                <option key={split.id} value={split.id}>
                  {split.title}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label>Type</label>
            <select
              value={discount.discountType}
              onChange={(e) =>
                updateDiscount(index, {
                  discountType: e.target.value as "fixed" | "percentage",
                })
              }
            >
              <option value="percentage">Percentage</option>
              <option value="fixed">Fixed Amount</option>
            </select>
          </div>

          <div className="form-group">
            <label>Value</label>
            <input
              type="number"
              min="0"
              max={discount.discountType === "percentage" ? 100 : undefined}
              value={discount.discountValue}
              onChange={(e) =>
                updateDiscount(index, {
                  discountValue: parseFloat(e.target.value) || 0,
                })
              }
            />
          </div>

          <button type="button" onClick={() => removeDiscount(index)}>
            Remove
          </button>
        </div>
      ))}

      <button type="button" onClick={addDiscount}>
        Add {discountType === "subscription" ? "Subscription" : "Full Price"}{" "}
        Discount
      </button>
    </div>
  );
};
```

## Error Handling

```typescript
// Error handling utility
export const handleDiscountApiError = (error: any): string => {
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  return "An unexpected error occurred";
};

// Common error messages
export const DISCOUNT_ERROR_MESSAGES = {
  NETWORK_ERROR: "Network error. Please check your connection.",
  UNAUTHORIZED: "You are not authorized to perform this action.",
  PROGRAM_NOT_FOUND: "Program not found or access denied.",
  ALREADY_EXISTS: "Discount configuration already exists. Use update instead.",
  NOT_FOUND: "No discount configuration found for this program.",
  VALIDATION_ERROR: "Please check your input and try again.",
  DUPLICATE_COUPON: "This coupon code is already in use.",
  INVALID_SPLIT: "Selected split does not belong to this program.",
};
```

## State Management (Redux/Zustand)

```typescript
// Zustand store example
import { create } from "zustand";

interface DiscountStore {
  discountConfigs: Record<number, ProgramDiscountConfig>;
  loading: boolean;
  error: string | null;

  // Actions
  setDiscountConfig: (programId: number, config: ProgramDiscountConfig) => void;
  removeDiscountConfig: (programId: number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useDiscountStore = create<DiscountStore>((set) => ({
  discountConfigs: {},
  loading: false,
  error: null,

  setDiscountConfig: (programId, config) =>
    set((state) => ({
      discountConfigs: { ...state.discountConfigs, [programId]: config },
    })),

  removeDiscountConfig: (programId) =>
    set((state) => {
      const { [programId]: removed, ...rest } = state.discountConfigs;
      return { discountConfigs: rest };
    }),

  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
}));
```

## CSS Styling Examples

```css
/* Discount form styles */
.discount-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.form-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.form-section h3 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #007bff;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.error {
  color: #dc3545;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.split-discount-manager {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.discount-item {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr auto;
  gap: 15px;
  align-items: end;
  padding: 15px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 10px;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

.form-actions button {
  background-color: #007bff;
  color: white;
  padding: 12px 30px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.form-actions button:hover:not(:disabled) {
  background-color: #0056b3;
}

.form-actions button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}
```

## Testing Examples

```typescript
// Jest test example
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { DiscountConfigurationForm } from "./DiscountConfigurationForm";

const mockProps = {
  programId: 1,
  programSplits: [
    { id: 1, title: "Split A" },
    { id: 2, title: "Split B" },
  ],
  authToken: "mock-token",
};

describe("DiscountConfigurationForm", () => {
  test("renders form elements correctly", () => {
    render(<DiscountConfigurationForm {...mockProps} />);

    expect(screen.getByLabelText(/affiliate link/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/enable sale discount/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/enable promo code/i)).toBeInTheDocument();
  });

  test("validates promo code input", async () => {
    render(<DiscountConfigurationForm {...mockProps} />);

    // Enable promo code
    fireEvent.click(screen.getByLabelText(/enable promo code/i));

    // Enter invalid code
    const codeInput = screen.getByLabelText(/promo code/i);
    fireEvent.change(codeInput, { target: { value: "AB" } });

    // Submit form
    fireEvent.click(screen.getByRole("button", { name: /create/i }));

    await waitFor(() => {
      expect(screen.getByText(/at least 3 characters/i)).toBeInTheDocument();
    });
  });
});
```

## Implementation Checklist

### Phase 1: Basic Setup

- [ ] Install required dependencies
- [ ] Set up TypeScript interfaces
- [ ] Create API service functions
- [ ] Implement basic form validation

### Phase 2: Core Components

- [ ] Create discount configuration form
- [ ] Implement split-specific discount manager
- [ ] Add promo code management
- [ ] Set up error handling

### Phase 3: Advanced Features

- [ ] Add state management (Redux/Zustand)
- [ ] Implement real-time validation
- [ ] Add loading states and optimistic updates
- [ ] Create comprehensive test suite

### Phase 4: Polish

- [ ] Add responsive design
- [ ] Implement accessibility features
- [ ] Add animations and transitions
- [ ] Performance optimization

## Common Pitfalls to Avoid

1. **Validation**: Always validate on both frontend and backend
2. **Error Handling**: Provide clear, actionable error messages
3. **State Management**: Keep discount state in sync with server
4. **Performance**: Debounce API calls for real-time validation
5. **Accessibility**: Ensure form is keyboard navigable and screen reader friendly
6. **Security**: Never trust client-side validation alone
7. **UX**: Provide clear feedback for all user actions

This comprehensive guide should provide everything needed to implement a robust discount management system in the frontend! 🚀

## React Component Examples

### Discount Configuration Form

```typescript
import React, { useState, useEffect } from "react";

interface DiscountFormProps {
  programId: number;
  programSplits: Array<{ id: number; title: string }>;
  authToken: string;
  onSuccess?: () => void;
}

export const DiscountConfigurationForm: React.FC<DiscountFormProps> = ({
  programId,
  programSplits,
  authToken,
  onSuccess,
}) => {
  const {
    discountConfig,
    loading,
    error,
    fetchDiscountConfig,
    createDiscountConfig,
    updateDiscountConfig,
  } = useDiscountManager({ programId, authToken });

  const [formData, setFormData] = useState<
    Omit<ProgramDiscountConfig, "id" | "lastUpdated">
  >({
    affiliateLink: "",
    subscriptionDiscounts: [],
    fullPriceDiscounts: [],
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  useEffect(() => {
    fetchDiscountConfig();
  }, [fetchDiscountConfig]);

  useEffect(() => {
    if (discountConfig) {
      setFormData({
        affiliateLink: discountConfig.affiliateLink || "",
        saleDiscount: discountConfig.saleDiscount,
        subscriptionDiscounts: discountConfig.subscriptionDiscounts || [],
        fullPriceDiscounts: discountConfig.fullPriceDiscounts || [],
        promoCode: discountConfig.promoCode,
      });
    }
  }, [discountConfig]);

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // Validate sale discount
    if (formData.saleDiscount) {
      const saleError = discountValidation.validateDiscount(
        formData.saleDiscount.type,
        formData.saleDiscount.value
      );
      if (saleError) errors.saleDiscount = saleError;
    }

    // Validate subscription discounts
    formData.subscriptionDiscounts.forEach((discount, index) => {
      const error = discountValidation.validateDiscount(
        discount.discountType,
        discount.discountValue
      );
      if (error) errors[`subscriptionDiscount_${index}`] = error;
    });

    // Validate full price discounts
    formData.fullPriceDiscounts.forEach((discount, index) => {
      const error = discountValidation.validateDiscount(
        discount.discountType,
        discount.discountValue
      );
      if (error) errors[`fullPriceDiscount_${index}`] = error;
    });

    // Validate promo code
    if (formData.promoCode) {
      const codeError = discountValidation.validatePromoCode(
        formData.promoCode.code
      );
      if (codeError) errors.promoCode = codeError;

      if (formData.promoCode.expiryDate) {
        const dateError = discountValidation.validateExpiryDate(
          formData.promoCode.expiryDate
        );
        if (dateError) errors.promoCodeExpiry = dateError;
      }

      if (formData.promoCode.usageLimit) {
        const limitError = discountValidation.validateUsageLimit(
          formData.promoCode.usageLimit
        );
        if (limitError) errors.promoCodeLimit = limitError;
      }
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    const success = discountConfig
      ? await updateDiscountConfig(formData)
      : await createDiscountConfig(formData);

    if (success && onSuccess) {
      onSuccess();
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <form onSubmit={handleSubmit} className="discount-form">
      {error && <div className="error-message">{error}</div>}

      {/* Affiliate Link */}
      <div className="form-group">
        <label htmlFor="affiliateLink">Affiliate Link</label>
        <input
          id="affiliateLink"
          type="url"
          value={formData.affiliateLink}
          onChange={(e) =>
            setFormData({ ...formData, affiliateLink: e.target.value })
          }
          placeholder="https://example.com/affiliate"
        />
      </div>

      {/* Sale Discount Section */}
      <div className="form-section">
        <h3>Sale Discount</h3>
        <div className="form-group">
          <label>
            <input
              type="checkbox"
              checked={!!formData.saleDiscount}
              onChange={(e) => {
                if (e.target.checked) {
                  setFormData({
                    ...formData,
                    saleDiscount: {
                      type: "percentage",
                      value: 0,
                      applyToAll: false,
                    },
                  });
                } else {
                  setFormData({ ...formData, saleDiscount: undefined });
                }
              }}
            />
            Enable Sale Discount
          </label>
        </div>

        {formData.saleDiscount && (
          <>
            <div className="form-group">
              <label>Discount Type</label>
              <select
                value={formData.saleDiscount.type}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    saleDiscount: {
                      ...formData.saleDiscount!,
                      type: e.target.value as "fixed" | "percentage",
                    },
                  })
                }
              >
                <option value="percentage">Percentage</option>
                <option value="fixed">Fixed Amount</option>
              </select>
            </div>

            <div className="form-group">
              <label>Discount Value</label>
              <input
                type="number"
                min="0"
                max={
                  formData.saleDiscount.type === "percentage" ? 100 : undefined
                }
                value={formData.saleDiscount.value}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    saleDiscount: {
                      ...formData.saleDiscount!,
                      value: parseFloat(e.target.value) || 0,
                    },
                  })
                }
              />
              {validationErrors.saleDiscount && (
                <span className="error">{validationErrors.saleDiscount}</span>
              )}
            </div>

            <div className="form-group">
              <label>
                <input
                  type="checkbox"
                  checked={formData.saleDiscount.applyToAll}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      saleDiscount: {
                        ...formData.saleDiscount!,
                        applyToAll: e.target.checked,
                      },
                    })
                  }
                />
                Apply to All Splits
              </label>
            </div>
          </>
        )}
      </div>

      {/* Promo Code Section */}
      <div className="form-section">
        <h3>Promotional Code</h3>
        <div className="form-group">
          <label>
            <input
              type="checkbox"
              checked={!!formData.promoCode}
              onChange={(e) => {
                if (e.target.checked) {
                  setFormData({
                    ...formData,
                    promoCode: {
                      code: "",
                      discountType: "percentage",
                      discountValue: 0,
                      appliesTo: { subscription: true, fullPayment: true },
                      isActive: true,
                    },
                  });
                } else {
                  setFormData({ ...formData, promoCode: undefined });
                }
              }}
            />
            Enable Promo Code
          </label>
        </div>

        {formData.promoCode && (
          <>
            <div className="form-group">
              <label>Promo Code</label>
              <input
                type="text"
                value={formData.promoCode.code}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    promoCode: {
                      ...formData.promoCode!,
                      code: e.target.value.toUpperCase(),
                    },
                  })
                }
                placeholder="SAVE20"
                maxLength={50}
              />
              {validationErrors.promoCode && (
                <span className="error">{validationErrors.promoCode}</span>
              )}
            </div>

            <div className="form-group">
              <label>Discount Type</label>
              <select
                value={formData.promoCode.discountType}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    promoCode: {
                      ...formData.promoCode!,
                      discountType: e.target.value as "fixed" | "percentage",
                    },
                  })
                }
              >
                <option value="percentage">Percentage</option>
                <option value="fixed">Fixed Amount</option>
              </select>
            </div>

            <div className="form-group">
              <label>Discount Value</label>
              <input
                type="number"
                min="0"
                max={
                  formData.promoCode.discountType === "percentage"
                    ? 100
                    : undefined
                }
                value={formData.promoCode.discountValue}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    promoCode: {
                      ...formData.promoCode!,
                      discountValue: parseFloat(e.target.value) || 0,
                    },
                  })
                }
              />
            </div>

            <div className="form-group">
              <label>Applies To</label>
              <div>
                <label>
                  <input
                    type="checkbox"
                    checked={formData.promoCode.appliesTo.subscription}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        promoCode: {
                          ...formData.promoCode!,
                          appliesTo: {
                            ...formData.promoCode!.appliesTo,
                            subscription: e.target.checked,
                          },
                        },
                      })
                    }
                  />
                  Subscription
                </label>
                <label>
                  <input
                    type="checkbox"
                    checked={formData.promoCode.appliesTo.fullPayment}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        promoCode: {
                          ...formData.promoCode!,
                          appliesTo: {
                            ...formData.promoCode!.appliesTo,
                            fullPayment: e.target.checked,
                          },
                        },
                      })
                    }
                  />
                  Full Payment
                </label>
              </div>
            </div>

            <div className="form-group">
              <label>Expiry Date (Optional)</label>
              <input
                type="datetime-local"
                value={formData.promoCode.expiryDate?.slice(0, 16) || ""}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    promoCode: {
                      ...formData.promoCode!,
                      expiryDate: e.target.value
                        ? new Date(e.target.value).toISOString()
                        : undefined,
                    },
                  })
                }
              />
              {validationErrors.promoCodeExpiry && (
                <span className="error">
                  {validationErrors.promoCodeExpiry}
                </span>
              )}
            </div>

            <div className="form-group">
              <label>Usage Limit (Optional)</label>
              <input
                type="number"
                min="1"
                value={formData.promoCode.usageLimit || ""}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    promoCode: {
                      ...formData.promoCode!,
                      usageLimit: e.target.value
                        ? parseInt(e.target.value)
                        : undefined,
                    },
                  })
                }
                placeholder="100"
              />
              {validationErrors.promoCodeLimit && (
                <span className="error">{validationErrors.promoCodeLimit}</span>
              )}
            </div>
          </>
        )}
      </div>

      <div className="form-actions">
        <button type="submit" disabled={loading}>
          {loading ? "Saving..." : discountConfig ? "Update" : "Create"}{" "}
          Discount Configuration
        </button>
      </div>
    </form>
  );
};
```
