# Frontend Progress Tracking Integration

## Overview

This document provides examples of how to integrate the athlete progress tracking system with your frontend application.

## Athlete Interface Examples

### 1. Exercise Completion Component

```typescript
interface ExerciseCompletionProps {
  exerciseInstance: ExerciseInstance;
  enrollmentId: number;
  onComplete: (data: any) => void;
}

const ExerciseCompletion: React.FC<ExerciseCompletionProps> = ({
  exerciseInstance,
  enrollmentId,
  onComplete
}) => {
  const [formData, setFormData] = useState({
    sets_completed: 0,
    reps_completed: '',
    weight_used: '',
    time_taken_seconds: 0,
    difficulty_rating: 3,
    notes: ''
  });

  const handleComplete = async () => {
    try {
      const response = await fetch('/v2/api/kanglink/custom/athlete/exercise/complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userToken}`
        },
        body: JSON.stringify({
          enrollment_id: enrollmentId,
          exercise_instance_id: exerciseInstance.id,
          ...formData
        })
      });

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.message);
      }

      onComplete(result.data);
      // Show success message
      toast.success('Exercise completed!');
      
    } catch (error) {
      toast.error('Failed to mark exercise as complete');
      console.error(error);
    }
  };

  return (
    <div className="exercise-completion">
      <h3>{exerciseInstance.label}</h3>
      
      <div className="form-group">
        <label>Sets Completed:</label>
        <input
          type="number"
          value={formData.sets_completed}
          onChange={(e) => setFormData({...formData, sets_completed: parseInt(e.target.value)})}
        />
      </div>

      <div className="form-group">
        <label>Reps/Time:</label>
        <input
          type="text"
          placeholder="e.g., 10,10,8 or 30 seconds"
          value={formData.reps_completed}
          onChange={(e) => setFormData({...formData, reps_completed: e.target.value})}
        />
      </div>

      <div className="form-group">
        <label>Weight Used:</label>
        <input
          type="text"
          placeholder="e.g., 50kg or bodyweight"
          value={formData.weight_used}
          onChange={(e) => setFormData({...formData, weight_used: e.target.value})}
        />
      </div>

      <div className="form-group">
        <label>Difficulty (1-5):</label>
        <select
          value={formData.difficulty_rating}
          onChange={(e) => setFormData({...formData, difficulty_rating: parseInt(e.target.value)})}
        >
          {[1,2,3,4,5].map(rating => (
            <option key={rating} value={rating}>{rating}</option>
          ))}
        </select>
      </div>

      <div className="form-group">
        <label>Notes:</label>
        <textarea
          value={formData.notes}
          onChange={(e) => setFormData({...formData, notes: e.target.value})}
          placeholder="How did it feel? Any observations?"
        />
      </div>

      <button onClick={handleComplete} className="btn-complete">
        Mark Complete
      </button>
    </div>
  );
};
```

### 2. Day Completion Component

```typescript
interface DayCompletionProps {
  day: Day;
  enrollmentId: number;
  exercises: ExerciseInstance[];
  onComplete: (data: any) => void;
}

const DayCompletion: React.FC<DayCompletionProps> = ({
  day,
  enrollmentId,
  exercises,
  onComplete
}) => {
  const [notes, setNotes] = useState('');
  const [completedExercises, setCompletedExercises] = useState<number[]>([]);

  const handleDayComplete = async () => {
    try {
      const response = await fetch('/v2/api/kanglink/custom/athlete/day/complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userToken}`
        },
        body: JSON.stringify({
          enrollment_id: enrollmentId,
          day_id: day.id,
          notes: notes
        })
      });

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.message);
      }

      onComplete(result.data);
      toast.success(result.message);
      
    } catch (error) {
      toast.error('Failed to mark day as complete');
      console.error(error);
    }
  };

  const allExercisesCompleted = exercises.length > 0 && 
    completedExercises.length === exercises.length;

  return (
    <div className="day-completion">
      <h2>{day.title}</h2>
      
      <div className="exercise-list">
        {exercises.map(exercise => (
          <ExerciseCompletion
            key={exercise.id}
            exerciseInstance={exercise}
            enrollmentId={enrollmentId}
            onComplete={(data) => {
              setCompletedExercises(prev => [...prev, exercise.id]);
            }}
          />
        ))}
      </div>

      <div className="day-notes">
        <label>Day Notes:</label>
        <textarea
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          placeholder="How was your workout today?"
        />
      </div>

      <div className="completion-status">
        <p>Progress: {completedExercises.length}/{exercises.length} exercises completed</p>
        
        <button 
          onClick={handleDayComplete}
          className={`btn-complete ${allExercisesCompleted ? 'enabled' : 'partial'}`}
        >
          {allExercisesCompleted ? 'Complete Day' : 'Save Progress'}
        </button>
      </div>
    </div>
  );
};
```

### 3. Progress Dashboard

```typescript
const AthleteProgressDashboard: React.FC<{enrollmentId: number}> = ({enrollmentId}) => {
  const [progress, setProgress] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProgress();
  }, [enrollmentId]);

  const fetchProgress = async () => {
    try {
      const response = await fetch(`/v2/api/kanglink/custom/athlete/progress/${enrollmentId}`, {
        headers: {
          'Authorization': `Bearer ${userToken}`
        }
      });

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.message);
      }

      setProgress(result.data);
    } catch (error) {
      console.error('Failed to fetch progress:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading progress...</div>;
  if (!progress) return <div>No progress data available</div>;

  return (
    <div className="progress-dashboard">
      <div className="overall-progress">
        <h2>Overall Progress</h2>
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{width: `${progress.overall_progress?.progress_percentage || 0}%`}}
          />
        </div>
        <p>{progress.overall_progress?.progress_percentage || 0}% Complete</p>
        
        <div className="stats">
          <div className="stat">
            <span className="label">Days Completed:</span>
            <span className="value">{progress.overall_progress?.total_days_completed || 0}</span>
          </div>
          <div className="stat">
            <span className="label">Exercises Completed:</span>
            <span className="value">{progress.overall_progress?.total_exercises_completed || 0}</span>
          </div>
        </div>
      </div>

      <div className="day-progress">
        <h3>Day Progress</h3>
        {progress.day_progress?.map((day: any) => (
          <div key={day.id} className={`day-item ${day.is_completed ? 'completed' : 'incomplete'}`}>
            <span className="day-title">{day.day_title}</span>
            <span className="completion-status">
              {day.completed_exercises}/{day.total_exercises} exercises
            </span>
            {day.is_completed && <span className="checkmark">✓</span>}
          </div>
        ))}
      </div>
    </div>
  );
};
```

## Trainer Interface Examples

### 1. Athletes Progress Overview

```typescript
const TrainerDashboard: React.FC = () => {
  const [athletesProgress, setAthletesProgress] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAthletesProgress();
  }, []);

  const fetchAthletesProgress = async () => {
    try {
      const response = await fetch('/v2/api/kanglink/custom/trainer/athletes-progress', {
        headers: {
          'Authorization': `Bearer ${trainerToken}`
        }
      });

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.message);
      }

      setAthletesProgress(result.data);
    } catch (error) {
      console.error('Failed to fetch athletes progress:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading athletes progress...</div>;

  return (
    <div className="trainer-dashboard">
      <h2>Athletes Progress</h2>
      
      <div className="athletes-grid">
        {athletesProgress.map(athlete => (
          <div key={athlete.athlete_id} className="athlete-card">
            <div className="athlete-info">
              <img 
                src={athlete.athlete_photo || '/default-avatar.png'} 
                alt={`${athlete.athlete_first_name} ${athlete.athlete_last_name}`}
                className="athlete-photo"
              />
              <div className="athlete-details">
                <h3>{athlete.athlete_first_name} {athlete.athlete_last_name}</h3>
                <p className="program-name">{athlete.program_name}</p>
                <p className="split-title">{athlete.split_title}</p>
              </div>
            </div>
            
            <div className="progress-info">
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{width: `${athlete.progress_percentage}%`}}
                />
              </div>
              <p>{athlete.progress_percentage}% Complete</p>
              
              <div className="stats">
                <span>Days: {athlete.total_days_completed}</span>
                <span>Exercises: {athlete.total_exercises_completed}</span>
              </div>
              
              <p className="last-activity">
                Last active: {new Date(athlete.last_activity_date).toLocaleDateString()}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

### 2. Trainer Notifications

```typescript
const TrainerNotifications: React.FC = () => {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      const response = await fetch('/v2/api/kanglink/custom/trainer/notifications', {
        headers: {
          'Authorization': `Bearer ${trainerToken}`
        }
      });

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.message);
      }

      setNotifications(result.data.notifications);
      setUnreadCount(result.data.unread_count);
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId: number) => {
    try {
      const response = await fetch(`/v2/api/kanglink/custom/trainer/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${trainerToken}`
        }
      });

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.message);
      }

      // Update local state
      setNotifications(prev => 
        prev.map(notif => 
          notif.id === notificationId 
            ? {...notif, is_read: true, read_at: new Date().toISOString()}
            : notif
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
      
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  if (loading) return <div>Loading notifications...</div>;

  return (
    <div className="trainer-notifications">
      <div className="notifications-header">
        <h2>Notifications</h2>
        {unreadCount > 0 && (
          <span className="unread-badge">{unreadCount} unread</span>
        )}
      </div>
      
      <div className="notifications-list">
        {notifications.map(notification => (
          <div 
            key={notification.id} 
            className={`notification-item ${notification.is_read ? 'read' : 'unread'}`}
            onClick={() => !notification.is_read && markAsRead(notification.id)}
          >
            <div className="notification-content">
              <h4>{notification.title}</h4>
              <p>{notification.message}</p>
              <div className="notification-meta">
                <span className="athlete-name">
                  {notification.athlete_first_name} {notification.athlete_last_name}
                </span>
                <span className="program-name">{notification.program_name}</span>
                <span className="timestamp">
                  {new Date(notification.created_at).toLocaleString()}
                </span>
              </div>
            </div>
            {!notification.is_read && <div className="unread-indicator" />}
          </div>
        ))}
      </div>
    </div>
  );
};
```

## CSS Styles Example

```css
.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  transition: width 0.3s ease;
}

.athlete-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin: 8px;
  background: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.notification-item.unread {
  background-color: #f0f8ff;
  border-left: 4px solid #2196F3;
}

.btn-complete {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.btn-complete:hover {
  background-color: #45a049;
}

.btn-complete.partial {
  background-color: #FF9800;
}
```

This integration guide provides a complete foundation for implementing the athlete progress tracking system in your frontend application.
