# Testing Stripe Integration with Program Creation

## Complete Flow Test

This document shows how to test the complete flow from program creation to athlete enrollment with automatic Stripe integration.

## Step 1: Create Program with Splits (Trainer)

```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/trainer/programs/published" \
  -H "Authorization: Bearer TRAINER_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "stepOneData": {
      "program_name": "Ultimate Strength Program",
      "type_of_program": "strength",
      "program_description": "Complete strength training program for all levels",
      "payment_plan": ["one_time", "subscription"],
      "track_progress": true,
      "allow_comments": true,
      "allow_private_messages": false,
      "target_levels": ["beginner", "intermediate"],
      "split_program": 2,
      "currency": "USD",
      "days_for_preview": 7,
      "splits": [
        {
          "title": "Push/Pull Split",
          "full_price": 149.99,
          "subscription": 39.99,
          "split_id": "split-uuid-1"
        },
        {
          "title": "Full Body Blast",
          "full_price": 99.99,
          "subscription": 24.99,
          "split_id": "split-uuid-2"
        }
      ]
    },
    "stepTwoData": {
      "program_split": "split-uuid-1",
      "description": "Complete workout structure",
      "splitConfigurations": {
        "split-uuid-1": [
          {
            "id": "week-1",
            "title": "Week 1 - Foundation",
            "equipment_required": "Dumbbells, Barbell",
            "week_order": 1,
            "days": [
              {
                "id": "day-1",
                "title": "Push Day",
                "day_order": 1,
                "is_rest_day": false,
                "sessions": [
                  {
                    "id": "session-1",
                    "title": "Upper Body Push",
                    "session_order": 1,
                    "exercises": [
                      {
                        "id": "exercise-1",
                        "sets": "3",
                        "reps_or_time": "8-12",
                        "exercise_details": "Bench Press",
                        "rest_duration_minutes": 2,
                        "rest_duration_seconds": 0,
                        "exercise_order": 1,
                        "exercise_id": "1",
                        "reps_time_type": "reps"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ],
        "split-uuid-2": [
          {
            "id": "week-2",
            "title": "Week 1 - Full Body",
            "equipment_required": "Full Gym",
            "week_order": 1,
            "days": [
              {
                "id": "day-2",
                "title": "Full Body Day",
                "day_order": 1,
                "is_rest_day": false,
                "sessions": [
                  {
                    "id": "session-2",
                    "title": "Complete Workout",
                    "session_order": 1,
                    "exercises": [
                      {
                        "id": "exercise-2",
                        "sets": "3",
                        "reps_or_time": "10-15",
                        "exercise_details": "Squats",
                        "rest_duration_minutes": 1,
                        "rest_duration_seconds": 30,
                        "exercise_order": 1,
                        "exercise_id": "2",
                        "reps_time_type": "reps"
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    }
  }'
```

**Expected Result:**
```json
{
  "error": false,
  "message": "Program created successfully",
  "data": 123
}
```

**What Happens Automatically:**
1. Program created in database
2. 2 splits created in database
3. Stripe products created:
   - "Ultimate Strength Program - Push/Pull Split"
   - "Ultimate Strength Program - Full Body Blast"
4. Stripe prices created:
   - "Ultimate Strength Program - Push/Pull Split - One Time" ($149.99)
   - "Ultimate Strength Program - Push/Pull Split - Monthly" ($39.99/month)
   - "Ultimate Strength Program - Full Body Blast - One Time" ($99.99)
   - "Ultimate Strength Program - Full Body Blast - Monthly" ($24.99/month)

## Step 2: Verify Stripe Resources Created

### Check Database
```sql
-- Check Stripe products
SELECT * FROM kanglink_stripe_product 
WHERE name LIKE '%Ultimate Strength Program%';

-- Check Stripe prices
SELECT * FROM kanglink_stripe_price 
WHERE name LIKE '%Ultimate Strength Program%';
```

### Check Stripe Dashboard
1. Go to Stripe Dashboard → Products
2. Look for products named "Ultimate Strength Program - ..."
3. Verify prices are correctly set

## Step 3: Get Available Splits (Athlete)

```bash
curl -X GET "http://localhost:3000/v2/api/kanglink/custom/splits" \
  -H "Authorization: Bearer ATHLETE_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

**Expected Result:**
```json
{
  "error": false,
  "data": [
    {
      "id": 1,
      "program_id": 123,
      "title": "Push/Pull Split",
      "full_price": 149.99,
      "subscription": 39.99,
      "program_name": "Ultimate Strength Program",
      "currency": "USD",
      "trainer_email": "<EMAIL>"
    },
    {
      "id": 2,
      "program_id": 123,
      "title": "Full Body Blast",
      "full_price": 99.99,
      "subscription": 24.99,
      "program_name": "Ultimate Strength Program",
      "currency": "USD",
      "trainer_email": "<EMAIL>"
    }
  ]
}
```

## Step 4: Enroll in Split (Athlete)

### One-Time Purchase
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/athlete/enroll" \
  -H "Authorization: Bearer ATHLETE_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "split_id": 1,
    "payment_type": "one_time",
    "payment_method_id": "pm_card_visa"
  }'
```

### Subscription Purchase
```bash
curl -X POST "http://localhost:3000/v2/api/kanglink/custom/athlete/enroll" \
  -H "Authorization: Bearer ATHLETE_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "split_id": 2,
    "payment_type": "subscription",
    "payment_method_id": "pm_card_visa"
  }'
```

## Step 5: Update Split Pricing (Trainer)

```bash
curl -X PUT "http://localhost:3000/v2/api/kanglink/custom/trainer/splits/1" \
  -H "Authorization: Bearer TRAINER_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Push/Pull Split Pro",
    "full_price": 199.99,
    "subscription": 49.99
  }'
```

**What Happens Automatically:**
1. Split updated in database
2. Old Stripe prices deactivated
3. New Stripe prices created with updated amounts
4. Database records updated

## Step 6: Verify Price Updates

### Check Database
```sql
-- Check price history (should see old and new prices)
SELECT * FROM kanglink_stripe_price 
WHERE JSON_EXTRACT(object, '$.metadata.split_id') = '1'
ORDER BY created_at DESC;
```

### Check Stripe Dashboard
1. Old prices should be marked as "Archived"
2. New prices should be "Active" with updated amounts

## Expected Console Output

During program creation, you should see logs like:
```
Created Stripe product: prod_1234567890 for split 1
Created Stripe one-time price: price_1111111111 for split 1
Created Stripe subscription price: price_2222222222 for split 1
Created Stripe product: prod_0987654321 for split 2
Created Stripe one-time price: price_3333333333 for split 2
Created Stripe subscription price: price_4444444444 for split 2
```

During price updates:
```
Updating Stripe products for split 1
Updated Stripe one-time price: price_5555555555 for split 1
Updated Stripe subscription price: price_6666666666 for split 1
```

## Testing Edge Cases

### 1. Program with No Pricing
```json
{
  "splits": [
    {
      "title": "Free Preview",
      "full_price": 0,
      "subscription": 0
    }
  ]
}
```
**Expected**: No Stripe resources created

### 2. Program with Only One-Time Pricing
```json
{
  "splits": [
    {
      "title": "One-Time Only",
      "full_price": 99.99,
      "subscription": 0
    }
  ]
}
```
**Expected**: Only one-time Stripe price created

### 3. Program with Only Subscription Pricing
```json
{
  "splits": [
    {
      "title": "Subscription Only",
      "full_price": 0,
      "subscription": 29.99
    }
  ]
}
```
**Expected**: Only subscription Stripe price created

### 4. Stripe API Failure Simulation
To test error handling, temporarily use invalid Stripe keys or disconnect from internet during program creation. The program should still be created successfully, with Stripe resources created later during enrollment.

## Verification Checklist

- [ ] Program created successfully in database
- [ ] Splits created with correct pricing
- [ ] Stripe products created with correct names
- [ ] Stripe one-time prices created (if full_price > 0)
- [ ] Stripe subscription prices created (if subscription > 0)
- [ ] Database records match Stripe resources
- [ ] Enrollment works with created prices
- [ ] Price updates deactivate old prices and create new ones
- [ ] Error handling works gracefully

This comprehensive test ensures the entire flow from program creation to enrollment works seamlessly with automatic Stripe integration.
