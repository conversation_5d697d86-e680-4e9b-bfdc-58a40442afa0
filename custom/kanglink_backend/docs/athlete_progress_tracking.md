# Athlete Progress Tracking System

## Overview

The athlete progress tracking system allows athletes to mark exercises and days as complete while providing trainers with real-time updates and notifications about their athletes' progress through programs.

## Database Tables

### 1. kanglink_athlete_progress
Tracks overall progress for each athlete's enrollment in a program split.

**Fields:**
- `id` - Primary key
- `athlete_id` - Reference to the athlete (user)
- `enrollment_id` - Reference to the enrollment record
- `split_id` - Reference to the program split
- `program_id` - Reference to the program
- `trainer_id` - Reference to the trainer
- `current_week_id` - Current week the athlete is on
- `current_day_id` - Current day the athlete is on
- `total_days_completed` - Number of days completed
- `total_exercises_completed` - Number of exercises completed
- `progress_percentage` - Overall completion percentage
- `last_activity_date` - Last time athlete made progress
- `created_at`, `updated_at` - Timestamps

### 2. kanglink_day_progress
Tracks completion status for each day in a program.

**Fields:**
- `id` - Primary key
- `athlete_id` - Reference to the athlete
- `enrollment_id` - Reference to the enrollment
- `day_id` - Reference to the specific day
- `week_id` - Reference to the week
- `split_id` - Reference to the split
- `is_completed` - <PERSON><PERSON><PERSON> indicating if day is complete
- `completed_at` - Timestamp when day was completed
- `total_exercises` - Total exercises in the day
- `completed_exercises` - Number of completed exercises
- `notes` - Optional notes from athlete
- `created_at`, `updated_at` - Timestamps

### 3. kanglink_exercise_progress
Tracks completion and performance data for individual exercises.

**Fields:**
- `id` - Primary key
- `athlete_id` - Reference to the athlete
- `enrollment_id` - Reference to the enrollment
- `exercise_instance_id` - Reference to the specific exercise instance
- `session_id` - Reference to the session
- `day_id` - Reference to the day
- `is_completed` - Boolean indicating completion
- `completed_at` - Timestamp when completed
- `sets_completed` - Number of sets completed
- `reps_completed` - Reps or time completed
- `weight_used` - Weight used for the exercise
- `time_taken_seconds` - Time taken to complete
- `difficulty_rating` - Rating from 1-5
- `notes` - Optional notes from athlete
- `created_at`, `updated_at` - Timestamps

### 4. kanglink_trainer_notifications
Stores notifications for trainers about athlete progress.

**Fields:**
- `id` - Primary key
- `trainer_id` - Reference to the trainer
- `athlete_id` - Reference to the athlete
- `enrollment_id` - Reference to the enrollment
- `notification_type` - Type of notification (exercise_completed, day_completed, etc.)
- `title` - Notification title
- `message` - Notification message
- `data` - JSON data with additional context
- `is_read` - Boolean indicating if notification was read
- `read_at` - Timestamp when read
- `created_at`, `updated_at` - Timestamps

## API Endpoints

### Athlete Endpoints

#### Mark Exercise as Complete
```
POST /v2/api/kanglink/custom/athlete/exercise/complete
```

**Headers:**
- Authorization: Bearer token (member role)

**Body:**
```json
{
  "enrollment_id": 123,
  "exercise_instance_id": 456,
  "sets_completed": 3,
  "reps_completed": "10,10,8",
  "weight_used": "50kg",
  "time_taken_seconds": 300,
  "difficulty_rating": 4,
  "notes": "Felt good, could increase weight next time"
}
```

**Response:**
```json
{
  "error": false,
  "message": "Exercise marked as complete",
  "data": {
    "exercise_instance_id": 456,
    "completed_at": "2025-01-02 10:30:00"
  }
}
```

#### Mark Day as Complete
```
POST /v2/api/kanglink/custom/athlete/day/complete
```

**Headers:**
- Authorization: Bearer token (member role)

**Body:**
```json
{
  "enrollment_id": 123,
  "day_id": 789,
  "notes": "Great workout today!"
}
```

**Response:**
```json
{
  "error": false,
  "message": "Day marked as complete",
  "data": {
    "day_id": 789,
    "is_completed": true,
    "completed_at": "2025-01-02 11:00:00",
    "total_exercises": 5,
    "completed_exercises": 5
  }
}
```

#### Get Athlete Progress
```
GET /v2/api/kanglink/custom/athlete/progress/:enrollment_id
```

**Headers:**
- Authorization: Bearer token (member role)

**Response:**
```json
{
  "error": false,
  "data": {
    "overall_progress": {
      "id": 1,
      "athlete_id": 123,
      "enrollment_id": 456,
      "progress_percentage": 45.50,
      "total_days_completed": 10,
      "total_exercises_completed": 50,
      "current_week_id": 3,
      "current_day_id": 15,
      "last_activity_date": "2025-01-02 11:00:00"
    },
    "day_progress": [...],
    "exercise_progress": [...]
  }
}
```

### Trainer Endpoints

#### Get Athletes Progress Overview
```
GET /v2/api/kanglink/custom/trainer/athletes-progress
```

**Headers:**
- Authorization: Bearer token (trainer role)

**Query Parameters:**
- `program_id` (optional) - Filter by specific program
- `split_id` (optional) - Filter by specific split

**Response:**
```json
{
  "error": false,
  "data": [
    {
      "athlete_id": 123,
      "athlete_first_name": "John",
      "athlete_last_name": "Doe",
      "athlete_photo": "photo_url",
      "program_name": "Strength Training",
      "split_title": "Beginner Split",
      "progress_percentage": 45.50,
      "total_days_completed": 10,
      "last_activity_date": "2025-01-02 11:00:00",
      "enrollment_status": "active"
    }
  ]
}
```

#### Get Trainer Notifications
```
GET /v2/api/kanglink/custom/trainer/notifications
```

**Headers:**
- Authorization: Bearer token (trainer role)

**Query Parameters:**
- `limit` (default: 20) - Number of notifications to return
- `offset` (default: 0) - Pagination offset
- `unread_only` (default: false) - Show only unread notifications

**Response:**
```json
{
  "error": false,
  "data": {
    "notifications": [
      {
        "id": 1,
        "notification_type": "day_completed",
        "title": "Day Completed",
        "message": "John Doe completed Day 1 - Push",
        "athlete_first_name": "John",
        "athlete_last_name": "Doe",
        "program_name": "Strength Training",
        "is_read": false,
        "created_at": "2025-01-02 11:00:00"
      }
    ],
    "unread_count": 5,
    "pagination": {
      "limit": 20,
      "offset": 0,
      "total": 1
    }
  }
}
```

#### Mark Notification as Read
```
PUT /v2/api/kanglink/custom/trainer/notifications/:notification_id/read
```

**Headers:**
- Authorization: Bearer token (trainer role)

**Response:**
```json
{
  "error": false,
  "message": "Notification marked as read"
}
```

## Setup Instructions

### 1. Create Progress Tracking Tables

First, create the progress tracking tables by calling the development endpoint:

```
POST /v2/api/kanglink/develop/create-progress-tables
```

**Headers:**
- Authorization: Bearer token (super_admin role)

This will create all the necessary database tables for progress tracking.

### 2. Initialize Progress on Enrollment

When an athlete enrolls in a program, you should initialize their progress tracking by creating an entry in the `kanglink_athlete_progress` table. This can be done automatically in the enrollment process.

### 3. Frontend Integration

The frontend should:

1. **For Athletes:**
   - Display program structure with completion status
   - Allow marking exercises as complete with performance data
   - Allow marking days as complete
   - Show overall progress and statistics

2. **For Trainers:**
   - Display athlete progress dashboard
   - Show real-time notifications
   - Allow viewing detailed progress for each athlete

## Notification Types

- `exercise_completed` - When an athlete completes an exercise
- `day_completed` - When an athlete completes all exercises in a day
- `week_completed` - When an athlete completes all days in a week
- `program_completed` - When an athlete completes the entire program
- `milestone_reached` - When an athlete reaches certain progress milestones

## Progress Calculation

- **Day Completion:** A day is marked complete when all exercises in that day are completed
- **Overall Progress:** Calculated as percentage of completed days vs total days in the split
- **Current Position:** Tracks the next incomplete day as the athlete's current position

## Performance Considerations

- Progress updates are handled efficiently with batch operations
- Notifications are created asynchronously to avoid blocking the main flow
- Indexes are created on frequently queried fields for optimal performance
