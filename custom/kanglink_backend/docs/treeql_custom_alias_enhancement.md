# TreeQL Custom Alias Enhancement

## Overview

The TreeQL system has been enhanced to support custom aliases for joins, resolving conflicts when joining the same table multiple times with different foreign keys.

## Problem Solved

**Before Enhancement:**
When joining the same table multiple times, the results would overwrite each other:

```javascript
// URL: ?join=user|athlete_id&join=user|trainer_id
// Result: { user: {...} } // Only the last join survives!
```

**After Enhancement:**
Each join can have a custom alias, preventing conflicts:

```javascript
// URL: ?join=user|athlete_id|athlete&join=user|trainer_id|trainer
// Result: { athlete: {...}, trainer: {...} } // Both joins preserved!
```

## Syntax

### New Syntax (with custom alias)
```
table|foreignKey|alias
```

**Examples:**
- `user|athlete_id|athlete` - Join user table using athlete_id, alias as "athlete"
- `user|trainer_id|trainer` - Join user table using trainer_id, alias as "trainer"
- `program|program_id|program_details` - Join program table, alias as "program_details"

### Legacy Syntax (backward compatible)
```
table|foreignKey
```

**Examples:**
- `user|trainer_id` - Join user table using trainer_id, result key is "user"

## Usage Examples

### Multiple User Joins (Enrollment System)
```javascript
// Get enrollment with both athlete and trainer information
const url = '/v2/api/kanglink/custom/super_admin/enrollment?join=user|athlete_id|athlete&join=user|trainer_id|trainer';

// Result structure:
{
  id: 1,
  athlete_id: 100,
  trainer_id: 200,
  athlete: {
    id: 100,
    email: "<EMAIL>",
    data: { full_name: "John Athlete" }
  },
  trainer: {
    id: 200,
    email: "<EMAIL>", 
    data: { full_name: "Jane Trainer" }
  }
}
```

### Complex Program Structure
```javascript
// Get program with creator and reviewer information
const url = '/programs?join=user|created_by|creator&join=user|reviewed_by|reviewer&join=split|split_id|selected_split';

// Result structure:
{
  id: 1,
  program_name: "Advanced Training",
  created_by: 100,
  reviewed_by: 200,
  split_id: 300,
  creator: { id: 100, email: "<EMAIL>" },
  reviewer: { id: 200, email: "<EMAIL>" },
  selected_split: { id: 300, title: "Upper Body Focus" }
}
```

## Implementation Details

### Files Modified

1. **mtpbk/baas/lambda/treeql.js**
   - Enhanced join parsing to extract custom alias (third parameter)
   - Updated `guessRelationInfo` function calls to pass custom alias

2. **mtpbk/baas/core/DBUtil.js**
   - Modified `guessRelationInfo` method to accept custom alias parameter
   - Added `alias` field to all relationship objects
   - Uses `customAlias || relation` pattern for backward compatibility

3. **Result Mapping Functions**
   - Updated `matchBelongsTo` to use alias when available
   - Updated `matchHasOneOrMany` to use alias when available

### Key Changes

#### Join Parsing Enhancement
```javascript
// Before
const relationData = joinChain[i].split("|");
const joinTable = relationData[0];
const joinForeignKey = relationData[1];

// After  
const relationData = joinChain[i].split("|");
const joinTable = relationData[0];
const joinForeignKey = relationData[1];
const customAlias = relationData[2]; // NEW: Third parameter for custom alias
```

#### Relationship Object Enhancement
```javascript
// Before
{
  type: this.BELONGS_TO,
  column,
  subject,
  relation,
  subjectTable,
  relationTable,
  subjectSchema,
  relationSchema,
}

// After
{
  type: this.BELONGS_TO,
  column,
  subject,
  relation,
  alias: customAlias || relation, // NEW: Custom alias support
  subjectTable,
  relationTable,
  subjectSchema,
  relationSchema,
}
```

#### Result Mapping Enhancement
```javascript
// Before
record[join?.relation] = relation;

// After
const relationKey = join?.alias || join?.relation;
record[relationKey] = relation;
```

## Backward Compatibility

The enhancement maintains full backward compatibility:

- **Old syntax** `table|foreignKey` continues to work exactly as before
- **New syntax** `table|foreignKey|alias` provides the enhanced functionality
- **No breaking changes** to existing API calls or implementations

## Testing

Run the test suite to verify the enhancement:

```bash
cd mtpbk/custom/ksl_be
node tests/treeql_custom_alias.test.js
```

The test verifies:
- ✅ Custom alias parsing works correctly
- ✅ Backward compatibility is maintained
- ✅ Multiple joins with same table resolve correctly

## Benefits

1. **Resolves Join Conflicts**: Multiple joins to the same table no longer overwrite each other
2. **Improved Readability**: Semantic aliases make result structure more intuitive
3. **Backward Compatible**: Existing code continues to work without changes
4. **Flexible**: Supports any number of joins with custom naming
5. **Performance**: No performance impact, just enhanced result mapping

## Real-World Use Cases

### Enrollment System
- Join user table for both athlete and trainer information
- Clear distinction between `enrollment.athlete` and `enrollment.trainer`

### Content Management
- Join user table for creator, editor, and reviewer roles
- Distinct `content.creator`, `content.editor`, `content.reviewer` objects

### Audit Trails
- Join user table for created_by, updated_by, deleted_by tracking
- Clear `record.creator`, `record.updater`, `record.deleter` references

### Hierarchical Data
- Join same table for parent/child relationships
- Distinct `category.parent` and `category.children` structures
