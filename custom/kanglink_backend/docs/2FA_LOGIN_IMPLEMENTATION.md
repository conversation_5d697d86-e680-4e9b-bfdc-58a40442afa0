# Two-Factor Authentication (2FA) Login Implementation

## Overview

This document describes the implementation of Two-Factor Authentication (2FA) during the login process. When a user has 2FA enabled, they will be prompted to enter a verification code from their authenticator app after successfully entering their email and password.

## Architecture

### Backend Components

1. **2FA Login Endpoints** (`/lambda/*_2fa_login.js`):
   - `member_2fa_login.js` - Handles member 2FA authentication
   - `trainer_2fa_login.js` - Handles trainer 2FA authentication  
   - `super_admin_2fa_login.js` - <PERSON>les admin 2FA authentication

2. **Regular Login Endpoints** (`/lambda/*_login.js`):
   - `member_login.js` - Standard member login
   - `trainer_login.js` - Standard trainer login
   - `super_admin_login.js` - Standard admin login

### Frontend Components

1. **Login Pages**:
   - `LoginPage.tsx` - Main login page for athletes and trainers
   - `AdminLoginPage.tsx` - Admin login page

2. **2FA Modal Component**:
   - `TwoFactorLoginModal.tsx` - Modal for 2FA code entry during login

3. **SDK Methods**:
   - `MkdSDK.ts` - Added 2FA authentication methods

## Flow Diagram

```
User Login Flow with 2FA:
1. User enters email/password
2. Backend validates credentials
3. If 2FA is enabled:
   - Return 2FA setup data (QR code + one-time token)
   - Show 2FA modal to user
   - User enters 6-digit code
   - Backend verifies code
   - Complete login process
4. If 2FA is disabled:
   - Complete normal login process
```

## API Endpoints

### 1. 2FA Login Setup
**Endpoint**: `POST /v1/api/kanglink/{role}/lambda/2fa/login`

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "role": "member"
}
```

**Response**:
```json
{
  "error": false,
  "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "one_time_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user_id": 1,
  "expire_at": 60,
  "role": "member"
}
```

### 2. 2FA Authentication
**Endpoint**: `POST /v2/api/kanglink/{role}/lambda/2fa/auth`

**Request Body**:
```json
{
  "code": "123456",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response**:
```json
{
  "error": false,
  "role": "member",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expire_at": 3600,
  "user_id": 1
}
```

## Frontend Implementation

### Login Page Changes

The login pages now check for `two_factor_authentication` in the login response:

```typescript
const onSubmit = async (data: LoginFormData) => {
  const result = await sdk.login(email, password, role, rememberMe);
  
  if (!result.error) {
    if (result.two_factor_authentication) {
      // Get 2FA setup data
      const twoFAResult = await sdk.get2FALoginSetup(email, password, role);
      
      if (!twoFAResult.error) {
        setTwoFAData({
          qrCodeUrl: twoFAResult.qr_code,
          oneTimeToken: twoFAResult.one_time_token,
          role: role,
        });
        setShow2FAModal(true);
        return;
      }
    }
    
    // Normal login flow
    authDispatch({ type: "LOGIN", payload: result });
    navigate(redirect_uri);
  }
};
```

### 2FA Modal Component

The `TwoFactorLoginModal` component handles the 2FA verification:

```typescript
const onVerifySubmit = async (data: VerificationFormData) => {
  const result = await sdk.authenticate2FA(data.verification_code, oneTimeToken);
  
  if (!result.error) {
    onSuccess(result);
    onClose();
  } else {
    showToast("Invalid verification code", ToastStatusEnum.ERROR);
  }
};
```

## SDK Methods

### New Methods Added

1. **`get2FALoginSetup(email, password, role)`**:
   - Initiates 2FA login process
   - Returns QR code and one-time token

2. **`authenticate2FA(code, token)`**:
   - Verifies 2FA code during login
   - Returns final authentication token

## Security Considerations

1. **One-Time Tokens**: Each 2FA setup generates a unique one-time token that expires quickly
2. **QR Code Security**: QR codes contain time-based secrets that change every 30 seconds
3. **Rate Limiting**: Implement rate limiting on 2FA endpoints to prevent brute force attacks
4. **Token Expiration**: One-time tokens expire after 60 seconds
5. **Validation**: All 2FA codes are validated against the user's stored secret

## Error Handling

### Common Error Responses

1. **Invalid Credentials**:
```json
{
  "error": true,
  "message": "Failed to login"
}
```

2. **Invalid 2FA Code**:
```json
{
  "error": true,
  "message": "Invalid verification code"
}
```

3. **Missing Required Fields**:
```json
{
  "error": true,
  "message": "Invalid Credentials",
  "validation": [
    { "field": "email", "message": "Email required" },
    { "field": "password", "message": "Password required" },
    { "field": "role", "message": "Role required" }
  ]
}
```

## Testing

### Test Files

1. **`2fa_login_integration.test.js`**: Comprehensive integration tests for 2FA login flow

### Test Scenarios

1. **Successful 2FA Login**: Complete flow from login to 2FA verification
2. **Invalid 2FA Code**: Rejection of incorrect verification codes
3. **Missing Fields**: Validation of required parameters
4. **Error Cases**: Various error scenarios and edge cases

## Configuration

### Role Configuration

Each role must have 2FA enabled in their role file:

```javascript
// roles/member.js
module.exports = {
  needs2FA: () => true, // Enable 2FA for this role
  canLogin: () => true,
  slug: "member"
};
```

### Environment Variables

Ensure the following are configured:
- `JWT_KEY`: Secret key for JWT tokens
- `JWT_EXPIRE`: Token expiration time
- `REFRESH_JWT_EXPIRE`: Refresh token expiration time

## Deployment Notes

1. **Database**: Ensure the `user` table has `two_factor_authentication` and `two_fa_type` columns
2. **Tokens Table**: Ensure the `tokens` table exists for storing 2FA secrets
3. **Dependencies**: Install required packages (`speakeasy`, `qrcode`)
4. **SMS Service**: Configure Twilio for SMS-based 2FA (optional)

## Troubleshooting

### Common Issues

1. **QR Code Not Displaying**: Check if the QR code data URL is properly generated
2. **Invalid Token Errors**: Ensure one-time tokens are not expired
3. **2FA Not Triggering**: Verify that `two_factor_authentication` is true in user record
4. **Code Validation Fails**: Check if the authenticator app is properly synced

### Debug Steps

1. Check user's 2FA status in database
2. Verify token expiration times
3. Test QR code generation separately
4. Check JWT token validation
5. Review server logs for detailed error messages

## Future Enhancements

1. **Backup Codes**: Implement backup codes for account recovery
2. **SMS 2FA**: Add SMS-based 2FA as an alternative
3. **Remember Device**: Option to remember device for 30 days
4. **Admin Override**: Admin ability to disable 2FA for users
5. **Audit Logging**: Track 2FA login attempts and failures 