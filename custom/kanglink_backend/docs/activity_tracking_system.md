# Activity Tracking System

## Overview

The Activity Tracking System automatically creates activity records when athletes complete exercises, days, weeks, or programs in programs that have `track_progress` enabled. This allows trainers to monitor their athletes' progress and engagement in real-time.

## How It Works

### 1. Program Configuration
Programs have a `track_progress` boolean field that determines whether activities should be created for that program.

```javascript
// In program.js model
{
  name: "track_progress",
  type: "boolean",
  validation: [],
  defaultValue: false,
  mapping: null,
}
```

### 2. Activity Creation Triggers
Activities are automatically created when:
- **Exercise Completion**: An athlete completes an exercise
- **Day Completion**: An athlete completes all exercises in a day
- **Week Completion**: An athlete completes all days in a week
- **Program Completion**: An athlete completes an entire program
- **Milestone Achievement**: An athlete reaches predefined milestones

### 3. Activity Types
The system supports the following activity types:
- `workout_completed`: Exercise completion
- `day_completed`: Day completion
- `week_completed`: Week completion
- `program_completed`: Program completion
- `milestone_reached`: Milestone achievement

## Backend Implementation

### ActivityService.js
The `ActivityService` class handles all activity creation logic:

```javascript
class ActivityService {
  constructor(sdk) {
    this.sdk = sdk;
  }

  // Create activity for exercise completion
  async createExerciseActivity(exerciseData, athleteData, trainerData, enrollmentData)

  // Create activity for day completion
  async createDayActivity(dayData, athleteData, trainerData, enrollmentData)

  // Create activity for week completion
  async createWeekActivity(weekData, athleteData, trainerData, enrollmentData)

  // Create activity for program completion
  async createProgramActivity(programData, athleteData, trainerData, enrollmentData)

  // Create activity for milestone reached
  async createMilestoneActivity(milestoneData, athleteData, trainerData, enrollmentData)

  // Get trainer activities for their programs
  async getTrainerActivities(trainerId, filters = {})
}
```

### Integration with Athlete Progress Routes
The activity creation is integrated into the athlete progress routes:

1. **Exercise Completion** (`/v2/api/kanglink/custom/athlete/exercise/complete`)
   - Creates `workout_completed` activity
   - Includes exercise details, sets, reps, weight, time taken

2. **Day Completion** (`/v2/api/kanglink/custom/athlete/day/complete`)
   - Creates `day_completed` activity when all exercises are completed
   - Includes day details, total exercises, completed exercises

### Activity Data Structure
Each activity record contains:

```javascript
{
  id: number,
  user_id: number,           // Athlete ID
  actor_id: number,          // Athlete ID (same as user_id)
  activity_type: string,     // Type of activity
  related_id: number,        // Related entity ID
  related_type: string,      // Type of related entity
  title: string,             // Activity title
  description: string,       // Activity description
  metadata: object,          // Additional data (JSON)
  visibility: string,        // "public", "private", "trainer_only"
  created_at: string,        // Timestamp
  updated_at: string         // Timestamp
}
```

## API Endpoints

### Trainer Activity Endpoints

#### 1. Get Trainer Activities
```
GET /v2/api/kanglink/custom/trainer/activities
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of activities per page (default: 20)
- `activity_type` (optional): Filter by activity type
- `visibility` (optional): Filter by visibility (all, public, private, trainer_only)
- `program_id` (optional): Filter by program ID
- `athlete_id` (optional): Filter by athlete ID
- `date_from` (optional): Filter from date (YYYY-MM-DD)
- `date_to` (optional): Filter to date (YYYY-MM-DD)

**Response:**
```json
{
  "error": false,
  "message": "Activities retrieved successfully",
  "data": {
    "activities": [
      {
        "id": 1,
        "user_id": 123,
        "user_name": "John Doe",
        "user_email": "<EMAIL>",
        "actor_id": 123,
        "actor_name": "John Doe",
        "activity_type": "workout_completed",
        "title": "Exercise Completed",
        "description": "John Doe completed Bench Press in Strength Training",
        "metadata": {
          "exercise_instance_id": 456,
          "session_id": 789,
          "day_id": 101,
          "program_name": "Strength Training",
          "split_title": "Beginner",
          "sets_completed": 3,
          "reps_completed": "10",
          "weight_used": "135 lbs",
          "time_taken_seconds": 1800,
          "difficulty_rating": 3,
          "enrollment_id": 202
        },
        "visibility": "trainer_only",
        "related_id": 456,
        "related_type": "exercise",
        "program_name": "Strength Training",
        "split_title": "Beginner",
        "enrollment_id": 202,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

#### 2. Get Activity Statistics
```
GET /v2/api/kanglink/custom/trainer/activities/stats
```

**Query Parameters:**
- `program_id` (optional): Filter by program ID
- `date_from` (optional): Filter from date
- `date_to` (optional): Filter to date

**Response:**
```json
{
  "error": false,
  "message": "Activity statistics retrieved successfully",
  "data": {
    "activity_type_stats": [
      {
        "activity_type": "workout_completed",
        "count": 45
      },
      {
        "activity_type": "day_completed",
        "count": 12
      }
    ],
    "recent_activity_count": 15,
    "top_athletes": [
      {
        "user_id": 123,
        "user_name": "John Doe",
        "user_email": "<EMAIL>",
        "activity_count": 25
      }
    ]
  }
}
```

#### 3. Get Activity Summary by Program
```
GET /v2/api/kanglink/custom/trainer/activities/programs
```

**Response:**
```json
{
  "error": false,
  "message": "Program activity summary retrieved successfully",
  "data": [
    {
      "program_id": 1,
      "program_name": "Strength Training",
      "total_activities": 150,
      "unique_athletes": 25,
      "last_activity_date": "2024-01-15T10:30:00Z",
      "recent_activities": 15
    }
  ]
}
```

#### 4. Get Activity Summary by Athlete
```
GET /v2/api/kanglink/custom/trainer/activities/athletes
```

**Response:**
```json
{
  "error": false,
  "message": "Athlete activity summary retrieved successfully",
  "data": [
    {
      "user_id": 123,
      "user_name": "John Doe",
      "user_email": "<EMAIL>",
      "total_activities": 25,
      "programs_count": 2,
      "last_activity_date": "2024-01-15T10:30:00Z",
      "recent_activities": 5,
      "workouts_completed": 20,
      "days_completed": 3,
      "weeks_completed": 1,
      "programs_completed": 0
    }
  ]
}
```

## Activity Creation Logic

### 1. Exercise Completion
When an athlete completes an exercise:

1. Check if the program has `track_progress` enabled
2. If enabled, create a `workout_completed` activity
3. Include exercise details in metadata (sets, reps, weight, time, difficulty)
4. Set visibility to `trainer_only`

### 2. Day Completion
When an athlete completes all exercises in a day:

1. Check if the program has `track_progress` enabled
2. If enabled, create a `day_completed` activity
3. Include day details in metadata (total exercises, completed exercises)
4. Set visibility to `trainer_only`

### 3. Week Completion
When an athlete completes all days in a week:

1. Check if the program has `track_progress` enabled
2. If enabled, create a `week_completed` activity
3. Include week details in metadata (total days, completed days)
4. Set visibility to `trainer_only`

### 4. Program Completion
When an athlete completes an entire program:

1. Check if the program has `track_progress` enabled
2. If enabled, create a `program_completed` activity
3. Include program details in metadata (total days, total exercises, progress percentage)
4. Set visibility to `trainer_only`

## Frontend Integration

### Dashboard Integration
The activity data can be integrated into the trainer dashboard to show:
- Recent athlete activities
- Activity statistics
- Top performing athletes
- Program engagement metrics

### Real-time Updates
Activities can be used to provide real-time updates to trainers about:
- Athlete progress
- Engagement levels
- Program completion rates
- Performance trends

## Testing

### Test Coverage
The system includes comprehensive tests for:
- Activity creation for different types
- Activity filtering and pagination
- Statistics calculation
- Authorization and access control

### Test Files
- `tests/trainer_activity.test.js`: Tests for trainer activity endpoints
- Integration tests in athlete progress routes

## Future Enhancements

1. **Real-time Notifications**: Push notifications for new activities
2. **Activity Analytics**: Advanced analytics and insights
3. **Custom Milestones**: Allow trainers to set custom milestones
4. **Activity Export**: Export activity data for analysis
5. **Activity Templates**: Predefined activity templates for common scenarios
6. **Activity Comments**: Allow trainers to comment on activities
7. **Activity Reactions**: Allow athletes to react to their own activities

## Usage Examples

### Creating a Program with Activity Tracking
```javascript
const programData = {
  program_name: "Strength Training",
  track_progress: true,  // Enable activity tracking
  // ... other program fields
};
```

### Viewing Trainer Activities
```javascript
// Get all activities
const activities = await fetch('/v2/api/kanglink/custom/trainer/activities');

// Get filtered activities
const filteredActivities = await fetch('/v2/api/kanglink/custom/trainer/activities?activity_type=workout_completed&program_id=1');

// Get activity statistics
const stats = await fetch('/v2/api/kanglink/custom/trainer/activities/stats');
```

### Activity Metadata Examples
```javascript
// Exercise completion metadata
{
  "exercise_instance_id": 456,
  "session_id": 789,
  "day_id": 101,
  "program_name": "Strength Training",
  "split_title": "Beginner",
  "sets_completed": 3,
  "reps_completed": "10",
  "weight_used": "135 lbs",
  "time_taken_seconds": 1800,
  "difficulty_rating": 3,
  "enrollment_id": 202
}

// Day completion metadata
{
  "day_id": 101,
  "week_id": 50,
  "program_name": "Strength Training",
  "split_title": "Beginner",
  "total_exercises": 8,
  "completed_exercises": 8,
  "enrollment_id": 202
}
``` 