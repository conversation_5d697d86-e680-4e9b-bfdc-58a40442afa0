# Super Admin API Documentation

## Overview

The Super Admin API provides administrative endpoints for managing programs, trainers, and platform operations. Super admins can approve/reject programs, update program statuses, and access detailed program information for review and management.

## Authentication

All endpoints require super admin authentication via <PERSON><PERSON> token:

```
Authorization: Bearer <super_admin_token>
```

## Base URL

```
https://your-domain.com/v2/api/kanglink/custom/super_admin
```

---

## Endpoints

### 1. Approve Program

**Endpoint:** `PUT /programs/:program_id/approve`

**Description:** Approve a pending program and automatically generate an affiliate link.

**Authentication:** Required (super_admin role)

**URL Parameters:**

- `program_id` (integer, required): The ID of the program to approve

**Request Body:** None

**Response (Success - 200):**

```json
{
  "error": false,
  "message": "Program approved successfully and affiliate link created"
}
```

**Response (Error - 400):**

```json
{
  "error": true,
  "message": "Program is currently published. Only programs with 'pending_approval' status can be approved."
}
```

**Response (Error - 404):**

```json
{
  "error": true,
  "message": "Program not found"
}
```

**Notes:**

- Only programs with status `pending_approval` can be approved
- Automatically updates program status to `published`
- Generates and stores affiliate link in `program_discount` table
- Affiliate code format: `{programId}{trainerId}{timestamp}` (base36 encoded)

---

### 2. Update Program Status

**Endpoint:** `PUT /programs/:program_id/status`

**Description:** Update the status of any program with optional rejection reason.

**Authentication:** Required (super_admin role)

**URL Parameters:**

- `program_id` (integer, required): The ID of the program to update

**Request Body:**

```json
{
  "status": "rejected",
  "rejection_reason": "Program content does not meet quality standards"
}
```

**Request Fields:**

- `status` (string, required): New program status
  - Valid values: `draft`, `pending_approval`, `live`, `published`, `rejected`, `archived`
- `rejection_reason` (string, required if status is "rejected"): Reason for rejection

**Response (Success - 200):**

```json
{
  "error": false,
  "message": "Program status updated to rejected successfully",
  "data": {
    "program_id": 123,
    "status": "rejected",
    "trainer": {
      "id": 456,
      "email": "<EMAIL>",
      "name": "John Doe"
    },
    "rejection_reason": "Program content does not meet quality standards",
    "updated_at": "2024-01-15 10:30:00"
  }
}
```

**Response (Error - 400):**

```json
{
  "error": true,
  "message": "Invalid status. Must be one of: draft, pending_approval, live, published, rejected, archived"
}
```

**Notes:**

- Rejection reason is stored in the program's `payment_plan` JSON field under `rejection` object
- Includes rejection timestamp and admin user ID for audit trail

---

### 3. Get Pending Programs

**Endpoint:** `GET /programs/pending`

**Description:** Retrieve programs awaiting approval with pagination.

**Authentication:** Required (super_admin role)

**Query Parameters:**

- `page` (integer, optional, default: 1): Page number for pagination
- `limit` (integer, optional, default: 10): Number of programs per page

**Response (Success - 200):**

```json
{
  "error": false,
  "message": "Pending programs retrieved successfully",
  "data": {
    "programs": [
      {
        "id": 123,
        "program_name": "Advanced Strength Training",
        "type_of_program": "strength",
        "program_description": "A comprehensive strength training program...",
        "status": "pending_approval",
        "split_count": 3,
        "payment_plan": {
          "subscription_enabled": true,
          "one_time_enabled": true
        },
        "image": "https://example.com/program-image.jpg",
        "trainer": {
          "id": 456,
          "email": "<EMAIL>",
          "name": "John Doe",
          "photo": "https://example.com/trainer-photo.jpg"
        },
        "created_at": "2024-01-10 09:00:00",
        "updated_at": "2024-01-10 09:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "total_pages": 3
    }
  }
}
```

**Notes:**

- Only returns programs with `pending_approval` status
- Includes trainer information and split count for quick review
- Results are ordered by creation date (newest first)

---

### 4. Get All Programs (Admin Management)

**Endpoint:** `GET /programs`

**Description:** Retrieve all programs with advanced filtering, searching, and pagination for admin management.

**Authentication:** Required (super_admin role)

**Query Parameters:**

- `page` (integer, optional, default: 1): Page number
- `limit` (integer, optional, default: 10): Programs per page
- `status` (string, optional): Filter by program status
- `trainer_id` (integer, optional): Filter by trainer ID
- `search` (string, optional): Search in program name, description, or trainer email
- `sort_by` (string, optional, default: "created_at"): Sort field
  - Valid values: `created_at`, `updated_at`, `program_name`, `status`
- `sort_order` (string, optional, default: "DESC"): Sort direction (ASC/DESC)

**Response (Success - 200):**

```json
{
  "error": false,
  "message": "Programs retrieved successfully",
  "data": {
    "programs": [
      {
        "id": 123,
        "program_name": "Advanced Strength Training",
        "type_of_program": "strength",
        "program_description": "A comprehensive strength training program...",
        "status": "published",
        "split_count": 3,
        "payment_plan": {
          "subscription_enabled": true,
          "one_time_enabled": true
        },
        "image": "https://example.com/program-image.jpg",
        "rating": 4.8,
        "trainer": {
          "id": 456,
          "email": "<EMAIL>",
          "name": "John Doe",
          "photo": "https://example.com/trainer-photo.jpg"
        },
        "created_at": "2024-01-10 09:00:00",
        "updated_at": "2024-01-15 10:30:00"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 150,
      "total_pages": 15
    },
    "filters": {
      "status": "published",
      "trainer_id": null,
      "search": null,
      "sort_by": "created_at",
      "sort_order": "DESC"
    }
  }
}
```

**Example Requests:**

```bash
# Get all published programs
GET /programs?status=published

# Search for programs containing "strength"
GET /programs?search=strength

# Get programs by specific trainer
GET /programs?trainer_id=456

# Sort by program name ascending
GET /programs?sort_by=program_name&sort_order=ASC
```

---

### 5. Get Program Details

**Endpoint:** `GET /programs/:program_id`

**Description:** Get comprehensive program details for admin review including structure, pricing, and trainer information.

**Authentication:** Required (super_admin role)

**URL Parameters:**

- `program_id` (integer, required): The ID of the program to retrieve

**Response (Success - 200):**

```json
{
  "error": false,
  "message": "Program details retrieved successfully",
  "data": {
    "id": 123,
    "program_name": "Advanced Strength Training",
    "type_of_program": "strength",
    "program_description": "A comprehensive strength training program for intermediate to advanced athletes...",
    "status": "pending_approval",
    "rating": 4.8,
    "track_progress": true,
    "allow_comments": true,
    "allow_private_messages": false,
    "target_levels": ["intermediate", "advanced"],
    "split_program": true,
    "currency": "USD",
    "days_for_preview": 3,
    "image": "https://example.com/program-image.jpg",
    "payment_plan": {
      "subscription_enabled": true,
      "one_time_enabled": true,
      "rejection": {
        "reason": "Previous rejection reason if any",
        "rejected_at": "2024-01-14 15:30:00",
        "rejected_by": 789
      }
    },
    "pricing": {
      "min_price": 29.99,
      "max_price": 199.99,
      "currency": "USD"
    },
    "structure": {
      "split_count": 3,
      "week_count": 12,
      "day_count": 84,
      "session_count": 168,
      "exercise_count": 420
    },
    "splits": [
      {
        "id": 1,
        "title": "Upper Body Focus",
        "full_price": 99.99,
        "subscription": 29.99,
        "equipment_required": "Dumbbells, Barbell, Bench",
        "created_at": "2024-01-10 09:00:00",
        "updated_at": "2024-01-10 09:00:00"
      },
      {
        "id": 2,
        "title": "Lower Body Focus",
        "full_price": 99.99,
        "subscription": 29.99,
        "equipment_required": "Barbell, Squat Rack",
        "created_at": "2024-01-10 09:00:00",
        "updated_at": "2024-01-10 09:00:00"
      }
    ],
    "trainer": {
      "id": 456,
      "email": "<EMAIL>",
      "name": "John Doe",
      "photo": "https://example.com/trainer-photo.jpg",
      "bio": "Certified personal trainer with 10+ years experience...",
      "specializations": ["Strength Training", "Powerlifting"],
      "qualifications": ["NASM-CPT", "CSCS"],
      "years_of_experience": 12,
      "joined_at": "2023-06-15 14:20:00"
    },
    "created_at": "2024-01-10 09:00:00",
    "updated_at": "2024-01-15 10:30:00"
  }
}
```

**Response (Error - 404):**

```json
{
  "error": true,
  "message": "Program not found"
}
```

**Notes:**

- Provides comprehensive program information for admin review
- Includes detailed trainer profile and qualifications
- Shows program structure statistics (weeks, days, sessions, exercises)
- Displays pricing information across all splits
- Includes rejection history if program was previously rejected

---

## Error Handling

All endpoints follow consistent error response patterns:

### Common Error Responses

**400 Bad Request:**

```json
{
  "error": true,
  "message": "Invalid program ID provided"
}
```

**401 Unauthorized:**

```json
{
  "error": true,
  "message": "Authentication required"
}
```

**403 Forbidden:**

```json
{
  "error": true,
  "message": "Super admin access required"
}
```

**404 Not Found:**

```json
{
  "error": true,
  "message": "Program not found"
}
```

**500 Internal Server Error:**

```json
{
  "error": true,
  "message": "Failed to approve program"
}
```

---

## Data Models

### Program Status Values

- `draft`: Program is being created by trainer
- `pending_approval`: Program submitted for admin review
- `published`: Program is approved and publicly available
- `rejected`: Program rejected by admin (includes rejection reason)
- `archived`: Program is no longer active

### Trainer Data Structure

Trainer information is stored in the `user.data` JSON field with the following structure:

```json
{
  "full_name": "John Doe",
  "first_name": "John",
  "last_name": "Doe",
  "photo": "https://example.com/photo.jpg",
  "bio": "Professional trainer bio...",
  "specializations": ["Strength Training", "Powerlifting"],
  "qualifications": ["NASM-CPT", "CSCS"],
  "years_of_experience": 12
}
```

### Payment Plan Structure

The `payment_plan` field stores configuration and rejection information:

```json
{
  "subscription_enabled": true,
  "one_time_enabled": true,
  "rejection": {
    "reason": "Rejection reason text",
    "rejected_at": "2024-01-14 15:30:00",
    "rejected_by": 789
  }
}
```

---

## Usage Examples

### Approve a Program

```bash
curl -X PUT \
  "https://api.example.com/v2/api/kanglink/custom/super_admin/programs/123/approve" \
  -H "Authorization: Bearer super_admin_token"
```

### Reject a Program

```bash
curl -X PUT \
  "https://api.example.com/v2/api/kanglink/custom/super_admin/programs/123/status" \
  -H "Authorization: Bearer super_admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "rejected",
    "rejection_reason": "Program content does not meet quality standards"
  }'
```

### Get Pending Programs

```bash
curl -X GET \
  "https://api.example.com/v2/api/kanglink/custom/super_admin/programs/pending?page=1&limit=20" \
  -H "Authorization: Bearer super_admin_token"
```

### Search Programs

```bash
curl -X GET \
  "https://api.example.com/v2/api/kanglink/custom/super_admin/programs?search=strength&status=published" \
  -H "Authorization: Bearer super_admin_token"
```

---

## Notes

- All timestamps are in MySQL datetime format: `YYYY-MM-DD HH:MM:SS`
- Program approval automatically generates affiliate links stored in `kanglink_program_discount` table
- Rejection reasons are stored in the program's `payment_plan` JSON field for audit trail
- Trainer information comes from the `user.data` JSON field, not separate preference tables
- Program structure statistics are calculated from related splits, weeks, days, sessions, and exercises
- Pricing information shows min/max across all splits in the program
