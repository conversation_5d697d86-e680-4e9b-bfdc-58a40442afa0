server {
    listen 80;
    server_name baas.mytechpassport.com www.baas.mytechpassport.com; 

    access_log /var/log/nginx/baas.mytechpassport.com.access.log;
    error_log /var/log/nginx/baas.mytechpassport.com.error.log;

    location / {
        proxy_pass http://localhost:5172;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
} 