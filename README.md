# Baas V5 System

This system is designed to accelerate backend development by providing a streamlined, configuration-driven approach. It enables you to quickly set up databases, models, APIs, roles, and authentication flows, emphasizing security, efficiency, and code generation best practices.

## Core Philosophy

The Baas V5 system is built on these core principles:

*   **Configuration-Driven:** Project structure and functionality are defined by a central JSON configuration, fostering consistency and reducing boilerplate.
*   **Rapid Development:** Automated code generation and pre-built services minimize the time and effort required to set up a backend.
*   **Security First:** Built-in authentication, role-based access control and permission list directly from your custom directory, and input validation help prevent common security vulnerabilities.
*   **Modularity and Customization:** The system uses a from-scratch service approach, it is designed to allow custom logic and extend core functionality.
*   **Developer Experience:** The system aims to simplify backend tasks, empowering developers to focus on building applications.

## Getting Started

### Prerequisites

*   **Docker:** Ensure Docker is installed and running on your machine.
*   **Node.js:** Node.js (v18 or higher) is required.
*   **Code Editor:** A code editor like <PERSON>ursor or Windsurf is recommended to make full use of the AI code completion with comments.

### 1. Initial Setup: Dockerizing the BaaS

1.  **Navigate to the Project Root:** Open your terminal and navigate to the root directory of the project where the DockerFile resides.
2.  **Build and Run Docker:** Execute the following command to build the Docker image and start the containers:

    ```bash
    docker-compose up --build
    ```

3.  **Access the Application:**

    *   **Node.js Application:** Open your browser and go to `http://localhost:5172` to access the backend Node.js application (this will not show anything visible).
    *   **phpMyAdmin:** Open your browser and go to `http://localhost:8080` to access the database management tool. Use the credentials defined in your configuration to login.

### 2. Creating a New Project

The typical project setup workflow consists of two phases

1.  **Configuration Generation**: You first generate a configuration object at [http://resilient-fairy-1f9082.netlify.app/](http://resilient-fairy-1f9082.netlify.app/) using its drag and drop system, where you define models, api routes, and roles.
2.  **Project Setup**: Once you have that configuration object, you save it as a JSON, and proceed with the project setup phase.

Here are steps for the project setup phase:

1.  **Frontend Setup:** You have your frontend application running on a port or already deployed, for testing, it will typically run on port `5173`

2.  **Create Configuration JSON:**
    *   Use the web interface mentioned above or manually craft a JSON file that matches your project requirements and the [configuration object](https://github.com/manaknight/baas/blob/master/README.md#sample-configuration-js-generated-from-that-website-and-used-to-generate-the-entire-project) provided below. This is the heart of the system, driving database schema, APIs and model generation.
    *   Save your configuration file, for example `config.json`.

3. **Using `setup_baas_project.js` or manually**

    *   **Using `setup_baas_project.js`:**
        *   This file automates the steps described below, run this file with the command `node setup_baas_project.js --name name --path  path/to/your/configuration-file.json` and it will handle steps `4-7` for you.
        *   If you want to proceed manually, skip to step 4.
    *   **Manual setup**
    *   **Create Configuration Folder:**
        *   Create a new folder under the `custom` directory, e.g. `custom/project1`
        *   Place your configuration JSON file inside the `configuration` folder, e.g `custom/project1/configuration/config.json`.

4. **Copy `index.js`**:
        *   Place the `index.js` file (provided below) in the root folder of your project or within the custom project folder you just created, i.e. `custom/project1/index.js`.

  ```javascript
   var fs = require("fs");
    module.exports = function (app) {
        fs.readdirSync(__dirname).forEach(function (file) {
            if (file === "index.js" || file.substr(file.lastIndexOf(".") + 1) !== "js") {
                return;
            }
            
            if (file === ".DS_Store") {
                return;
            }
            
            var name = file.substr(0, file.indexOf("."));
            require("./" + name)(app);
        });
    }
   ```
        *   This is the entry point that loads all the generated file.

5.  **Run Database Migration**:
    *   Execute the `migration.js` script to generate SQL for your database schema (creating tables) and also drop tables and seed tables.
    *   Replace `path/to/your/configuration-file.json` with the actual path to your JSON config file.

        ```bash
        node migration.js path/to/your/configuration-file.json create
        ```

    *   The command above will generate `create_tables.sql` which you will need to use in `phpmyadmin` to update your database under the sql tab.

6. **Run database transform**
    *   After adding tables to database, we will now run transform, this will generate our models, api routes and roles and lambdas, and also the sdk

        ```bash
        node migration.js path/to/your/configuration-file.json transform
        ```
7. **Generate Postman Collection**
        ```bash
        node migration.js postman path/to/your/configuration-file.json
        ```

7.  **Code Generation Completion:** The `migration.js` script will handle:
    *   Creation of model files in `custom/project1/models/`
    *   Creation of role files in `custom/project1/roles/`
    *   Creation of lambdas in `custom/project1/lambda/`
    *   Creation of API routes in `custom/project1/api.js`
    *   Creation of the SDK for that project in the `custom/project1/MkdSDK.js`

### 3.  Testing

1.  **Using `copy_folder_to_testing`:** You can run the `setup_baas_project.js` on the sample config file in `copy_folder_to_testing` for a working version of the system without creating your own custom configs.
2.  **Unit Tests:** Tests are included in the `tests` folder, and follow a similar folder structure as the project and lambdas. These are very useful for verifying the integrity of the system after modifications and should be run using a test runner such as `jest`.

### 4.  Using Cursor Code Editor AI

Cursor is an AI-powered code editor that can greatly accelerate your development process within the Baas V5 System. The way you would typically work within this system is as follows:

1.  **Review Generated Code**: After running `node migration.js`, examine the generated code in your models, roles, lambdas, and API files. These files will have comments indicating areas of customization.
2.  **Leverage AI Code Generation**:
    *   **In-File Editing:** Use Cursor's AI chat feature to make changes to the generated files. For example, if you need to add custom methods to a model, or add extra logic in a lambda, you can prompt Cursor with "add this function to this file" along with the details.
    *   **Prompt Examples:**
        *   "Add a method to User model to fetch all users with a specific status"
        *   "Create a new service in a `custom/project1/services` folder for sending emails"
        *   "Update the user registration lambda to validate additional fields"
        *   "Create unit tests for this function"
3.  **AI Code Completion:** As you write code within the Baas V5 System, Cursor can help complete functions, parameters, and logic by using the existing code within the project.
4.  **Iterative Development**: This workflow fosters an iterative process where AI aids in extending the base functionality.

## Key Features

### 1.  Database Schema Generation

*   **Automatic SQL Generation:** Based on the provided JSON, the system can generate both `CREATE TABLE` statements (for database initialization) and `DROP TABLE` statements (for cleanup).
*   **Data Types:** Supports a wide range of data types: primary keys, strings, long text, integers, doubles, big numbers, booleans, dates, datetimes, timestamps, UUIDs, passwords, JSON, and mapping types (enums).
*   **Constraints:** Handles `required` constraints on fields.
*   **Default Values:** Supports default values for fields, including `CURRENT_TIMESTAMP` and mapping values.

### 2.  Model Generation

*   **Class-Based Models:** Creates classes in `/models` folder that map to database tables, enhancing organization and code maintainability.
*   **Schema Definition:** Automatically includes schema definition within each model using the config file, including data types, validation rules, and default values.
*   **Mapping Transformation:** Automatically generated methods to transform fields based on the `mapping` config
*   **Relation Support:** Handles relationships between models using foreign keys and generates methods for retrieving related models.

### 3. Role-Based Access Control

*   **Role Definition:** Creates classes representing different user roles, each with its own specific permissions.
*   **Permission Management:** Defines permissions at the role level, controlling access to specific routes, data models, and operations.
*   **Fine-Grained Control:** Implements blacklisting of fields to limit the data available to a specific role.
*  **API and Lambda Permissions**: This enables you to grant access to endpoints at a role based level, and to have custom lambdas that are also accessed at a role level.
*  **Authentication**: Role based authentication is enforced by the system, so that users can only do what they are allowed to do based on the logged in user's role, using a token authentication method that has been enabled by the config

### 4. Authentication and Authorization

*   **Authentication Lambdas:** Generates authentication lambdas for common operations such as login, registration, password resets, email verification, profile management, and social logins for Google and Apple.
*   **Token-Based Security:** Employs token-based authentication, enhancing the security of API endpoints, and ensuring role based access control.

### 5.  API Route Generation

*   **Dynamic Router:** Creates dynamic API route handlers based on the provided `routes` configuration.
*   **Request Validation:** Includes input validation for both query parameters and request bodies, preventing malformed requests from reaching the application logic.
*   **Mock APIs:** Allows easy mocking of API endpoints with configurable data, status codes, and output types for rapid prototyping.
*   **Database Operations:** Integrates database operations for retrieving data (`find`, `query`), creating data (`insert`), updating data (`update`), and deleting data (`delete`).
*   **Logic Nodes**: Allows you to create complex data processing flows with `logic` nodes where you can write your own javascript code.
*   **Authorization**: Authentication is automatically enforced in every api endpoint.

### 6.  SDK Generation

*   **Frontend SDK:** Creates SDK classes for each role in the project, enabling frontend applications to easily interact with the backend using a typed and structured API.
*   **Admin SDK**: Generates an admin SDK for admin-related tasks.
*   **Type Safety**: Uses type definitions to ensure type-safe interactions, promoting predictable behavior and catching errors early in development.

## Code Structure and Security Considerations

### 1.  File and Folder Structure

*   `baas/`: Contains the core framework code including base models, core services, and lambda templates.
*   `custom/`: This directory is where your project-specific code resides (e.g., `custom/project1`).
    *   `custom/project1/configuration/`: Holds the `config.json` file.
    *   `custom/project1/roles/`: Houses the generated role model files.
    *   `custom/project1/models/`: Contains the generated database model classes.
    *   `custom/project1/lambda/`: Stores lambda functions for authentication and other logic, customized for each role.
    *   `custom/project1/api.js`: Contains all the project API routes.
    *   `custom/project1/MkdSDK.js`: Contains the SDK used for all roles.
    *   `custom/project1/index.js`: The file to load the entire application.
*   `tests/`: Contains unit tests, following the same structure as the `custom` folder.

### 2. Security Enhancements

*   **Role-Based Access Control (RBAC):** By defining roles with different permissions, you can ensure that users only have access to the resources they need, reducing the risk of unauthorized access or data breaches. The roles are also included in each custom project directory making it less prone to issues from the database table. The permissions are also included in the roles.
*   **Input Validation:** The system automatically validates all inputs against specified data types and validation rules, preventing injections and invalid data from affecting your system.
*   **Less Custom Packages:** This system was designed to build services from scratch, reducing package dependecy risks.
*   **Prevent API Abuse:** The system has methods of preventing unauthorized requests, and only allowing requests from roles that have permissions to that endpoint.

## Sample Configuration

```json
{
  "settings": {
    "globalKey": "key_1734835041254_5nflydnk6",
    "databaseType": "mysql",
    "authType": "jwt",
    "timezone": "America/New_York",
    "dbHost": "localhost",
    "dbPort": "3306",
    "dbUser": "root",
    "dbPassword": "root",
    "dbName": "database_2024-12-22",
    "id": "project_1734835041254_zydcxsqwv",
    "model_namespace": "testing",
    "payment_option": "none",
    "isPWA": true
  },
  "models": [
    {
      "id": "model_1734835047616_c01kea7q9",
      "name": "company",
      "fields": [
        {
          "name": "id",
          "type": "primary key",
          "defaultValue": "",
          "validation": ""
        },
        {
          "name": "name",
          "type": "string",
          "defaultValue": "",
          "validation": "required"
        },
        {
          "name": "logo",
          "type": "string",
          "defaultValue": "",
          "validation": ""
        },
        {
          "name": "address",
          "type": "string",
          "defaultValue": "",
          "validation": ""
        },
        {
          "name": "phone",
          "type": "string",
          "defaultValue": "",
          "validation": "phone"
        },
        {
          "name": "status",
          "type": "mapping",
          "mapping": "0:Active,1:Inactive,2:Suspend",
          "defaultValue": "0",
          "validation": ""
        },
        {
          "name": "created_at",
          "type": "timestamp",
          "defaultValue": "CURRENT_TIMESTAMP",
          "validation": "date"
        }
      ]
    },
    {
      "id": "model_1734835047616_al96tijas",
      "name": "user",
      "fields": [
        {
          "name": "id",
          "type": "primary key",
          "defaultValue": "",
          "validation": ""
        },
        {
          "name": "email",
          "type": "string",
          "defaultValue": "",
          "validation": "required,email"
        },
        {
          "name": "company_id",
          "type": "foreign key",
          "defaultValue": "",
          "validation": "required"
        },
        {
          "name": "login_type",
          "type": "mapping",
          "mapping": "0:Regular,1:Google,2:Microsoft,3:Apple",
          "defaultValue": "0",
          "validation": "required,min:0,max:3"
        },
        {
          "name": "role_id",
          "type": "string",
          "defaultValue": "",
          "validation": ""
        },
        {
          "name": "data",
          "type": "json",
          "defaultValue": "",
          "validation": ""
        },
        {
          "name": "status",
          "type": "mapping",
          "mapping": "0:Active,1:Inactive,2:Suspend",
          "defaultValue": "0",
          "validation": "required,positive"
        },
        {
          "name": "verify",
          "type": "boolean",
          "defaultValue": 0,
          "validation": "required"
        },
        {
          "name": "company_id",
          "type": "integer",
          "defaultValue": "0",
          "validation": ""
        },
        { 
          "name": "created_at", 
          "type": "date", 
          "defaultValue": "", 
          "validation": "date" 
        }
      ]
    },
    {
      "id": "model_1734835047616_dabn11p58",
      "name": "cms",
      "fields": [
        {
          "name": "id",
          "type": "primary key",
          "defaultValue": "",
          "validation": ""
        },
        {
          "name": "label",
          "type": "string",
          "defaultValue": "",
          "validation": "required"
        },
        {
          "name": "type",
          "type": "mapping",
          "mapping": "0:Text,1:Number,2:Image,3:Raw",
          "defaultValue": "0",
          "validation": "required"
        },
        {
          "name": "value",
          "type": "long text",
          "defaultValue": "",
          "validation": "required"
        }
      ]
    },
    {
      "id": "model_1734835052283_jjrxf2c9p",
      "name": "inventory",
      "fields": [
        {
          "name": "id",
          "type": "primary key",
          "defaultValue": "",
          "validation": "",
          "validationOptions": {}
        },
        {
          "name": "name",
          "type": "string",
          "defaultValue": "",
          "validation": "required",
          "validationOptions": {},
          "relation": ""
        },
        {
          "name": "quantity",
          "type": "integer",
          "defaultValue": "",
          "validation": "required,integer",
          "validationOptions": {},
          "relation": ""
        },
        {
          "name": "status",
          "type": "mapping",
          "defaultValue": "",
          "validation": "required",
          "validationOptions": {},
          "relation": "",
          "mapping": "0:inactive,1:active,2:intransit"
        },
        {
          "name": "description",
          "type": "long text",
          "defaultValue": "",
          "validation": "",
          "validationOptions": {},
          "relation": ""
        },
        {
          "name": "user_id",
          "type": "integer",
          "defaultValue": "",
          "validation": "required",
          "validationOptions": {},
          "relation": ""
        },
        {
          "name": "expiry",
          "type": "integer",
          "defaultValue": "",
          "validation": "required,negative",
          "validationOptions": {},
          "relation": ""
        },
        {
          "name": "manufactured_version",
          "type": "double",
          "defaultValue": "",
          "validation": "required,decimal",
          "validationOptions": {},
          "relation": ""
        },
        {
          "name": "warehouse_id",
          "type": "integer",
          "defaultValue": "",
          "validation": "required",
          "validationOptions": {},
          "relation": ""
        }
      ],
      "relations": []
    },
    {
      "id": "model_1734835224274_r5q1pophi",
      "name": "warehouse",
      "fields": [
        {
          "name": "id",
          "type": "primary key",
          "defaultValue": "",
          "validation": "",
          "validationOptions": {}
        },
        {
          "name": "name",
          "type": "string",
          "defaultValue": "",
          "validation": "required",
          "validationOptions": {},
          "relation": ""
        },
        {
          "name": "website",
          "type": "string",
          "defaultValue": "",
          "validation": "required,url",
          "validationOptions": {},
          "relation": ""
        }
      ],
      "relations": []
    },
    {
      "id": "model_1734835257622_i0q1yh3fz",
      "name": "accounting",
      "fields": [
        {
          "name": "id",
          "type": "primary key",
          "defaultValue": "",
          "validation": "",
          "validationOptions": {}
        },
        {
          "name": "inventory_id",
          "type": "integer",
          "defaultValue": "",
          "validation": "required",
          "validationOptions": {},
          "relation": ""
        },
        {
          "name": "description",
          "type": "string",
          "defaultValue": "",
          "validation": "required,pattern:^[a-zA-Z]+$",
          "validationOptions": {},
          "relation": ""
        },
        {
          "name": "quantity",
          "type": "integer",
          "defaultValue": "",
          "validation": "required,integer",
          "validationOptions": {},
          "relation": ""
        },
        {
          "name": "status",
          "type": "mapping",
          "defaultValue": "0",
          "validation": "required",
          "validationOptions": {},
          "relation": "",
          "mapping": "0:new,1:completed"
        }
      ],
      "relations": []
    },
    {
      "id": "model_1734835336446_qi3vtvaa4",
      "name": "accounting_inventory_secret",
      "fields": [
        {
          "name": "id",
          "type": "primary key",
          "defaultValue": "",
          "validation": "",
          "validationOptions": {}
        },
        {
          "name": "inventory_id",
          "type": "integer",
          "defaultValue": "",
          "validation": "required",
          "validationOptions": {},
          "relation": "inventory"
        },
        {
          "name": "accounting_id",
          "type": "integer",
          "defaultValue": "",
          "validation": "required",
          "validationOptions": {},
          "relation": "accounting"
        }
      ],
      "relations": [
        "accounting"
      ]
    },
    {
      "id": "model_1734835541220_edyudr05b",
      "name": "hidden",
      "fields": [
        {
          "name": "id",
          "type": "primary key",
          "defaultValue": "",
          "validation": "",
          "validationOptions": {}
        },
        {
          "name": "name",
          "type": "string",
          "defaultValue": "",
          "validation": "required,length:10,alphanumeric",
          "validationOptions": {},
          "relation": ""
        },
        {
          "name": "code",
          "type": "integer",
          "defaultValue": "",
          "validation": "required,enum:1,2,3",
          "validationOptions": {},
          "relation": ""
        }
      ],
      "relations": []
    },
    {
      "name": "ValidationTest",
      "fields": [
        { "name": "id", "type": "primary key" },
        { "name": "requiredField", "type": "string", "validation": "required" },
        { "name": "emailField", "type": "string", "validation": "email" },
        { "name": "lengthField", "type": "string", "validation": ["length", { "length": 5 }] },
        { "name": "enumField", "type": "string", "validation": ["enum", { "enum": ["a", "b", "c"] }] },
        { "name": "patternField", "type": "string", "validation": ["pattern", { "pattern": "^[A-Z]+$" }] },
        { "name": "positiveField", "type": "integer", "validation": "positive" },
        { "name": "negativeField", "type": "integer", "validation": "negative" },
        { "name": "integerField", "type": "integer", "validation": "integer" },
        { "name": "decimalField", "type": "double", "validation": "decimal" },
        { "name": "alphanumericField", "type": "string", "validation": "alphanumeric" },
        { "name": "uuidField", "type": "uuid", "validation": "uuid" },
        { "name": "jsonField", "type": "json", "validation": "json" },
        { "name": "dateField", "type": "date", "validation": "date" },
        { "name": "phoneField", "type": "string", "validation": "phone" },
        { "name": "relatedField", "type": "mapping", "relation": "related" }
      ]
    },
    {
      "id": "model_1735148740278_aox522fx7",
      "name": "tokens",
      "fields": [
        {
          "name": "id",
          "type": "primary key",
          "defaultValue": "",
          "validation": ""
        },
        {
          "name": "user_id",
          "type": "foreign key",
          "defaultValue": "",
          "validation": "required"
        },
        {
          "name": "token",
          "type": "string",
          "defaultValue": "",
          "validation": "required"
        },
        {
          "name": "type",
          "type": "mapping",
          "mapping": "0:Access,1:Refresh,2:Reset,3:Verify,4:Magic",
          "defaultValue": "0",
          "validation": "required,enum:0,1,2,3,4"
        },
        {
          "name": "data",
          "type": "json",
          "defaultValue": "",
          "validation": ""
        },
        {
          "name": "status",
          "type": "mapping",
          "mapping": "0:Inactive,1:Active",
          "defaultValue": "1",
          "validation": "required,enum:0,1"
        },
        {
          "name": "created_at",
          "type": "timestamp",
          "defaultValue": "CURRENT_TIMESTAMP",
          "validation": "date"
        },
        {
          "name": "updated_at",
          "type": "timestamp",
          "defaultValue": "CURRENT_TIMESTAMP",
          "validation": "date"
        },
        {
          "name": "expired_at",
          "type": "timestamp",
          "defaultValue": "",
          "validation": "date"
        }
      ]
    }
  ],
  "routes": [
    {
      "id": "route_1734835558351_p7t0wlpbr",
      "name": "Get All hidden",
      "method": "GET",
      "url": "/api/hidden",
      "flowData": {
        "nodes": [
          {
            "id": "url_node_1734835558351",
            "type": "url",
            "position": {
              "x": 100,
              "y": 100
            },
            "data": {
              "label": "Url",
              "apiname": "Get All hidden",
              "path": "/api/hidden",
              "method": "GET"
            }
          },
          {
            "id": "db_find_node_1734835558351",
            "type": "db-find",
            "position": {
              "x": 100,
              "y": 200
            },
            "data": {
              "label": "Database Find",
              "model": "hidden",
              "operation": "findMany",
              "query": "SELECT * FROM hidden",
              "resultVar": "hiddenResult"
            }
          },
          {
            "id": "output_node_1734835558351",
            "type": "outputs",
            "position": {
              "x": 100,
              "y": 300
            },
            "data": {
              "label": "Response",
              "outputType": "json",
              "fields": [
                {
                  "name": "id",
                  "type": "number"
                },
                {
                  "name": "name",
                  "type": "string"
                },
                {
                  "name": "code",
                  "type": "integer"
                }
              ],
              "statusCode": 200
            }
          }
        ],
        "edges": [
          {
            "id": "url-to-db_1734835558351",
            "source": "url_node_1734835558351",
            "target": "db_find_node_1734835558351"
          },
          {
            "id": "db-to-output_1734835558351",
            "source": "db_find_node_1734835558351",
            "target": "output_node_1734835558351"
          }
        ]
      }
    },
    {
      "id": "route_1734835558351_umk8qzgaq",
      "name": "Get One hidden",
      "method": "GET",
      "url": "/api/hidden/:id",
      "flowData": {
        "nodes": [
          {
            "id": "url_node_1734835558351_1",
            "type": "url",
            "position": {
              "x": 100,
              "y": 100
            },
            "data": {
              "label": "Url",
              "apiname": "Get One hidden",
              "path": "/api/hidden/:id",
              "method": "GET",
              "queryFields": [
                {
                  "name": "id",
                  "type": "number",
                  "validation": "required"
                }
              ]
            }
          },
          {
            "id": "db_query_node_1734835558351_1",
            "type": "db-query",
            "position": {
              "x": 100,
              "y": 200
            },
            "data": {
              "label": "Database Find",
              "model": "hidden",
              "operation": "findOne",
              "query": "SELECT * FROM hidden WHERE id=id",
              "resultVar": "hiddenOneResult"
            }
          },
          {
            "id": "output_node_1734835558351_1",
            "type": "outputs",
            "position": {
              "x": 100,
              "y": 300
            },
            "data": {
              "label": "Response",
              "outputType": "json",
              "fields": [
                {
                  "name": "id",
                  "type": "number"
                },
                {
                  "name": "name",
                  "type": "string"
                },
                {
                  "name": "code",
                  "type": "integer"
                },
                {
                  "name": "error",
                  "type": "boolean"
                }
              ],
              "resultVar": "hiddenOneResult",
              "statusCode": 200
            }
          }
        ],
        "edges": [
          {
            "id": "url-to-db_1734835558351_1",
            "source": "url_node_1734835558351_1",
            "target": "db_query_node_1734835558351_1"
          },
          {
            "id": "db-to-output_1734835558351_1",
            "source": "db_query_node_1734835558351_1",
            "target": "output_node_1734835558351_1"
          }
        ]
      }
    },
    {
      "id": "route_1734835558351_vq257bfqw",
      "name": "Create hidden",
      "method": "POST",
      "url": "/api/hidden",
      "flowData": {
        "nodes": [
          {
            "id": "url_node_1734835558351_3",
            "type": "url",
            "position": {
              "x": 100,
              "y": 100
            },
            "data": {
              "label": "Url",
              "apiname": "Create hidden",
              "path": "/api/hidden",
              "method": "POST",
              "fields": [
                {
                  "name": "name",
                  "type": "string",
                  "validation": ""
                },
                {
                  "name": "code",
                  "type": "integer",
                  "validation": ""
                }
              ]
            }
          },
          {
            "id": "db_insert_node_1734835558351_3",
            "type": "db-insert",
            "position": {
              "x": 100,
              "y": 200
            },
            "data": {
              "label": "Database Insert",
              "model": "hidden",
              "operation": "create",
              "query": "INSERT INTO hidden (name, code)\n                      VALUES (:name, :code)",
              "resultVar": "hiddenCreateResult"
            }
          },
          {
            "id": "output_node_1734835558351_3",
            "type": "outputs",
            "position": {
              "x": 100,
              "y": 300
            },
            "data": {
              "label": "Response",
              "outputType": "json",
              "fields": [
                {
                  "name": "error",
                  "type": "boolean"
                },
                {
                  "name": "id",
                  "type": "number"
                }
              ],
              "statusCode": 200
            }
          }
        ],
        "edges": [
          {
            "id": "url-to-db_1734835558351_3",
            "source": "url_node_1734835558351_3",
            "target": "db_insert_node_1734835558351_3"
          },
          {
            "id": "db-to-output_1734835558351_3",
            "source": "db_insert_node_1734835558351_3",
            "target": "output_node_1734835558351_3"
          }
        ]
      }
    },
    {
      "id": "route_1734835558352_dna6umo13",
      "name": "Update hidden",
      "method": "PUT",
      "url": "/api/hidden/:id",
      "flowData": {
        "nodes": [
          {
            "id": "url_node_1734835558351_4",
            "type": "url",
            "position": {
              "x": 100,
              "y": 100
            },
            "data": {
              "label": "Url",
              "apiname": "Update hidden",
              "path": "/api/hidden/:id",
              "method": "PUT",
              "queryFields": [
                {
                  "name": "id",
                  "type": "number",
                  "validation": "required"
                }
              ],
              "fields": [
                {
                  "name": "name",
                  "type": "string",
                  "validation": "required"
                },
                {
                  "name": "code",
                  "type": "integer",
                  "validation": "required"
                }
              ]
            }
          },
          {
            "id": "db_update_node_1734835558351_4",
            "type": "db-update",
            "position": {
              "x": 100,
              "y": 200
            },
            "data": {
              "label": "Database Update",
              "model": "hidden",
              "operation": "update",
              "idField": "id",
              "query": "UPDATE hidden SET name=:name, code=:code WHERE id=:id",
              "resultVar": "hiddenUpdateResult"
            }
          },
          {
            "id": "output_node_1734835558351_4",
            "type": "outputs",
            "position": {
              "x": 100,
              "y": 300
            },
            "data": {
              "label": "Response",
              "outputType": "json",
              "fields": [
                {
                  "name": "error",
                  "type": "boolean"
                },
                {
                  "name": "id",
                  "type": "number"
                }
              ],
              "statusCode": 200
            }
          }
        ],
        "edges": [
          {
            "id": "url-to-db_1734835558351_4",
            "source": "url_node_1734835558351_4",
            "target": "db_update_node_1734835558351_4"
          },
          {
            "id": "db-to-output_1734835558351_4",
            "source": "db_update_node_1734835558351_4",
            "target": "output_node_1734835558351_4"
          }
        ]
      }
    },
    {
      "id": "route_1734835558351_578vyr31y",
      "name": "Delete One hidden",
      "method": "DELETE",
      "url": "/api/hidden/:id",
      "flowData": {
        "nodes": [
          {
            "id": "url_node_1734835558351_1",
            "type": "url",
            "position": {
              "x": 100,
              "y": 100
            },
            "data": {
              "label": "Url",
              "apiname": "Delete One hidden",
              "path": "/api/hidden/:id",
              "method": "DELETE",
              "queryFields": [
                {
                  "name": "id",
                  "type": "number",
                  "validation": "required"
                }
              ]
            }
          },
          {
            "id": "db_delete_node_1734835558351_2",
            "type": "db-delete",
            "position": {
              "x": 100,
              "y": 200
            },
            "data": {
              "label": "Database Delete",
              "model": "hidden",
              "operation": "delete",
              "query": "DELETE FROM hidden WHERE id=id",
              "resultVar": "hiddenDeleteResult"
            }
          },
          {
            "id": "output_node_1734835558351_2",
            "type": "outputs",
            "position": {
              "x": 100,
              "y": 300
            },
            "data": {
              "label": "Response",
              "outputType": "json",
              "fields": [
                {
                  "name": "error",
                  "type": "boolean"
                },
                {
                  "name": "id",
                  "type": "integer"
                }
              ],
              "statusCode": 200
            }
          }
        ],
        "edges": [
          {
            "id": "url-to-db_1734835558351_1",
            "source": "url_node_1734835558351_1",
            "target": "db_delete_node_1734835558351_2"
          },
          {
            "id": "db-to-output_1734835558351_2",
            "source": "db_delete_node_1734835558351_2",
            "target": "output_node_1734835558351_2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              "
          }
        ]
      }
    },
    {
      "id": "route_1735706765196_fd7leiser",
      "name": "mock",
      "flowData": {
        "nodes": [
          {
            "id": "url_node_1735706776881",
            "type": "mock-api",
            "position": {
              "x": 100,
              "y": 100
            },
            "data": {
              "label": "Mock API",
              "apiname": "mock",
              "path": "/api/mockapi",
              "method": "POST",
              "description": "this is mock api",
              "fields": [
                {
                  "name": "a1",
                  "type": "string",
                  "validation": ""
                },
                {
                  "name": "a2",
                  "type": "integer",
                  "validation": ""
                },
                {
                  "name": "a3",
                  "type": "boolean",
                  "validation": ""
                },
                {
                  "name": "a4",
                  "type": "date",
                  "validation": ""
                },
                {
                  "name": "a5",
                  "type": "datetime",
                  "validation": ""
                },
                {
                  "name": "a6",
                  "type": "file",
                  "validation": ""
                },
                {
                  "name": "a7",
                  "type": "array",
                  "value": "string"
                },
                {
                  "name": "a8",
                  "type": "array",
                  "value": {
                    "type": "object",
                    "fields": [
                      {
                        "name": "a9",
                        "type": "string"
                      },
                      {
                        "name": "a10",
                        "type": "number"
                      },
                      {
                        "name": "a11",
                        "type": "object",
                        "fields": [
                          {
                            "name": "a12",
                            "type": "string"
                          }
                        ]
                      }
                    ]
                  }
                },
                {
                  "name": "a9",
                  "type": "object",
                  "value": [
                    {
                      "name": "a13",
                      "type": "string"
                    },
                    {
                      "name": "a14",
                      "type": "object",
                      "fields": [
                        {
                          "name": "a15",
                          "type": "string"
                        }
                      ]
                    }
                  ]
                }
              ],
              "queryFields": [
                {
                  "name": "a16",
                  "type": "integer",
                  "validation": ""
                }
              ],
              "responseFields": [
                {
                  "name": "error",
                  "type": "boolean",
                  "validation": ""
                },
                {
                  "name": "model",
                  "type": "object",
                  "value": [
                    {
                      "name": "message",
                      "type": "string"
                    },
                    {
                      "name": "page",
                      "type": "integer"
                    },
                    {
                      "name": "obj",
                      "type": "object",
                      "fields": [
                        {
                          "name": "a16",
                          "type": "string"
                        },
                        {
                          "name": "a17",
                          "type": "number"
                        },
                        {
                          "name": "obj2",
                          "type": "object"
                        },
                        {
                          "name": "a18",
                          "type": "string"
                        }
                      ]
                    }
                  ]
                }
              ],
              "authType": "bearer",
              "outputType": "json",
              "statusCode": "201"
            },
            "width": 180,
            "height": 56,
            "selected": false,
            "dragging": false
          }
        ],
        "edges": []
      }
    },
    {
      "id": "route_1735711320512_e5xtvptwe",
      "name": "a",
      "flowData": {
        "nodes": [
          {
            "id": "url_node_1735711325783",
            "type": "url",
            "position": {
              "x": -30,
              "y": -15
            },
            "data": {
              "label": "URL",
              "apiname": "a",
              "path": "/api/a",
              "method": "GET",
              "fields": [],
              "queryFields": [
                {
                  "name": "id",
                  "type": "integer",
                  "validation": "required"
                }
              ]
            },
            "width": 180,
            "height": 56,
            "selected": false,
            "dragging": false,
            "positionAbsolute": {
              "x": -30,
              "y": -15
            }
          },
          {
            "id": "node_1735711337597",
            "type": "auth",
            "position": {
              "x": -45,
              "y": 75
            },
            "data": {
              "label": "Auth",
              "fields": [],
              "queryFields": [],
              "authType": "apiKey"
            },
            "width": 180,
            "height": 56,
            "selected": false,
            "positionAbsolute": {
              "x": -45,
              "y": 75
            },
            "dragging": false
          },
          {
            "id": "node_1735711347758",
            "type": "logic",
            "position": {
              "x": -45,
              "y": 150
            },
            "data": {
              "label": "Logic",
              "fields": [],
              "queryFields": [],
              "code": "//step 1 do x\n\n//step 2 do y"
            },
            "width": 180,
            "height": 56,
            "selected": false,
            "dragging": false
          },
          {
            "id": "node_1735711363929",
            "type": "db-query",
            "position": {
              "x": -45,
              "y": 240
            },
            "data": {
              "label": "Db query",
              "fields": [],
              "queryFields": [],
              "model": "company",
              "operation": "findOne",
              "query": "SELECT * FROM company WHERE id=:id",
              "resultVar": "result"
            },
            "width": 180,
            "height": 56,
            "selected": false,
            "dragging": false
          },
          {
            "id": "node_1735711386199",
            "type": "outputs",
            "position": {
              "x": -30,
              "y": 330
            },
            "data": {
              "label": "Outputs",
              "fields": [],
              "queryFields": [],
              "outputType": "json",
              "resultVar": "result",
              "statusCode": "201"
            },
            "width": 180,
            "height": 56,
            "selected": false,
            "dragging": false
          }
        ],
        "edges": [
          {
            "source": "url_node_1735711325783",
            "sourceHandle": null,
            "target": "node_1735711337597",
            "targetHandle": null,
            "id": "reactflow__edge-url_node_1735711325783-node_1735711337597"
          },
          {
            "source": "node_1735711337597",
            "sourceHandle": null,
            "target": "node_1735711347758",
            "targetHandle": null,
            "id": "reactflow__edge-node_1735711337597-node_1735711347758"
          },
          {
            "source": "node_1735711347758",
            "sourceHandle": null,
            "target": "node_1735711363929",
            "targetHandle": null,
            "id": "reactflow__edge-node_1735711347758-node_1735711363929"
          },
          {
            "source": "node_1735711363929",
            "sourceHandle": null,
            "target": "node_1735711386199",
            "targetHandle": null,
            "id": "reactflow__edge-node_1735711363929-node_1735711386199"
          }
        ]
      }
    }
  ],
  "roles": [
    {
      "id": "role_admin_1734835047616",
      "name": "Super Admin",
      "slug": "super_admin",
      "permissions": {
        "authRequired": true,
        "routes": [
          "route_1734835558351_p7t0wlpbr",
          "route_1734835558351_umk8qzgaq",
          "route_1734835558351_vq257bfqw",
          "route_1734835558352_dna6umo13",
          "route_1734835558351_578vyr31y"
        ],
        "canCreateUsers": true,
        "canEditUsers": true,
        "canDeleteUsers": true,
        "canManageRoles": true,
        "canLogin": true,
        "canRegister": true,
        "canForgot": false,
        "canReset": false,
        "canGoogleLogin": true,
        "canAppleLogin": false,
        "canMicrosoftLogin": false,
        "canMagicLinkLogin": false,
        "needs2FA": true,
        "canSetPermissions": true,
        "canPreference": true,
        "canVerifyEmail": true,
        "canUpload": true,
        "canStripe": true,
        "canStripeWebhook": true,
        "canRealTime": true,
        "canAI": true,
        "canUpdateEmail": true,
        "canUpdatePassword": true,
        "canUpdateOtherUsers": true,
        "treeql": {
          "enabled": true,
          "models": {
            "company": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "user": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "cms": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "inventory": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "warehouse": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "accounting": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "accounting_inventory_secret": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "hidden": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            }
          }
        }
      }
    },
    {
      "id": "role_company_admin_1734835047616",
      "name": "Company Admin",
      "slug": "company_admin",
      "permissions": {
        "authRequired": true,
        "routes": [],
        "canCreateUsers": true,
        "canEditUsers": true,
        "canDeleteUsers": true,
        "canManageRoles": true,
        "canLogin": true,
        "canRegister": true,
        "canForgot": false,
        "canReset": false,
        "canGoogleLogin": true,
        "canAppleLogin": false,
        "canMicrosoftLogin": false,
        "canMagicLinkLogin": false,
        "needs2FA": true,
        "canSetPermissions": true,
        "canPreference": true,
        "canVerifyEmail": true,
        "canUpload": true,
        "canStripe": true,
        "canStripeWebhook": true,
        "canRealTime": true,
        "canAI": true,
        "canUpdateEmail": true,
        "canUpdatePassword": true,
        "canUpdateOtherUsers": true,
        "treeql": {
          "enabled": true,
          "models": {
            "accounting": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "warehouse": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "inventory": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "cms": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "company": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "user": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            }
          }
        },
        "companyScoped": true
      }
    },
    {
      "id": "role_member_1734835047616",
      "name": "Member",
      "slug": "member",
      "permissions": {
        "authRequired": true,
        "routes": [],
        "canCreateUsers": false,
        "canEditUsers": false,
        "canDeleteUsers": false,
        "canManageRoles": false,
        "canLogin": true,
        "canRegister": false,
        "canGoogleLogin": false,
        "canAppleLogin": false,
            "canForgot": true,
    "canReset": true,
        "canMicrosoftLogin": false,
        "canMagicLinkLogin": true,
        "needs2FA": false,
        "canSetPermissions": false,
        "canUpdateOtherUsers": false,
        "treeql": {
          "enabled": true,
          "models": {
            "inventory": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": true,
                "put": true,
                "delete": true,
                "paginate": true,
                "join": true
              }
            },
            "accounting": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": false,
                "put": false,
                "delete": false,
                "paginate": true,
                "join": false
              }
            },
            "warehouse": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": false,
                "put": false,
                "delete": false,
                "paginate": true,
                "join": true
              }
            },
            "cms": {
              "allowed": true,
              "blacklistedFields": [],
              "operations": {
                "get": true,
                "getOne": true,
                "getAll": true,
                "post": false,
                "put": false,
                "delete": false,
                "paginate": true,
                "join": true
              }
            }
          }
        },
        "companyScoped": true
      }
    }
  ]
}
```



Okay, here are the new points, designed and formatted in Markdown, focusing on the `app` object enhancements, role initialization, and the TreeQL API:

### Enhanced Features and System Architecture

*   **Database Connection Pooling:** The system employs connection pooling to optimize database interactions. By reusing existing database connections, it reduces the overhead of establishing new connections for each request, leading to better performance and resource management, especially under high traffic loads.

*   **Dynamic Lambda Execution:** Lambdas are loaded and executed dynamically based on the configuration. This modular approach allows for flexible and customizable server-side logic, enabling developers to easily extend core functionality and create custom workflows.

*   **Customizable Error Handling:** While a default error handler is included, the system allows for project-specific error handling. This enables you to tailor responses and logging mechanisms, enhancing debugging and user experience. Custom error handlers can be implemented at the project level and within individual lambdas, ensuring fine-grained control over error management.

*   **File Upload Support:** The system provides built-in support for file uploads via API endpoints. This allows for a straightforward implementation of features requiring file processing, such as image uploads or document handling, directly integrated with the API routes defined by the project configuration.

*   **Cross-Origin Resource Sharing (CORS):** CORS is automatically enabled with a permissive configuration to allow access from any origin `*`. This facilitates easy integration of the backend with diverse front-end applications. For more control, you can specify custom CORS options within your main `index.js` file, tailoring the access rules for specific use cases.

*   **Realtime Functionality:** Provides realtime functionality using websockets, and also with a permissions system, based on the configuration object.

*   **AI Functionality:** Provides a way to create AI functionality based on the permissions, using a configuration object.

*   **Centralized Configuration Access:** The Express `app` object is enhanced by storing the project's configuration (`config`) as an accessible property using `app.set("configuration", config)`. This enables easy access to essential project settings, database credentials, and other keys throughout the application, while allowing for central management and avoiding passing config objects around.

*   **Backend SDK Integration:** The application integrates a custom Backend SDK instance using `app.set("sdk", sdk)`. This SDK, accessible throughout the application, provides a unified interface for interacting with the database, simplifying data access, query building, and other common backend tasks. It also has methods for adding namespacing to the project.

* **Role-Based Configuration:** The system allows you to define role-based access in the `custom/project/configuration.js` file, which looks like this:


const super_admin = require("./roles/super_admin");
const company_admin = require("./roles/company_admin");
const member = require("./roles/member");
      module.exports = function(app) {
          return {
              roles: ["super_admin", "company_admin", "member"],
              super_admin: new super_admin(),
                company_admin: new company_admin(),
                member: new member(),
              configuration: {}
          };
      }
```

This file enables the dynamic loading of roles by importing them and exporting them. It contains the role names and the classes for those roles. This way, there are no database dependencies for roles, and they can be edited as files in the local directory.

*  **TreeQL Custom API Routes:** The system provides a custom API to perform database operations using a single endpoint. This functionality uses a custom routing methodology for the system. These custom routes can be seen here, this is an example `/api/v1/records/xyz/wxy/:table/:id` where `xyz` is the project id, and `wxy` is the role, and `table` and `id` are used to perform crud operations on that table and with a specific id. A similar route structure also works with listing and pagination of data: `/api/v1/records/xyz/wxy/:table` , this api provides the ability to have custom filter, pagination, sorting and joins based on the role and table. The purpose of these api endpoints is to have one way to get data, no matter the table and role, and it is permission based.

These points give a more complete picture of how the Baas V5 system is structured, its core features, and how the `app` object is enriched to support a configuration-driven approach.
