const path = require("path");
const fs = require("fs");

class TestRunner {
  constructor() {
    this.baseDir = process.cwd();
    this.summary = {
      suites: 0,
      totalTests: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      failedTests: [],
      passedTests: [],
    };
  }

  isTestFile(filename, pattern) {
    if (pattern) {
      return filename.endsWith(".test.js") && filename.includes(pattern);
    }
    return filename.endsWith(".test.js");
  }

  async findAndRunTests(testPath, pattern) {
    const absolutePath = path.resolve(this.baseDir, testPath);
    const files = await fs.promises.readdir(absolutePath);

    for (const file of files) {
      const filePath = path.join(absolutePath, file);
      const stat = await fs.promises.stat(filePath);

      if (stat.isDirectory()) {
        await this.findAndRunTests(
          path.relative(this.baseDir, filePath),
          pattern
        );
      } else if (this.isTestFile(file, pattern)) {
        const relativePath = path.relative(this.baseDir, filePath);
        await this.runTestFile(relativePath);
      }
    }
  }

  aggregateResults(testResults) {
    if (!testResults) return;
    // console.log("testResults >>", JSON.stringify(testResults, null, 2));
    // Count suites
    if (testResults.suites) {
      this.summary.suites += testResults.suites.length;

      // Aggregate results from each suite
      testResults.suites.forEach((suite) => {
        this.summary.totalTests += suite.passed.length + suite.failed.length;
        this.summary.passed += suite.passed.length;
        this.summary.failed += suite.failed.length;

        // Add failed tests with suite context
        suite.failed.forEach((test) => {
          this.summary.failedTests.push({
            suite: suite.name,
            test: test.name,
            error: test.error,
            duration: test.duration,
          });
        });

        // Add passed tests with suite context
        suite.passed.forEach((test) => {
          this.summary.passedTests.push({
            suite: suite.name,
            test: test.name,
            duration: test.duration,
          });
        });
      });
    }

    // Add standalone tests (not in suites)
    if (testResults.passed) {
      this.summary.totalTests += testResults.passed.length;
      this.summary.passed += testResults.passed.length;

      // Add passed standalone tests
      testResults.passed.forEach((test) => {
        if (!this.summary.passedTests.some((t) => t.test === test.name)) {
          this.summary.passedTests.push({
            test: test.name,
            duration: test.duration,
          });
        }
      });
    }
    if (testResults.failed) {
      this.summary.totalTests += testResults.failed.length;
      this.summary.failed += testResults.failed.length;

      // Add failed standalone tests
      testResults.failed.forEach((test) => {
        if (!this.summary.failedTests.some((t) => t.test === test.name)) {
          this.summary.failedTests.push({
            test: test.name,
            error: test.error,
            duration: test.duration,
          });
        }
      });
    }

    // Update total duration
    this.summary.duration += testResults.duration || 0;
  }

  async runTestFile(testPath) {
    console.log(`\nRunning: ${testPath}`);
    try {
      const testModule = require(path.resolve(this.baseDir, testPath));

      // If the module exports a test instance with results
      if (
        testModule &&
        testModule.framework &&
        testModule.framework.testResults
      ) {
        this.aggregateResults(testModule.framework.testResults);
      }
      // If it's a promise (async execution)
      else if (testModule && testModule.then) {
        const results = await testModule;
        this.aggregateResults(results);
      }
    } catch (error) {
      console.error(`Error running ${testPath}:`, error);
      this.summary.failed++;
      this.summary.failedTests.push({
        file: testPath,
        error: error.message,
      });
    }
  }

  printSummary() {
    console.log("\n=== Test Runner Summary ===");
    // Only print the full summary in verbose mode
    if (process.argv.includes("--verbose")) {
      console.log(`Full Summary: ${JSON.stringify(this.summary, null, 2)}`);
    }
    console.log(`Test Suites  : ${this.summary.suites}`);
    console.log(`Total Tests  : ${this.summary.totalTests}`);
    console.log(`Passed       : ${this.summary.passed}`);
    console.log(`Failed       : ${this.summary.failed}`);
    console.log(`Skipped      : ${this.summary.skipped}`);
    console.log(`Duration     : ${this.summary.duration}ms`);

    // Display passed tests
    if (this.summary.passedTests.length > 0) {
      console.log("\nPassed Tests:");
      this.summary.passedTests.forEach((passed) => {
        console.log(
          `\n✅ ${passed.suite ? `${passed.suite} > ` : ""}${
            passed.test || passed.file
          }`
        );
        if (passed.duration) {
          console.log(`   Duration: ${passed.duration}ms`);
        }
      });
    }

    // Display failed tests
    if (this.summary.failedTests.length > 0) {
      console.log("\nFailed Tests:");
      this.summary.failedTests.forEach((failure) => {
        console.log(
          `\n❌ ${failure.suite ? `${failure.suite} > ` : ""}${
            failure.test || failure.file
          }`
        );
        console.log(`   Error: ${failure.error}`);
        if (failure.duration) {
          console.log(`   Duration: ${failure.duration}ms`);
        }
      });
    }

    if (this.summary.totalTests === 0) {
      console.log("\n⚠️  Warning: No tests were executed!");
    }
  }

  async run(testPath, pattern) {
    const startTime = Date.now();

    try {
      const absolutePath = path.resolve(this.baseDir, testPath);
      const stat = await fs.promises.stat(absolutePath);

      if (stat.isDirectory()) {
        await this.findAndRunTests(testPath, pattern);
      } else if (this.isTestFile(path.basename(testPath), pattern)) {
        const relativePath = path.relative(this.baseDir, absolutePath);
        await this.runTestFile(relativePath);
      }

      this.summary.duration = Date.now() - startTime;
      this.printSummary();

      // Exit with appropriate code
      process.exit(this.summary.failed > 0 ? 1 : 0);
    } catch (error) {
      console.error("Test runner error:", error);
      process.exit(1);
    }
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  path: "tests",
  pattern: null,
  verbose: false,
};

for (let i = 0; i < args.length; i++) {
  if (args[i] === "--path" && args[i + 1]) {
    options.path = args[i + 1];
    i++;
  } else if (args[i] === "--pattern" && args[i + 1]) {
    options.pattern = args[i + 1];
    i++;
  } else if (args[i] === "--verbose") {
    options.verbose = true;
  }
}

// Run tests
const runner = new TestRunner();
runner.run(options.path, options.pattern);
