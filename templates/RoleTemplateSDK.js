const RoleTemplateSDK = (role = "mkd", project = "core") => {
  const nameParts = role.split("_");
  const pascalCaseName = nameParts
    .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
    .join("");

  return `
  
  
import TreeSD<PERSON> from "./TreeSDK";
import MkdSDK from "./MkdSDK";
import { RestAPIMethodEnum } from "./Enums";
import { RestAPIMethod } from "./types/types";


// Comprehensive configuration interface
interface ${pascalCaseName}SDKConfig {
  baseurl?: string;
  fe_baseurl?: string;
  project_id?: string;
  secret?: string;
  table?: string;
  GOOGLE_CAPTCHA_SITEKEY?: string;
}

// Comprehensive API response interface
interface ${pascalCaseName}APIResponse {
  status?: number;
  message?: string;
  data?: any;
  list?: any;
  model?: any;
  error?: boolean;
}

interface ${pascalCaseName}APIResponse {
  message?: string;
  [key: string]: any;
}


export default class ${pascalCaseName}SDK extends MkdSDK {
  constructor(config: ${pascalCaseName}SDKConfig = {}) {
    super(config);
    this._baseurl = config.baseurl || "https://baas.mytechpassport.com";
    this._project_id = config.project_id || "${project}";
    this._table = config.table || "";
  }

    /**
   * Oauth Login API for social authentication
   * @param type Social login type (e.g., google, facebook, microsoft, apple)
   * @returns Social login link or error
   */
  async oauthLoginApi(type: "google" | "facebook" | "microsoft" | "apple"): Promise<string> {
    const socialLogin = await fetch(\`\${this.baseUrl()}/v1/api/\${this.getProjectId()}/${role}/lambda/\${type}/login\`,);

    const socialLink = await socialLogin.text();

    if (socialLogin.status === 401 || socialLogin.status === 403) {
      throw new Error(socialLink);
    }

    return socialLink;
  }

  async sampleMethod(): Promise<${pascalCaseName}APIResponse> {
    return this.request({
      endpoint: \`/v1/api/{{project}}/{{role}}/lambda/sampleMethod\`,
      method: RestAPIMethodEnum.POST,
    });
  }

  /* CODE */
  
  }
  `;
};

module.exports = RoleTemplateSDK;
// node setup_baas_project.js --name pave --path "C:\Users\<USER>\Desktop\Possible\tech_space\mkd\baas_v5\mtpbk\custom\sample.json"
