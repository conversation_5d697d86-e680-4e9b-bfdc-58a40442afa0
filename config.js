const config = {
  // Global Application Key
  globalKey: process.env.GLOBAL_KEY,
  jwt_key: process.env.JWT_KEY,
  refresh_jwt_expire: process.env.REFRESH_JWT_EXPIRE || 60 * 60 * 24 * 7, // 7 days
  access_jwt_expire: process.env.ACCESS_JWT_EXPIRE || 60 * 60, // 1 hour
  verification_token_expire:
    process.env.VERIFICATION_TOKEN_EXPIRE || 60 * 60 * 24, // 24 hours
  app_url: process.env.APP_URL,
  // Database Configuration
  database: {
    type: process.env.DB_TYPE,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || "baas",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "baas_db",
    timezone: process.env.DB_TIMEZONE || "UTC",
  },
  mail: {
    mail_host: process.env.MAIL_HOST,
    mail_port: process.env.MAIL_PORT,
    mail_user: process.env.MAIL_USER,
    mail_pass: process.env.MAIL_PASS,
    from_mail: process.env.FROM_MAIL,
  },
  // Authentication Configuration
  auth: {
    type: "session",
    secret: process.env.SESSION,
    cookie: {
      secure: "production",
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
  },
  // Server Configuration
  server: {
    port: process.env.PORT || 5172,
    cors: {
      origin: process.env.CORS_ORIGIN,
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
    },
  },
  // API Configuration
  api: {
    prefix: "/api/v1",
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
    },
  },
  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL,
    format: "combined",
  },
  // Stripe Configuration
  stripe: {
    publish_key: process.env.STRIPE_PUBLISH_KEY || "",
    secret_key: process.env.STRIPE_SECRET_KEY || "",
    endpoint_secret: process.env.STRIPE_ENDPOINT_SECRET || "",
    webhook_secret: process.env.STRIPE_WEBHOOK_SECRET || "",
    currency: process.env.STRIPE_CURRENCY || "usd",
    ieti_secret: process.env.STRIPE_IETI_SECRET || "",
  },
  // Twilio Configuration
  twilio: {
    account_sid: process.env.TWILIO_ACCOUNT_SID,
    auth_token: process.env.TWILIO_AUTH_TOKEN,
    phoneNumber: process.env.TWILIO_PHONE_NUMBER,
  },
  aws_key: process.env.AWS_ACCESS_KEY_ID,
  aws_secret: process.env.AWS_SECRET_ACCESS_KEY,
  aws_bucket: process.env.AWS_BUCKET_NAME,
  aws_region: process.env.AWS_REGION,
  basic_auth: process.env.BASIC_AUTH,
  bearer_auth: process.env.BEARER_AUTH,
  api_key: process.env.API_KEY,
  oauth2: process.env.OAUTH2,
  digest: process.env.DIGEST,
  session: process.env.SESSION,
  hmac: process.env.HMAC,
  ldap: process.env.LDAP,
  saml: process.env.SAML,
  git: {
    token: process.env.GIT_TOKEN,
  },
  frontend_server: {
    ip: process.env.FRONTEND_SERVER_IP,
    username: process.env.FRONTEND_SERVER_USERNAME,
    password: process.env.FRONTEND_SERVER_PASSWORD,
  },
  digital_ocean: {
    token: process.env.DIGITAL_OCEAN_TOKEN,
  },
  jenkins: {
    user: "emmy",
    token: process.env.JENKINS_TOKEN,
  },
  google: {
    client_id: process.env.GOOGLE_CLIENT_ID || "",
    client_secret: process.env.GOOGLE_CLIENT_SECRET || "",
    redirect_url: process.env.GOOGLE_REDIRECT_URL || "",
    calendar_client_id: process.env.GOOGLE_CALENDAR_CLIENT_ID || "",
    calendar_client_secret: process.env.GOOGLE_CALENDAR_CLIENT_SECRET || "",
    calendar_redirect_uri: process.env.GOOGLE_CALENDAR_REDIRECT_URI || "",
    drive: {
      client_id: process.env.GOOGLE_DRIVE_CLIENT_ID || "",
      client_secret: process.env.GOOGLE_DRIVE_CLIENT_SECRET || "",
      redirect_url: process.env.GOOGLE_DRIVE_REDIRECT_URL || "",
      api_key: process.env.GOOGLE_DRIVE_API_KEY || "",
    },
  },
  apple: {
    client_id: process.env.APPLE_CLIENT_ID,
    client_secret: process.env.APPLE_CLIENT_SECRET,
    redirect_url: process.env.APPLE_REDIRECT_URL,
    team_id: process.env.APPLE_TEAM_ID,
    private_key: process.env.APPLE_PRIVATE_KEY,
    key_id: process.env.APPLE_KEY_ID,
  },
  facebook: {
    client_id: process.env.FACEBOOK_CLIENT_ID,
    client_secret: process.env.FACEBOOK_CLIENT_SECRET,
    callback_uri: process.env.FACEBOOK_CALLBACK_URI,
  },
  instagram: {
    client_id: process.env.INSTAGRAM_CLIENT_ID,
    client_secret: process.env.INSTAGRAM_CLIENT_SECRET,
    callback_uri: process.env.INSTAGRAM_CALLBACK_URI,
  },
  microsoft: {
    client_secret: process.env.MICROSOFT_CLIENT_SECRET,
    client_values: process.env.MICROSOFT_CLIENT_VALUES,
    application_id: process.env.MICROSOFT_APPLICATION_ID,
    object_id: process.env.MICROSOFT_OBJECT_ID,
    redirect_url: process.env.MICROSOFT_REDIRECT_URL,
  },
  linkedin: {
    client_id: process.env.LINKEDIN_CLIENT_ID,
    client_secret: process.env.LINKEDIN_CLIENT_SECRET,
    redirect_url: process.env.LINKEDIN_REDIRECT_URL,
  },
  hostnames: {
    kanglink: process.env.KANGLINK_HOSTNAME,
  },
};

module.exports = config;
