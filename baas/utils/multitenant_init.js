const StripeService = require("../services/StripeService");
const { sqlDateFormat, sqlDateTimeFormat } = require("../services/UtilService");

exports.multitenant_init = async (options, sdk, projectId) => {
  if (options.multitenant && options.subscription) {
    const plan_name = `${projectId} Multitenant Plan`;
    const plan_description = "Multitenant Plan";
    sdk.setProjectId(projectId);
    sdk.setTable("stripe_product");
    const stripe = new StripeService();

    try {
      const product = await stripe.createStripeProduct({
        name: plan_name,
        description: plan_description,
        metadata: {
          projectId: sdk.getProjectId()
        }
      });

      const data = await sdk.insert({
        stripe_id: product.id,
        name: plan_name,
        object: JSON.stringify(product),
        status: 1,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });
      sdk.setTable("stripe_price");

      const price_name = "default price";
      const amount = 10;

      const price = await stripe.createStripeRecurringPrice({
        productId: product.id,
        price_name,
        amount,
        currency: "usd",
        interval: "month",
        interval_count: 1,
        trial_days: 0,
        metadata: {
          multitenant: 1
        }
      });

      await sdk.insert({
        stripe_id: price.id,
        product_id: data,
        name: price_name,
        object: JSON.stringify(price),
        amount: parseFloat(amount),
        type: "month",
        is_usage_metered: false,
        usage_limit: null,
        status: 1,
        create_at: sqlDateFormat(new Date()),
        update_at: sqlDateTimeFormat(new Date())
      });
    } catch (e) {
      console.log(e);
    }
  }
};
