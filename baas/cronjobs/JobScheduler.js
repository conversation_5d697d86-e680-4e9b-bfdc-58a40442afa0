const JobService = require("./../services/JobService")
const BackendSDK = require("./../core/BackendSDK")


async function performTask(projectId) {
  try {
    let sdk = new BackendSDK();
    let projectDB = await sdk.getProjectDatabase(projectId)
    sdk.setProjectId(projectId)
    sdk.setDatabase(projectDB)
    await (new JobService(sdk)).runQueuedJobs();
    return projectDB
  } catch (error) {
    console.error(error);
  }

}




function startScheduler(worker, projectId, DELAY = 10000) {
  async function performTaskAndReschedule() {
    try {
      const result = await performTask(projectId);

      worker.postMessage(result + " " + projectId);
    } catch (error) {
      console.error('Error in job:', projectId,  error);
    }

    // Scheduling the next task after the current task completes
    setTimeout(performTaskAndReschedule, DELAY);
  }

  // Initial trigger to start the process
  setTimeout(performTaskAndReschedule, DELAY);
}

module.exports = startScheduler;