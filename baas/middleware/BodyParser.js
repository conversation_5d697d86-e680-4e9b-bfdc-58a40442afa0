import bodyParser from "body-parser";

// Middleware function to parse incoming request bodies based on content type
export default (req, res, next) => {
  const contentType = req.headers["content-type"];

  // If the request contains a Stripe signature, use raw body parser
  if (req.headers["stripe-signature"]) {
    return bodyParser.raw({ type: "*/*", limit: "50mb" })(req, res, next);
  }

  // If the content type is application/x-www-form-urlencoded, use urlencoded parser
  if (contentType && contentType === "application/x-www-form-urlencoded") {
    return bodyParser.urlencoded({ extended: true })(req, res, next);
  }

  // Default to JSON body parser for other content types
  return bodyParser.json()(req, res, next);
};
