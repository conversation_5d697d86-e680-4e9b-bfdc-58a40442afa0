const { buildBackend } = require("../../setup_baas_project");
const config = require('../../config');
const Repository = require('./Repository');


module.exports.setupBackend = async function (projectName, projectConfig) {

    if (!projectName.includes('_backend')) {
        projectName = projectName + '_backend';
    }

    repo = await Repository.createRepo(projectName);
    await Repository.addDevTeam(projectName);

    // add token to repo url http://<token>@23.29.118.76:3000/mkdlabs/examplerepo.git
    repo = `http://${config.git.token}@23.29.118.76:3000/mkdlabs/${projectName}.git`;

    await buildBackend(projectName, projectConfig, repo);

}


