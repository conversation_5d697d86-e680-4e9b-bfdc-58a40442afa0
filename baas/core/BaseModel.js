class BaseModel {
  /**
   * Constructor for BaseModel
   * Initializes model fields based on the schema and validates the input
   * @param {Object} fields - Object containing field values
   */
  constructor(fields) {
    // Convert fields object to array format matching schema
    const schema = this.constructor.schema();
    
    // Map schema fields to a standardized format with validation and metadata
    this.fields = schema.map(field => ({
      name: field.name,           // Field name
      type: field.type,            // Field data type
      value: fields[field.name],   // Actual field value
      validation: field.validation || [], // Validation rules
      defaultValue: field.defaultValue // Default value for the field
    }));
    
    // Initialize error tracking
    this.errors = [];
    
    // Validate the model immediately upon creation
    this.validate();
  }

  /**
   * Validates all fields based on their defined validation rules
   * Populates the errors array with any validation failures
   * @returns {boolean} - True if validation passes, false otherwise
   */
  validate() {
    // Normalize validation rules to ensure they are arrays
    const validationRules = this.fields.map(field => {
      if (Array.isArray(field.validation)) {
        return field.validation;
      } else if (typeof field.validation === 'string') {
        return field.validation.split(',').map(r => r.trim());
      }
      return [];
    });
    
    // Reset errors before validation
    this.errors = [];
    
    // Iterate through each field and apply validation rules
    for (let i = 0; i < validationRules.length; i++) {
      const rules = Array.isArray(validationRules[i]) ? validationRules[i] : [validationRules[i]];
      const field = this.fields[i];
      const fieldName = field.name;
      const value = field.value;

      // Validation methods broken out into separate calls for readability
      this._validateRequired(rules, fieldName, value);
      this._validateEmail(rules, fieldName, value);
      this._validateLength(rules, fieldName, value);
      this._validateEnum(rules, fieldName, value);
      this._validatePattern(rules, fieldName, value);
      this._validateNumeric(rules, fieldName, value);
      this._validateSpecialTypes(rules, fieldName, value);
    }

    // Return true if no errors were found
    return this.errors.length === 0;
  }

  /**
   * Validate required fields
   * @private
   */
  _validateRequired(rules, fieldName, value) {
    const isRequired = rules.includes('required');
    if (isRequired && (value === undefined || value === null || value === '')) {
      this.errors.push(`Field ${fieldName} is required.`);
    }
  }

  /**
   * Validate email format
   * @private
   */
  _validateEmail(rules, fieldName, value) {
    if (rules.includes('email') && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      this.errors.push(`Field ${fieldName} must be a valid email.`);
    }
  }

  /**
   * Validate field length
   * @private
   */
  _validateLength(rules, fieldName, value) {
    if (rules.includes('length') && value) {
      const lengthRule = rules.find(r => typeof r === 'object' && r.length);
      if (lengthRule && value.length !== lengthRule.length) {
        this.errors.push(`Field ${fieldName} must be exactly ${lengthRule.length} characters long.`);
      }
    }
  }

  /**
   * Validate enum values
   * @private
   */
  _validateEnum(rules, fieldName, value) {
    if (rules.includes('enum') && value) {
      const enumRule = rules.find(r => typeof r === 'object' && r.enum);
      if (enumRule && !enumRule.enum.includes(value)) {
        this.errors.push(`Field ${fieldName} must be one of: ${enumRule.enum.join(', ')}`);
      }
    }
  }

  /**
   * Validate pattern matching
   * @private
   */
  _validatePattern(rules, fieldName, value) {
    if (rules.includes('pattern') && value) {
      const patternRule = rules.find(r => typeof r === 'object' && r.pattern);
      if (patternRule && !new RegExp(patternRule.pattern).test(value)) {
        this.errors.push(`Field ${fieldName} does not match the required pattern.`);
      }
    }
  }

  /**
   * Validate numeric constraints
   * @private
   */
  _validateNumeric(rules, fieldName, value) {
    if (rules.includes('positive') && value !== undefined && value <= 0) {
      this.errors.push(`Field ${fieldName} must be positive.`);
    }

    if (rules.includes('negative') && value !== undefined && value >= 0) {
      this.errors.push(`Field ${fieldName} must be negative.`);
    }

    if (rules.includes('integer') && value !== undefined && !Number.isInteger(Number(value))) {
      this.errors.push(`Field ${fieldName} must be an integer.`);
    }

    if (rules.includes('decimal') && value !== undefined) {
      const decimalStr = String(value);
      if (!/^\d*\.\d+$/.test(decimalStr)) {
        this.errors.push(`Field ${fieldName} must be a decimal number.`);
      }
    }
  }

  /**
   * Validate special data types
   * @private
   */
  _validateSpecialTypes(rules, fieldName, value) {
    // Alphanumeric validation
    if (rules.includes('alphanumeric') && value && !/^[a-zA-Z0-9]+$/.test(value)) {
      this.errors.push(`Field ${fieldName} must contain only letters and numbers.`);
    }

    // UUID validation
    if (rules.includes('uuid') && value && !/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(value)) {
      this.errors.push(`Field ${fieldName} must be a valid UUID.`);
    }

    // JSON validation
    if (rules.includes('json') && value) {
      try {
        JSON.parse(value);
      } catch (e) {
        this.errors.push(`Field ${fieldName} must be valid JSON.`);
      }
    }

    // Date validation
    if (rules.includes('date') && value) {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        this.errors.push(`Field ${fieldName} must be a valid date.`);
      }
    }

    // Phone validation
    if (rules.includes('phone') && value && !/^\+?[\d\s-]+$/.test(value)) {
      this.errors.push(`Field ${fieldName} must be a valid phone number.`);
    }
  }

  /**
   * Retrieve validation errors
   * @returns {Array} - List of validation error messages
   */
  getErrors() {
    return this.errors;
  }

  /**
   * Check if the model is valid
   * @returns {boolean} - True if no validation errors, false otherwise
   */
  isValid() {
    return this.validate();
  }

  static mapping() {
    return {};
  }

  /**
   * Generate a simple checksum for the model's fields
   * @returns {string} - Checksum hash
   */
  checksum() {
    return this.simpleHash(JSON.stringify(this.fields));
  }

  /**
   * Generate a simple hash for a given string
   * @param {string} str - Input string to hash
   * @returns {string} - Hashed value
   */
  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = (hash << 5) - hash + str.charCodeAt(i);
      hash |= 0; // Convert to 32-bit integer
    }
    return hash.toString();
  }
}

module.exports = BaseModel; 