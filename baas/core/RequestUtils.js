module.exports.getRequestOrdering = function (req) {
  let order = "id";
  let direction = "desc";
  let hasOrder = req.query.hasOwnProperty("order");
  if (hasOrder) {
    let orderData = req.query.order.split(",");
    order = orderData[0] ? orderData[0] : "id";
    direction = orderData[1] ? orderData[1] : "desc";
  }

  return { order, direction, hasOrder };
};

module.exports.getRequestPagination = function (req) {
  let page = 1;
  let limit = 20;
  let hasPagination = req.query.hasOwnProperty("page");
  if (hasPagination) {
    let paginationData = req.query.page.split(",");
    page = paginationData[0] ? paginationData[0] : page;
    limit = paginationData[1] ? paginationData[1] : limit;
  }

  return { page, limit, hasPagination };
};

module.exports.getRequestJoins = function (req) {
  let joins = [];
  let hasJoin = req.query.hasOwnProperty("join");

  if (hasJoin && req.query.join) {
    // Handle both array and string inputs
    const joinInputs = Array.isArray(req.query.join)
      ? req.query.join
      : [req.query.join];

    joinInputs.forEach((join) => {
      // Check if this contains comma-separated nested joins first
      if (join.includes(",")) {
        // Handle nested joins: "token,user" or "table1|fk1,table2|fk2|alias"
        const nestedJoins = join.split(",").map((j) => j.trim());
        const processedChain = [];

        nestedJoins.forEach((nestedJoin) => {
          if (nestedJoin.includes("|")) {
            // New format within nested chain: table|foreignKey|alias
            const parts = nestedJoin.split("|").map((p) => p.trim());
            if (parts.length >= 2) {
              processedChain.push({
                table: parts[0],
                foreignKey: parts[1],
                alias: parts.length > 2 ? parts[2] : undefined,
              });
            }
          } else {
            // Legacy format within nested chain
            processedChain.push(nestedJoin);
          }
        });

        joins.push(processedChain);
      } else if (join.includes("|")) {
        // Single new format join: table|foreignKey|alias or table|foreignKey
        const parts = join.split("|").map((p) => p.trim());
        if (parts.length >= 2) {
          const joinObj = {
            table: parts[0],
            foreignKey: parts[1],
            alias: parts.length > 2 ? parts[2] : undefined,
          };
          joins.push(joinObj);
        }
      } else {
        // Single legacy format join
        joins.push([join.trim()]);
      }
    });
  }

  return { joins, hasJoin };
};

module.exports.getRequestFilters = function (req) {
  let projectId = req.projectId;
  let filterData = [];
  let hasFilter =
    req.query.hasOwnProperty("filter") && req.query.filter != null;
  let filters = req.query.filter;

  if (hasFilter && Array.isArray(filters)) {
    filters.forEach((filter) => {
      filter = filter.replace("#", projectId + "_");
      filter = filter.replace("[user]", req.user_id);
      filterData.push(filter);
    });
  } else if (hasFilter) {
    filters = filters.replace("[user]", req.user_id);
    filters = filters.replace("#", projectId + "_");
    filterData.push(filters);
  }

  return { filterData, hasFilter };
};

module.exports.getRequestIncludes = function (req) {
  let includes = [];
  let hasIncludes = req.query.hasOwnProperty("include");

  if (hasIncludes && req.query.include) {
    // Split by comma and handle both array and string inputs
    includes = Array.isArray(req.query.include)
      ? req.query.include
      : req.query.include.split(",");

    // Trim whitespace from each field
    includes = includes.map((field) => field.trim());
  }

  return { includes, hasIncludes };
};

module.exports.getRequestExcludes = function (req) {
  let excludes = [];
  let hasExcludes = req.query.hasOwnProperty("exclude");

  if (hasExcludes && req.query.exclude) {
    // Split by comma and handle both array and string inputs
    excludes = Array.isArray(req.query.exclude)
      ? req.query.exclude
      : req.query.exclude.split(",");

    // Trim whitespace from each field
    excludes = excludes.map((field) => field.trim());
  }

  return { excludes, hasExcludes };
};

function hidePasswordFields(data) {
  if (Array.isArray(data)) {
    return data.map((item) => hidePasswordFields(item));
  } else if (data && typeof data === "object") {
    const newObj = {};
    for (const [key, value] of Object.entries(data)) {
      if (key.toLowerCase().includes("password")) {
        newObj[key] = "********";
      } else if (
        typeof value === "object" &&
        !Date.prototype.isPrototypeOf(value)
      ) {
        newObj[key] = hidePasswordFields(value);
      } else {
        newObj[key] = value;
      }
    }
    return newObj;
  }
  return data;
}

module.exports.hidePasswordFields = hidePasswordFields;
