module.exports = class QueryBuilder {
  constructor(sdk) {
    this.sdk = sdk;
    this.table = sdk.getTable();
    this.fromTable = ` FROM ${this.sdk.getTable()}`;
    this.query = "";
    this.limitClause = "";
    this.selectClause = "SELECT * ";
    this.whereConditions = [];
    this.orWhereConditions = [];
    this.orderByConditions = [];
    this.joins = [];
    this.validOperators = [
      "=",
      ">",
      "<",
      ">=",
      "<=",
      "<>",
      "!=",
      "LIKE",
      "NOT LIKE",
    ];
  }

  quoteValue(value) {
    if (value === null || value === undefined) {
      return "NULL";
    }
    if (typeof value === "number") {
      return value;
    }
    if (typeof value === "boolean") {
      return value ? 1 : 0;
    }
    // For strings, escape single quotes and wrap in quotes
    return `'${String(value).replace(/'/g, "''")}'`;
  }

  select(select = "*") {
    let columns = "";
    if (typeof select === "object") {
      columns += ` ${this.table}.* `;
      for (let key in select) {
        columns += `, ${key} AS ${select[key]} `;
      }
      select = columns;
    }
    this.selectClause = `SELECT ${select}`;
    return this;
  }

  selectDistinct(select = "*") {
    let columns = "";
    if (typeof select === "object") {
      for (let key in select) {
        columns += `${key} AS ${select[key]}`;
      }
      select = columns;
    }
    this.selectClause = `SELECT DISTINCT ${select} `;
    return this;
  }

  selectCount(select = "*", alias = "count") {
    this.selectClause = `SELECT COUNT( ${select} ) AS count `;
    return this;
  }

  from(table = undefined) {
    let prefix = this.sdk.getProjectId();
    if (typeof table == "undefined") table = this.table;
    this.fromTable = ` FROM ${prefix}_${table} `;

    return this;
  }

  where(...args) {
    if (args.length === 2) {
      const value = this.quoteValue(args[1]);
      this.whereConditions.push(` ${args[0]} = ${value}`);
    } else if (args.length === 3 && this.validOperators.includes(args[1])) {
      const value = this.quoteValue(args[2]);
      this.whereConditions.push(` ${args[0]} ${args[1]} ${value}`);
    }

    return this;
  }

  orWhere(...args) {
    if (args.length === 2) {
      const value = this.quoteValue(args[1]);
      this.orWhereConditions.push(` ${args[0]} = ${value}`);
    } else if (args.length === 3 && this.validOperators.includes(args[1])) {
      const value = this.quoteValue(args[2]);
      this.orWhereConditions.push(` ${args[0]} ${args[1]} ${value}`);
    }

    return this;
  }

  whereNot(...args) {
    if (args.length === 2) {
      const value = this.quoteValue(args[1]);
      this.whereConditions.push(` NOT ${args[0]} = ${value} `);
    } else if (args.length === 3 && this.validOperators.includes(args[1])) {
      const value = this.quoteValue(args[2]);
      this.whereConditions.push(` NOT ${args[0]} ${args[1]} ${value} `);
    }

    return this;
  }

  whereIn(column = undefined, items = []) {
    if (typeof column == "undefined" || items.length === 0) return this;

    this.whereConditions.push(` ${column} IN ( ${items.join(",")} ) `);

    return this;
  }

  whereNotIn(column = undefined, items = []) {
    if (typeof column == "undefined" || items.length === 0) return this;

    this.whereConditions.push(` ${column} NOT IN ( ${items.join(",")} ) `);

    return this;
  }

  whereBetween(column, begin, end) {
    if (arguments.length !== 3) return this;
    this.whereConditions.push(` ${column} BETWEEN ${begin} AND ${end} `);

    return this;
  }

  whereNotBetween(column, begin, end) {
    if (arguments.length !== 3) return this;
    this.whereConditions.push(` ${column} NOT BETWEEN ${begin} AND ${end} `);

    return this;
  }

  whereStartsWith(column, subString = "") {
    this.whereConditions.push(` ${column} LIKE '${subString}%'`);
    return this;
  }

  whereNotStartsWith(column, subString = "") {
    this.whereConditions.push(` ${column} NOT LIKE '${subString}%'`);
    return this;
  }

  whereEndsWith(column, subString = "") {
    this.whereConditions.push(` ${column} LIKE '%${subString}'`);
    return this;
  }

  whereNotEndsWith(column, subString = "") {
    this.whereConditions.push(` ${column} NOT LIKE '%${subString}'`);
    return this;
  }

  whereContains(column, subString = "") {
    this.whereConditions.push(` ${column} LIKE '%${subString}%'`);
    return this;
  }

  whereNotContains(column, subString = "") {
    this.whereConditions.push(` ${column} NOT LIKE '%${subString}%'`);
    return this;
  }

  whereNull(column) {
    this.whereConditions.push(` ${column} IS NULL `);
    return this;
  }

  whereNotNull(column) {
    this.whereConditions.push(` ${column} IS NOT NULL `);
    return this;
  }

  limit(...args) {
    if (args.length === 1) {
      this.limitClause = ` LIMIT ${args[0]} `;
    } else if (arguments.length == 2) {
      //Offset, Limit
      this.limitClause = ` LIMIT ${args[1]}, ${args[0]} `;
    }
    return this;
  }

  orderBy(column, direction = "") {
    this.orderByConditions.push(` ${column} ${direction} `);
    return this;
  }

  join(subject, relation, subjectColumn, relationColumn, type = "left") {
    switch (type.toLowerCase()) {
      case "left":
        return this.leftJoin(subject, relation, subjectColumn, relationColumn);
      case "right":
        return this.rightJoin(subject, relation, subjectColumn, relationColumn);
      case "inner":
        return this.innerJoin(subject, relation, subjectColumn, relationColumn);
      default:
        return this.leftJoin(subject, relation, subjectColumn, relationColumn);
    }
  }

  leftJoin(subject, relation, subjectColumn, relationColumn) {
    this.joins.push(
      ` LEFT JOIN ${relation} ON ${relation}.${relationColumn} = ${subject}.${subjectColumn} `
    );
    return this;
  }

  rightJoin(subject, relation, subjectColumn, relationColumn) {
    this.joins.push(
      ` RIGHT JOIN ${relation} ON ${relation}.${relationColumn} = ${subject}.${subjectColumn} `
    );
    return this;
  }

  innerJoin(subject, relation, subjectColumn, relationColumn) {
    this.joins.push(
      ` INNER JOIN ${relation} ON ${relation}.${relationColumn} = ${subject}.${subjectColumn} `
    );
    return this;
  }

  joinRaw(statement) {
    this.joins.push(statement);
    return this;
  }

  get(...args) {
    if (args.length > 0) this.limitClause = ` LIMIT ${args[0]} `;

    this.query = `
          ${this.selectClause}
          ${this.fromTable}
          ${this.joins.join("  ")}
          ${this.generateWhereConditions()}
          ${this.generateOrderByConditions()}
          ${this.limitClause}
      `;
    this.flush();
    return this.query;
  }

  count(...args) {
    if (args.length > 0) this.limitClause = ` LIMIT ${args[0]} `;

    this.query = `
          SELECT COUNT(*) AS count
          ${this.fromTable}
          ${this.joins.join("  ")}
          ${this.generateWhereConditions()}
          ${this.generateOrderByConditions()}
          ${this.limitClause}
      `;
    this.flush();
    return this.query;
  }

  generateWhereConditions() {
    let where = " ";

    this.whereConditions.forEach((condition) => {
      if (where.toLowerCase().includes("where")) {
        where += ` AND ${condition} `;
      } else {
        where += ` WHERE ${condition} `;
      }
    });

    //if (this.whereConditions.length == 0) this.orWhereConditions = []; // clear OR conditions if no where condition

    this.orWhereConditions.forEach((condition) => {
      if (where.toLowerCase().includes("where")) {
        where += ` OR ${condition} `;
      } else {
        where += ` WHERE ${condition} `;
      }
    });

    return where;
  }

  generateOrderByConditions() {
    let order = " ";

    this.orderByConditions.forEach((condition) => {
      if (order.toLowerCase().includes("order")) {
        order += ` ,${condition} `;
      } else {
        order += ` ORDER BY ${condition} `;
      }
    });

    return order;
  }

  flush() {
    this.limitClause = "";
    this.selectClause = "SELECT * ";
    this.whereConditions = [];
    this.orWhereConditions = [];
    this.orderByConditions = [];
  }
};
