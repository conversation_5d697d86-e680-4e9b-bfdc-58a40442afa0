class DatabaseService {
    /**
     * Create a DatabaseService instance
     * @param {object} dbConnector - A database connector with CRUD methods
     * @throws {Error} If no database connector is provided
     */
    constructor(dbConnector) {
        if (!dbConnector) {
            throw new Error('Database connector is required');
        }
        this.db = dbConnector;
    }

    /**
     * Create a new record in the specified collection
     * @param {string} collection - Name of the database collection/table
     * @param {object} data - Data object to be inserted
     * @returns {Promise<object>} The created record, including its assigned ID
     * @throws {Error} If record creation fails
     */
    async create(collection, data) {
        try {
            return await this.db.create(collection, data);
        } catch (error) {
            throw new Error(`Failed to create record: ${error.message}`);
        }
    }

    /**
     * Find records matching the specified query
     * @param {string} collection - Name of the database collection/table
     * @param {object} [query={}] - Query conditions to filter records
     * @param {object} [options={}] - Optional parameters for pagination, sorting
     * @param {number} [options.limit] - Maximum number of records to return
     * @param {number} [options.offset] - Number of records to skip
     * @param {object} [options.sort] - Sorting configuration (e.g., {field: 1 or -1})
     * @returns {Promise<Array>} Array of records matching the query
     * @throws {Error} If record search fails
     */
    async find(collection, query = {}, options = {}) {
        try {
            return await this.db.find(collection, query, options);
        } catch (error) {
            throw new Error(`Failed to find records: ${error.message}`);
        }
    }

    /**
     * Retrieve a single record by its unique identifier
     * @param {string} collection - Name of the database collection/table
     * @param {string|number} id - Unique identifier of the record
     * @returns {Promise<object|null>} The found record or null if not found
     * @throws {Error} If record retrieval fails
     */
    async findById(collection, id) {
        try {
            return await this.db.findById(collection, id);
        } catch (error) {
            throw new Error(`Failed to find record by ID: ${error.message}`);
        }
    }

    /**
     * Find the first record matching the specified query
     * @param {string} collection - Name of the database collection/table
     * @param {object} query - Query conditions to find the record
     * @returns {Promise<object|null>} The first matching record or null
     * @throws {Error} If record search fails
     */
    async findOne(collection, query) {
        try {
            return await this.db.findOne(collection, query);
        } catch (error) {
            throw new Error(`Failed to find record: ${error.message}`);
        }
    }

    /**
     * Update records matching the specified query
     * @param {string} collection - Name of the database collection/table
     * @param {object} query - Query conditions to select records for update
     * @param {object} update - Update data to apply to matching records
     * @returns {Promise<object>} Update operation result with affected rows
     * @throws {Error} If record update fails
     */
    async update(collection, query, update) {
        try {
            return await this.db.update(collection, query, update);
        } catch (error) {
            throw new Error(`Failed to update records: ${error.message}`);
        }
    }

    /**
     * Update a single record by its unique identifier
     * @param {string} collection - Name of the database collection/table
     * @param {string|number} id - Unique identifier of the record to update
     * @param {object} update - Update data to apply to the record
     * @returns {Promise<object>} The updated record
     * @throws {Error} If record update fails
     */
    async updateById(collection, id, update) {
        try {
            return await this.db.updateById(collection, id, update);
        } catch (error) {
            throw new Error(`Failed to update record by ID: ${error.message}`);
        }
    }

    /**
     * Delete records matching the specified query
     * @param {string} collection - Name of the database collection/table
     * @param {object} query - Query conditions to select records for deletion
     * @returns {Promise<object>} Deletion operation result with affected rows
     * @throws {Error} If record deletion fails
     */
    async delete(collection, query) {
        try {
            return await this.db.delete(collection, query);
        } catch (error) {
            throw new Error(`Failed to delete records: ${error.message}`);
        }
    }

    /**
     * Delete a single record by its unique identifier
     * @param {string} collection - Name of the database collection/table
     * @param {string|number} id - Unique identifier of the record to delete
     * @returns {Promise<object>} Deletion operation result
     * @throws {Error} If record deletion fails
     */
    async deleteById(collection, id) {
        try {
            return await this.db.deleteById(collection, id);
        } catch (error) {
            throw new Error(`Failed to delete record by ID: ${error.message}`);
        }
    }

    /**
     * Count the number of records matching the specified query
     * @param {string} collection - Name of the database collection/table
     * @param {object} [query={}] - Query conditions to filter records
     * @returns {Promise<number>} Total number of matching records
     * @throws {Error} If record counting fails
     */
    async count(collection, query = {}) {
        try {
            return await this.db.count(collection, query);
        } catch (error) {
            throw new Error(`Failed to count records: ${error.message}`);
        }
    }

    /**
     * Execute a raw database query with optional parameterization
     * @param {string} query - Raw SQL or database-specific query string
     * @param {Array} [params=[]] - Optional parameters to safely inject into the query
     * @returns {Promise<any>} Query execution result
     * @throws {Error} If query execution fails
     */
    async rawQuery(query, params = []) {
        try {
            // Ensure params is always an array
            const parameters = Array.isArray(params) ? params : [params];
            return await this.db.rawQuery(query, parameters);
        } catch (error) {
            throw new Error(`Failed to execute raw query: ${error.message}`);
        }
    }

    /**
     * Sanitize input to prevent injection and ensure data safety
     * @param {*} input - Input value to sanitize (string, number, object, etc.)
     * @returns {*} Sanitized input value
     */
    sanitize(input) {
        if (input === null || input === undefined) {
            return null;
        }

        if (typeof input === 'object') {
            const sanitizedObj = {};
            for (const key in input) {
                if (Object.prototype.hasOwnProperty.call(input, key)) {
                    sanitizedObj[this.sanitize(key)] = this.sanitize(input[key]);
                }
            }
            return sanitizedObj;
        }

        if (typeof input === 'string') {
            // Remove common SQL injection patterns
            return input
                .replace(/[\0\x08\x09\x1a\n\r"'\\\%]/g, char => {
                    switch (char) {
                        case "\0":
                            return "\\0";
                        case "\x08":
                            return "\\b";
                        case "\x09":
                            return "\\t";
                        case "\x1a":
                            return "\\z";
                        case "\n":
                            return "\\n";
                        case "\r":
                            return "\\r";
                        case "\"":
                        case "'":
                        case "\\":
                        case "%":
                            return "\\"+char; // prepends a backslash to backslash, percent, and double/single quotes
                        default:
                            return char;
                    }
                });
        }

        // Return numbers and booleans as-is
        return input;
    }

    /**
     * Prepare a query object by recursively sanitizing all values
     * @param {object} queryObject - Query object to sanitize
     * @returns {object} Sanitized query object with safe values
     */
    prepareSafeQuery(queryObject) {
        return this.sanitize(queryObject);
    }
}

module.exports = DatabaseService;
