const fs = require('fs').promises;
const path = require('path');

class JSONConnector {
    constructor(config) {
        this.config = config;
        this.data = {};
        this.dbPath = path.join(process.cwd(), config.filename || 'db.json');
    }

    async initialize() {
        try {
            // Try to read existing database file
            try {
                const fileContent = await fs.readFile(this.dbPath, 'utf8');
                this.data = JSON.parse(fileContent);
            } catch (error) {
                if (error.code === 'ENOENT') {
                    // File doesn't exist, create new one
                    this.data = {};
                    await this.saveData();
                } else {
                    throw error;
                }
            }
        } catch (error) {
            throw new Error(`Failed to initialize JSON database: ${error.message}`);
        }
    }

    async saveData() {
        try {
            await fs.writeFile(this.dbPath, JSON.stringify(this.data, null, 2));
        } catch (error) {
            throw new Error(`Failed to save JSON database: ${error.message}`);
        }
    }

    ensureCollection(collection) {
        if (!this.data[collection]) {
            this.data[collection] = [];
        }
    }

    generateId() {
        return Date.now() + Math.random().toString(36).substr(2, 9);
    }

    async create(collection, data) {
        try {
            this.ensureCollection(collection);
            const newRecord = {
                id: this.generateId(),
                ...data,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            
            this.data[collection].push(newRecord);
            await this.saveData();
            return newRecord;
        } catch (error) {
            throw new Error(`JSON Create Error: ${error.message}`);
        }
    }

    async find(collection, query = {}, options = {}) {
        try {
            this.ensureCollection(collection);
            let results = this.data[collection].filter(item => 
                Object.entries(query).every(([key, value]) => item[key] === value)
            );

            // Handle sorting
            if (options.sort) {
                results = results.sort((a, b) => {
                    for (const [field, order] of Object.entries(options.sort)) {
                        if (a[field] < b[field]) return order === -1 ? 1 : -1;
                        if (a[field] > b[field]) return order === -1 ? -1 : 1;
                    }
                    return 0;
                });
            }

            // Handle pagination
            if (options.limit) {
                const offset = options.offset || 0;
                results = results.slice(offset, offset + options.limit);
            }

            return results;
        } catch (error) {
            throw new Error(`JSON Find Error: ${error.message}`);
        }
    }

    async findById(collection, id) {
        try {
            this.ensureCollection(collection);
            return this.data[collection].find(item => item.id === id) || null;
        } catch (error) {
            throw new Error(`JSON FindById Error: ${error.message}`);
        }
    }

    async findOne(collection, query) {
        try {
            this.ensureCollection(collection);
            return this.data[collection].find(item =>
                Object.entries(query).every(([key, value]) => item[key] === value)
            ) || null;
        } catch (error) {
            throw new Error(`JSON FindOne Error: ${error.message}`);
        }
    }

    async update(collection, query, update) {
        try {
            this.ensureCollection(collection);
            let affected = 0;
            const records = [];

            this.data[collection] = this.data[collection].map(item => {
                if (Object.entries(query).every(([key, value]) => item[key] === value)) {
                    affected++;
                    const updatedItem = {
                        ...item,
                        ...update,
                        updatedAt: new Date().toISOString()
                    };
                    records.push(updatedItem);
                    return updatedItem;
                }
                return item;
            });

            await this.saveData();
            return {
                affected,
                changed: affected,
                records
            };
        } catch (error) {
            throw new Error(`JSON Update Error: ${error.message}`);
        }
    }


    async joinPaginate({
        primaryCollection,
        secondaryCollection,
        joinId1,
        joinId2,
        query = {},
        limit = 10,
        offset = 0
    }) {
        this.ensureCollection(primaryCollection);
        this.ensureCollection(secondaryCollection);

        // Filter primary collection based on query
        let primaryData = this.data[primaryCollection].filter(item =>
            Object.entries(query).every(([key, value]) => item[key] === value)
        );

        // Perform the join
        const joinedData = primaryData.map(primaryItem => {
            const secondaryItems = this.data[secondaryCollection].filter(
                secondaryItem => primaryItem[joinId1] === secondaryItem[joinId2]
            );
            return { ...primaryItem, related: secondaryItems };
        });

        // Paginate the results
        const paginatedData = joinedData.slice(offset, offset + limit);

        return {
            data: paginatedData,
            total: joinedData.length,
            limit,
            offset
        };
    }

    async updateById(collection, id, update) {
        try {
            this.ensureCollection(collection);
            const index = this.data[collection].findIndex(item => item.id === id);
            
            if (index === -1) {
                return null;
            }

            const updatedItem = {
                ...this.data[collection][index],
                ...update,
                updatedAt: new Date().toISOString()
            };

            this.data[collection][index] = updatedItem;
            await this.saveData();
            return updatedItem;
        } catch (error) {
            throw new Error(`JSON UpdateById Error: ${error.message}`);
        }
    }

    async delete(collection, query) {
        try {
            this.ensureCollection(collection);
            const initialLength = this.data[collection].length;
            
            this.data[collection] = this.data[collection].filter(item =>
                !Object.entries(query).every(([key, value]) => item[key] === value)
            );

            const affected = initialLength - this.data[collection].length;
            await this.saveData();
            return { affected };
        } catch (error) {
            throw new Error(`JSON Delete Error: ${error.message}`);
        }
    }

    async deleteById(collection, id) {
        try {
            this.ensureCollection(collection);
            const initialLength = this.data[collection].length;
            
            this.data[collection] = this.data[collection].filter(item => item.id !== id);
            
            const affected = initialLength - this.data[collection].length;
            await this.saveData();
            return { affected };
        } catch (error) {
            throw new Error(`JSON DeleteById Error: ${error.message}`);
        }
    }

    async count(collection, query = {}) {
        try {
            this.ensureCollection(collection);
            return this.data[collection].filter(item =>
                Object.entries(query).every(([key, value]) => item[key] === value)
            ).length;
        } catch (error) {
            throw new Error(`JSON Count Error: ${error.message}`);
        }
    }

    async rawQuery(query, params = []) {
        throw new Error('Raw queries are not supported in JSON database');
    }

    async close() {
        try {
            await this.saveData();
        } catch (error) {
            throw new Error(`Failed to close JSON database: ${error.message}`);
        }
    }
}

module.exports = JSONConnector;
