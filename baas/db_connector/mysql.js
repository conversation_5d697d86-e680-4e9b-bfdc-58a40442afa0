const mysql = require("mysql2/promise");

class MySQLConnector {
  constructor(config) {
    this.config = config;
    this.pool = null;
  }

  async initialize() {
    try {
      this.pool = mysql.createPool({
        host: this.config.host,
        user: this.config.user,
        password: this.config.password,
        database: this.config.database,
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0,
      });

      await this.pool.query("SELECT 1");
    } catch (error) {
      throw new Error(
        `Failed to initialize MySQL connection: ${error.message}`
      );
    }
  }

  async create(collection, data) {
    try {
      const [result] = await this.pool.query(
        `INSERT INTO ${collection} SET ?`,
        [data]
      );
      if (result.insertId) {
        return { ...data, id: result.insertId };
      }
      return data;
    } catch (error) {
      throw new Error(`MySQL Create Error: ${error.message}`);
    }
  }

  async find(collection, query = {}, options = {}) {
    try {
      let sql = `SELECT * FROM ${collection}`;
      const whereConditions = [];
      const params = [];

      // Build WHERE clause
      Object.entries(query).forEach(([key, value]) => {
        whereConditions.push(`${key} = ?`);
        params.push(value);
      });

      if (whereConditions.length > 0) {
        sql += ` WHERE ${whereConditions.join(" AND ")}`;
      }

      // Handle pagination
      if (options.limit) {
        sql += ` LIMIT ?`;
        params.push(parseInt(options.limit));

        if (options.offset) {
          sql += ` OFFSET ?`;
          params.push(parseInt(options.offset));
        }
      }

      // Handle sorting
      if (options.sort) {
        const sortFields = Object.entries(options.sort).map(
          ([field, order]) => `${field} ${order === -1 ? "DESC" : "ASC"}`
        );
        if (sortFields.length > 0) {
          sql += ` ORDER BY ${sortFields.join(", ")}`;
        }
      }

      const [rows] = await this.pool.query(sql, params);
      return rows;
    } catch (error) {
      throw new Error(`MySQL Find Error: ${error.message}`);
    }
  }

  async findById(collection, id) {
    try {
      const [rows] = await this.pool.query(
        `SELECT * FROM ${collection} WHERE id = ?`,
        [id]
      );
      return rows[0] || null;
    } catch (error) {
      throw new Error(`MySQL FindById Error: ${error.message}`);
    }
  }

  async findOne(collection, query) {
    try {
      const whereConditions = [];
      const params = [];

      Object.entries(query).forEach(([key, value]) => {
        whereConditions.push(`\`${key}\` = ?`);
        params.push(value);
      });

      const sql = `SELECT * FROM ${collection}${
        whereConditions.length ? ` WHERE ${whereConditions.join(" AND ")}` : ""
      } LIMIT 1`;

      console.log(sql);

      const [rows] = await this.pool.query(sql, params);
      return rows[0] || null;
    } catch (error) {
      throw new Error(`MySQL FindOne Error: ${error.message}`);
    }
  }

  async update(collection, query, update) {
    try {
      const whereConditions = [];
      const params = [];

      const updateFields = Object.entries(update).map(([key, value]) => {
        params.push(value);
        return `${key} = ?`;
      });

      Object.entries(query).forEach(([key, value]) => {
        whereConditions.push(`${key} = ?`);
        params.push(value);
      });


      const sql = `UPDATE ${collection} SET ${updateFields.join(", ")}${
        whereConditions.length ? ` WHERE ${whereConditions.join(" AND ")}` : ""
      }`;


      const [result] = await this.pool.query(sql, params);

      return {
        affected: result.affectedRows,
        changed: result.changedRows,
      };
    } catch (error) {
      throw new Error(`MySQL Update Error: ${error.message}`);
    }
  }

  async updateById(collection, id, update) {
    try {
      const params = [];
      const updateFields = Object.entries(update).map(([key, value]) => {
        params.push(value);
        return `${key} = ?`;
      });

      params.push(id);
      const sql = `UPDATE ${collection} SET ${updateFields.join(
        ", "
      )} WHERE id = ?`;

      const [result] = await this.pool.query(sql, params);
      if (result.affectedRows === 0) {
        return null;
      }
      return { id, ...update };
    } catch (error) {
      throw new Error(`MySQL UpdateById Error: ${error.message}`);
    }
  }

  async delete(collection, query) {
    try {
      const whereConditions = [];
      const params = [];

      Object.entries(query).forEach(([key, value]) => {
        whereConditions.push(`${key} = ?`);
        params.push(value);
      });

      const sql = `DELETE FROM ${collection}${
        whereConditions.length ? ` WHERE ${whereConditions.join(" AND ")}` : ""
      }`;

      const [result] = await this.pool.query(sql, params);
      return {
        affected: result.affectedRows,
      };
    } catch (error) {
      throw new Error(`MySQL Delete Error: ${error.message}`);
    }
  }

  async deleteById(collection, id) {
    try {
      const [result] = await this.pool.query(
        `DELETE FROM ${collection} WHERE id = ?`,
        [id]
      );
      return {
        affected: result.affectedRows,
      };
    } catch (error) {
      throw new Error(`MySQL DeleteById Error: ${error.message}`);
    }
  }

  async count(collection, query = {}) {
    try {
      const whereConditions = [];
      const params = [];

      Object.entries(query).forEach(([key, value]) => {
        whereConditions.push(`${key} = ?`);
        params.push(value);
      });

      const sql = `SELECT COUNT(*) as count FROM ${collection}${
        whereConditions.length ? ` WHERE ${whereConditions.join(" AND ")}` : ""
      }`;

      const [rows] = await this.pool.query(sql, params);
      return rows[0].count;
    } catch (error) {
      throw new Error(`MySQL Count Error: ${error.message}`);
    }
  }

  async rawQuery(query, params = []) {
    try {
      const [result] = await this.pool.query(query, params);
      return result;
    } catch (error) {
      throw new Error(`MySQL Raw Query Error: ${error.message}`);
    }
  }

  async joinPaginate(
    table1,
    table2,
    joinId1,
    joinId2,
    selectStr = "*",
    where = {},
    page = 0,
    limit = 10,
    sortField = null,
    sortDirection = "ASC"
  ) {
    try {
      const offset = page * limit;

      let sql = `SELECT ${selectStr} FROM ${table1} INNER JOIN ${table2} ON ${table1}.${joinId1} = ${table2}.${joinId2}`;

      const whereConditions = [];
      const params = [];

      Object.entries(where).forEach(([key, value]) => {
        whereConditions.push(`${key} = ?`);
        params.push(value);
      });

      if (whereConditions.length > 0) {
        sql += ` WHERE ${whereConditions.join(" AND ")}`;
      }

      if (sortField) {
        sql += ` ORDER BY ${sortField} ${sortDirection}`;
      }

      sql += ` LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      console.log(sql);

      const [rows] = await this.pool.query(sql, params);
      return rows;
    } catch (error) {
      throw new Error(`MySQL JoinPaginate Error: ${error.message}`);
    }
  }

  async paginateStr(collection, where = {}, selectStr = "*", page = 1, limit = 10, order = null, direction = 'ASC') {
    try {
      let sql = `SELECT ${selectStr} FROM ${collection}`;
      const whereConditions = [];
      const params = [];

      // Build WHERE clause
      Object.entries(where).forEach(([key, value]) => {
        whereConditions.push(`${key} = ?`);
        params.push(value);
      });

      if (whereConditions.length > 0) {
        sql += ` WHERE ${whereConditions.join(" AND ")}`;
      }

      // Add ORDER BY if specified
      if (order) {
        sql += ` ORDER BY ${order} ${direction}`;
      }

      // Calculate offset and add pagination
      const offset = (page - 1) * limit;
      sql += ` LIMIT ? OFFSET ?`;
      params.push(parseInt(limit), parseInt(offset));

      console.log(sql, params);

      const [rows] = await this.pool.query(sql, params);
      return rows.length > 0 ? rows : [];
    } catch (error) {
      throw new Error(`MySQL PaginateStr Error: ${error.message}`);
    }
  }

  async close() {
    try {
      await this.pool.end();
    } catch (error) {
      throw new Error(`Failed to close MySQL connection: ${error.message}`);
    }
  }
}

module.exports = MySQLConnector;
