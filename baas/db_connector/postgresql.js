const { Pool } = require('pg');

class PostgreSQLConnector {
    constructor(config) {
        this.config = config;
        this.pool = null;
    }

    async initialize() {
        try {
            this.pool = new Pool({
                host: this.config.host,
                user: this.config.user,
                password: this.config.password,
                database: this.config.database,
                port: this.config.port || 5432,
                max: 10, // connection pool max size
                idleTimeoutMillis: 30000
            });
        } catch (error) {
            throw new Error(`Failed to initialize PostgreSQL connection: ${error.message}`);
        }
    }

    async create(collection, data) {
        try {
            const columns = Object.keys(data);
            const values = Object.values(data);
            const placeholders = values.map((_, index) => `$${index + 1}`);

            const sql = `
                INSERT INTO ${collection} (${columns.join(', ')})
                VALUES (${placeholders.join(', ')})
                RETURNING *
            `;

            const { rows } = await this.pool.query(sql, values);
            return rows[0];
        } catch (error) {
            throw new Error(`PostgreSQL Create Error: ${error.message}`);
        }
    }

    async find(collection, query = {}, options = {}) {
        try {
            const whereConditions = [];
            const params = [];
            let paramCount = 1;

            // Build WHERE clause
            Object.entries(query).forEach(([key, value]) => {
                whereConditions.push(`${key} = $${paramCount}`);
                params.push(value);
                paramCount++;
            });

            let sql = `SELECT * FROM ${collection}`;
            if (whereConditions.length > 0) {
                sql += ` WHERE ${whereConditions.join(' AND ')}`;
            }

            // Handle sorting
            if (options.sort) {
                const sortFields = Object.entries(options.sort)
                    .map(([field, order]) => `${field} ${order === -1 ? 'DESC' : 'ASC'}`);
                if (sortFields.length > 0) {
                    sql += ` ORDER BY ${sortFields.join(', ')}`;
                }
            }

            // Handle pagination
            if (options.limit) {
                sql += ` LIMIT $${paramCount}`;
                params.push(parseInt(options.limit));
                paramCount++;

                if (options.offset) {
                    sql += ` OFFSET $${paramCount}`;
                    params.push(parseInt(options.offset));
                }
            }

            const { rows } = await this.pool.query(sql, params);
            return rows;
        } catch (error) {
            throw new Error(`PostgreSQL Find Error: ${error.message}`);
        }
    }

    async joinPaginate({
        table1,
        table2,
        joinId1,
        joinId2,
        query = {},
        limit = 10,
        offset = 0
    }) {
        const client = await this.pool.connect();
        try {
            // Build query filters
            const whereClauses = Object.entries(query)
                .map(([key, value], index) => `${key} = $${index + 1}`)
                .join(' AND ');

            const params = Object.values(query);

            // Construct SQL query
            const sql = `
                SELECT *
                FROM ${table1}
                LEFT JOIN ${table2}
                ON ${table1}.${joinId1} = ${table2}.${joinId2}
                ${whereClauses ? `WHERE ${whereClauses}` : ''}
                LIMIT $${params.length + 1}
                OFFSET $${params.length + 2};
            `;

            const result = await client.query(sql, [...params, limit, offset]);

            // Count total rows (ignoring LIMIT/OFFSET)
            const countSql = `
                SELECT COUNT(*)
                FROM ${table1}
                LEFT JOIN ${table2}
                ON ${table1}.${joinId1} = ${table2}.${joinId2}
                ${whereClauses ? `WHERE ${whereClauses}` : ''};
            `;
            const countResult = await client.query(countSql, params);

            return {
                data: result.rows,
                total: parseInt(countResult.rows[0].count, 10),
                limit,
                offset
            };
        } finally {
            client.release();
        }
    }

    async findById(collection, id) {
        try {
            const sql = `SELECT * FROM ${collection} WHERE id = $1`;
            const { rows } = await this.pool.query(sql, [id]);
            return rows[0] || null;
        } catch (error) {
            throw new Error(`PostgreSQL FindById Error: ${error.message}`);
        }
    }

    async findOne(collection, query) {
        try {
            const whereConditions = [];
            const params = [];
            let paramCount = 1;

            Object.entries(query).forEach(([key, value]) => {
                whereConditions.push(`${key} = $${paramCount}`);
                params.push(value);
                paramCount++;
            });

            const sql = `
                SELECT * FROM ${collection}
                ${whereConditions.length ? ` WHERE ${whereConditions.join(' AND ')}` : ''}
                LIMIT 1
            `;

            const { rows } = await this.pool.query(sql, params);
            return rows[0] || null;
        } catch (error) {
            throw new Error(`PostgreSQL FindOne Error: ${error.message}`);
        }
    }

    async update(collection, query, update) {
        try {
            const whereConditions = [];
            const setValues = [];
            const params = [];
            let paramCount = 1;

            // Build SET clause
            Object.entries(update).forEach(([key, value]) => {
                setValues.push(`${key} = $${paramCount}`);
                params.push(value);
                paramCount++;
            });

            // Build WHERE clause
            Object.entries(query).forEach(([key, value]) => {
                whereConditions.push(`${key} = $${paramCount}`);
                params.push(value);
                paramCount++;
            });

            const sql = `
                UPDATE ${collection}
                SET ${setValues.join(', ')}
                ${whereConditions.length ? ` WHERE ${whereConditions.join(' AND ')}` : ''}
                RETURNING *
            `;

            const { rowCount, rows } = await this.pool.query(sql, params);
            return {
                affected: rowCount,
                changed: rowCount,
                records: rows
            };
        } catch (error) {
            throw new Error(`PostgreSQL Update Error: ${error.message}`);
        }
    }

    async updateById(collection, id, update) {
        try {
            const setValues = [];
            const params = [id];
            let paramCount = 2;

            Object.entries(update).forEach(([key, value]) => {
                setValues.push(`${key} = $${paramCount}`);
                params.push(value);
                paramCount++;
            });

            const sql = `
                UPDATE ${collection}
                SET ${setValues.join(', ')}
                WHERE id = $1
                RETURNING *
            `;

            const { rows } = await this.pool.query(sql, params);
            return rows[0] || null;
        } catch (error) {
            throw new Error(`PostgreSQL UpdateById Error: ${error.message}`);
        }
    }

    async delete(collection, query) {
        try {
            const whereConditions = [];
            const params = [];
            let paramCount = 1;

            Object.entries(query).forEach(([key, value]) => {
                whereConditions.push(`${key} = $${paramCount}`);
                params.push(value);
                paramCount++;
            });

            const sql = `
                DELETE FROM ${collection}
                ${whereConditions.length ? ` WHERE ${whereConditions.join(' AND ')}` : ''}
                RETURNING *
            `;

            const { rowCount } = await this.pool.query(sql, params);
            return {
                affected: rowCount
            };
        } catch (error) {
            throw new Error(`PostgreSQL Delete Error: ${error.message}`);
        }
    }

    async deleteById(collection, id) {
        try {
            const sql = `
                DELETE FROM ${collection}
                WHERE id = $1
                RETURNING *
            `;

            const { rowCount } = await this.pool.query(sql, [id]);
            return {
                affected: rowCount
            };
        } catch (error) {
            throw new Error(`PostgreSQL DeleteById Error: ${error.message}`);
        }
    }

    async count(collection, query = {}) {
        try {
            const whereConditions = [];
            const params = [];
            let paramCount = 1;

            Object.entries(query).forEach(([key, value]) => {
                whereConditions.push(`${key} = $${paramCount}`);
                params.push(value);
                paramCount++;
            });

            const sql = `
                SELECT COUNT(*) as count
                FROM ${collection}
                ${whereConditions.length ? ` WHERE ${whereConditions.join(' AND ')}` : ''}
            `;

            const { rows } = await this.pool.query(sql, params);
            return parseInt(rows[0].count);
        } catch (error) {
            throw new Error(`PostgreSQL Count Error: ${error.message}`);
        }
    }

    async rawQuery(query, params = []) {
        try {
            const { rows } = await this.pool.query(query, params);
            return rows;
        } catch (error) {
            throw new Error(`PostgreSQL Raw Query Error: ${error.message}`);
        }
    }

    async close() {
        try {
            await this.pool.end();
        } catch (error) {
            throw new Error(`Failed to close PostgreSQL connection: ${error.message}`);
        }
    }
}

module.exports = PostgreSQLConnector;
