const AuthService = require("../services/AuthService");
const JwtService = require("../services/JwtService");
const axios = require("axios");
const fs = require("fs");
const path = require("path");

module.exports = function (app) {
  app.get("/v2/api/lambda/linkedin/code", async function (req, res) {
    const state = JSON.parse(req.query.state);
    let hostname = state.hostname;
    // console.log(req.query);
    // console.log("\n\nstate",state);
    // console.log("\n\nscope",req.query.scope);
    // console.log("\n\nerror",req.query.error);
    // console.log("\n\nerror_description",req.query.error_description);

    try {
      const base64DecodeBuffer = Buffer.from(state.project_id, "base64");
      const config = app.get("configuration");

      let base64Decode = base64DecodeBuffer.toString().split(":");
      const projectId = base64Decode[0];
      const role = state.role;
      let needRefreshToken = false;
      let refreshToken = undefined;

      if (state.is_refresh) {
        needRefreshToken = true;
      }

      const database = base64Decode[2] ?? config.databaseName;

      let sdk = app.get("sdk");
      sdk.setProjectId(projectId);

      let manaknightSDK = app.get("sdk");
      manaknightSDK.getDatabase();
      manaknightSDK.setProjectId(projectId);

      let originalUrl = req.originalUrl;

      // LinkedIn OAuth confi||

      const linkedinConfig = {
        clientID: config.linkedin.client_id,
        clientSecret: config.linkedin.client_secret,
        redirectURI: "http://localhost:5172/v2/api/lambda/linkedin/code",
        scopes: ["openid", "profile", "email"],
      };

      console.log("\n\nlinkedinConfig", linkedinConfig);

      // **Injection Point Start:** Attempt to load and run custom code
      const injectionPath = path.join(__dirname, "..", "..", "custom", `${projectId}_backend`, "social_callback.js"); // Path to the injection file
      try {
        if (fs.existsSync(injectionPath)) {
          const { linkedin_callback } = require(injectionPath);

          await linkedin_callback({
            req,
            res,
            project: { hostname },
            state,
            projectId,
            role,
            needRefreshToken,
            refreshToken,
            database,
            sdk,
            manaknightSDK,
            originalUrl,
            config,
            linkedinConfig
          });
          if (res.headersSent) return;
        }

      } catch (injectError) {
        console.error("Error during injection:", injectError);
      }
      // **Injection Point End:**

      // Exchange the authorization code for an access token
      let tokenResponse;
      try {
        tokenResponse = await axios.post(
          "https://www.linkedin.com/oauth/v2/accessToken",
          null,
          {
            params: {
              grant_type: "authorization_code",
              code: req.query.code,
              redirect_uri: linkedinConfig.redirectURI,
              client_id: linkedinConfig.clientID,
              client_secret: linkedinConfig.clientSecret,
            },
            headers: {
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );
      } catch (error) {
        console.error(
          "LinkedIn token exchange error:",
          error.response ? error.response.data : error.message
        );
        const response = {
          error: true,
          message: "Failed to exchange code for token",
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      const tokens = tokenResponse.data;

      // Get user profile information
      let profileResponse;
      try {
        profileResponse = await axios.get(
          "https://api.linkedin.com/v2/userinfo",
          {
            headers: {
              Authorization: `Bearer ${tokens.access_token}`,
            },
          }
        );
      } catch (error) {
        console.error(
          "LinkedIn profile request error:",
          error.response ? error.response.data : error.message
        );
        const response = {
          error: true,
          message: "Failed to get LinkedIn profile",
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      // With the new scopes, email is included in the userinfo response
      const profile = profileResponse.data;
      const email = profile.email;

      // Format user data
      const user = {
        id: profile.sub,
        firstName: profile.given_name,
        lastName: profile.family_name,
        email: email,
      };

      // Login or register user
      let service = new AuthService();
      let id;
      if (state.company_id) {
        const company_id = state.company_id;
        sdk.setProjectId(projectId);
        const company = await sdk.findOne("company", { id: company_id });
        if (!company) {
          return res
            .status(404)
            .json({ message: "Company Not found", error: true });
        }
        id = await service.linkedinLogin(
          sdk,
          projectId,
          user,
          tokens,
          role,
          company_id
        );
      } else {
        id = await service.linkedinLogin(sdk, projectId, user, tokens, role);
      }

      if (typeof id == "string") {
        // Redirect to the frontend with error
        const response = {
          error: true,
          message: id,
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(
          expireDate.getSeconds() + config.refresh_jwt_expire
        );
        await service.saveRefreshToken(
          sdk,
          req.projectId,
          id,
          refreshToken,
          expireDate
        );
      }

      const data = JSON.stringify({
        error: false,
        role: role,
        token: JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
          },
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: id,
        refresh_token: refreshToken,
      });

      const encodedURI = encodeURI(data);
      res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
    } catch (error) {
      console.log(error);
      // Redirect to frontend with errors
      const response = {
        error: true,
        message: error.message,
      };
      const data = JSON.stringify(response);
      const encodedURI = encodeURI(data);
      return res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
    }
  });
  return [
    {
      method: "GET",
      name: "Google Code API",
      url: "/v2/api/lambda/linkedin/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      queryBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "Google Login API",
      url: "/v2/api/lambda/linkedin/login",
      successPayload: "['Will redirect to google login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",
      name: "Google Code API",
      url: "/v2/api/lambda/linkedin/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      queryBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "Google Login API",
      url: "/v2/api/lambda/linkedin/login",
      successPayload: "['Will redirect to google login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};
