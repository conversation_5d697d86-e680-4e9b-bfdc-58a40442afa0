const AuthService = require("../services/AuthService");
const JwtService = require("../services/JwtService");
const axios = require("axios");
const ManaKnightSDK = require("../core/ManaKnightSDK");
const BackendSDK = require("../core/BackendSDK");
const fs = require("fs");
const path = require("path");

module.exports = function (app) {
  app.get("/v2/api/lambda/instagram/code", async function (req, res) {
    const state = JSON.parse(req.query.state);
    let hostname = state.hostname;
    try {
      const base64DecodeBuffer = Buffer.from(state.project_id, "base64");
      const config = app.get("configuration");
      let base64Decode = base64DecodeBuffer.toString().split(":");
      const projectId = base64Decode[0];
      const role = state.role;
      let needRefreshToken = false;
      let refreshToken = undefined;

      if (state.is_refresh) {
        needRefreshToken = true;
      }

      // Note: Checking for permission as we can't use PermissionMiddleware here

      const database = base64Decode[2] ?? config.databaseName;

      let sdk = app.get("sdk");
      // sdk.setDatabase(database);
      sdk.setProjectId(projectId);

      // let manaknightSDK = new ManaKnightSDK();
      let manaknightSDK = app.get("sdk");

      manaknightSDK.getDatabase();
      manaknightSDK.setProjectId(projectId);

      let originalUrl = req.originalUrl;

      manaknightSDK.getDatabase();
      manaknightSDK.setProjectId(projectId);

      const instagramConfig = {
        clientID: config.instagram.client_id,
        clientSecret: config.instagram.client_secret,
        redirectURL: config.instagram.redirect_url,
        scope: "user_profile,user_media",
      };

      // **Injection Point Start:** Attempt to load and run custom code
      const injectionPath = path.join(
        __dirname,
        "..",
        "custom",
        projectId,
        "social_callback.js"
      ); // Path to the injection file
      try {
        if (fs.existsSync(injectionPath)) {
          const { instagram_callback } = require(injectionPath);

          await instagram_callback({
            req,
            res,
            project: { hostname },
            state,
            projectId,
            role,
            needRefreshToken,
            refreshToken,
            database,
            sdk,
            manaknightSDK,
            originalUrl,
            config,
            instagramConfig,
          });
          if (res.headersSent) return;
        }
      } catch (injectError) {
        console.error("Error during injection:", injectError);
      }
      // **Injection Point End:**

      // Instagram Basic Display API token exchange
      const tokenData = new URLSearchParams({
        client_id: instagramConfig.clientID,
        client_secret: instagramConfig.clientSecret,
        grant_type: "authorization_code",
        redirect_uri: instagramConfig.redirectURL,
        code: req.query.code,
      });

      const tokenResponse = await axios.post(
        "https://api.instagram.com/oauth/access_token",
        tokenData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        }
      );

      const accessToken = tokenResponse.data.access_token;
      const userId = tokenResponse.data.user_id;

      // Get user profile from Instagram Basic Display API
      const profileResponse = await axios.get(
        `https://graph.instagram.com/me?fields=id,username,account_type&access_token=${accessToken}`
      );

      // Try to get user media to get more profile info
      let userMedia = null;
      try {
        const mediaResponse = await axios.get(
          `https://graph.instagram.com/me/media?fields=id,caption,media_type,media_url,thumbnail_url&limit=1&access_token=${accessToken}`
        );
        userMedia = mediaResponse.data.data[0] || null;
      } catch (mediaError) {
        console.log("Could not fetch user media:", mediaError.message);
      }

      const userProfile = {
        user: {
          id: profileResponse.data.id,
          email: profileResponse.data.username + "@instagram.local", // Instagram Basic Display doesn't provide email
          first_name: profileResponse.data.username,
          last_name: "",
          photo: userMedia?.thumbnail_url || userMedia?.media_url || null,
        },
        tokens: {
          access_token: accessToken,
        },
      };
      let service = new AuthService();
      //verify if that user belongs to that company
      // check if parts has third item
      let id;
      if (state.company_id) {
        const company_id = state.company_id;
        sdk.setProjectId(projectId);
        const company = await sdk.findOne("company", { id: company_id });
        if (!company) {
          return res
            .status(404)
            .json({ message: "Company Not found", error: true });
        }
        id = await service.instagramLogin(
          sdk,
          projectId,
          userProfile.user,
          userProfile.tokens,
          role,
          company_id
        );
      } else {
        id = await service.instagramLogin(
          sdk,
          projectId,
          userProfile.user,
          userProfile.tokens,
          role
        );
      }

      if (typeof id == "string") {
        // redirect to the frontend with error
        const response = {
          error: true,
          message: id,
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(
          expireDate.getSeconds() + config.refresh_jwt_expire
        );
        await service.saveRefreshToken(
          sdk,
          req.projectId,
          id,
          refreshToken,
          expireDate
        );
      }

      const data = JSON.stringify({
        error: false,
        role: role,
        token: JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
          },
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: id,
        refresh_token: refreshToken,
      });

      const encodedURI = encodeURI(data);

      res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
    } catch (error) {
      console.log(error);
      // redirect to frontend with errors
      const response = {
        error: true,
        message: error.message,
      };
      const data = JSON.stringify(response);
      const encodedURI = encodeURI(data);
      return res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
    }
  });

  return [
    {
      method: "GET",
      name: "Instagram Code API",
      url: "/v2/api/lambda/instagram/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      queryBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "Instagram Login API",
      url: "/v2/api/lambda/instagram/login",
      successPayload: "['Will redirect to instagram login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",
      name: "Instagram Code API",
      url: "/v2/api/lambda/instagram/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      queryBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "Instagram Login API",
      url: "/v2/api/lambda/instagram/login",
      successPayload: "['Will redirect to instagram login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};
