const JwtService = require("../../../baas/services/JwtService");
const MailService = require("../../../baas/services/MailService");
const { sqlDateFormat, sqlDateTimeFormat } = require("../../../baas/services/UtilService");

// Helper functions to reduce code duplication
const validateEmail = (email) => {
  if (!email) {
    return {
      status: 403,
      response: {
        error: true,
        message: "Email Missing",
        validation: [{ field: "email", message: "Email missing" }],
      },
    };
  }
  return null;
};

const validateRole = (Role) => {
  if (!Role.canForgot()) {
    return {
      status: 403,
      response: {
        error: true,
        message: "Forbidden access",
      },
    };
  }
  return null;
};

const generateResetToken = () => {
  return Math.floor(Math.random() * 100000000)
    .toString()
    .padStart(8, "0");
};

const handleForgotPassword = async (req, res, sdk, mailService) => {
  try {
    // Validate email
    const emailError = validateEmail(req.body.email);
    if (emailError)
      return res.status(emailError.status).json(emailError.response);

    // Validate role permissions
    const Role = require(`../roles/wxy`);
    const roleError = validateRole(Role);
    if (roleError) return res.status(roleError.status).json(roleError.response);

    sdk.setProjectId("xyz");
    sdk.setTable("user");

    // Find user
    const result = await sdk.findOne('user', {
      email: req.body.email,
      role_id: Role.slug,
    });

    if (!result) {
      return res.status(403).json({
        error: true,
        message: "Cannot find User",
      });
    }

    if (result.login_type != 0) {
      return res.status(403).json({
        error: true,
        message: "Cannot do forgot password on single sign on",
      });
    }

    // Generate reset token and code
    const code = generateResetToken();
    const token = code;

    // Save token
    const tokenPayload = {
      code,
      token: token,
      type: 2,
      data: JSON.stringify({
        email: req.body.email,
        code,
      }),
      user_id: result.id,
      status: 1,
      created_at: sqlDateFormat(new Date()),
      updated_at: sqlDateTimeFormat(new Date()),
      expired_at: sqlDateTimeFormat(new Date(Date.now() + 24 * 60 * 60 * 1000)),
    };

    await sdk.create("tokens", tokenPayload);

    // Send reset email
    await mailService.sendPasswordResetEmail(req.body.email, code, token);

    return res.status(200).json({
      error: false,
      message: "Email Sent",
    });
  } catch (err) {
    console.error("Password reset error:", err);
    return res.status(403).json({
      error: true,
      message: err.message,
    });
  }
};

module.exports = function (app) {
  const config = app.get("configuration");
  const mailService = new MailService(config);

  app.post("/v1/api/xyz/wxy/lambda/forgot", async (req, res) => {
    await handleForgotPassword(req, res, app.get("sdk"), mailService);
  });

  app.post("/v1/api/xyz/wxy/lambda/mobile/forgot", async (req, res) => {
    const isMobileApp =
      req.headers["user-agent"] && req.headers["user-agent"].includes("Mobile");
    if (!isMobileApp) {
      return res.status(403).json({
        error: true,
        message:
          "Forbidden: This endpoint is only accessible from the mobile app.",
      });
    }
    await handleForgotPassword(req, res, app.get("sdk"), mailService);
  });
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "Forgot Password API",
      url: "/v1/api/xyz/wxy/lambda/forgot",
      successPayload: "['Email sent']",
      queryBody: [{ key: "email", value: "<EMAIL>" }],
      needToken: false,
      errors: [],
    },
    {
      method: "POST",
      name: "Forgot Password Mobile API",
      url: "/v1/api/xyz/wxy/lambda/mobile/forgot",
      successPayload: "['Email sent']",
      queryBody: [{ key: "email", value: "<EMAIL>" }],
      needToken: false,
      errors: [],
    },
  ];
};
