const JwtService = require("../services/JwtService");
var axios = require("axios");
var FormData = require("form-data");
const ManaKnightSDK = require("../core/ManaKnightSDK");
const {
  filterEmptyFields,
  sqlDateFormat,
  sqlDateTimeFormat,
} = require("../services/UtilService");
const path = require("path");
const fs = require("fs");

const middlewares = [];

module.exports = function (app) {

  app.get("/v1/api/lambda/microsoft/code", async (req, res) => {
    const config = app.get("configuration");
    
    const { code } = req.query;
    const clientId = config.microsoft.application_id; // Replace with your Azure AD application's client ID
    const clientSecret = config.microsoft.client_values; // Replace with your Azure AD application's client secret
    const redirectUri = config.microsoft.redirect_url; // Change to your redirect URI
    // return res.json({message:code})
    const tokenEndpoint =
      "https://login.microsoftonline.com/common/oauth2/v2.0/token";
    var data = new FormData();

    const parts = req.query.state.split("~");
    const projectId = parts[0];
    const hostname = parts[1];
    const role = parts[2];
    const company_id = parts[3] ?? null;
    let needRefreshToken = false;
    let refreshToken = undefined;

    if (req.query.state.includes("with_refresh")) {
      needRefreshToken = true;
    }

    // Note: Checking for permission as we can't use PermissionMiddleware here


    const sdk = app.get("sdk");

    sdk.setProjectId(projectId);

    let manaknightSDK = null;


    const injectionPath = path.join(__dirname, "..", "custom", projectId, "social_callback.js"); // Path to the injection file
    try {
      if (fs.existsSync(injectionPath)) {
        const { microsoft_callback } = require(injectionPath);

        await microsoft_callback({
          req,
          res,
          project: { hostname },
          state,
          projectId,
          role,
          needRefreshToken,
          refreshToken,
          sdk,
          manaknightSDK,
          config,
          code
        });
        if (res.headersSent) return;
      }

    } catch (injectError) {
      console.error("Error during injection:", injectError);
    }
 

    try {
      data.append("grant_type", "authorization_code");
      data.append("client_id", clientId);
      data.append("client_secret", clientSecret);
      // data.append('scope', 'User.Read');
      data.append("redirect_uri", redirectUri);
      data.append("code", code);
      // data.append('code', code);

      var con = {
        method: "post",
        maxBodyLength: Infinity,
        url: tokenEndpoint,
        headers: {
          ...data.getHeaders(),
        },
        data: data,
      };

      const response = await axios(con);

      // Retrieve the access token from the response or your session/database
      const { access_token, refresh_token } = response.data;
      let profile = null;
      try {
        const response2 = await axios.get(
          "https://graph.microsoft.com/v1.0/me",
          {
            headers: {
              Authorization: "Bearer " + access_token,
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );

        // Handle the user's profile data as needed
        profile = response2.data;
      } catch (error) {
        return res.status(500).json({
          error: true,
          message: "Error retrieving user profile",
        });
      }

  
      let refreshToken = null;
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(
          expireDate.getSeconds() + config.refresh_jwt_expire
        );
        await service.saveRefreshToken(
          sdk,
          projectId,
          id,
          refreshToken,
          expireDate
        );
      }

      sdk.setTable("user");
      sdk.setProjectId(projectId);
      sdk.getDatabase();
      if (profile) {
        const exist = await sdk.findOne("user", {
          email: profile.mail,
        });
        if (exist) {
          const result = exist;
          const data = JSON.stringify({
            error: false,
            role: exist.role,
            token: JwtService.createAccessToken(
              {
                user_id: result.id,
                role: exist.role_id,
              },
              config.access_jwt_expire,
              config.jwt_key
            ),
            refresh_token: refreshToken,
            expire_at: config.access_jwt_expire,
            user_id: result.id,
            two_factor_enabled:
              result.two_factor_authentication === 1 ? true : false,
          });

          const encodedURI = encodeURI(data);

          return res.redirect(
            `https://${hostname}/login/oauth?data=${encodedURI}`
          );
        } else {
          const result = await sdk.create("user", filterEmptyFields({
            email: profile.mail,
            data: JSON.stringify({first_name: profile.givenName, last_name: profile.surname}),
            role_id: role,
            password: " ",
            login_type: 2,
            verify: 1,
            status: 1,
            two_factor_authentication: 0,
            company_id: company_id ?? 0,
            created_at: sqlDateFormat(new Date()),
            updated_at: sqlDateTimeFormat(new Date()),
            })
          );

          const data = JSON.stringify({
            error: false,
            role,
            token: JwtService.createAccessToken(
              {
                user_id: result.id,
                role,
              },
              config.access_jwt_expire,
              config.jwt_key
            ),
            refresh_token: refreshToken,
            expire_at: config.access_jwt_expire,
            user_id: result.id,
            two_factor_enabled:
              result.two_factor_authentication === 1 ? true : false,
          });

          const encodedURI = encodeURI(data);

          return res.redirect(
            `https://${hostname}/login/oauth?data=${encodedURI}`
          );
        }
      } else {
        return res.status(500).json({
          error: true,
          message: "Error retrieving user profile",
        });
      }
    } catch (e) {
      return res.status(500).json({
        error: true,
        message: e.message,
      });
    }
  });

  return [
    {
      method: "GET",
      name: "microsoft Code API",
      url: "/v2/api/lambda/microsoft/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      queryBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "microsoft Login API",
      url: "/v2/api/lambda/microsoft/login",
      successPayload: "['Will redirect to microsoft login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",
      name: "microsoft Code API",
      url: "/v2/api/lambda/microsoft/code",
      successPayload:
        "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}",
      queryBody: [{ code: "role", state: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
    {
      method: "GET",
      name: "microsoft Login API",
      url: "/v2/api/lambda/microsoft/login",
      successPayload: "['Will redirect to microsoft login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};

