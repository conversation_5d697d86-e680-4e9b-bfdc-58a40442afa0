var fs = require("fs");

// we still have path issue when we open up the lambda folder for all lambdas, this is a temporary fix to only expose fixed lambdas
const fixed_lambdas = [
  "stripe_webhook.js",
  "google_login.js",
  "microsoft_login.js",
  "facebook_login.js",
  "apple_login.js",
  "linkedin_login.js",
  "instagram_login.js",
];

module.exports = function (app) {
  fs.readdirSync(__dirname).forEach(function (file) {
    if (
      file === "index.js" ||
      !fixed_lambdas.includes(file)
      //   file.substr(file.lastIndexOf(".") + 1) !== "js" ||
      //   file.startsWith("__")
    )
      return;
    var name = file.substr(0, file.indexOf("."));
    require("./" + name)(app);
  });
};
