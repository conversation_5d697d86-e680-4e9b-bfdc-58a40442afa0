const AuthService = require("../services/AuthService");
const JwtService = require("../services/JwtService");
const middlewares = [];
const fs = require("fs");
const path = require("path");

module.exports = function (app) {
  // url = https://graph.facebook.com/v4.0/oauth/access_token
  // params = client_id, client_secret, code, redirect_uri
  // let project = { hostname: "mkdlabs.com" };

  app.get("/v2/api/lambda/facebook/code", [], async function (req, res) {
    let hostname;
    try {
      const config = app.get("configuration");
      const parts = req.query.state.split("~");
      const projectId = parts[0];
      hostname = parts[1];
      const role = parts[2];

      let needRefreshToken = false;
      let refreshToken = undefined;

      if (req.query.state.includes("with_refresh")) {
        needRefreshToken = true;
      }

      const database = projectId ?? config.databaseName;

      let sdk = app.get("sdk");
      sdk.setProjectId(projectId);

      let service = new AuthService();

      let manaknightSDK = app.get("sdk");

      // Note: Checking for permission as we can't use PermissionMiddleware here
      let originalUrl = req.originalUrl;

      manaknightSDK.getDatabase();
      // const svc = new permissionService(manaknightSDK, projectId, req.header);
      // let validate = await svc.validate(originalUrl);
      // if (validate.error) {
      //   return res.status(401).json({ message: validate.message });
      // }

      // Remark: Fetching Project
      // if (config.env == "production") {
      //   project = require("../project");
      // } else {
      //   manaknightSDK.setProjectId("manaknight");
      //   manaknightSDK.setTable("projects");

      //   project = (
      //     await manaknightSDK.get({
      //       project_id: projectId,
      //     })
      //   )[0];
      // }

      // **Injection Point Start:** Attempt to load and run custom code
      const injectionPath = path.join(__dirname, "..", "..", "custom", `${projectId}_backend`, "social_callback.js"); // Path to the injection file
      try {
        if (fs.existsSync(injectionPath)) {
          const { facebook_callback } = require(injectionPath);

          await facebook_callback({
            req,
            res,
            project: { hostname },
            state: { projectId, hostname, role, company_id: parts[3] },
            projectId,
            role,
            needRefreshToken,
            refreshToken,
            database,
            sdk,
            manaknightSDK,
            originalUrl,
            config
          });
          if (res.headersSent) return;
        }

      } catch (injectError) {
        console.error("Error during injection:", injectError);
      }
      // **Injection Point End:**

      // get access token from facebook
      const facebookAccessTokenCall = await fetch(
        `https://graph.facebook.com/v4.0/oauth/access_token?client_id=${config.facebook.client_id}&client_secret=${config.facebook.client_secret}&code=${req.query.code}&redirect_uri=${config.facebook.callback_uri}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const data = await facebookAccessTokenCall.json();
      console.log(data);
      if (facebookAccessTokenCall.status !== 200) {
        return res.status(403).json({
          error: true,
          failure: "access token",
          message: "Something went wrong",
        });
      }

      const facebookMeCall = await fetch(
        `https://graph.facebook.com/v4.0/me?fields=id,email,first_name,last_name&access_token=${data.access_token}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const userData = await facebookMeCall.json();

      if (facebookMeCall.status !== 200) {
        return res.status(403).json({
          error: true,
          failure: "me",
          message: "Something went wrong",
        });
      }
      let id;
      if (parts[3]) {
        const company_id = parts[3];
        sdk.setProjectId(projectId);
        sdk.setTable("company");
        const company = await sdk.get({ id: company_id });
        if (!company) {
          return res
            .status(404)
            .json({ message: "Company Not found", error: true });
        }
        id = await service.facebookLogin(
          sdk,
          projectId,
          userData,
          data,
          role,
          company_id
        );
      } else {
        id = await service.facebookLogin(sdk, projectId, userData, data, role);
      }

      console.log("***", id);

      if (typeof id === "string") {
        const response = {
          error: true,
          message: id,
        };
        const data = JSON.stringify(response);
        const encodedURI = encodeURI(data);
        return res.redirect(
          `https://${hostname}/login/oauth?data=${encodedURI}`
        );
      }

      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );
        let expireDate = new Date();
        expireDate.setSeconds(
          expireDate.getSeconds() + config.refresh_jwt_expire
        );
        await service.saveRefreshToken(
          sdk,
          req.projectId,
          id,
          refreshToken,
          expireDate
        );
      }

      const resData = JSON.stringify({
        error: false,
        role: role,
        token: JwtService.createAccessToken(
          {
            user_id: id,
            role: role,
          },
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: id,
        refresh_token: refreshToken,
      });

      const encodedURI = encodeURI(resData);

      res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);
    } catch (error) {
      console.log(error);

      const data = JSON.stringify({
        error: true,
        message: error.message,
      });

      const encodedURI = encodeURI(data);
      res.redirect(`https://${hostname}/login/oauth?data=${encodedURI}`);

      // return res.status(403).json({
      //   error: true,
      //   message: "Invalid Credentials"
      // });
    }
  });

  return [
    {
      method: "GET",
      name: "Facebook Login API",
      url: "/v2/api/lambda/facebook/lambda",
      successPayload: "['Will redirect to facebook login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [],
    },
    {
      method: "GET",
      name: "Facebook Code Webhook",
      url: "/v2/api/lambda/facebook/code",
      successPayload:
        '{"error": false,"role": "admin","qr_code": "qrCode","one_time_token": "token","expire_at": 60,"user_id": 1}',
      queryBody: [{ key: "state", value: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",
      name: "Facebook Login API",
      url: "/v2/api/lambda/facebook/lambda",
      successPayload: "['Will redirect to facebook login with auth link']",
      queryBody: [{ key: "role", value: "admin" }],
      needToken: false,
      errors: [],
    },
    {
      method: "GET",
      name: "Facebook Code Webhook",
      url: "/v2/api/lambda/facebook/code",
      successPayload:
        '{"error": false,"role": "admin","qr_code": "qrCode","one_time_token": "token","expire_at": 60,"user_id": 1}',
      queryBody: [{ key: "state", value: "projectId~secret" }],
      needToken: false,
      errors: [
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "access token", "message": "Something went wrong"}',
        },
        {
          name: "403",
          query: [{ key: "state", value: "projectId~secret" }],
          response:
            '{"error": true, "failure": "me", "message": "Something went wrong"}',
        },
      ],
    },
  ];
};
