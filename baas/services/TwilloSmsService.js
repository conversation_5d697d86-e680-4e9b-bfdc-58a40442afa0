const config = require("../../config");
const twilio = require("twilio");

const accountSid = config.twilio.account_sid;
const authToken = config.twilio.auth_token;
const phoneNumber = config.twilio.phoneNumber;
const client = twilio(accountSid, authToken);

const SmsService = {
  from: phoneNumber,

  inject(template, payload) {
    let body = template.content;

    for (const key in payload) {
      const element = payload[key];
      body = body.replace(new RegExp(`{{{${key}}}}`, "g"), element);
    }

    return body;
  },

  /**
   * Send SMS
   * @param {string} to
   * @param {string} body
   * @returns {Promise<string>} Message SID
   */
  async send(to, body) {
    try {
      const message = await client.messages.create({
        body,
        from: this.from,
        to,
      });
      return message.sid;
    } catch (error) {
      throw new Error(`Failed to send SMS: ${error.message}`);
    }
  },
};

module.exports = SmsService;
