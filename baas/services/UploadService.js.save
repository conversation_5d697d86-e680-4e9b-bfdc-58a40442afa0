const path = require("path");
const aws = require("aws-sdk");
const multer = require("multer");
const multerS3 = require("multer-s3");
const { createDirectoriesRecursive } = require("./UtilService");
const config = require("../../config");
const crypto = require("crypto");
const { S3Client } = require('@aws-sdk/client-s3')
const { Credentials } = require('aws-sdk');

// Configuration constants
const UPLOAD_CONFIG = {
  LOCAL_UPLOAD_DIR: path.join(__dirname, "..", "public", "uploads"),
  S3_BASE_PATH: 'baas',
  RANDOM_ID_LENGTH: 12,
  DEFAULT_URL_EXPIRATION: 3600 // 1 hour
};

// S3 client initialization with memoization
const createS3Client = () => {
  return new aws.S3({
    accessKeyId: config.aws_key,
    secretAccessKey: config.aws_secret,
    region: config.aws_region
  });
};

const credentials = new Credentials({
  accessKeyId: config.aws_key,
  secretAccessKey: config.aws_secret,
});

// Memoized S3 client to reduce unnecessary instantiations
const getS3Client = (() => {
  let s3Client = null;
  return () => {
    if (!s3Client) {
      s3Client = createS3Client();
    }
    return s3Client;
  };
})();

// Utility function to generate random filename
const generateRandomFilename = (length = UPLOAD_CONFIG.RANDOM_ID_LENGTH) => {
  const n = crypto.randomInt(0, Math.pow(10, length));
  return n.toString().padStart(length, "0");
};

// Utility function to clean filename
const cleanFilename = (filename) => filename.replace(/\s+/g, "_").trim();

module.exports = {
  /**
   * Determine upload method based on configuration
   * @param {string} location - Optional upload location
   * @returns {Function} Multer upload middleware
   */
  upload: function (location) {
    return config.upload_type === "s3" 
      ? this.s3_upload(location)
      : this.local_upload();
  },

  /**
   * S3 upload configuration
   * @param {string} location - Optional upload location
   * @param {boolean} appendFilename - Whether to append original filename
   * @returns {Function} Multer S3 upload middleware
   */
  s3_upload: function (location, appendFilename = false) {
    try {
      const s3 = new S3Client({ credentials, region: config.aws_region,
      })
      
      return multer({
        storage: multerS3({
          s3,
          bucket: config.aws_bucket,
          acl: "public-read",
          contentType: multerS3.AUTO_CONTENT_TYPE,
          key: function (req, file, cb) {
            const randomId = generateRandomFilename();
            const locationFilename = appendFilename ? location + file.originalname : location;
            const finalLocation = location 
              ? locationFilename 
              : `${UPLOAD_CONFIG.S3_BASE_PATH}/${req.projectId}/${randomId}${file.originalname}`;

            console.log(`Uploading to: ${finalLocation}`);
            cb(null, finalLocation);
          }
        })
      });
    } catch (error) {
      console.error("S3 upload error:", error);
      throw error;
    }
  },

  /**
   * Public S3 upload configuration with randomized filename
   * @returns {Function} Multer S3 upload middleware for public files
   */
  s3_upload_public: function () {
    try {
      const s3 = getS3Client();

      return multer({
        storage: multerS3({
          s3,
          bucket: config.aws_bucket,
          acl: "public-read",
          contentType: multerS3.AUTO_CONTENT_TYPE,
          key: function (req, file, cb) {
            const randomId = generateRandomFilename();
            const fileName = cleanFilename(file.filename ?? file.originalname);
            cb(null, randomId + fileName);
          }
        })
      });
    } catch (error) {
      console.error("S3 public upload error:", error);
      throw error;
    }
  },

  /**
   * Local file upload configuration
   * @returns {Function} Multer local upload middleware
   */
  local_upload: function () {
    try {
      const storage = multer.diskStorage({
        destination: function (req, file, cb) {
          createDirectoriesRecursive(UPLOAD_CONFIG.LOCAL_UPLOAD_DIR);
          cb(null, UPLOAD_CONFIG.LOCAL_UPLOAD_DIR);
        },
        filename: function (req, file, cb) {
          const fileName = cleanFilename(file.filename ?? file.originalname);
          cb(null, `${Date.now()}-${fileName}`);
        }
      });

      return multer({ storage });
    } catch (error) {
      console.error("Local upload error:", error);
      throw error;
    }
  },

  /**
   * Clear all images for a specific project in S3
   * @param {string} project - Project identifier
   */
  clear_project_images: async function (project) {
    try {
      const s3 = getS3Client();
      const virtualPath = `${UPLOAD_CONFIG.S3_BASE_PATH}/${project}`;
      
      const data = await s3.listObjectsV2({ 
        Bucket: config.aws_bucket, 
        Prefix: virtualPath 
      }).promise();
  
      if (data.Contents.length === 0) {
        console.log(`Folder '${virtualPath}' is empty.`);
        return;
      }
  
      const deletePromises = data.Contents.map(obj => 
        s3.deleteObject({ 
          Bucket: config.aws_bucket, 
          Key: obj.Key 
        }).promise()
      );
  
      await Promise.all(deletePromises);
      console.log(`Deleted ${data.Contents.length} object(s) from '${virtualPath}'.`);
    } catch (error) {
      console.error('Error clearing project images:', error);
      throw error;
    }
  },

  /**
   * Generate a temporary signed URL for S3 object
   * @param {string} url - S3 object URL
   * @param {number} expirationInSeconds - URL expiration time
   * @returns {Promise<string>} Signed temporary URL
   */
  generate_temporary_s3_url: async function (
    url = "", 
    expirationInSeconds = UPLOAD_CONFIG.DEFAULT_URL_EXPIRATION
  ) {
    try {
      const s3 = getS3Client();
      const key = url.split(`${config.aws_bucket}/`)[1];
      
      const params = {
        Bucket: config.aws_bucket,
        Key: key,
        Expires: expirationInSeconds
      };

      return await s3.getSignedUrlPromise("getObject", params);
    } catch (error) {
      console.error('Error generating temporary URL:', error);
      throw error;
    }
  }
};


