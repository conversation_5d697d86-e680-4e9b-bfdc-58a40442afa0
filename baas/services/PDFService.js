const puppeteer = require("puppeteer")
const eta = require("eta");


module.exports = class PDFService {

  async ETAtoPDF(pdf_path = "example.pdf", format = 'A4', payload, eta_path) {
    const browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
    const page = await browser.newPage();
    await page.setContent(eta.renderFile(eta_path, {
      payload
    }));
    await page.pdf({ path: pdf_path, format });
    await browser.close();

  }
  async URLtoPDF(path = "example.pdf", format = 'A4', url = 'https://example.com') {
    const browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
    const page = await browser.newPage();
    await page.goto(url);
    await page.pdf({ path, format });
    await browser.close();
  }
  async HTMLtoPDF(path = "example.pdf", format = 'A4', html = '<h1>Example</h1>') {
    const browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
    const page = await browser.newPage();
    await page.setContent(html);
    await page.pdf({ path, format });
    await browser.close();
  }
  async TexttoPDF(path = "example.pdf", format = 'A4', text = 'My name is Teslim') {
    const browser = await puppeteer.launch({ headless: true, args: ['--no-sandbox'] });
    const page = await browser.newPage();
    await page.setContent(text);
    await page.pdf({ path, format });
    await browser.close();
  }
}