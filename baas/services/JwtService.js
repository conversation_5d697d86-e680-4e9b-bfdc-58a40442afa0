/*Powered By: Manaknightdigital Inc. https://manaknightdigital.com/ Year: 2020*/
/**
 * JWT Service
 * @copyright 2020 Manaknightdigital Inc.
 * @link https://manaknightdigital.com
 * @license Proprietary Software licensing
 * <AUTHOR>
 *
 */
var jwt = require("jsonwebtoken");
const jwksClient = require("jwks-rsa");

module.exports = {
  /**
   * Creates an access token with the given payload and expiration time.
   * @param {Object} payload - The payload to include in the token.
   * @param {number} expireIn - The expiration time in seconds.
   * @param {string} secret - The secret key to sign the token.
   * @returns {string} The signed access token.
   */
  createAccessToken: function (payload, expireIn, secret) {
    return jwt.sign(payload, secret, {
      expiresIn: Number(expireIn),
      algorithm: "HS256"
    });
  },

  /**
   * Creates a refresh token with the given payload and expiration time.
   * @param {Object} payload - The payload to include in the token.
   * @param {string} key - The key to sign the token.
   * @param {number} expireAt - The expiration time in seconds.
   * @returns {string} The signed refresh token.
   */
  createRefreshToken: function (payload, key, expireAt) {
    return jwt.sign(payload, key, {
      expiresIn: Number(expireAt),
      algorithm: "HS256"
    });
  },

  /**
   * Verifies an access token and checks for specific roles if provided.
   * @param {string} token - The token to verify.
   * @param {string} key - The key to verify the token.
   * @param {Object} options - Additional options for verification.
   * @returns {Object|boolean} The decoded token if valid, otherwise false.
   */
  verifyAccessToken: function (token, key, options = {}) {
    try {
      const decoded = jwt.verify(token, key, options);
      if (decoded) {
        if (decoded.hasOwnProperty("role") && decoded["role"].toLowerCase() == "superadmin") {
          return decoded;
        }
        for (const key of Object.keys(options)) {
          if (key == "role" && decoded.role.split(",").length > 1) {
            const user_roles = decoded.role.split(",");
            const roles = options[key].split("|");
            if (user_roles.filter((value) => roles.includes(value)).length == 0) {
              return false;
            }
          } else if (!options[key].split("|").includes(decoded[key])) {
            return false;
          }
        }
        return decoded;
      }
    } catch (err) {
      console.log("verifyAccessToken", err);
      return false;
    }
  },

  /**
   * Verifies a refresh token.
   * @param {string} token - The token to verify.
   * @param {string} key - The key to verify the token.
   * @param {Object} options - Additional options for verification.
   * @returns {Object|boolean} The decoded token if valid, otherwise false.
   */
  verifyRefreshToken: function (token, key, options = {}) {
    try {
      return jwt.verify(token, key, options);
    } catch (err) {
      return false;
    }
  },

  /**
   * Verifies a token.
   * @param {string} token - The token to verify.
   * @param {string} key - The key to verify the token.
   * @param {Object} options - Additional options for verification.
   * @returns {Object|boolean} The decoded token if valid, otherwise false.
   */
  verifyToken: function (token, key, options = {}) {
    try {
      return jwt.verify(token, key, options);
    } catch (err) {
      return false;
    }
  },

  /**
   * Generates a random string of a specified length.
   * @param {number} length - The length of the string to generate.
   * @returns {string} The generated string.
   */
  generateString: function (length) {
    let d = new Date().getTime();
    const time = new Date().getTime();
    const uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx-xxxx".replace(/[xy]/g, function (c) {
      let r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c == "x" ? r : (r & 0x7) | 0x8).toString(16);
    });

    return (uuid.toUpperCase() + "-" + time.toString()).substring(0, length);
  },

  /**
   * Generates a UUID.
   * @returns {string} The generated UUID.
   */
  generateUUID: function () {
    let d = new Date().getTime();
    const uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
      let r = (d + Math.random() * 16) % 16 | 0;
      d = Math.floor(d / 16);
      return (c == "x" ? r : (r & 0x7) | 0x8).toString(16);
    });
    return uuid.toUpperCase();
  },

  /**
   * Retrieves the token from the request headers, query, or cookies.
   * @param {Object} req - The request object.
   * @returns {string|null} The token if found, otherwise null.
   */
  getToken: function (req) {
    if (req.headers.authorization && req.headers.authorization.split(" ")[0] === "Bearer") {
      return req.headers.authorization.split(" ")[1];
    } else if (req.query && req.query.token) {
      return req.query.token;
    } else if (req.cookies && req.cookies["access_token"]) {
      return req.cookies["access_token"];
    }

    return null;
  },

  /**
   * Middleware to verify the token and attach user information to the request.
   * @param {string} key - The key to verify the token.
   * @param {Object} options - Additional options for verification.
   * @returns {Function} The middleware function.
   */
  verifyTokenMiddleware: function (key, options) {
    const self = this;
    return function (req, res, next) {
      const token = self.getToken(req);
      if (!token) {
        return res.status(401).json({
          success: false,
          message: "UNAUTHORIZED",
          code: "UNAUTHORIZED"
        });
      } else {
        const result = self.verifyAccessToken(token, key, options);
        if (!result) {
          return res.status(401).json({
            success: false,
            message: "TOKEN_EXPIRED",
            code: "TOKEN_EXPIRED"
          });
        }
        req.user_id = result.user_id;
        req.role = result.role;
        next();
      }
    };
  },

  /**
   * Retrieves the Apple signing keys using the provided key ID.
   * @param {string} kid - The key ID to retrieve the signing key.
   * @returns {Promise<string|null>} The public key if found, otherwise null.
   */
  getAppleSigningKeys: async function (kid) {
    const client = jwksClient({
      jwksUri: "https://appleid.apple.com/auth/keys"
    });

    return new Promise(function (resolve, reject) {
      client.getSigningKey(kid, (err, result) => {
        if (!result) resolve(null);
        resolve(result.getPublicKey());
      });
    });
  },

  /**
   * Verifies the Apple login data using the provided key.
   * @param {string} data - The data to verify.
   * @param {string} appleKey - The key to verify the data.
   * @returns {Promise<Object>} The payload if verification is successful.
   */
  verifyAppleLogin: async function (data, appleKey) {
    return new Promise(function (resolve, reject) {
      jwt.verify(data, appleKey, (err, payload) => {
        if (err) {
          throw new Error(err.message);
        }
        return resolve(payload);
      });
    });
  }
};
