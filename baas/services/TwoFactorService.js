const speakeasy = require("speakeasy");
const QRCode = require("qrcode");
const { sqlDateTimeFormat } = require("./UtilService");

module.exports = {
  getTwoFactorAuthenticationCode: async function (projectId) {
    const secretCode = speakeasy.generateSecret({
      name: projectId
    });

    const img = await QRCode.toDataURL(secretCode.otpauth_url);
    return {
      otpauthUrl: secretCode.otpauth_url,
      base32: secretCode.base32,
      ascii_code: secretCode.ascii,
      img: img
    };
  },
  
  setUpTwoFactorAuth: async function (projectId, user_id, sdk) {
    sdk.setTable("tokens");
    const result = await sdk.find("tokens", { user_id, type: 7 });
    let secretCode;

    if (result.length > 0) {
      secretCode = JSON.parse(result[0].token);
    } else {
      secretCode = speakeasy.generateSecret({ name: projectId });
      const month = new Date();
      month.setMonth(month.getMonth() + 3);
      await sdk.create("tokens", {
        user_id: user_id,
        token: JSON.stringify(secretCode),
        type: 7,
        status: 1,
        data: JSON.stringify({ projectId: projectId, user_id: user_id, type: 'qr', phone: '' }),
        expired_at: sqlDateTimeFormat(month),
        created_at: sqlDateTimeFormat(new Date()),
        updated_at: sqlDateTimeFormat(new Date())
      });
    }

    const token = speakeasy.totp({
      secret: secretCode.base32,
      encoding: "base32"
    });

    return {
      token,
      otpauthUrl: secretCode.otpauth_url,
      expire_at: (30 - Math.floor((new Date().getTime() / 1000.0 % 30)))
    };
  },

  getDataURL: async function (otpauth_url) {
    const img = await QRCode.toDataURL(otpauth_url);
    return img;
  },

  verifyOtp: function (ascii_code, token) {
    return speakeasy.totp.verify({
      secret: ascii_code,
      encoding: "ascii",
      token: token,
    });
  },

  verifyTotp: async function (token, user_id, sdk, window = 60) {
    sdk.setTable("tokens");
    const userSecret = await sdk.find("tokens", { user_id, type: 7 });
    const base32 = JSON.parse(userSecret[0].token).base32;
    return speakeasy.totp.verify({
      secret: base32,
      encoding: "base32",
      token: token,
      window
    });
  },
  
  enable2FA: async function (sdk, user_id, projectId, type = 'qr', tok = '', phone = '') {
    let success = false;
    sdk.setTable("user");

    try {
      if (tok) {
        const result = await this.verifyTotp(tok, user_id, sdk, 1);
        if (result === true) success = result;
      }
      if (success) {
        await sdk.update("user", { id:user_id }, { two_factor_authentication: 1 });
      } else {
        await this.disable2FA(sdk, user_id, projectId, type, phone);
      }
    } catch (e) {
      console.log(e);
    }

    if (success) return { verified: success };

    const secretCode = speakeasy.generateSecret({ name: projectId });
    const month = new Date();
    month.setMonth(month.getMonth() + 3);

    sdk.setTable("tokens");
    await sdk.create("tokens", {
      user_id: user_id,
      token: JSON.stringify(secretCode),
      type: 7,
      status: 1,
      data: JSON.stringify({ projectId: projectId, user_id: user_id, type, phone }),
      expired_at: sqlDateTimeFormat(month),
      created_at: sqlDateTimeFormat(new Date()),
      updated_at: sqlDateTimeFormat(new Date())
    });

    const token = speakeasy.totp({
      secret: secretCode.base32,
      encoding: "base32"
    });

    return {
      token: token,
      otpauthUrl: secretCode.otpauth_url,
      expire_at: (30 - Math.floor((new Date().getTime() / 1000.0 % 30)))
    };
  },

  disable2FA: async function (sdk, user_id, projectId = '', type = '', phone = '') {
    sdk.setTable("tokens");
    const tokens = await sdk.find("tokens", { user_id: user_id, type: 7 });

    if (tokens.length > 0) {
      for (let i = 0; i < tokens.length; i++) {
        try {
          await sdk.deleteById("tokens", tokens[i].id);
        } catch (e) {
          console.log(e, 'delete token Failed');
        }
      }
    }

    sdk.setTable("user");
    await sdk.update("user", { id:user_id }, { two_factor_authentication: null });

    return {
      user_id,
      status: 'success',
      message: '2FA disabled'
    };
  }
};
