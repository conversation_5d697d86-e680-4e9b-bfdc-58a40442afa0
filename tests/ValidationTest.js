
class ValidationTest {
  constructor(fields) {
    // Convert fields object to array format matching schema
    const schema = this.constructor.schema();
    this.fields = schema.map(field => ({
      name: field.name,
      type: field.type,
      value: fields[field.name],
      validation: field.validation,
      defaultValue: field.defaultValue
    }));
    
    this.errors = [];
    this.validate();
  }

  static schema() {
    return [{"name":"id","type":"primary key","validation":[],"defaultValue":null},{"name":"requiredField","type":"string","validation":"required","defaultValue":null},{"name":"emailField","type":"string","validation":"email","defaultValue":null},{"name":"lengthField","type":"string","validation":["length",{"length":5}],"defaultValue":null},{"name":"enumField","type":"string","validation":["enum",{"enum":["a","b","c"]}],"defaultValue":null},{"name":"patternField","type":"string","validation":["pattern",{"pattern":"^[A-Z]+$"}],"defaultValue":null},{"name":"positiveField","type":"integer","validation":"positive","defaultValue":null},{"name":"negativeField","type":"integer","validation":"negative","defaultValue":null},{"name":"integerField","type":"integer","validation":"integer","defaultValue":null},{"name":"decimalField","type":"double","validation":"decimal","defaultValue":null},{"name":"alphanumericField","type":"string","validation":"alphanumeric","defaultValue":null},{"name":"uuidField","type":"uuid","validation":"uuid","defaultValue":null},{"name":"jsonField","type":"json","validation":"json","defaultValue":null},{"name":"dateField","type":"date","validation":"date","defaultValue":null},{"name":"phoneField","type":"string","validation":"phone","defaultValue":null},{"name":"relatedField","type":"mapping","validation":[],"defaultValue":null}]; 
  }

  validate() {
    const validationRules = this.fields.map(field => {
      if (Array.isArray(field.validation)) {
        return field.validation;
      } else if (typeof field.validation === 'string') {
        return field.validation.split(',').map(r => r.trim());
      }
      return [];
    });
    
    this.errors = []; // Reset errors for each validation call
    
    for (let i = 0; i < validationRules.length; i++) {
      const rules = Array.isArray(validationRules[i]) ? validationRules[i] : [validationRules[i]];
      const field = this.fields[i];
      const fieldName = field.name;
      const value = field.value;

      // Check for required rule
      const isRequired = rules.includes('required');
      if (isRequired) {
        if (value === undefined || value === null || value === '') {
          this.errors.push(`Field ${fieldName} is required.`);
        }
      }

      // Check for email rule
      if (rules.includes('email') && value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        this.errors.push(`Field ${fieldName} must be a valid email.`);
      }

      // Length validation
      if (rules.includes('length') && value) {
        const lengthRule = rules.find(r => typeof r === 'object' && r.length);
        if (lengthRule && value.length !== lengthRule.length) {
          this.errors.push(`Field ${fieldName} must be exactly ${lengthRule.length} characters long.`);
        }
      }

      // Enum validation
      if (rules.includes('enum') && value) {
        const enumRule = rules.find(r => typeof r === 'object' && r.enum);
        if (enumRule && !enumRule.enum.includes(value)) {
          this.errors.push(`Field ${fieldName} must be one of: ${enumRule.enum.join(', ')}`);
        }
      }

      // Pattern validation
      if (rules.includes('pattern') && value) {
        const patternRule = rules.find(r => typeof r === 'object' && r.pattern);
        if (patternRule && !new RegExp(patternRule.pattern).test(value)) {
          this.errors.push(`Field ${fieldName} does not match the required pattern.`);
        }
      }

      // Numeric validations
      if (rules.includes('positive') && value !== undefined && value <= 0) {
        this.errors.push(`Field ${fieldName} must be positive.`);
      }

      if (rules.includes('negative') && value !== undefined && value >= 0) {
        this.errors.push(`Field ${fieldName} must be negative.`);
      }

      if (rules.includes('integer') && value !== undefined && !Number.isInteger(Number(value))) {
        this.errors.push(`Field ${fieldName} must be an integer.`);
      }

      if (rules.includes('decimal') && value !== undefined) {
        const decimalStr = String(value);
        if (!/^\d*\.\d+$/.test(decimalStr)) {
          this.errors.push(`Field ${fieldName} must be a decimal number.`);
        }
      }

      // Alphanumeric validation
      if (rules.includes('alphanumeric') && value && !/^[a-zA-Z0-9]+$/.test(value)) {
        this.errors.push(`Field ${fieldName} must contain only letters and numbers.`);
      }

      // UUID validation
      if (rules.includes('uuid') && value && !/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(value)) {
        this.errors.push(`Field ${fieldName} must be a valid UUID.`);
      }

      // JSON validation
      if (rules.includes('json') && value) {
        try {
          JSON.parse(value);
        } catch (e) {
          this.errors.push(`Field ${fieldName} must be valid JSON.`);
        }
      }

      // Date validation
      if (rules.includes('date') && value) {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          this.errors.push(`Field ${fieldName} must be a valid date.`);
        }
      }

      // Phone validation
      if (rules.includes('phone') && value && !/^\+?[\d\s-]+$/.test(value)) {
        this.errors.push(`Field ${fieldName} must be a valid phone number.`);
      }
    }

    return this.errors.length === 0;
  }

  getErrors() {
    return this.errors;
  }

  isValid() {
    return this.validate();
  }

  checksum() {
    return this.simpleHash(JSON.stringify(this.fields));
  }

  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = (hash << 5) - hash + str.charCodeAt(i);
      hash |= 0;
    }
    return hash.toString();
  }

  
getRelated(fields) {
  const relatedModel = require('./related.js');
  return new relatedModel(fields);
}

}

module.exports = ValidationTest;
