const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * Twitter Login API Tests
 * Class-based implementation of the Twitter Login API tests
 */
class TwitterLoginTests {
  constructor() {
    this.framework = new APITestFramework();
    
    // Define expected response schema
    this.twitterLoginResponseSchema = {
      error: false,
      role: "string",
      access_token: "string",
      refresh_token: "string",
      expire_at: "number",
      user_id: "number",
      is_newuser: "boolean",
    };
    
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("wxy Twitter Login API Tests", () => {
      // Setup before each test
      this.framework.beforeEach(async () => {
        // Setup mock data if needed
      });

      // Test case for Twitter Login URL
      this.framework.addTestCase("wxy Twitter Login URL - Success Path", async () => {
        // Create spy to track request
        const requestSpy = this.framework.createSpy(this.framework, "makeRequest");

        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v2/api/lambda/twitter/login`,
          "https://api.twitter.com/oauth/authenticate?oauth_token=mock_request_token",
          {
            status: 200,
            headers: {
              "Content-Type": "text/plain",
            },
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/lambda/twitter/login`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Twitter Login URL should return 200 status"
        );
        this.framework.assert(
          response.body.includes("api.twitter.com"),
          "Response should contain Twitter authorization URL"
        );

        // Verify request was made correctly
        this.framework.assert(
          requestSpy.callCount() === 1,
          "API should be called exactly once"
        );
      });

      // Test case for Twitter Login URL - Error Path
      this.framework.addTestCase("wxy Twitter Login URL - Error Path", async () => {
        // Mock the API response for error case
        this.framework.mockRequest(
          `${BASE_URL}/v2/api/lambda/twitter/login`,
          {
            error: true,
            message: "Failed to get request token",
          },
          {
            status: 403,
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/lambda/twitter/login`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        // Assertions for error case
        this.framework.assertions.assertEquals(
          response.status,
          403,
          "Should return 403 status for error"
        );
        this.framework.assertions.assertEquals(
          response.body.error,
          true,
          "Error flag should be true"
        );
        this.framework.assertions.assertEquals(
          response.body.message,
          "Failed to get request token",
          "Should return correct error message"
        );
      });

      // Test case for Twitter Access Token
      this.framework.addTestCase("wxy Twitter Access Token - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v2/api/lambda/twitter/access-token?oauth_token=mock_token&oauth_verifier=mock_verifier`,
          {
            error: false,
            role: "wxy",
            access_token: "mock_access_token",
            refresh_token: "mock_refresh_token",
            expire_at: 3600,
            user_id: 123,
            is_newuser: false,
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/lambda/twitter/access-token?oauth_token=mock_token&oauth_verifier=mock_verifier`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Twitter Access Token should return 200 status"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(
          response,
          this.twitterLoginResponseSchema
        );
        this.framework.assertions.assertEquals(
          response.body.role,
          "wxy",
          "Role should be wxy"
        );
      });

      // Test case for Twitter Access Token - Error Path (Role not allowed)
      this.framework.addTestCase(
        "wxy Twitter Access Token - Role Not Allowed",
        async () => {
          // Mock the API response for error case
          this.framework.mockRequest(
            `${BASE_URL}/v2/api/lambda/twitter/access-token?oauth_token=mock_token&oauth_verifier=mock_verifier`,
            {
              error: true,
              message: "Forbidden access",
            },
            {
              status: 403,
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

          const response = await this.framework.makeRequest(
            `${BASE_URL}/v2/api/lambda/twitter/access-token?oauth_token=mock_token&oauth_verifier=mock_verifier`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

          // Assertions for error case
          this.framework.assertions.assertEquals(
            response.status,
            403,
            "Should return 403 status for forbidden access"
          );
          this.framework.assertions.assertEquals(
            response.body.error,
            true,
            "Error flag should be true"
          );
          this.framework.assertions.assertEquals(
            response.body.message,
            "Forbidden access",
            "Should return correct error message"
          );
        }
      );

      // Test case for Twitter Access Token - Error Path (Email access denied)
      this.framework.addTestCase(
        "wxy Twitter Access Token - Email Access Denied",
        async () => {
          // Mock the API response for error case
          this.framework.mockRequest(
            `${BASE_URL}/v2/api/lambda/twitter/access-token?oauth_token=mock_token&oauth_verifier=mock_verifier`,
            {
              error: true,
              message: "Could not access email address",
            },
            {
              status: 403,
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

          const response = await this.framework.makeRequest(
            `${BASE_URL}/v2/api/lambda/twitter/access-token?oauth_token=mock_token&oauth_verifier=mock_verifier`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

          // Assertions for error case
          this.framework.assertions.assertEquals(
            response.status,
            403,
            "Should return 403 status for email access denied"
          );
          this.framework.assertions.assertEquals(
            response.body.error,
            true,
            "Error flag should be true"
          );
          this.framework.assertions.assertEquals(
            response.body.message,
            "Could not access email address",
            "Should return correct error message"
          );
        }
      );
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new TwitterLoginTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
