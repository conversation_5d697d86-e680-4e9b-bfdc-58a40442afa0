const APITestFramework = require("../apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * Reset Password API Tests
 * Class-based implementation of the Reset Password API tests
 */
class ResetTests {
  constructor() {
    this.framework = new APITestFramework();
    this.baseUrl = BASE_URL;
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("wxy Reset Password API Tests", () => {
      // Test case for reset password
      this.framework.addTestCase(
        "wxy Reset Password API - Success",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/v1/api/xyz/wxy/lambda/reset`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                code: `${Math.floor(Math.random() * 10000000)}`,
                password: "a123456",
              }),
            }
          );

          // Assertions for the health check
          this.framework.assert(
            response.status === 200,
            "Reset wxy should return 200 status"
          );

          this.framework.assert(
            response.body.error === false,
            "Reset wxy error flag should be false"
          );
        }
      );

      // Add more test cases here as needed
      this.framework.addTestCase(
        "wxy Reset Password API - Invalid Code",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/v1/api/xyz/wxy/lambda/reset`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                code: "invalid-code",
                password: "a123456",
              }),
            }
          );

          // Assertions for invalid code
          this.framework.assert(
            response.status === 400,
            "Reset with invalid code should return 400 status"
          );

          this.framework.assert(
            response.body.error === true,
            "Reset with invalid code should have error flag set to true"
          );
        }
      );

      this.framework.addTestCase(
        "wxy Reset Password API - Invalid Password",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/v1/api/xyz/wxy/lambda/reset`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                code: `${Math.floor(Math.random() * 10000000)}`,
                password: "", // Empty password
              }),
            }
          );

          // Assertions for invalid password
          this.framework.assert(
            response.status === 400,
            "Reset with invalid password should return 400 status"
          );

          this.framework.assert(
            response.body.error === true,
            "Reset with invalid password should have error flag set to true"
          );
        }
      );
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      // without generating a report (let the test runner handle that)
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new ResetTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
