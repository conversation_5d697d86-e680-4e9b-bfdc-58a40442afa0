const APITestFramework = require("../../../../tests/apitesting.base.js");

const apiTest = new APITestFramework();

const BASE_URL = "http://localhost:5172";

class ForgotPasswordTests {
  constructor(baseUrl) {
    this.framework = new APITestFramework();
    this.baseUrl = BASE_URL;
    this.setupTests();
  }

  setupTests() {
    // Setup hooks
    this.framework.beforeEach(async () => {
      // Reset any test data or mocks before each test
      // Could initialize test database or mock services here
    });

    this.framework.afterEach(async () => {
      // Cleanup after each test
    });

    // Test cases for web endpoint
    this.framework.addTestCase(
      "Web - Should successfully send reset email",
      async () => {
        const response = await this.framework.makeRequest(
          `${this.baseUrl}/v1/api/xyz/wxy/lambda/forgot`,
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
            }),
          }
        );

        this.framework.assert(
          response.status === 200,
          `Expected status 200 but got ${response.status}`
        );
        this.framework.assert(
          response.body.error === false,
          "Expected error to be false"
        );
        this.framework.assert(
          response.body.message === "Email Sent",
          "Expected success message"
        );
      }
    );

    this.framework.addTestCase(
      "Web - Should fail with missing email",
      async () => {
        const response = await this.framework.makeRequest(
          `${this.baseUrl}/v1/api/xyz/wxy/lambda/forgot`,
          {
            method: "POST",
            body: JSON.stringify({}),
          }
        );

        this.framework.assert(
          response.status === 403,
          `Expected status 403 but got ${response.status}`
        );
        this.framework.assert(
          response.body.error === true,
          "Expected error to be true"
        );
        this.framework.assert(
          response.body.message === "Email Missing",
          "Expected email missing message"
        );
      }
    );

    this.framework.addTestCase("Web - Should fail for SSO user", async () => {
      const response = await this.framework.makeRequest(
        `${this.baseUrl}/v1/api/xyz/wxy/lambda/forgot`,
        {
          method: "POST",
          body: JSON.stringify({
            email: "<EMAIL>",
          }),
        }
      );

      this.framework.assert(
        response.status === 403,
        `Expected status 403 but got ${response.status}`
      );
      this.framework.assert(
        response.body.message === "Cannot do forgot password on single sign on",
        "Expected SSO error message"
      );
    });

    // Test cases for mobile endpoint
    this.framework.addTestCase(
      "Mobile - Should succeed with mobile user agent",
      async () => {
        const response = await this.framework.makeRequest(
          `${this.baseUrl}/v1/api/xyz/wxy/lambda/mobile/forgot`,
          {
            method: "POST",
            headers: {
              "User-Agent": "Mobile Android App",
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: "<EMAIL>",
            }),
          }
        );

        this.framework.assert(
          response.status === 200,
          `Expected status 200 but got ${response.status}`
        );
        this.framework.assert(
          response.body.error === false,
          "Expected error to be false"
        );
      }
    );

    this.framework.addTestCase(
      "Mobile - Should fail without mobile user agent",
      async () => {
        const response = await this.framework.makeRequest(
          `${this.baseUrl}/v1/api/xyz/wxy/lambda/mobile/forgot`,
          {
            method: "POST",
            headers: {
              "User-Agent": "Mozilla/5.0 Desktop Browser",
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: "<EMAIL>",
            }),
          }
        );

        this.framework.assert(
          response.status === 403,
          `Expected status 403 but got ${response.status}`
        );
        this.framework.assert(
          response.body.message ===
            "Forbidden: This endpoint is only accessible from the mobile app.",
          "Expected mobile-only error message"
        );
      }
    );

    this.framework.addTestCase(
      "Should fail for non-existent user",
      async () => {
        const response = await this.framework.makeRequest(
          `${this.baseUrl}/v1/api/xyz/wxy/lambda/forgot`,
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
            }),
          }
        );

        this.framework.assert(
          response.status === 403,
          `Expected status 403 but got ${response.status}`
        );
        this.framework.assert(
          response.body.message === "Cannot find User",
          "Expected user not found message"
        );
      }
    );
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new ForgotPasswordTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
