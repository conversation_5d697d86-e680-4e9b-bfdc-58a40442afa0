const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * Magic Login API Tests
 * Class-based implementation of the Magic Login API tests
 */
class MagicLoginTests {
  constructor() {
    this.framework = new APITestFramework();
    
    // Define expected response schemas
    this.magicLoginSchema = {
      error: false,
      message: "string"
    };
    
    this.magicLoginVerifySchema = {
      error: false,
      role: "string",
      access_token: "string",
      refresh_token: "string",
      expire_at: "number",
      user_id: "number"
    };
    
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("wxy Magic Login API Tests", () => {
      // Setup before each test
      this.framework.beforeEach(async () => {
        // Setup mock data if needed
      });

      // Test case for Magic Login Request
      this.framework.addTestCase("wxy Magic Login Request - Success Path", async () => {
        // Create spy to track request
        const requestSpy = this.framework.createSpy(this.framework, "makeRequest");

        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v2/api/lambda/magic/login`,
          {
            error: false,
            message: "Magic link sent successfully"
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/lambda/magic/login`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              email: "<EMAIL>",
              role: "wxy",
              projectId: "test_project"
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Magic Login Request should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Magic Login Request error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.magicLoginSchema);
        this.framework.assertions.assertEquals(
          response.body.message,
          "Magic link sent successfully",
          "Should return success message"
        );

        // Verify request was made correctly
        this.framework.assert(
          requestSpy.callCount() === 1,
          "API should be called exactly once"
        );
      });

      // Test case for Magic Login Request - Error Path (Invalid Email)
      this.framework.addTestCase("wxy Magic Login Request - Invalid Email", async () => {
        // Mock the API response for error case
        this.framework.mockRequest(
          `${BASE_URL}/v2/api/lambda/magic/login`,
          {
            error: true,
            message: "Invalid email address"
          },
          {
            status: 400,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/lambda/magic/login`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              email: "invalid-email",
              role: "wxy",
              projectId: "test_project"
            })
          }
        );

        // Assertions for error case
        this.framework.assertions.assertEquals(
          response.status,
          400,
          "Should return 400 status for invalid email"
        );
        this.framework.assertions.assertEquals(
          response.body.error,
          true,
          "Error flag should be true"
        );
        this.framework.assertions.assertEquals(
          response.body.message,
          "Invalid email address",
          "Should return correct error message"
        );
      });

      // Test case for Magic Login Verify
      this.framework.addTestCase("wxy Magic Login Verify - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v2/api/lambda/magic/verify`,
          {
            error: false,
            role: "wxy",
            access_token: "mock_access_token",
            refresh_token: "mock_refresh_token",
            expire_at: 3600,
            user_id: 123
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/lambda/magic/verify`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              token: "mock_magic_token",
              projectId: "test_project"
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Magic Login Verify should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Magic Login Verify error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.magicLoginVerifySchema);
        this.framework.assertions.assertEquals(
          response.body.role,
          "wxy",
          "Role should be wxy"
        );
      });

      // Test case for Magic Login Verify - Error Path (Invalid Token)
      this.framework.addTestCase("wxy Magic Login Verify - Invalid Token", async () => {
        // Mock the API response for error case
        this.framework.mockRequest(
          `${BASE_URL}/v2/api/lambda/magic/verify`,
          {
            error: true,
            message: "Invalid or expired token"
          },
          {
            status: 403,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/lambda/magic/verify`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              token: "invalid_token",
              projectId: "test_project"
            })
          }
        );

        // Assertions for error case
        this.framework.assertions.assertEquals(
          response.status,
          403,
          "Should return 403 status for invalid token"
        );
        this.framework.assertions.assertEquals(
          response.body.error,
          true,
          "Error flag should be true"
        );
        this.framework.assertions.assertEquals(
          response.body.message,
          "Invalid or expired token",
          "Should return correct error message"
        );
      });

      // Test case for Magic Login Mobile
      this.framework.addTestCase("wxy Magic Login Mobile - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v2/api/lambda/magic/login/mobile`,
          {
            error: false,
            message: "Magic link sent successfully"
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v2/api/lambda/magic/login/mobile`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              email: "<EMAIL>",
              role: "wxy",
              projectId: "test_project",
              redirect_url: "myapp://auth"
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Magic Login Mobile should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Magic Login Mobile error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.magicLoginSchema);
        this.framework.assertions.assertEquals(
          response.body.message,
          "Magic link sent successfully",
          "Should return success message"
        );
      });
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new MagicLoginTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
