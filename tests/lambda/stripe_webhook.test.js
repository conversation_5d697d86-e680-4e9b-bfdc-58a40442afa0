const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * Stripe Webhook API Tests
 * Class-based implementation of the Stripe Webhook API tests
 */
class StripeWebhookTests {
  constructor() {
    this.framework = new APITestFramework();
    
    // Define expected response schema
    this.webhookResponseSchema = {
      error: false,
      message: "string"
    };
    
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("wxy Stripe Webhook API Tests", () => {
      let mockStripeSignature;

      // Setup before each test
      this.framework.beforeEach(async () => {
        // Setup mock data
        mockStripeSignature = "whsec_mock_signature";
      });

      // Test case for Stripe Webhook - Customer Subscription Created
      this.framework.addTestCase("wxy Stripe Webhook - Customer Subscription Created", async () => {
        // Create spy to track request
        const requestSpy = this.framework.createSpy(this.framework, "makeRequest");

        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/webhook`,
          {
            error: false,
            message: "Webhook processed successfully"
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/webhook`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Stripe-Signature": mockStripeSignature
            },
            body: JSON.stringify({
              id: "evt_123456",
              object: "event",
              type: "customer.subscription.created",
              data: {
                object: {
                  id: "sub_123456",
                  customer: "cus_123456",
                  status: "active",
                  items: {
                    data: [
                      {
                        id: "si_123456",
                        price: {
                          id: "price_123456",
                          product: "prod_123456"
                        }
                      }
                    ]
                  }
                }
              }
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Stripe Webhook should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Stripe Webhook error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.webhookResponseSchema);
        this.framework.assertions.assertEquals(
          response.body.message,
          "Webhook processed successfully",
          "Should return success message"
        );

        // Verify request was made correctly
        this.framework.assert(
          requestSpy.callCount() === 1,
          "API should be called exactly once"
        );
      });

      // Test case for Stripe Webhook - Customer Subscription Updated
      this.framework.addTestCase("wxy Stripe Webhook - Customer Subscription Updated", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/webhook`,
          {
            error: false,
            message: "Webhook processed successfully"
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/webhook`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Stripe-Signature": mockStripeSignature
            },
            body: JSON.stringify({
              id: "evt_123457",
              object: "event",
              type: "customer.subscription.updated",
              data: {
                object: {
                  id: "sub_123456",
                  customer: "cus_123456",
                  status: "active",
                  items: {
                    data: [
                      {
                        id: "si_123456",
                        price: {
                          id: "price_123456",
                          product: "prod_123456"
                        }
                      }
                    ]
                  }
                }
              }
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Stripe Webhook should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Stripe Webhook error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.webhookResponseSchema);
      });

      // Test case for Stripe Webhook - Customer Subscription Deleted
      this.framework.addTestCase("wxy Stripe Webhook - Customer Subscription Deleted", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/webhook`,
          {
            error: false,
            message: "Webhook processed successfully"
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/webhook`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Stripe-Signature": mockStripeSignature
            },
            body: JSON.stringify({
              id: "evt_123458",
              object: "event",
              type: "customer.subscription.deleted",
              data: {
                object: {
                  id: "sub_123456",
                  customer: "cus_123456",
                  status: "canceled",
                  items: {
                    data: [
                      {
                        id: "si_123456",
                        price: {
                          id: "price_123456",
                          product: "prod_123456"
                        }
                      }
                    ]
                  }
                }
              }
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Stripe Webhook should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Stripe Webhook error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(response, this.webhookResponseSchema);
      });

      // Test case for Stripe Webhook - Invalid Signature
      this.framework.addTestCase("wxy Stripe Webhook - Invalid Signature", async () => {
        // Mock the API response for error case
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/webhook`,
          {
            error: true,
            message: "Invalid signature"
          },
          {
            status: 400,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/webhook`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Stripe-Signature": "invalid_signature"
            },
            body: JSON.stringify({
              id: "evt_123456",
              object: "event",
              type: "customer.subscription.created",
              data: {
                object: {
                  id: "sub_123456",
                  customer: "cus_123456"
                }
              }
            })
          }
        );

        // Assertions for error case
        this.framework.assertions.assertEquals(
          response.status,
          400,
          "Should return 400 status for invalid signature"
        );
        this.framework.assertions.assertEquals(
          response.body.error,
          true,
          "Error flag should be true"
        );
        this.framework.assertions.assertEquals(
          response.body.message,
          "Invalid signature",
          "Should return correct error message"
        );
      });

      // Test case for Stripe Webhook - Unsupported Event Type
      this.framework.addTestCase("wxy Stripe Webhook - Unsupported Event Type", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/stripe/webhook`,
          {
            error: false,
            message: "Event type not handled"
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json"
            }
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/stripe/webhook`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Stripe-Signature": mockStripeSignature
            },
            body: JSON.stringify({
              id: "evt_123459",
              object: "event",
              type: "unsupported.event.type",
              data: {
                object: {
                  id: "obj_123456"
                }
              }
            })
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Stripe Webhook should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Stripe Webhook error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertEquals(
          response.body.message,
          "Event type not handled",
          "Should return correct message for unsupported event"
        );
      });
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new StripeWebhookTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
