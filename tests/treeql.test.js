const APITestFramework = require('./apitesting.base.js');
const config = require('../config.js');
const JwtService = require('../baas/services/JwtService');

/**
 * TreeQL API Tests
 * Class-based implementation of the TreeQL API tests
 */
class TreeQLTests {
  constructor() {
    this.framework = new APITestFramework();
    this.BASE_URL = 'http://localhost:5172';
    
    this.setupTests();
  }

  // JWT Token generation helper
  async getSuperAdminToken() {
    const payload = {
      user_id: 1,
      role: 'super_admin'
    };
    return JwtService.createAccessToken(
      payload, 
      config.access_jwt_expire, 
      config.jwt_key
    );
  }

  setupTests() {
    this.framework.describe("wxy TreeQL API Tests", () => {
      // Add test cases
      this.framework.addTestCase('wxy Get All Switches', async () => {
        // Create JWT token with user_id=1 and role=super_admin
        const token = await this.getSuperAdminToken();

        const requestOptions = {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        };

        const response = await this.framework.makeRequest(
          `${this.BASE_URL}/api/v1/records/testing/super_admin/switchs`, 
          requestOptions
        );

        // Assertions
        this.framework.assert(response.status === 200, 'Get All Switches should return 200 status');
        this.framework.assert(response.body, 'Response should have body');
        this.framework.assert(Array.isArray(response.body), 'Response body should be an array');
      });

      // Optional: Commented out test case for creating a switch
      // this.framework.addTestCase('wxy Create Switch', async () => {
      //   const token = await this.getSuperAdminToken();
      //   
      //   const requestOptions = {
      //     method: 'POST',
      //     headers: {
      //       'Content-Type': 'application/json',
      //       'Authorization': `Bearer ${token}`
      //     },
      //     body: JSON.stringify({
      //       name: 'Test Switch',
      //       status: 1
      //     })
      //   };
      // 
      //   const response = await this.framework.makeRequest(
      //     `${this.BASE_URL}/api/v1/treeql/switchs`, 
      //     requestOptions
      //   );
      // 
      //   this.framework.assert(response.status === 201, 'Create Switch should return 201 status');
      //   this.framework.assert(response.body, 'Response should have body');
      //   this.framework.assert(response.body.id, 'Response should return created switch ID');
      // });
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new TreeQLTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
