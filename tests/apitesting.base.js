/**
 * Create a framework for api testing without using any external libraries
 * To Start:
 * 1. define the structure of the api testing
 * 2. create before and after hooks for what info we need
 * 3. create a way to run the tests
 * 4. create a way to see results of test report without crashing other tests
 */

class APITestFramework {
  constructor(config = {}) {
    // Store test cases and results
    this.testCases = [];
    this.testResults = {
      passed: [],
      failed: [],
      total: 0,
      duration: 0,
    };

    // Hooks for setup and teardown
    this.beforeEachHook = null;
    this.afterEachHook = null;
    this.customMatchers = new Map();
    this.mocks = new Map();
    this.originalImplementations = new Map();
    this.environment = config.environment || "testenv";
    this.fixtures = new Map();
    this.databases = new Map();
    this.testSuites = new Map();
    this.currentSuite = null;
  }

  // Environment management
  async setupTestEnvironment() {
    // Load environment-specific configuration
    const config = require(`../config.${this.environment}.js`);

    // Setup test database
    if (config.database) {
      await this.setupTestDatabase(config.database);
    }

    return config;
  }

  // Database management
  async setupTestDatabase(config) {
    const dbMap = {
      MySQL: "mysql",
      PostgreSQL: "postgresql",
      JSON: "json",
    };

    const MyDBConnector = require(`../baas/db_connector/${dbMap[config.type]}`);
    const dbConnector = new MyDBConnector(config);
    await dbConnector.initialize();
    this.databases.set("default", dbConnector);

    return {
      async cleanup() {
        const db = this.databases.get("default");
        if (db && db.pool) {
          await db.pool.end();
        }
        this.databases.delete("default");
      },
    };
  }

  // Fixture management
  async useFixture(name, setupFn) {
    if (!this.fixtures.has(name)) {
      const fixture = await setupFn();
      this.fixtures.set(name, fixture);
    }
    return this.fixtures.get(name);
  }

  async cleanupFixtures() {
    for (const [name, fixture] of this.fixtures) {
      if (fixture.cleanup) {
        await fixture.cleanup();
      }
    }
    this.fixtures.clear();
  }

  // 1. Define structure of API testing
  addTestCase(name, testFunction) {
    if (this.currentSuite) {
      this.currentSuite.tests.push({ name, fn: testFunction });
    } else {
      this.testCases.push({ name, testFunction });
    }
  }

  // 2. Create before and after hooks
  beforeEach(hookFunction) {
    this.beforeEachHook = hookFunction;
  }

  afterEach(hookFunction) {
    this.afterEachHook = hookFunction;
  }

  // Test organization
  describe(suiteName, fn) {
    const suite = {
      name: suiteName,
      tests: [],
      before: null,
      after: null,
      beforeEach: null,
      afterEach: null,
    };

    this.currentSuite = suite;
    fn();
    this.testSuites.set(suiteName, suite);
    this.currentSuite = null;
  }

  // Enhanced test runner
  async runTests() {
    const startTime = Date.now();
    const results = {
      suites: [],
      totalPassed: 0,
      totalFailed: 0,
      duration: 0,
      failedTests: [],
    };

    try {
      // Run suite-based tests
      for (const [suiteName, suite] of this.testSuites) {
        const suiteResult = {
          name: suiteName,
          passed: [],
          failed: [],
          duration: 0,
        };

        const suiteStartTime = Date.now();

        try {
          if (suite.before) await suite.before();

          for (const test of suite.tests) {
            const testStartTime = Date.now();

            try {
              if (suite.beforeEach) await suite.beforeEach();
              if (this.beforeEachHook) await this.beforeEachHook();

              await test.fn();

              suiteResult.passed.push({
                name: test.name,
                duration: Date.now() - testStartTime,
              });
              results.totalPassed++;
            } catch (error) {
              suiteResult.failed.push({
                name: test.name,
                error: error.message,
                duration: Date.now() - testStartTime,
              });
              results.totalFailed++;
              results.failedTests.push({
                suite: suiteName,
                test: test.name,
                error: error.message,
              });
            } finally {
              if (this.afterEachHook) await this.afterEachHook();
              if (suite.afterEach) await suite.afterEach();
            }
          }
        } finally {
          if (suite.after) await suite.after();
        }

        suiteResult.duration = Date.now() - suiteStartTime;
        results.suites.push(suiteResult);
      }

      // Run standalone tests
      for (const test of this.testCases) {
        const testStartTime = Date.now();

        try {
          if (this.beforeEachHook) await this.beforeEachHook();
          //   console.log("test >>", test.testFunction.toString());
          await test.testFunction();

          this.testResults.passed.push({
            name: test.name,
            duration: Date.now() - testStartTime,
          });
          results.totalPassed++;
        } catch (error) {
          this.testResults.failed.push({
            name: test.name,
            error: error.message,
            duration: Date.now() - testStartTime,
          });
          results.totalFailed++;
          results.failedTests.push({
            test: test.name,
            error: error.message,
          });
        } finally {
          if (this.afterEachHook) await this.afterEachHook();
        }
      }
    } catch (error) {
      console.error("Test execution error:", error);
      throw error;
    }

    results.duration = Date.now() - startTime;
    const passed = results.suites.flatMap((suite) => suite.passed);
    const failed = results.suites.flatMap((suite) => suite.failed);
    this.testResults = {
      ...this.testResults,
      passed: [...this.testResults.passed, ...passed],
      failed: [...this.testResults.failed, ...failed],
      total: results.totalPassed + results.totalFailed,
      duration: results.duration,
    };

    return results;
  }

  // Enhanced test report generation
  generateTestReport() {
    console.log("\n=== API Test Report ===");

    // console.log(`Tests: ${JSON.stringify(this.testResults, null, 2)}`);
    console.log(`Total Tests: ${this.testResults?.total}`);
    console.log(`Passed: ${this.testResults?.passed?.length}`);
    console.log(`Failed: ${this.testResults?.failed?.length}`);
    console.log(`Duration: ${this.testResults?.duration}ms`);

    if (this.testResults.failed.length > 0) {
      console.log("\nFailed Tests:");
      this.testResults.failed.forEach((failedTest) => {
        console.log(`\n❌ ${failedTest.name}`);
        console.log(`   Error: ${failedTest.error}`);
        console.log(`   Duration: ${failedTest.duration}ms`);
      });
    }

    if (this.testResults.total === 0) {
      console.log("\n⚠️  Warning: No tests were executed!");
    }

    return {
      total: this.testResults.total,
      passed: this.testResults.passed.length,
      failed: this.testResults.failed.length,
      failedTests: this.testResults.failed,
      duration: this.testResults.duration,
    };
  }

  // Utility method for assertions
  assert(condition, message) {
    if (!condition) {
      throw new Error(message || "Assertion failed");
    }
  }

  // Advanced assertions
  assertions = {
    assertEquals: (actual, expected, message) => {
      this.assert(
        JSON.stringify(actual) === JSON.stringify(expected),
        message ||
          `Expected ${JSON.stringify(expected)} but got ${JSON.stringify(
            actual
          )}`
      );
    },

    assertMatch: (actual, pattern, message) => {
      this.assert(
        pattern.test(actual),
        message || `Expected ${actual} to match ${pattern}`
      );
    },

    assertType: (value, type, message) => {
      this.assert(
        typeof value === type,
        message || `Expected type ${type} but got ${typeof value}`
      );
    },

    assertThrows: async (fn, errorType, message) => {
      try {
        await fn();
        throw new Error(
          message || `Expected function to throw ${errorType.name}`
        );
      } catch (error) {
        this.assert(
          error instanceof errorType,
          message ||
            `Expected error of type ${errorType.name} but got ${error.constructor.name}`
        );
      }
    },

    assertResponseValid: (response, schema) => {
      // Validate API response against schema
      const validate = (obj, schemaObj) => {
        for (const [key, value] of Object.entries(schemaObj)) {
          if (!(key in obj)) {
            throw new Error(`Missing required field: ${key}`);
          }
          if (typeof obj[key] !== typeof value) {
            throw new Error(
              `Invalid type for ${key}: expected ${typeof value}, got ${typeof obj[
                key
              ]}`
            );
          }
        }
      };

      validate(response.body, schema);
    },
  };

  // Utility method for async HTTP requests
  async makeRequest(url, options = {}) {
    const defaultOptions = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    };

    const mergedOptions = { ...defaultOptions, ...options };

    // Start timing
    const startTime = performance.now();

    try {
      const response = await fetch(url, mergedOptions);

      // End timing
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Try to parse response body
      let responseBody;
      try {
        responseBody = await response.json();
      } catch (parseError) {
        responseBody = null;
      }

      return {
        status: response.status,
        body: responseBody,
        headers: Object.fromEntries(response.headers.entries()),
        url: url,
        requestOptions: mergedOptions,
        timing: {
          startTime,
          endTime,
          duration: Number(duration.toFixed(2)), // ms with 2 decimal places
          durationUnit: "ms",
        },
      };
    } catch (error) {
      // End timing for error case
      const endTime = performance.now();
      const duration = endTime - startTime;

      const errorDetails = {
        url: url,
        method: mergedOptions.method,
        headers: mergedOptions.headers,
        body: mergedOptions.body ? JSON.parse(mergedOptions.body) : null,
        errorMessage: error.message,
        timing: {
          startTime,
          endTime,
          duration: Number(duration.toFixed(2)),
          durationUnit: "ms",
        },
      };

      throw new Error(`Request failed:
URL: ${errorDetails.url}
Method: ${errorDetails.method}
Headers: ${JSON.stringify(errorDetails.headers)}
Body: ${JSON.stringify(errorDetails.body)}
Duration: ${errorDetails.timing.duration}ms
Error: ${errorDetails.errorMessage}`);
    }
  }

  // Mocking system
  mock(target, property, implementation) {
    const key = `${target.constructor.name}.${property}`;
    this.originalImplementations.set(key, target[property]);
    this.mocks.set(key, implementation);
    target[property] = implementation;

    return {
      restore: () => {
        target[property] = this.originalImplementations.get(key);
        this.mocks.delete(key);
        this.originalImplementations.delete(key);
      },
    };
  }

  // HTTP request mocking
  mockRequest(url, response, options = {}) {
    const mockResponse = {
      status: options.status || 200,
      body: response,
      headers: options.headers || {},
      ...options,
    };

    this.mock(this, "makeRequest", async (requestUrl) => {
      if (requestUrl === url) {
        return mockResponse;
      }
      throw new Error(`Unexpected request to ${requestUrl}`);
    });
  }

  // Spy functionality
  createSpy(target, method) {
    const calls = [];
    const original = target[method];

    const spy = {
      calls,
      callCount: () => calls.length,
      calledWith: (...args) =>
        calls.some((call) => JSON.stringify(call) === JSON.stringify(args)),
      restore: () => {
        target[method] = original;
      },
    };

    target[method] = (...args) => {
      calls.push(args);
      return original.apply(target, args);
    };

    return spy;
  }
}

// Export the framework for use in other test files
module.exports = APITestFramework;
