/**
 * TODO:
 * 1. Load in the testing framework from apitesting.base.js
 * 2. server is live at localhost:5172 already
 * 3. test api for health check /api/v1/health, expected response is {message: 'ok', error: false}
 */

// 1. Load in the testing framework from apitesting.base.js
const APITestFramework = require('./apitesting.base.js');

// Create an instance of the API Test Framework
const apiTest = new APITestFramework();

// 2. Server is live at localhost:5172 already
const BASE_URL = 'http://localhost:5172';

// 3. Test API for health check
apiTest.addTestCase('Health Check API', async () => {
    // Make a request to the health check endpoint
    const response = await apiTest.makeRequest(`${BASE_URL}/api/v1/health`);

    // Assertions for the health check
    apiTest.assert(response.status === 200, 'Health check should return 200 status');
    apiTest.assert(
        response.body.message === 'ok', 
        'Health check response should have message "ok"'
    );
    apiTest.assert(
        response.body.error === false, 
        'Health check error flag should be false'
    );
});

apiTest.addTestCase('Health Check API Does Not Exist', async () => {
    // Make a request to the health check endpoint
    const response = await apiTest.makeRequest(`${BASE_URL}/api/v1/healths`);
    // Assertions for the health check
    apiTest.assert(response.status === 404, 'Health check should return 404 status');
});

apiTest.addTestCase('Health Check API Performance', async () => {
    const response = await apiTest.makeRequest(`${BASE_URL}/api/v1/health`);

    // Assert on response time
    apiTest.assert(
        response.timing.duration < 100, 
        `Health check should respond in less than 100ms (actual: ${response.timing.duration}ms)`
    );

    // Log additional timing information
    console.log(`Request Details:
    URL: ${response.url}
    Status: ${response.status}
    Duration: ${response.timing.duration}ms`);
});

// Run the tests and generate report
apiTest.runTests().then(() => {
    const report = apiTest.generateTestReport();
    
    // Optional: You could add additional reporting or logging here
    if (report.failed > 0) {
        process.exit(1);
    }
}).catch(error => {
    console.error('Test framework error:', error);
    process.exit(1);
});