#!/usr/bin/env node

const fs = require("fs");
const { execSync } = require("child_process");
const path = require("path");

// Helper function to run shell commands
function runCommand(command) {
  try {
    console.log(`Running: ${command}`);
    const output = execSync(command, {
      stdio: ["inherit", "pipe", "pipe"],
      encoding: "utf-8"
    });
    console.log(output);
  } catch (error) {
    console.error(`<PERSON><PERSON><PERSON> executing command: ${command}`);
    console.error("Error details:", {
      message: error.message,
      stdout: error.stdout?.toString(),
      stderr: error.stderr?.toString(),
      status: error.status,
      signal: error.signal
    });
    throw error;
  }
}

// Helper function to ensure directory exists
function ensureDir(dirPath) {
  try {
    fs.mkdirSync(dirPath, { recursive: true });
  } catch (error) {
    console.error(`Error creating directory: ${dirPath}`);
    throw error;
  }
}

// Helper function to copy file
function copyFile(src, dest) {
  try {
    fs.copyFileSync(src, dest);
  } catch (error) {
    console.error(`Error copying file from ${src} to ${dest}`);
    throw error;
  }
}

// Main setup function
async function setupProject(project, configPath) {
  const projectName = project.includes("_backend")
    ? project
    : `${project}_backend`;
  const [projectWithOutBackend] = project.includes("_backend")
    ? project.split("_backend")
    : [project];

  const baseDir = process.cwd();
  const index = path.join(baseDir, "custom", "index.js");
  const projectDir = path.join(baseDir, "custom", projectName);
  const configDir = path.join(projectDir, "configuration");
  const destConfigPath = path.join(configDir, path.basename(configPath));

  // Step 1: Create project directory
  ensureDir(configDir);

  // Step 2: Copy configuration files
  copyFile(index, path.join(projectDir, path.basename(index)));
  copyFile(configPath, destConfigPath);
  copyFile(index, path.join(configDir, path.basename(index)));

  // Step 3: Run migration commands
  const migrationCommands = [
    `node migration.js models ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js roles ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js api ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js create ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js drop ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js seed ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js transform ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js sdk ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js postman ${destConfigPath} ${projectWithOutBackend}`
  ];

  for (const command of migrationCommands) {
    runCommand(command);
  }

  return {
    projectDir,
    configDir,
    destConfigPath
  };
}

// If running as script
if (require.main === module) {
  const args = process.argv.slice(2);

  if (args.length !== 4 || args[0] !== "--name" || args[2] !== "--path") {
    console.error(
      "Usage: node setup_baas_project --name <project_name> --path <file_path>"
    );
    process.exit(1);
  }

  const projectName = args[1];
  const filePath = args[3];

  if (!fs.existsSync(filePath)) {
    console.error(`Error: Configuration file not found at "${filePath}"`);
    process.exit(1);
  }

  setupProject(projectName, filePath)
    .then(() => console.log("Project setup completed successfully!"))
    .catch((error) => {
      console.error("An error occurred during setup:", error);
      process.exit(1);
    });
}

async function buildBackend(
  project,
  config = null,
  repo = null,
  branch = "master"
) {
  if (!config) return;

  const projectName = project.includes("_backend")
    ? project
    : `${project}_backend`;
  const [projectWithOutBackend] = project.includes("_backend")
    ? project.split("_backend")
    : [project];

  const baseDir = process.cwd();
  const index = path.join(baseDir, "custom", "index.js");
  const projectDir = path.join(baseDir, "custom", projectName);
  const configDir = path.join(projectDir, "configuration");
  const destConfigPath = path.join(configDir, "config.json");

  ensureDir(configDir);

  copyFile(index, path.join(projectDir, path.basename(index)));
  fs.writeFileSync(destConfigPath, config);
  copyFile(index, path.join(configDir, path.basename(index)));

  const migrationCommands = [
    `node migration.js models ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js roles ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js api ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js create ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js drop ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js seed ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js transform ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js sdk ${destConfigPath} ${projectWithOutBackend}`,
    `node migration.js postman ${destConfigPath} ${projectWithOutBackend}`
  ];

  for (const command of migrationCommands) {
    runCommand(command);
  }

  // Initialize git repository if repo URL is provided
  if (repo) {
    try {
      // Change to project directory
      process.chdir(projectDir);

      // Initialize git repository
      runCommand("git init");

      // Initial commit
      runCommand("git add .");
      runCommand('git commit -m "Initial commit"');

      // Add remote origin
      runCommand(`git remote add origin ${repo}`);

      // Pull then push to branch, continue if fails
      try {
        runCommand(`git pull origin ${branch} --allow-unrelated-histories`);
      } catch (error) {
        console.warn(
          "Warning: Failed to pull from remote repository it may be clean: ",
          error.message
        );
      }
      runCommand(`git push -u origin ${branch}`);

      // Change back to original directory
      process.chdir(baseDir);
    } catch (error) {
      console.error("Error during git initialization:", error);
      // Change back to original directory even if there's an error
      process.chdir(baseDir);
      throw error;
    }
  }

  return {
    projectDir,
    configDir,
    destConfigPath,
    repo
  };
}

module.exports = {
  setupProject,
  runCommand,
  ensureDir,
  copyFile,
  buildBackend
};
