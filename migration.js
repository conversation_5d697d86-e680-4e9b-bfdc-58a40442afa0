const fs = require("fs");
const path = require("path");
const PasswordService = require("./baas/services/PasswordService");
const {
  buildLambdaSdk,
  buildApiSdk,
  buildTreeqlSDK,
  getSDKWrapper,
} = require("./build_sdk");
const { generateAdminPages } = require("./build_admin_pages");
const useSDKTemplate = require("./templates/useSDKTemplate");
const useSDKIndexTemplate = require("./templates/useSDKIndexTemplate");
const { StringCaser } = require("./utils");

const stringCaser = new StringCaser();
// Type mapping from config to MySQL
const TYPE_MAPPING = {
  "primary key": "INT AUTO_INCREMENT PRIMARY KEY",
  string: "VARCHAR(512)",
  text: "TEXT",
  "long text": "LONGTEXT",
  "medium text": "MEDIUMTEXT",
  integer: "INT",
  int: "INT",
  double: "DOUBLE",
  float: "FLOAT",
  "big number": "BIGINT",
  boolean: "BOOLEAN",
  date: "DATE",
  datetime: "DATETIME",
  timestamp: "TIMESTAMP",
  uuid: "VARCHAR(36)",
  password: "VARCHAR(100)",
  json: "TEXT",
  mapping: "INT",
  "foreign key": "INT",
};

/**
 * Generates SQL CREATE TABLE statement for a given model
 * @param {Object} model - The model configuration object
 * @param {string} modelNamespace - The namespace prefix for the table
 * @returns {string} SQL CREATE TABLE statement
 */
function generateCreateTableSQL(model, modelNamespace) {
  const tableName = `${modelNamespace}_${model.name}`;
  let sql = `CREATE TABLE IF NOT EXISTS ${tableName} (\n`;

  // Add timestamp columns created_at and updated_at
  model.fields.push({
    name: "created_at",
    type: "timestamp",
    defaultValue: "CURRENT_TIMESTAMP",
  });
  model.fields.push({
    name: "updated_at",
    type: "timestamp",
    defaultValue: "CURRENT_TIMESTAMP",
    onUpdate: "CURRENT_TIMESTAMP",
  });

  // Track unique fields by name
  const uniqueFields = new Map();
  model.fields.forEach((field) => {
    // Only keep the first occurrence of each field name
    if (!uniqueFields.has(field.name)) {
      uniqueFields.set(field.name, field);
    }
  });

  const columns = Array.from(uniqueFields.values())
    .map((field) => {
      let columnDef = `  \`${field.name}\` ${TYPE_MAPPING[field.type]}`;

      // Handle default values
      if (field.defaultValue) {
        if (field.defaultValue === "CURRENT_TIMESTAMP") {
          columnDef += ` DEFAULT ${field.defaultValue}`;
        } else if (field.type === "json") {
        } else if (field.type === "mapping") {
          columnDef += ` DEFAULT ${field.defaultValue}`;
        } else if (field.defaultValue) {
          columnDef += ` DEFAULT '${field.defaultValue}'`;
        }
      }

      // Handle required fields
      if (field.validation && field.validation.includes("required")) {
        columnDef += " NOT NULL";
      }

      return columnDef;
    })
    .join(",\n");

  sql += columns;
  sql += "\n);\n\n";

  return sql;
}

/**
 * Generates SQL DROP TABLE statement for a given model
 * @param {Object} model - The model configuration object
 * @param {string} modelNamespace - The namespace prefix for the table
 * @returns {string} SQL DROP TABLE statement
 */
function generateDropTableSQL(model, modelNamespace) {
  const tableName = `${modelNamespace}_${model.name}`;
  return `DROP TABLE IF EXISTS ${tableName};\n`;
}

/**
 * Generates a relation method for a specific model field
 * @param {string} fieldName - The name of the field with a relation
 * @param {string} relationModel - The name of the related model
 * @returns {string} A method string for creating a related model instance
 */
function generateRelationMethod(relationModel) {
  const capitalizedModel =
    relationModel.charAt(0).toUpperCase() + relationModel.slice(1);
  return `
    get${capitalizedModel}(fields) {
      const ${relationModel}Model = require('./${relationModel}.js');
      return new ${relationModel}Model(fields);
    }
  `;
}

/**
 * Generates relation methods for all relation fields in a model
 * @param {Object} model - The model configuration object
 * @returns {string} Concatenated relation method strings
 */
function generateRelationMethods(model) {
  return model.fields
    .filter((field) => field.relation)
    .map((field) => generateRelationMethod(field.relation))
    .join("\n");
}

/**
 * Generates the content for a model class
 * @param {Object} model - The model configuration object
 * @returns {string} Complete model class source code
 */
function generateModelContent(model) {
  // First, find any mapping fields
  const mappingFields = model.fields.filter(
    (field) => field.type === "mapping" && field.mapping
  );

  // Generate transform functions for mapping fields
  const transformFunctions = mappingFields
    .map((field) => {
      try {
        const mappingPairs = field.mapping.split(",").map((pair) => {
          const [key, val] = pair.split(":");
          if (!key || !val) {
            console.warn(
              `Warning: Invalid mapping format for field ${field.name}`
            );
            return ["", ""];
          }
          return [key, val];
        });

        return `
    transform${
      field.name.charAt(0).toUpperCase() +
      field.name.slice(1).replace(/_./g, (match) => match[1].toUpperCase())
    }(value) {
      const mappings = {
        ${mappingPairs
          .filter(([key]) => key)
          .map(([key, val]) => `'${key}': '${val}'`)
          .join(",\n        ")}
      };
      return mappings[value] || value;
    }`;
      } catch (error) {
        console.warn(
          `Warning: Could not process mapping for field ${field.name}:`,
          error
        );
        return "";
      }
    })
    .filter(Boolean)
    .join("\n\n");

  const mappingObject = {};
  mappingFields.forEach((field) => {
    const mappingPairs = field.mapping.split(",").map((pair) => {
      const [key, val] = pair.split(":");
      return [key, val];
    });
    mappingObject[field.name] = Object.fromEntries(mappingPairs);
  });

  const mappingFunctions =
    Object.keys(mappingObject).length > 0
      ? `\n    static mapping () { \n     return ${JSON.stringify(
          mappingObject,
          null,
          6
        )}; \n    }`
      : "";

  return `
    const BaseModel = require('../../../baas/core/BaseModel');

    class ${model.name} extends BaseModel {
      static schema() {
        return ${JSON.stringify(
          model.fields.map((field) => ({
            name: field.name,
            type: field.type,
            validation: field.validation || [],
            defaultValue: field.defaultValue || null,
            mapping: field.mapping || null,
          })),
          null,
          7
        )};
      }

      ${transformFunctions}
    ${mappingFunctions}
      ${generateRelationMethods(model)}
    }

    module.exports = ${model.name};
  `;
}

/**
 * Generates a role model class
 * @param {Object} role - The role configuration object
 * @param {number} index - The index of the role
 * @returns {string} Complete role model class source code
 */
function generateRoleModel(role, index) {
  // Get list of allowed models from treeql permissions
  const treeqlModels = role.permissions?.treeql?.models || {};
  const allowedModels = Object.entries(treeqlModels)
    .filter(([_, config]) => config.allowed)
    .map(([modelName]) => modelName);

  return `
    const BaseRole = require('../../../baas/core/BaseRole');

    class ${role.slug} extends BaseRole {
      static id = '${role.id}';
      static name = '${role.name}';
      static slug = '${role.slug}';
      static permissions = ${JSON.stringify(role.permissions, null, 2)};
      static index = ${index};

      // Helper method to check route permission
      static hasRoutePermission(routeId) {
        return this.permissions?.routes?.includes(routeId) || false;
      }

      // List of models this role can access
      static allowedModels = ${JSON.stringify(allowedModels, null, 2)};

      /**
       * Check if role can access a specific model
       * @param {string} modelName - Name of the model to check
       * @returns {boolean} Whether model access is allowed
       */
      static canAccessModel(modelName) {
        return this.permissions?.treeql?.models?.[modelName]?.allowed || false;
      }

      /**
       * Get blacklisted fields for a model
       * @param {string} modelName - Name of the model
       * @returns {string[]} Array of blacklisted field names
       */
      static getBlacklistedFields(modelName) {
        return this.permissions?.treeql?.models?.[modelName]?.blacklistedFields || [];
      }

      /**
       * Check if role can perform an operation on a model
       * @param {string} modelName - Name of the model
       * @param {string} operation - Operation to check (get, getOne, getAll, post, put, delete, paginate, join)
       * @returns {boolean} Whether operation is allowed
       */
      static canPerformOperation(modelName, operation) {
        const modelConfig = this.permissions?.treeql?.models?.[modelName];
        if (!modelConfig?.allowed) {
          return false;
        }
        return modelConfig.operations?.[operation] || false;
      }

      /**
       * Get all allowed operations for a model
       * @param {string} modelName - Name of the model
       * @returns {Object<string, boolean>} Object mapping operations to permission status
       */
      static getAllowedOperations(modelName) {
        return this.permissions?.treeql?.models?.[modelName]?.operations || {};
      }

      /**
       * Check if TreeQL is enabled for this role
       * @returns {boolean} Whether TreeQL is enabled
       */
      static isTreeQLEnabled() {
        return this.permissions?.treeql?.enabled || false;
      }
    }

    module.exports = ${role.slug};
  `;
}

/**
 * Copies and modifies authentication files for a specific role
 * @param {Object} role - The role configuration object
 * @param {string} projectPath - The base path of the project
 * @param {Object} settings - Project settings
 * @param {string} projectName - The name of the project
 */
function copyAuthFiles(role, projectPath, settings, projectName) {
  let permissions = role.permissions;
  permissions.updatePassword = true;
  permissions.updateEmail = true;
  permissions.canProfile = true;
  const baasPath = path.join(__dirname, "baas", "lambda");
  const customPath = path.join(projectPath, "lambda");

  // Create lambda directory if it doesn't exist
  if (!fs.existsSync(customPath)) {
    fs.mkdirSync(customPath, { recursive: true });
  }

  // Copy auth files based on permissions
  const authFiles = [
    {
      permission: "canLogin",
      source: "login.js",
      dest: role.slug + "_login.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canLogin",
      source: "refresh_token.js",
      dest: role.slug + "_refresh_token.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canRegister",
      source: "register.js",
      dest: role.slug + "_register.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canForgot",
      source: "forgot.js",
      dest: role.slug + "_forgot.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canReset",
      source: "reset.js",
      dest: role.slug + "_reset.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canUpdatePassword",
      source: "update_password.js",
      dest: role.slug + "_update_password.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canUpdateEmail",
      source: "update_email.js",
      dest: role.slug + "_update_email.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canMagicLinkLogin",
      source: "magic_login.js",
      dest: role.slug + "_magic_login.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canProfile",
      source: "profile.js",
      dest: role.slug + "_profile.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canPreference",
      source: "preference.js",
      dest: role.slug + "_preference.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canVerifyEmail",
      source: "verify.js",
      dest: role.slug + "_verify.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "needs2FA",
      source: "2fa_login.js",
      dest: role.slug + "_2fa_login.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canUpload",
      source: "upload.js",
      dest: role.slug + "_upload.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canUpload",
      source: "upload.js",
      dest: role.slug + "_upload.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "treeql.enabled",
      source: "treeql.js",
      dest: role.slug + "_treeql.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
  ];

  if (settings.payment_option != "none") {
    authFiles.push({
      permission: "canStripe",
      source: "stripe.js",
      dest: role.slug + "_stripe.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    });
  }

  if (settings.isMultiTenant) {
    authFiles.push({
      permission: "canMultitenant",
      source: "multitentant_system.js",
      dest: role.slug + "_multitentant_system.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    });
  }

  authFiles.forEach(({ permission, source, dest, role, namespace }) => {
    if (permissions[permission]) {
      const sourcePath = path.join(baasPath, source);
      const destPath = path.join(customPath, dest);

      if (fs.existsSync(sourcePath)) {
        // Read the source file content
        let content = fs.readFileSync(sourcePath, "utf8");

        // Replace all occurrences of 'xyz' with the actual namespace
        content = content.replace(/xyz/g, namespace);
        content = content.replace(/wxy/g, role);

        // Write the modified content to destination
        fs.writeFileSync(destPath, content);
        console.log(`Copied and modified ${source} to ${dest}`);
      } else {
        console.warn(`Warning: Source file ${source} not found in baas/lambda`);
      }
    } else {
      if (permission === "treeql.enabled" && permissions?.treeql?.enabled) {
        const sourcePath = path.join(baasPath, source);
        const destPath = path.join(customPath, dest);

        if (fs.existsSync(sourcePath)) {
          // Read the source file content
          let content = fs.readFileSync(sourcePath, "utf8");

          // Replace all occurrences of 'xyz' with the actual namespace
          content = content.replace(/xyz/g, namespace);
          content = content.replace(/wxy/g, role);

          // Write the modified content to destination
          fs.writeFileSync(destPath, content);
          console.log(`Copied and modified ${source} to ${dest}`);
        } else {
          console.warn(
            `Warning: Source file ${source} not found in baas/lambda`
          );
        }
      }
    }
  });
}

function copySocialLogins(role, projectPath, settings, projectName) {
  const baasPath = path.join(__dirname, "baas", "lambda");
  const customPath = path.join(projectPath, "lambda");

  // Create lambda directory if it doesn't exist
  if (!fs.existsSync(customPath)) {
    fs.mkdirSync(customPath, { recursive: true });
  }
  const servicesPath = path.join(projectPath); // Path for services folder
  const socialServicesFile = path.join(servicesPath, "social_callback.js"); // Path for social_login_services.js

  // Create lambda directory if it doesn't exist
  if (!fs.existsSync(customPath)) {
    fs.mkdirSync(customPath, { recursive: true });
  }

  // Create services directory if it doesn't exist
  if (!fs.existsSync(servicesPath)) {
    fs.mkdirSync(servicesPath, { recursive: true });
  }

  // Create social_login_services.js if it doesn't exist
  if (!fs.existsSync(socialServicesFile)) {
    const dummySocialServicesContent = `module.exports = {
            google_callback: async ({
                req,
                res,
                project,
                parts,
                projectId,
                role,
                needRefreshToken,
                refreshToken,
                database,
                sdk,
                manaknightSDK,
                originalUrl,
                config,
                googleConfig
              }) => {
              console.log("Dummy google_callback function called");
              return;
            },
            apple_callback: async ({
                req,
                res,
                project,
                parts,
                projectId,
                role,
                needRefreshToken,
                refreshToken,
                database,
                sdk,
                manaknightSDK,
                originalUrl,
                config,
                googleConfig
              }) => {
              console.log("Dummy google_callback function called");
              return;
            },
          };`;
    fs.writeFileSync(socialServicesFile, dummySocialServicesContent);
    console.log(`Created dummy social services file at ${socialServicesFile}`);
  }

  // Copy auth files based on permissions
  const socialLogins = [
    { source: "google_login.stub.js", dest: role.slug + "_google_login.js" },
    { source: "apple_login.stub.js", dest: role.slug + "_apple_login.js" },
    {
      source: "facebook_login.stub.js",
      dest: role.slug + "_facebook_login.js",
    },
    {
      source: "microsoft_login.stub.js",
      dest: role.slug + "_microsoft_login.js",
    },
  ];

  socialLogins.forEach((file) => {
    const sourcePath = path.join(baasPath, file.source);
    const destPath = path.join(customPath, file.dest);

    if (fs.existsSync(sourcePath)) {
      // Read the source file content
      let content = fs.readFileSync(sourcePath, "utf8");

      // Remove code between @ignore and @ignore_end
      content = content.replace(
        /@ignore[\s\S]*?@ignore_end/g,
        "// Callback Url in general Google Login Lambda"
      );
      content = content.replace(/..\/core/g, "../../../baas/core");
      content = content.replace(/..\/services/g, "../../../baas/services");
      content = content.replace(/..\/middleware/g, "../../../baas/middleware");
      content = content.replace(/..\/utils/g, "../../../baas/utils");

      // Replace all occurrences of 'xyz' with the actual namespace
      content = content.replace(
        /xyz/g,
        projectName || settings.model_namespace
      );
      content = content.replace(/wxy/g, role.slug);

      // Write the modified content to destination
      fs.writeFileSync(destPath, content);
      console.log(`Copied and modified ${sourcePath} to ${destPath}`);
    } else {
      console.warn(
        `Warning: Source file ${sourcePath} not found in baas/lambda`
      );
    }
  });
}

module.exports = copySocialLogins;

function generateLambdaTest(role, projectPath, settings, projectName) {
  let permissions = role.permissions;
  const baasPath = path.join(__dirname, "tests", "lambda");
  const customPath = path.join(projectPath, "tests", "lambda");
  const testsPath = path.join(projectPath, "tests");

  // Create tests directory if it doesn't exist
  if (!fs.existsSync(testsPath)) {
    fs.mkdirSync(testsPath, { recursive: true });
  }

  // Create lambda directory if it doesn't exist
  if (!fs.existsSync(customPath)) {
    fs.mkdirSync(customPath, { recursive: true });
  }

  // Copy README.md to tests folder
  const readmePath = path.join(__dirname, "tests", "README.md");
  const destReadmePath = path.join(testsPath, "README.md");

  if (fs.existsSync(readmePath)) {
    fs.copyFileSync(readmePath, destReadmePath);
    console.log(`Copied README.md to ${destReadmePath}`);
  } else {
    console.warn(`Warning: README.md not found in tests folder`);
  }

  const testConfigs = [
    {
      permission: "canRegister",
      source: "register.test.js",
      dest: role.slug + "_register.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canLogin",
      source: "refresh_token.test.js",
      dest: role.slug + "_refresh_token.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canReset",
      source: "reset.test.js",
      dest: role.slug + "_reset.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canVerifyEmail",
      source: "verify.test.js",
      dest: role.slug + "_verify.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canUpdateEmail",
      source: "update_email.test.js",
      dest: role.slug + "_update_email.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canUpdatePassword",
      source: "update_password.test.js",
      dest: role.slug + "_update_password.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    // Add additional test configs for other lambda functions
    {
      permission: "canLogin",
      source: "login.test.js",
      dest: role.slug + "_login.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "needs2FA",
      source: "2fa_login.test.js",
      dest: role.slug + "_2fa_login.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canUpload",
      source: "upload.test.js",
      dest: role.slug + "_upload.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "treeql.enabled",
      source: "treeql.test.js",
      dest: role.slug + "_treeql.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canProfile",
      source: "profile.test.js",
      dest: role.slug + "_profile.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      permission: "canPreference",
      source: "preference.test.js",
      dest: role.slug + "_preference.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
  ];

  // Add social login test configs
  const socialLoginTestConfigs = [
    {
      source: "apple_login.test.js",
      dest: role.slug + "_apple_login.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      source: "facebook_login.test.js",
      dest: role.slug + "_facebook_login.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      source: "google_login.test.js",
      dest: role.slug + "_google_login.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
    {
      source: "microsoft_login.test.js",
      dest: role.slug + "_microsoft_login.test.js",
      role: role.slug,
      namespace: projectName || settings.model_namespace,
    },
  ];

  // Process regular test configs
  testConfigs.forEach(({ permission, source, dest, role, namespace }) => {
    if (!permissions[permission]) {
      // Special case for treeql.enabled
      if (permission === "treeql.enabled" && permissions?.treeql?.enabled) {
        // Continue with the test generation
      } else {
        return;
      }
    }
    const sourcePath = path.join(baasPath, source);
    const destPath = path.join(customPath, dest);

    if (fs.existsSync(sourcePath)) {
      // Read the source file content
      let content = fs.readFileSync(sourcePath, "utf8");

      // Replace all occurrences of 'xyz' with the actual namespace
      content = content.replace(/xyz/g, namespace);
      content = content.replace(/wxy/g, role);

      // Write the modified content to destination
      fs.writeFileSync(destPath, content);
      console.log(`Copied and modified ${source} to ${dest}`);
    } else {
      console.warn(`Warning: Source file ${source} not found in baas/lambda`);
    }
  });

  // Process social login test configs (these don't have specific permissions)
  socialLoginTestConfigs.forEach(({ source, dest, role, namespace }) => {
    const sourcePath = path.join(baasPath, source);
    const destPath = path.join(customPath, dest);

    if (fs.existsSync(sourcePath)) {
      // Read the source file content
      let content = fs.readFileSync(sourcePath, "utf8");

      // Replace all occurrences of 'xyz' with the actual namespace
      content = content.replace(/xyz/g, namespace);
      content = content.replace(/wxy/g, role);

      // Write the modified content to destination
      fs.writeFileSync(destPath, content);
      console.log(`Copied and modified ${source} to ${dest}`);
    } else {
      console.warn(`Warning: Source file ${source} not found in baas/lambda`);
    }
  });
}

/**
 * Processes and generates role models and authentication files
 * @param {Object} config - The configuration object containing roles
 * @param {string} projectPath - The base path of the project
 * @param {string} projectName - The name of the project
 */
function processRoles(config, projectPath, projectName) {
  // Create custom/project1/roles directory if it doesn't exist
  const customRolesPath = path.join(projectPath, "roles");
  if (!fs.existsSync(customRolesPath)) {
    fs.mkdirSync(customRolesPath, { recursive: true });
  }
  const servicesPath = path.join(projectPath); // Path for services folder
  const configurationFile = path.join(servicesPath, "configuration.js"); // Path for social_login_services.js

  if (!fs.existsSync(configurationFile)) {
    const configurationContent = `${config.roles
      .map((role) => `const ${role.slug} = require("./roles/${role.slug}");`)
      .join("\n")}
      module.exports = function(app) {
          return {
              roles: [${config.roles
                .map((role) => `"${role.slug}"`)
                .join(", ")}],
              ${config.roles
                .map((role) => `${role.slug}: new ${role.slug}()`)
                .join(",\n")},
              configuration: {}
          };
      }`;
    fs.writeFileSync(configurationFile, configurationContent);
    console.log(`Created configuration file at ${configurationFile}`);
  }

  // Process each role
  config.roles.forEach((role, index) => {
    // Generate and save role model
    const roleModelContent = formatModelCode(generateRoleModel(role, index));
    const roleFilePath = path.join(customRolesPath, `${role.slug}.js`);
    fs.writeFileSync(roleFilePath, roleModelContent);
    console.log(`Generated role model: ${roleFilePath}`);

    // Copy necessary auth files based on permissions
    copyAuthFiles(role, projectPath, config.settings, projectName);
    generateLambdaTest(role, projectPath, config.settings, projectName);
    copySocialLogins(role, projectPath, config.settings, projectName);
  });
}

/**
 * Processes and generates API routes based on configuration
 * @param {Object} config - The configuration object containing routes
 * @param {string} projectPath - The base path of the project
 */
/**
 * Generates test files for mock APIs
 * @param {Object} config - The configuration object containing routes
 * @param {string} projectPath - The base path of the project
 */
function generateMockApiTests(config, projectPath) {
  // Import the mock test template generator
  const generateMockApiTest = require("./templates/MockTestTemplate");

  // Create tests/unit directory if it doesn't exist
  const unitTestsPath = path.join(projectPath, "tests", "unit");
  ensureDirExists(unitTestsPath);
  // Process each route to find mock APIs
  if (!Array.isArray(config.routes)) {
    console.warn("No routes found in configuration");
    return;
  }

  let testCount = 0;

  config.routes.forEach((route) => {
    if (!route.flowData?.nodes) {
      return;
    }

    const nodes = route.flowData.nodes;

    // Find mock-api nodes
    const mockApiNodes = nodes.filter((node) => node.type === "mock-api");

    mockApiNodes.forEach((node) => {
      const nodeData = node.data;
      if (!nodeData || !nodeData.path || !nodeData.method) {
        return;
      }

      // Generate test file name based on API name or path
      const apiName =
        nodeData.apiname ||
        nodeData.path.replace(/\//g, "_").replace(/:/g, "").replace(/^_/, "");
      const testFileName = `${stringCaser.lowercase(apiName, "_")}.test.js`;
      const testFilePath = path.join(unitTestsPath, testFileName);

      // Skip if test file already exists
      if (fs.existsSync(testFilePath)) {
        console.log(`Test file already exists: ${testFilePath}`);
        return;
      }

      // Prepare API config for test generation
      const apiConfig = {
        method: nodeData.method,
        path: nodeData.path,
        name:
          nodeData.apiname ||
          `API ${nodeData.method.toUpperCase()} ${nodeData.path}`,
        description: nodeData.description || "",
        role: config.settings?.model_namespace || "default",
        namespace: config.settings?.model_namespace || "default",
        fields: nodeData.fields || [],
        queryFields: nodeData.queryFields || [],
        responseFields: nodeData.responseFields || [],
        authType: nodeData.authType || "none",
      };

      // Generate test content
      const testContent = generateMockApiTest(apiConfig);

      // Write test file
      fs.writeFileSync(testFilePath, testContent);
      console.log(`Generated mock API test: ${testFilePath}`);
      testCount++;
    });
  });

  console.log(`Generated ${testCount} mock API test files`);
}

/**
 * Processes and generates API routes based on configuration
 * @param {Object} config - The configuration object containing routes
 * @param {string} projectPath - The base path of the project
 */
function processAPI(config, projectPath) {
  // Create api.js file in the custom project directory
  const apiFilePath = path.join(projectPath, "api.js");

  // Generate the API router code
  let variableList = {};
  let apiCode = `
const AuthService = require('../../baas/services/AuthService');
const TokenMiddleware = require('../../baas/middleware/TokenMiddleware');

module.exports = function(app) {
${generateRoutes(config.routes, config, variableList)}
};`;

  // Write the API file
  fs.writeFileSync(apiFilePath, apiCode);
  console.log(`Generated API router: ${apiFilePath}`);

  // Generate test files for mock APIs
  generateMockApiTests(config, projectPath);
}

function generateRoutes(routes, config, variableList) {
  if (!Array.isArray(routes)) {
    console.warn("No routes found in configuration");
    return "";
  }

  return routes
    .map((route) => {
      if (!route.flowData?.nodes) {
        console.warn(`Invalid route configuration for route: ${route.name}`);
        return "";
      }

      const nodes = route.flowData.nodes;
      const edges = route.flowData.edges;

      // Find the first node to determine route type and configuration
      const firstNode = nodes[0];
      if (!firstNode) {
        console.warn(`No nodes found for route: ${route.name}`);
        return "";
      }

      // For mock-api type, use the node's data for route configuration
      if (firstNode.type === "mock-api") {
        const nodeData = firstNode.data;
        return `  // ${nodeData.description || "Mock API Route"}
  app.${nodeData.method.toLowerCase()}('${
          nodeData.path
        }', [TokenMiddleware()], async (req, res) => {
    try {
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(\`./roles/\${roleClassName}\`);
      } catch (error) {
        console.error(\`Role class not found for role: \${roleClassName}\`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('${config.settings.model_namespace}');
${generateRouteHandler(nodes, route, variableList)}
    } catch (error) {
      console.error('Error in ${nodeData.apiname}:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });`;
      }
      // For other route types, use the route's configuration
      else {
        const nodeData = firstNode.data;
        if (!route.method && !nodeData.method) {
          console.warn(`Method not defined for route: ${route.name}`);
          return "";
        }
        const method = (route.method || nodeData.method).toLowerCase();
        const url = nodeData.path;
        return `  // ${route.name} - ${route.id}
  app.${method}('${url}', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "${route.id}";
      const user = req.user;

      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }

      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(\`./roles/\${roleClassName}\`);
      } catch (error) {
        console.error(\`Role class not found for role: \${roleClassName}\`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('${config.settings.model_namespace}');
${generateRouteHandler(nodes, route, variableList)}
    } catch (error) {
      console.error('Error in ${route.name}:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });`;
      }
    })
    .filter(Boolean)
    .join("\n\n");
}

function generateRouteHandler(nodes, route, variableList) {
  if (!nodes || !Array.isArray(nodes)) return "";

  let code = [];
  let resultVar = null;

  nodes.forEach((node) => {
    if (!node || !node.type) return;

    switch (node.type) {
      case "mock-api":
        if (node.data) {
          // Add validation for query parameters
          if (node.data.queryFields?.length > 0) {
            code.push(`    // Validate query parameters`);
            node.data.queryFields.forEach((field) => {
              code.push(`    if (req.query.${field.name} !== undefined) {

      ${generateFieldValidation(field, "req.query")}
    }`);
              if (field.validation?.includes("required")) {
                code.push(`    if (req.query.${field.name} === undefined) {
      return res.status(400).json({ error: true, message: '${field.name} is required in query parameters' });
    }`);
              }
            });
          }

          // Add validation for request body
          if (node.data.fields?.length > 0) {
            code.push(`    // Validate request body`);
            node.data.fields.forEach((field) => {
              code.push(`    if (req.body.${field.name} !== undefined) {
      ${generateFieldValidation(field)}
    }`);
              if (field.validation?.includes("required")) {
                code.push(`    if (req.body.${field.name} === undefined) {
      return res.status(400).json({ error: true, message: '${field.name} is required in request body' });
    }`);
              }
            });
          }

          // Replace bearer token validation with generateAuth function
          if (node.data.authType === "bearer") {
            code.push(generateAuth(node.data));
          }

          // Generate mock response
          const mockResponse = generateMockResponseObject(
            node.data.responseFields
          );
          code.push(`    // Send mock response
    res.status(${node.data.statusCode || 200})
       .type('${node.data.outputType || "json"}')
       .json(${JSON.stringify(mockResponse, null, 2)});`);
        }
        break;

      // Keep existing cases for other node types
      case "url":
        if (node.data) {
          code.push(generateUrlValidation(node.data));
        }
        break;
      case "logic":
        if (node.data) {
          const logicCode = node.data.code
            .split("\n")
            .filter((line) => `${line}`.trim() !== "")
            .join("\n");
          code.push(logicCode);
        }
        break;
      case "auth":
        if (node.data) {
          code.push(generateAuth(node.data));
        }
        break;
      case "db-find":
        if (node.data) {
          code.push(generateDatabaseFind(node.data, variableList));
          resultVar = node.data.resultVar;
        }
        break;
      case "variable":
        if (node.data) {
          code.push(generateVariable(node.data, variableList));
        }
        break;
      case "db-query":
        if (node.data) {
          code.push(generateDatabaseQuery(node.data, variableList));
          resultVar = node.data.resultVar;
        }
        break;
      case "db-insert":
        if (node.data) {
          code.push(generateDatabaseInsert(node.data, variableList));
          resultVar = node.data.resultVar;
        }
        break;
      case "db-update":
        if (node.data) {
          code.push(generateDatabaseUpdate(node.data, variableList));
          resultVar = node.data.resultVar;
        }
        break;
      case "db-delete":
        if (node.data) {
          code.push(generateDatabaseDelete(node.data, variableList));
          resultVar = node.data.resultVar;
        }
        break;
      case "outputs":
        if (node.data) {
          code.push(generateOutput(node.data, variableList));
        }
        break;
    }
  });

  return code.join("\n");
}

function generateUrlValidation(data) {
  let validationCode = [];
  if (data.queryFields) {
    validationCode.push(`// Validate URL parameters`);
    data.queryFields.forEach((field) => {
      if (field.validation?.includes("required")) {
        validationCode.push(`if (!req.params.${field.name}) {
      throw new Error('${field.name} is required');
    }`);
      }
    });
  }

  if (data.fields) {
    validationCode.push(`// Validate request body`);
    data.fields.forEach((field) => {
      if (field.validation?.includes("required")) {
        validationCode.push(`if (!req.body.${field.name}) {
      throw new Error('${field.name} is required');
    }`);
      }
    });
  }

  return validationCode.join("\n    ");
}

function generateDatabaseFind(data, variableList) {
  variableList[data.resultVar] = [];
  return `// Execute database find
    req.sdk.setTable('${data.model}');
    const ${data.resultVar} = await req.sdk.find({});
    `;
}

function generateDatabaseQuery(data, variableList) {
  variableList[data.resultVar] = {};
  return `// Execute database query
    req.sdk.setTable('${data.model}');
    const ${data.resultVar} = await req.sdk.rawQuery(\`${data.query}\`, [${
    data.idField ? "req.params.id" : ""
  }]);
    `;
}

function generateDatabaseInsert(data, variableList) {
  variableList[data.resultVar] = {};
  return `// Execute database insert
    req.sdk.setTable('${data.model}');
    const ${data.resultVar} = await req.sdk.create(req.body);
    `;
}

function generateDatabaseUpdate(data, variableList) {
  variableList[data.resultVar] = true;
  return `// Execute database update
    req.sdk.setTable('${data.model}');
    const ${data.resultVar} = await req.sdk.update({ id: req.params.id }, req.body);
    `;
}

function generateDatabaseDelete(data, variableList) {
  variableList[data.resultVar] = true;
  return `// Execute database delete
    req.sdk.setTable('${data.model}');
    const ${data.resultVar} = await req.sdk.delete({ id: req.params.id });
    `;
}

function generateMockAPI(data) {
  let mockCode = [];
  // Add validation for query fields
  if (data.queryFields && data.queryFields.length > 0) {
    mockCode.push(`// Validate query parameters`);
    data.queryFields.forEach((field) => {
      mockCode.push(`if (req.query.${field.name} !== undefined) {
      // Validate ${field.name} of type ${field.type}
      const ${field.name}Value = req.query.${field.name};
      ${generateFieldValidation(field, "req.query")}
    }`);
    });
  }

  // Add validation for body fields
  if (data.fields && data.fields.length > 0) {
    mockCode.push(`// Validate request body fields`);
    data.fields.forEach((field) => {
      mockCode.push(`if (req.body.${field.name} !== undefined) {
      // Validate ${field.name} of type ${field.type}
      const ${field.name}Value = req.body.${field.name};
      ${generateFieldValidation(field)}
    }`);
    });
  }

  // Add auth validation if required
  if (data.authType === "bearer") {
    mockCode.push(`// Validate bearer token
    if (!req.headers.authorization) {
      return res.status(401).json({ error: true, message: 'Authorization header missing' });
    }
    const token = req.headers.authorization.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ error: true, message: 'Invalid token format' });
    }`);
  }

  // Generate mock response data
  const mockResponse = generateMockResponseObject(data.responseFields);

  // Add response with proper status code and content type
  mockCode.push(`// Send mock response
    res.status(${data.statusCode || 200})
       .type('${data.outputType || "json"}')
       .json(${JSON.stringify(mockResponse, null, 2)});`);

  return mockCode.join("\n    ");
}

function generateFieldValidation(
  field,
  parentObject = "req.body",
  arrayIndex = null
) {
  let validationCode = [];

  const fieldAccessor =
    arrayIndex !== null
      ? `${parentObject}[${arrayIndex}]['${field.name}']`
      : `${parentObject}['${field.name}']`;

  // Create a safe variable name by replacing dots with underscores
  const safeFieldName = `${parentObject.replace(/\./g, "_")}_${field.name}`;

  switch (field.type) {
    case "string":
      validationCode.push(`const ${safeFieldName} = ${fieldAccessor};
      if (typeof ${safeFieldName} !== 'string') {
        return res.status(400).json({ error: true, message: '${field.name} must be a string' });
      }`);
      break;
    case "integer":
      validationCode.push(`const ${safeFieldName} = ${fieldAccessor};
      if (!Number.isInteger(Number(${safeFieldName}))) {
        return res.status(400).json({ error: true, message: '${field.name} must be an integer' });
      }`);
      break;
    case "boolean":
      validationCode.push(`const ${safeFieldName} = ${fieldAccessor};
      if (typeof ${safeFieldName} !== 'boolean' && ${safeFieldName} !== 'true' && ${safeFieldName} !== 'false') {
        return res.status(400).json({ error: true, message: '${field.name} must be a boolean' });
      }`);
      break;
    case "date":
      validationCode.push(`const ${safeFieldName} = ${fieldAccessor};
      if (isNaN(Date.parse(${safeFieldName}))) {
        return res.status(400).json({ error: true, message: '${field.name} must be a valid date (YYYY-MM-DD)' });
      }`);
      break;
    case "datetime":
      validationCode.push(`const ${safeFieldName} = ${fieldAccessor};
      if (isNaN(Date.parse(${safeFieldName}))) {
        return res.status(400).json({ error: true, message: '${field.name} must be a valid datetime (ISO 8601)' });
      }`);
      break;
    case "file":
      validationCode.push(`const ${safeFieldName} = req.files?.['${field.name}'];
      if (!${safeFieldName}) {
        return res.status(400).json({ error: true, message: '${field.name} must be a file' });
      }`);
      break;
    case "array":
      validationCode.push(`const ${safeFieldName} = ${fieldAccessor};
      if (!Array.isArray(${safeFieldName})) {
        return res.status(400).json({ error: true, message: '${field.name} must be an array' });
      }`);
      if (field.value) {
        if (typeof field.value === "string") {
          validationCode.push(`for (let i = 0; i < ${safeFieldName}.length; i++) {
        const ${safeFieldName}_item = ${safeFieldName}[i];
        if (typeof ${safeFieldName}_item !== '${field.value}') {
          return res.status(400).json({ error: true, message: \`${field.name}[\${i}] must be a ${field.value}\` });
        }
      }`);
        } else if (field.value.type === "object" && field.value.fields) {
          validationCode.push(`for (let i = 0; i < ${safeFieldName}.length; i++) {
        const ${safeFieldName}_item = ${safeFieldName}[i];
        if (typeof ${safeFieldName}_item !== 'object' || ${safeFieldName}_item === null) {
          return res.status(400).json({ error: true, message: \`${
            field.name
          }[\${i}] must be a valid object\` });
        }
        ${generateObjectValidation(field.value.fields, `${safeFieldName}_item`)}
      }`);
        }
      }
      break;
    case "object":
      validationCode.push(`const ${safeFieldName} = ${fieldAccessor};
      if (typeof ${safeFieldName} !== 'object' || ${safeFieldName} === null) {
        return res.status(400).json({ error: true, message: '${field.name} must be an object' });
      }`);
      if (Array.isArray(field.value)) {
        validationCode.push(
          generateObjectValidation(field.value, safeFieldName)
        );
      }
      break;
  }

  // Add any custom validation from field.validation
  if (field.validation && typeof field.validation === "string") {
    if (field.validation.includes("email")) {
      validationCode.push(`if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(${safeFieldName})) {
        return res.status(400).json({ error: true, message: '${field.name} must be a valid email address' });
      }`);
    }
  }

  return validationCode.join("\n      ");
}

function generateObjectValidation(fields, parentObject) {
  let validationCode = [];

  fields.forEach((field) => {
    const fieldPath = `${parentObject}['${field.name}']`;

    if (field.type === "object" && field.fields) {
      const safeFieldName = `${parentObject.replace(/\./g, "_")}_${field.name}`;
      validationCode.push(`const ${safeFieldName} = ${fieldPath};
      if (${safeFieldName} && typeof ${safeFieldName} === 'object') {
        ${generateObjectValidation(field.fields, safeFieldName)}
      }`);
    } else {
      validationCode.push(`if (${fieldPath} !== undefined) {
        ${generateFieldValidation(field, parentObject)}
      }`);
    }
  });

  return validationCode.join("\n      ");
}

function generateMockResponseObject(fields) {
  const response = {};

  fields.forEach((field) => {
    switch (field.type) {
      case "boolean":
        response[field.name] = false;
        break;
      case "object":
        if (field.value) {
          response[field.name] = field.value.reduce((obj, subField) => {
            obj[subField.name] = generateMockValue(subField);
            return obj;
          }, {});
        } else {
          response[field.name] = {};
        }
        break;
      default:
        response[field.name] = generateMockValue(field);
    }
  });

  return response;
}

function generateMockValue(field) {
  switch (field.type) {
    case "string":
      return "mock_string";
    case "integer":
    case "number":
      return 123;
    case "boolean":
      return false;
    case "date":
      return new Date().toISOString().split("T")[0];
    case "datetime":
      return new Date().toISOString();
    case "array":
      if (typeof field.value === "string") {
        return [generateMockValueForType(field.value)];
      } else if (field.value?.type === "object") {
        return [generateMockObjectValue(field.value.fields)];
      }
      return [];
    case "object":
      if (field.fields) {
        return generateMockObjectValue(field.fields);
      }
      return {};
    default:
      return null;
  }
}

function generateMockObjectValue(fields) {
  return fields.reduce((obj, field) => {
    obj[field.name] = generateMockValue(field);
    return obj;
  }, {});
}

function generateMockValueForType(type) {
  switch (type) {
    case "string":
      return "mock_string";
    case "integer":
    case "number":
      return 123;
    case "boolean":
      return false;
    case "date":
      return new Date().toISOString().split("T")[0];
    case "datetime":
      return new Date().toISOString();
    case "file":
      return "mock_file.txt";
    case "array":
      return [];
    case "object":
      return {};
    default:
      return null;
  }
}

function generateOutput(data, variableList) {
  if (data.outputType === "json") {
    // If resultVar exists and is in variableList
    if (
      data.resultVar &&
      typeof data.resultVar === "string" &&
      data.resultVar in variableList
    ) {
      // Check if the variable is an array
      if (Array.isArray(variableList[data.resultVar])) {
        return `// Send response with list
    res.status(${data.statusCode}).json({
      error: false,
      list: ${data.resultVar}
    });`;
      } else {
        return `// Send response with model
    res.status(${data.statusCode}).json({
      error: false,
      model: ${data.resultVar}
    });`;
      }
    }

    // If no resultVar, generate mock response using fields
    if (data.fields && Array.isArray(data.fields)) {
      const mockResponse = data.fields.reduce((acc, field) => {
        acc[field.name] = generateMockValue(field);
        return acc;
      }, {});

      return `// Send mock response
    res.status(${data.statusCode}).json({
      error: false,
      model: ${JSON.stringify(mockResponse, null, 2)}
    });`;
    }
  }

  // Default response if no conditions met
  return `// Send default response
    res.status(${data.statusCode}).json({
      error: false
    });`;
}

function generateVariable(data, variableList) {
  // Store variable name and default value in variableList
  variableList[data.name] = data.defaultValue;

  return `// Initialize variable ${data.name}
    let ${data.name} = ${data.defaultValue};`;
}

function generateAuth(data) {
  let authCode = [];

  switch (data.authType) {
    case "none":
      authCode.push(`// No authentication required`);
      break;
    case "basic":
      authCode.push(`// Basic authentication
    const isAuthenticated = AuthService.basic(app, req);
    if (!isAuthenticated) {
      return res.status(401).json({ error: true, message: 'Basic authentication failed' });
    }`);
      break;
    case "bearer":
      authCode.push(`// Bearer token authentication
    const isAuthenticated = AuthService.bearer(app, req);
    if (!isAuthenticated) {
      return res.status(401).json({ error: true, message: 'Bearer token authentication failed' });
    }`);
      break;
    case "apiKey":
      authCode.push(`// API Key authentication
    const isAuthenticated = AuthService.apiKey(app, req);
    if (!isAuthenticated) {
      return res.status(401).json({ error: true, message: 'API Key authentication failed' });
    }`);
      break;
    case "oauth2":
      authCode.push(`// OAuth2 authentication
    const isAuthenticated = AuthService.oauth2(app, req);
    if (!isAuthenticated) {
      return res.status(401).json({ error: true, message: 'OAuth2 authentication failed' });
    }`);
      break;
    case "digest":
      authCode.push(`// Digest authentication
    const isAuthenticated = AuthService.digest(app, req);
    if (!isAuthenticated) {
      return res.status(401).json({ error: true, message: 'Digest authentication failed' });
    }`);
      break;
    case "hmac":
      authCode.push(`// HMAC authentication
    const isAuthenticated = AuthService.hmac(app, req);
    if (!isAuthenticated) {
      return res.status(401).json({ error: true, message: 'HMAC authentication failed' });
    }`);
      break;
    case "session":
      authCode.push(`// Session authentication
    const isAuthenticated = AuthService.session(app, req);
    if (!isAuthenticated) {
      return res.status(401).json({ error: true, message: 'Session authentication failed' });
    }`);
      break;
    case "ldap":
      authCode.push(`// LDAP authentication
    const isAuthenticated = AuthService.ldap(app, req);
    if (!isAuthenticated) {
      return res.status(401).json({ error: true, message: 'LDAP authentication failed' });
    }`);
      break;
    case "saml":
      authCode.push(`// SAML authentication
    const isAuthenticated = AuthService.saml(app, req);
    if (!isAuthenticated) {
      return res.status(401).json({ error: true, message: 'SAML authentication failed' });
    }`);
      break;
    default:
      authCode.push(`// Unknown authentication type`);
  }

  return authCode.join("\n    ");
}

// Add this function near other utility functions
function formatModelCode(sourceCode) {
  const lines = sourceCode.split("\n");
  let formattedLines = [];
  let indentLevel = 0;

  for (let line of lines) {
    line = line.trim();

    // Decrease indent for closing braces/brackets
    if (line.match(/^[}\]]/)) {
      indentLevel--;
    }

    // Add the line with proper indentation
    if (line.length > 0) {
      formattedLines.push("  ".repeat(Math.max(0, indentLevel)) + line);
    } else {
      formattedLines.push(""); // Keep empty lines
    }

    // Increase indent for opening braces/brackets
    if (line.match(/[{\[]$/)) {
      indentLevel++;
    }
  }

  return formattedLines.join("\n");
}

/**
 * Generates SQL INSERT statements for seeding data into tables
 * @param {Object} model - The model configuration object
 * @param {string} modelNamespace - The namespace prefix for the table
 * @returns {string} SQL INSERT statements
 */
async function generateSeedTableSQL(model, modelNamespace) {
  const tableName = `${modelNamespace}_${model.name}`;
  let sql = "";

  // Track unique fields by name
  const uniqueFields = new Map();
  model.fields.forEach((field) => {
    // Only keep the first occurrence of each field name
    if (!uniqueFields.has(field.name)) {
      uniqueFields.set(field.name, field);
    }
  });

  for (let i = 0; i < 3; i++) {
    // Generate 3 entries
    const values = await Promise.all(
      Array.from(uniqueFields.values()).map(async (field) => {
        let value;
        switch (field.type) {
          case "string":
            value = `'random_string_${i}'`;
            break;
          case "integer":
            value = Math.floor(Math.random() * 100);
            break;
          case "double":
          case "number":
            value = Math.random();
            break;
          case "boolean":
            value = Math.random() < 0.5 ? 0 : 1; // Random boolean
            break;
          case "date":
            value = `'${new Date().toISOString().split("T")[0]}'`;
            break;
          case "datetime":
            value = `'${new Date().toISOString().split("T")[0]}'`;
            break;
          case "json":
            value = `'{"key": "value"}'`;
            break;
          case "long text":
            value = `'This is a long text entry for record ${i}'`;
            break;
          case "timestamp":
            value = `'${new Date().toISOString().split("T")[0]}}'`;
            break;
          case "mapping":
            value = 0; // Random mapping value
            break;
          case "foreign key":
            value = 1; // Assuming foreign key references an ID
            break;
          default:
            value = "NULL"; // Default case
        }

        if (field.name === "email") {
          value = `'${Math.random().toString(36).substring(2, 15)}@gmail.com'`; //TODO random email generated;
        }
        if (field.name === "password") {
          value = `'${await PasswordService.hash("123456")}'`;
        }

        return value;
      })
    );

    sql += `INSERT INTO ${tableName} VALUES (${values.join(", ")});\n`;
  }

  return sql;
}

function processSDK(config, projectPath, projectName) {
  const lambdaCode = buildLambdaSdk(config, projectName);
  const apiCode = buildApiSdk(config, projectName);
  const treeqlCode = buildTreeqlSDK(config, projectName);

  ensureDirExists(path.join(projectPath, `/frontend/utils`));
  ensureDirExists(path.join(projectPath, `/frontend/hooks/useSDK`));

  let content = "";
  const toPascalCase = (str) =>
    str
      .split("_")
      .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");

  for (const role of config.roles) {
    const lambdaFilePath = path.join(
      projectPath,
      `/frontend/utils/${toPascalCase(role.slug)}SDK.ts`
    );

    content = getSDKWrapper(role.slug, projectName);
    content = content.replace(
      "/* CODE */",
      lambdaCode
        .filter((item) => item.role === role.slug)
        .map((item) => item.content)
        .join("\n")
    );
    fs.writeFileSync(lambdaFilePath, content);
  }

  let mkdCode = [...apiCode];
  mkdCode.push(...lambdaCode.filter((item) => item.role === "mkd"));

  const apiFilePath = path.join(projectPath, `/frontend/utils/MkdSDK.ts`);

  content = getSDKWrapper("mkd", projectName);
  content = content.replace(
    "/* CODE */",
    mkdCode.map((item) => item.content).join("\n")
  );

  fs.writeFileSync(apiFilePath, content);

  const treeqlFilePath = path.join(projectPath, `/frontend/utils/TreeSDK.ts`);

  fs.writeFileSync(treeqlFilePath, treeqlCode);

  const useSDKFilePath = path.join(
    projectPath,
    `/frontend/hooks/useSDK/useSDK.tsx`
  );
  const useSDKIndexFilePath = path.join(
    projectPath,
    `/frontend/hooks/useSDK/index.ts`
  );

  const useSDKCode = useSDKTemplate(config?.roles);
  const useSDKIndexCode = useSDKIndexTemplate();

  fs.writeFileSync(useSDKFilePath, useSDKCode);
  fs.writeFileSync(useSDKIndexFilePath, useSDKIndexCode);
}

function ensureDirExists(dirPath) {
  try {
    fs.mkdirSync(dirPath, { recursive: true });
  } catch (error) {
    console.error(`Error creating directory: ${dirPath}`);
    throw error;
  }
}

function processAdminPages(config, projectPath) {
  const adminPages = generateAdminPages(config);
  const adminPagesPath = path.join(projectPath, "frontend");
  const adminListPath = path.join(adminPagesPath, "pages", "Admin", "List");
  const adminAddPath = path.join(adminPagesPath, "pages", "Admin", "Add");
  const adminEditPath = path.join(adminPagesPath, "pages", "Admin", "Edit");
  const adminViewPath = path.join(adminPagesPath, "pages", "Admin", "View");
  ensureDirExists(adminPagesPath);
  ensureDirExists(adminListPath);
  ensureDirExists(adminAddPath);
  ensureDirExists(adminEditPath);
  ensureDirExists(adminViewPath);
  adminPages.pages.forEach((page) => {
    const filePath = path.join(adminPagesPath, page.file);
    fs.writeFileSync(filePath, page.content);
  });

  const adminRoutesPath = path.join(projectPath, "frontend");
  ensureDirExists(path.join(adminRoutesPath, "routes"));
  const routesFilePath = path.join(adminRoutesPath, adminPages.routes.file);
  fs.writeFileSync(routesFilePath, adminPages.routes.content);

  const lazyLoadFilePath = path.join(adminRoutesPath, adminPages.lazyLoad.file);
  fs.writeFileSync(lazyLoadFilePath, adminPages.lazyLoad.content);

  const adminHeaderPath = path.join(projectPath, "frontend");
  ensureDirExists(path.join(adminHeaderPath, "components", "AdminHeader"));
  const adminHeaderFilePath = path.join(
    adminHeaderPath,
    adminPages.header.file
  );
  fs.writeFileSync(adminHeaderFilePath, adminPages.header.content);
}

/**
 * Extracts API definitions from lambda files
 * @param {string} projectPath - The base path of the project
 * @param {string} projectName - The name of the project
 * @returns {Array} Array of API endpoint definitions
 */
function extractLambdaApiDefinitions(projectPath, projectName) {
  const lambdaPath = path.join(projectPath, 'lambda');
  const apiDefinitions = [];

  // Check if lambda directory exists
  if (!fs.existsSync(lambdaPath)) {
    console.log(`Lambda directory not found at ${lambdaPath}`);
    return apiDefinitions;
  }

  // Get all files in the lambda directory
  const lambdaFiles = fs.readdirSync(lambdaPath);
  console.log(`Found ${lambdaFiles.length} lambda files`);

  // Process each lambda file
  lambdaFiles.forEach(fileName => {
    const filePath = path.join(lambdaPath, fileName);
    
    // Skip directories and non-JavaScript files
    if (fs.lstatSync(filePath).isDirectory() || !fileName.endsWith('.js')) {
      return;
    }

    try {
      // Read file content
      // just call the function getPostmanDefinition and return the result
      const {getPostmanDefinition} = require(filePath);
      apiDefinitions.push(...getPostmanDefinition());
      
      // Check for getPostmanDefinition export function first
      // const exportMatch = fileContent.match(/exports\.getPostmanDefinition\s*=\s*function\s*\(\)\s*{[\s\S]*?return\s*\[\s*{[\s\S]*?}\s*\]\s*;?\s*}\s*;?/);
      
    //   if (exportMatch) {
    //     // Extract the return array from the function
    //     const returnMatch = exportMatch[0].match(/return\s*\[\s*{[\s\S]*?}\s*\]\s*;?/);
        
    //     if (returnMatch) {
    //       // Process the API definitions from the return statement
    //       processReturnStatement(returnMatch[0], fileName, projectName, apiDefinitions);
    //     }
    //   } else {
    //     // Fall back to looking for return statement with array of API definitions
    //     const returnMatch = fileContent.match(/return\s*\[\s*{[\s\S]*?}\s*\]\s*;?\s*$/);
        
    //     if (returnMatch) {
    //       // Process the API definitions from the return statement
    //       processReturnStatement(returnMatch[0], fileName, projectName, apiDefinitions);
    //     }
    //   }
    } catch (fileError) {
      console.warn(`Error reading lambda file ${fileName}: ${fileError.message}`);
    }
  });

  console.log(`Extracted ${apiDefinitions.length} API definitions from lambda files`);
  return apiDefinitions;
}

/**
 * Process a return statement from a lambda file and extract API definitions
 * @param {string} returnStatement - The return statement to process
 * @param {string} fileName - The name of the source file
 * @param {string} projectName - The name of the project
 * @param {Array} apiDefinitions - Array to add extracted definitions to
 */
function processReturnStatement(returnStatement, fileName, projectName, apiDefinitions) {
  // Extract the array part
  let arrayStr = returnStatement.replace(/^return\s*/, '');
  // Clean up the string for evaluation
  arrayStr = arrayStr.replace(/(\w+):/g, '"$1":');
  arrayStr = arrayStr.replace(/'/g, '"');
  arrayStr = arrayStr.replace(/,\s*}\s*\]\s*;?\s*$/, '}]');
  
  try {
    // Evaluate the string to get the array
    // This is safer than using eval directly
    const apiDefs = JSON.parse(arrayStr);
    
    if (Array.isArray(apiDefs)) {
      // Replace project and role placeholders
      apiDefs.forEach(apiDef => {
        if (apiDef.url) {
          apiDef.url = apiDef.url.replace(/xyz/g, projectName);
          
          // Extract role from filename (e.g., admin_login.js -> admin)
          const roleName = fileName.split('_')[0];
          apiDef.url = apiDef.url.replace(/wxy/g, roleName);
          
          // Add role and file reference to the API definition
          apiDef.role = roleName;
          apiDef.sourceFile = fileName;
          
          apiDefinitions.push(apiDef);
        }
      });
    }
  } catch (parseError) {
    console.warn(`Error parsing API definitions in ${fileName}: ${parseError.message}`);
  }
}

/**
 * Enhances the Postman collection with API definitions from lambda files
 * @param {Object} collection - The Postman collection object
 * @param {Array} lambdaApis - Array of API definitions from lambda files
 * @param {Object} roleFolders - Map of role folders in the collection
 */
function addLambdaApisToCollection(collection, lambdaApis, roleFolders) {
  if (!lambdaApis || lambdaApis.length === 0) {
    return;
  }

  // Create a dedicated folder for Lambda APIs if it doesn't exist
  let lambdaFolder = collection.item.find(folder => folder.name === "Lambda Endpoints");
  
  if (!lambdaFolder) {
    lambdaFolder = {
      name: "Lambda Endpoints",
      description: "API endpoints defined in Lambda functions",
      item: []
    };
    collection.item.push(lambdaFolder);
  }

  // Process each API definition
  lambdaApis.forEach(apiDef => {
    // Create Postman request item
    const requestItem = {
      name: apiDef.name || `${apiDef.method} ${apiDef.url}`,
      request: {
        method: apiDef.method,
        description: `Lambda API from ${apiDef.sourceFile}`,
        header: [],
        url: {
          raw: `{{baseUrl}}${apiDef.url}`,
          host: ["{{baseUrl}}"],
          path: apiDef.url.split('/').filter(p => p !== "")
        },
        body: {
          mode: "raw",
          raw: apiDef.successBody || "{}",
          options: {
            raw: {
              language: "json"
            }
          }
        }
      },
      response: []
    };

    // Add authentication headers if needed
    if (apiDef.needToken) {
      requestItem.request.header.push({
        key: "Authorization",
        value: "Bearer {{authToken}}",
        type: "text"
      });
    }

    // Add success response example
    if (apiDef.successPayload) {
      requestItem.response.push({
        name: "Success Response",
        originalRequest: requestItem.request,
        status: "OK",
        code: 200,
        _postman_previewlanguage: "json",
        header: [
          {
            key: "Content-Type",
            value: "application/json"
          }
        ],
        body: apiDef.successPayload
      });
    }

    // Add error response examples
    if (apiDef.errors && Array.isArray(apiDef.errors)) {
      apiDef.errors.forEach((error, index) => {
        const errorRequestCopy = JSON.parse(JSON.stringify(requestItem.request));
        if (error.body) {
          errorRequestCopy.body.raw = error.body;
        }
        
        requestItem.response.push({
          name: `Error Response - ${error.name || index + 1}`,
          originalRequest: errorRequestCopy,
          status: "Error",
          code: parseInt(error.name) || 400,
          _postman_previewlanguage: "json",
          header: [
            {
              key: "Content-Type",
              value: "application/json"
            }
          ],
          body: error.response || '{"error": true, "message": "Error occurred"}'
        });
      });
    }

    // Add to appropriate folder
    if (apiDef.role && roleFolders[apiDef.role]) {
      // Create a Lambda subfolder in the role folder if it doesn't exist
      let roleLambdaFolder = roleFolders[apiDef.role].item.find(subfolder => 
        subfolder.name === "Lambda APIs"
      );
      
      if (!roleLambdaFolder) {
        roleLambdaFolder = {
          name: "Lambda APIs",
          description: `Lambda API endpoints for ${apiDef.role}`,
          item: []
        };
        roleFolders[apiDef.role].item.push(roleLambdaFolder);
      }
      
      roleLambdaFolder.item.push(requestItem);
    } else {
      // Add to general Lambda folder
      lambdaFolder.item.push(requestItem);
    }
  });
}

/**
 * Generates a Postman collection for the API routes defined in the configuration
 * @param {Object} config - The configuration object containing routes and settings
 * @param {string} projectName - The name of the project
 * @param {string} projectPath - The base path of the project
 * @returns {Object} Postman collection object
 */
function generatePostmanCollection(config, projectName, projectPath) {
  const collection = {
    info: {
      name: `${projectName || config.settings.model_namespace} API Collection`,
      schema: "https://schema.getpostman.com/json/collection/v2.1.0/collection.json",
      description: `API collection for ${projectName || config.settings.model_namespace} project`,
      _postman_id: `${Date.now()}`
    },
    item: [],
    event: [],
    variable: []
  };

  // Add auth token variable
  collection.variable.push({
    key: "authToken",
    value: "",
    type: "string",
    description: "Authentication token for requests"
  });

  // Add base URL variable
  collection.variable.push({
    key: "baseUrl",
    value: "https://baas.mytechpassport.com",
    type: "string",
    description: "Base URL for API requests"
  });

  // Create folder structure for roles
  const roleFolders = {};
  if (config.roles && Array.isArray(config.roles)) {
    config.roles.forEach(role => {
      roleFolders[role.slug] = {
        name: `${role.name} Endpoints`,
        description: `Endpoints for ${role.name} role`,
        item: []
      };
    });
  }

  // Add default folder for general endpoints
  roleFolders.general = {
    name: "Custom Endpoints",
    description: "General API endpoints",
    item: []
  };

  // Process routes from configuration
  if (config.routes && Array.isArray(config.routes)) {
    config.routes.forEach(route => {
      if (!route.flowData?.nodes) return;

      const nodes = route.flowData.nodes;
      const firstNode = nodes[0];
      if (!firstNode) return;

      let routeConfig = {};
      let targetFolder = roleFolders.general;

      // Extract route information
      if (firstNode.type === "mock-api") {
        const nodeData = firstNode.data;
        if (!nodeData || !nodeData.path || !nodeData.method) return;

        routeConfig = {
          name: nodeData.apiname || `${nodeData.method.toUpperCase()} ${nodeData.path}`,
          method: nodeData.method.toUpperCase(),
          path: nodeData.path,
          description: nodeData.description || "",
          queryFields: nodeData.queryFields || [],
          bodyFields: nodeData.fields || [],
          responseFields: nodeData.responseFields || [],
          authType: nodeData.authType || "none"
        };
      } else {
        const nodeData = firstNode.data;
        if (!nodeData || !nodeData.path || !(route.method || nodeData.method)) return;

        routeConfig = {
          name: route.name || `${(route.method || nodeData.method).toUpperCase()} ${nodeData.path}`,
          method: (route.method || nodeData.method).toUpperCase(),
          path: nodeData.path,
          description: route.description || "",
          queryFields: nodeData.queryFields || [],
          bodyFields: nodeData.fields || [],
          authType: "bearer" // Assume bearer auth by default for custom routes
        };

        // Look for associated role
        if (route.role && roleFolders[route.role]) {
          targetFolder = roleFolders[route.role];
        }
      }

      // Create Postman request item
      const requestItem = {
        name: routeConfig.name,
        request: {
          method: routeConfig.method,
          description: routeConfig.description,
          header: [],
          url: {
            raw: `{{baseUrl}}${routeConfig.path}`,
            host: ["{{baseUrl}}"],
            path: routeConfig.path.split('/').filter(p => p !== "")
          },
          body: {
            mode: "raw",
            raw: "{}",
            options: {
              raw: {
                language: "json"
              }
            }
          }
        },
        response: []
      };

      // Add authentication headers
      if (routeConfig.authType === "bearer") {
        requestItem.request.header.push({
          key: "Authorization",
          value: "Bearer {{authToken}}",
          type: "text"
        });
      }

      // Add query parameters
      if (routeConfig.queryFields && routeConfig.queryFields.length > 0) {
        requestItem.request.url.query = routeConfig.queryFields.map(field => ({
          key: field.name,
          value: "",
          description: field.description || `${field.type} field`,
          disabled: !field.validation?.includes("required")
        }));
      }

      // Add request body fields
      if (routeConfig.bodyFields && routeConfig.bodyFields.length > 0) {
        const bodyObj = {};
        routeConfig.bodyFields.forEach(field => {
          bodyObj[field.name] = generateMockValue(field);
        });
        requestItem.request.body.raw = JSON.stringify(bodyObj, null, 2);
      }

      // Add example response
      if (routeConfig.responseFields && routeConfig.responseFields.length > 0) {
        const mockResponse = generateMockResponseObject(routeConfig.responseFields);
        requestItem.response.push({
          name: "Example Response",
          originalRequest: requestItem.request,
          status: "OK",
          code: 200,
          _postman_previewlanguage: "json",
          header: [
            {
              key: "Content-Type",
              value: "application/json"
            }
          ],
          body: JSON.stringify({ error: false, model: mockResponse }, null, 2)
        });
      }

      // Add request to appropriate folder
      targetFolder.item.push(requestItem);
    });
  }

  // Extract API definitions from lambda files if project path is provided
  let lambdaApis = [];
  if (projectPath) {
    lambdaApis = extractLambdaApiDefinitions(projectPath, projectName || config.settings.model_namespace);
    // Add lambda APIs to the collection
    addLambdaApisToCollection(collection, lambdaApis, roleFolders);
  }

  // Add folders to collection
  Object.values(roleFolders).forEach(folder => {
    if (folder.item.length > 0) {
      collection.item.push(folder);
    }
  });

  return collection;
}

/**
 * Creates a Postman collection file in the project directory
 * @param {Object} config - The configuration object
 * @param {string} projectPath - The base path of the project
 * @param {string} projectName - The name of the project
 */
function generatePostmanCollectionFile(config, projectPath, projectName) {
  try {
    const collection = generatePostmanCollection(config, projectName, projectPath);
    
    // Create the postman file
    const postmanFilePath = path.join(projectPath, `${projectName || config.settings.model_namespace}_postman_collection.json`);
    fs.writeFileSync(postmanFilePath, JSON.stringify(collection, null, 2));
    
    console.log(`Generated Postman collection at: ${postmanFilePath}`);
  } catch (error) {
    console.error('Error generating Postman collection:', error);
  }
}

/**
 * Main function to run the migration process
 * @param {string} configPath - Path to the configuration file
 * @param {string} operation - Operation to perform (roles, models, api, create, drop)
 * @param {string} projectName - Name of the project
 */
async function main(
  configPath = "./config.json",
  operation = null,
  projectName = null
) {
  try {
    // Load configuration from JSON file
    const configContent = fs.readFileSync(configPath, "utf8");
    const config = JSON.parse(configContent);

    if (!config || !config.settings || !projectName) {
      throw new Error("Invalid configuration: missing model namespace");
    }

    const projectPath = path.join(path.dirname(configPath), "..");

    // Create project directory if it doesn't exist
    if (!fs.existsSync(projectPath)) {
      fs.mkdirSync(projectPath, { recursive: true });
    }

    // Remove duplicate models by name
    if (config.models) {
      const uniqueModels = new Map();
      config.models.forEach((model) => {
        if (!uniqueModels.has(model.name)) {
          // Remove duplicate fields within each model
          const uniqueFields = new Map();
          model.fields.forEach((field) => {
            if (!uniqueFields.has(field.name)) {
              uniqueFields.set(field.name, field);
            }
          });
          model.fields = Array.from(uniqueFields.values());
          uniqueModels.set(model.name, model);
        }
      });
      config.models = Array.from(uniqueModels.values());
    }

    // Remove duplicate roles by slug
    if (config.roles) {
      const uniqueRoles = new Map();
      config.roles.forEach((role) => {
        if (!uniqueRoles.has(role.slug)) {
          uniqueRoles.set(role.slug, role);
        }
      });
      config.roles = Array.from(uniqueRoles.values());
    }

    // Execute specific operation based on argument
    switch (operation) {
      case "create":
        if (config.models) {
          let sql = "";
          for (const model of config.models) {
            sql += generateCreateTableSQL(model, projectName);
          }
          // Write SQL to file
          const createSqlPath = path.join(projectPath, "create_tables.sql");
          fs.writeFileSync(createSqlPath, sql);
          console.log(`Create tables SQL generated at: ${createSqlPath}`);
        }
        break;

      case "drop":
        if (config.models) {
          let sql = "";
          for (const model of config.models) {
            sql += generateDropTableSQL(model, projectName);
          }
          // Write SQL to file
          const dropSqlPath = path.join(projectPath, "drop_tables.sql");
          fs.writeFileSync(dropSqlPath, sql);
          console.log(`Drop tables SQL generated at: ${dropSqlPath}`);
        }
        break;

      case "roles":
        if (config.roles) {
          processRoles(config, projectPath, projectName);
          console.log("Roles migration completed successfully");
        }
        break;

      case "models":
        if (config.models) {
          const customModelPath = path.join(projectPath, "models");
          if (!fs.existsSync(customModelPath)) {
            fs.mkdirSync(customModelPath, { recursive: true });
          }

          for (const model of config.models) {
            const modelFilePath = path.join(
              customModelPath,
              `${model.name}.js`
            );
            const modelSourceCode = generateModelContent(model, projectName);
            const formattedCode = formatModelCode(modelSourceCode);
            fs.writeFileSync(modelFilePath, formattedCode);
            console.log(`Generated model: ${modelFilePath}`);
          }
          console.log("Models migration completed successfully");
        }
        break;

      case "api":
        if (config.routes) {
          processAPI(config, projectPath);
          console.log("API migration completed successfully");
        }
        break;

      case "sdk":
        if (config.routes) {
          processSDK(config, projectPath, projectName); //This will make sdk for frontend to call per role
          console.log("SDK migration completed successfully");
          processAdminPages(config, projectPath, projectName);
          console.log("Admin pages migration completed successfully");
        }
        break;

      case "seed":
        if (config.models) {
          let sql = "";
          for (const model of config.models) {
            sql += await generateSeedTableSQL(model, projectName);
          }
          // Write SQL to file
          const seedSqlPath = path.join(projectPath, "seed_tables.sql");
          fs.writeFileSync(seedSqlPath, sql);
          console.log(`Seed tables SQL generated at: ${seedSqlPath}`);
        }
        break;

      case "postman":
        if (config.routes) {
          generatePostmanCollectionFile(config, projectPath, projectName);
          console.log("Postman collection generated successfully");
        }
        break;

      default:
        // If no operation specified, run all
        if (config.roles) {
          processRoles(config, projectPath, projectName);
        }
        if (config.models) {
          // Add model processing logic here
        }
        if (config.routes) {
          processAPI(config, projectPath, projectName);
          generatePostmanCollectionFile(config, projectPath, projectName);
        }
        console.log("Full migration completed successfully");
    }
  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  }
}

// Get operation from command line arguments
if (require.main === module) {
  const args = process.argv.slice(2);
  let configPath, operation, projectName;

  // If there are 2 arguments, first is operation, second is path
  if (args.length >= 2) {
    operation = args[0];
    configPath = args[1];
    projectName = args[2];
  }
  // If there's only 1 argument, check if it's a path or operation
  else if (args.length === 1) {
    if (args[0].endsWith(".json")) {
      configPath = args[0];
    } else {
      operation = args[0];
      configPath = "./config.json";
    }
  }

  // Resolve the config path relative to current directory
  if (configPath) {
    configPath = path.resolve(process.cwd(), configPath);
  }

  main(configPath, operation, projectName).catch((error) => {
    console.error("Migration failed:", error);
    process.exit(1);
  });
}

// Export all the functions we need to test
module.exports = {
  generateCreateTableSQL,
  generateSeedTableSQL,
  generateDropTableSQL,
  generateRelationMethod,
  generateRelationMethods,
  generateModelContent,
  generateRoleModel,
  processAPI,
  generateMockApiTests,
  generateRoutes,
  generateRouteHandler,
  generateUrlValidation,
  generateOutput,
  generateDatabaseFind,
  generateDatabaseQuery,
  generateDatabaseInsert,
  generateDatabaseUpdate,
  generateDatabaseDelete,
  generateFieldValidation,
  generateObjectValidation,
  generateMockResponseObject,
  generateMockValue,
  generateMockObjectValue,
  generateMockValueForType,
  generateVariable,
  generateAuth,
  generatePostmanCollection,
  generatePostmanCollectionFile,
  extractLambdaApiDefinitions,
  processReturnStatement,
  addLambdaApisToCollection,
  main,
};
