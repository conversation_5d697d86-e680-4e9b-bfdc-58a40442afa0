# TODO: create a bash script to deploy this project on nginx
# domain: baas.mytechpassport.com
# pm2 name: baas-v5

#!/bin/bash

# Deployment script for mtp-node application

echo "Starting deployment process..."

# Check if the directory exists, if not create it
if [ ! -d "/var/www/baas.mytechpassport.com" ]; then
    echo "Creating application directory..."
    sudo mkdir -p /var/www/baas.mytechpassport.com
fi


# Pull the latest code
echo "Pulling latest code..."
git pull origin main

# Install dependencies
echo "Installing dependencies..."
npm install --production




# Check if the app is already running in PM2
if pm2 list | grep -q "baas-v5"; then
    echo "Stopping existing PM2 process..."
    pm2 stop baas-v5
    pm2 delete baas-v5
fi

# Start the application with PM2
echo "Starting application with PM2..."
pm2 start ecosystem.config.js

# Save PM2 process list
echo "Saving PM2 process list..."
pm2 save

# Setup PM2 startup script
echo "Setting up PM2 startup..."
pm2 startup

# Setup Nginx configuration
echo "Setting up Nginx configuration..."
sudo cp nginx.conf /etc/nginx/sites-available/baas.mytechpassport.com
sudo ln -sf /etc/nginx/sites-available/baas.mytechpassport.com /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl restart nginx

sudo certbot --nginx -d baas.mytechpassport.com

echo "Deployment completed successfully!" 