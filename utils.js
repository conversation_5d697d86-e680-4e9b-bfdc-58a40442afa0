function StringCaser() {
  /**
   * @typedef {Object} StringCaserOptions
   * @property {"space" | String} separator - define what separates each word, undefined returns no separation - passing "space" separates the words by a space
   * @property {"uppercase" | "lowercase" | "capitalize" | "camelCase" | "PascalCase"} casetype - text case type, uppercase, lowercase of capitalized | default is lowercase
   */
  /**
   *
   * @param {String} string - text to convert
   * @param {StringCaserOptions} options - options
   * @param {Array} exclude - special characters to retain
   * @returns String
   */

  this._stringCaser = (string, options, exclude = []) => {
    if (!string) return null;
    if (typeof string !== "string") return null;
    // const removedSpecialCharacters = exclude.length
    //   ? string.replace(new RegExp(`[^a-zA-Z0-9${exclude.join("")}]`, "g"), " ")
    //   : string.replace(/[^a-zA-Z0-9]/g, " ");
    let casedText = [];
    // const splitWords = removedSpecialCharacters.split(" ").filter(Boolean);

    const removedSpecialCharacters = exclude?.length
      ? string.replace(new RegExp(`[^a-zA-Z0-9${exclude.join("")}]`, "g"), " ")
      : string.replace(/[^a-zA-Z0-9]/g, " ");

    let splitWords = removedSpecialCharacters.split(/[\s_-]/).filter(Boolean);

    if (options?.casetype === "capitalize") {
      casedText = splitWords.map(
        (/** @type {string} */ dt) =>
          `${dt[0].toUpperCase()}${dt.substring(1).toLowerCase()} `
      );
    }
    if (options?.casetype === "uppercase") {
      casedText = splitWords.map((/** @type {string} */ dt) =>
        dt.toUpperCase()
      );
    }
    if (options?.casetype === "lowercase") {
      casedText = splitWords.map((/** @type {string} */ dt) =>
        dt.toLowerCase()
      );
    }
    if (options?.casetype === "camelCase") {
      casedText = splitWords.map((/** @type {string} */ dt, index) =>
        index === 0
          ? dt.toLowerCase()
          : `${dt[0].toUpperCase()}${dt.substring(1)} `
      );
    }
    if (options?.casetype === "PascalCase") {
      casedText = splitWords.map(
        (/** @type {string} */ dt) => `${dt[0].toUpperCase()}${dt.substring(1)}`
      );
    }

    if (options?.separator) {
      if (options?.separator === "space") {
        return casedText.join(" ").trim().replace(/\s+/g, " ");
      } else {
        return casedText.join(options?.separator).trim().replace(/\s+/g, " ");
      }
    } else {
      return casedText.join("").trim().replace(/\s+/g, " ");
    }
  };

  /**
   * @description Convert text to UPPERCASE
   * @param {string} text - text to convert
   * @param {string} separator - separator
   * @param {Array} exclude - special characters to retain
   * @returns {string}
   *
   * */
  this.UPPERCASE = (text, separator = "", exclude = []) => {
    return this._stringCaser(
      text,
      { separator, casetype: "uppercase" },
      exclude
    );
  };

  /**
   * @description Convert text to lowercase
   * @param {string} text - text to convert
   * @param {string} separator - separator
   * @param {Array} exclude - special characters to retain
   * @returns {string}
   *
   * */

  this.lowercase = (text, separator = "", exclude = []) => {
    return this._stringCaser(
      text,
      { separator, casetype: "lowercase" },
      exclude
    );
  };

  /**
   * @description Convert text to Capitalized
   * @param {string} text - text to convert
   * @param {string} separator - separator
   * @param {Array} exclude - special characters to retain
   * @returns {string}
   *
   * */

  this.Capitalize = (text, separator = "", exclude = []) => {
    return this._stringCaser(
      text,
      { separator, casetype: "capitalize" },
      exclude
    );
  };

  /**
   * @description Convert text to camelCase
   * @param {string} text - text to convert
   * @param {string} separator - separator
   * @param {Array} exclude - special characters to retain
   * @returns {string}
   *
   * */

  this.camelCase = (text, separator = "", exclude = []) => {
    return this._stringCaser(
      text,
      { separator, casetype: "camelCase" },
      exclude
    );
  };

  /**
   * @description Convert text to PascalCase
   * @param {string} text - text to convert
   * @param {string} separator - separator
   * @param {Array} exclude - special characters to retain
   * @returns {string}
   *
   * */

  this.PascalCase = (text, separator = "", exclude = []) => {
    return this._stringCaser(
      text,
      { separator, casetype: "PascalCase" },
      exclude
    );
  };

  return {
    UPPERCASE: this.UPPERCASE,
    lowercase: this.lowercase,
    Capitalize: this.Capitalize,
    camelCase: this.camelCase,
    PascalCase: this.PascalCase
  };
}

module.exports = {
  StringCaser
};
