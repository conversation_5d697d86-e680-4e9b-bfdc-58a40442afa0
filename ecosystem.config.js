module.exports = {
  apps: [
    {
      name: "<PERSON><PERSON>",
      script: "index.js",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "2G",
      node_args: "-r dotenv/config",
      args: "dotenv_config_path=./.env",
      exp_backoff_restart_delay: 100,
      merge_logs: true,
      error_file: "/var/log/baas.mytechpassport.com.pm2.error.log",
      out_file: "/var/log/baas.mytechpassport.com.pm2.access.log",
      time: true,
      kill_timeout: 5000,
      wait_ready: true,
    },
  ],
};
