services:
  app:
    build: .
    ports:
      - "5172:5172"
    depends_on:
      - mysql
    environment:
      - NODE_ENV=development
    volumes:
      - .:/app
      - /app/node_modules
    networks:
      - baas-network
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: baas_db
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - baas-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 5s
      retries: 10

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      MYSQL_ROOT_PASSWORD: root
    depends_on:
      - mysql
    networks:
      - baas-network

networks:
  baas-network:
    driver: bridge

volumes:
  mysql-data: 