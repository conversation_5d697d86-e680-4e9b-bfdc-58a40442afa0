# Global Application Configuration
GLOBAL_KEY=your_global_key_here
JWT_KEY=your_jwt_key_here
REFRESH_JWT_EXPIRE=604800
ACCESS_JWT_EXPIRE=3600
VERIFICATION_TOKEN_EXPIRE=86400
APP_URL=http://localhost:3000

# Database Configuration
DB_TYPE=MySQL
DB_HOST=localhost
DB_PORT=3306
DB_USER=db
DB_PASSWORD=db_pw
DB_NAME=baas_name
DB_TIMEZONE=UTC

# Mail Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_email_password_here
FROM_MAIL=<EMAIL>

# Authentication Configuration
SESSION=your_session_secret_here

# Server Configuration
PORT=5172
CORS_ORIGIN=http://localhost:3000

# Logging Configuration
LOG_LEVEL=info

# Stripe Configuration
STRIPE_PUBLISH_KEY=pk_test_your_stripe_publish_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_ENDPOINT_SECRET=whsec_your_webhook_secret
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_CURRENCY=usd
STRIPE_IETI_SECRET=your_ieti_secret

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_BUCKET_NAME=your_s3_bucket_name
AWS_REGION=us-east-1

# Authentication Methods
BASIC_AUTH=your_basic_auth_credentials
BEARER_AUTH=your_bearer_auth_token
API_KEY=your_api_key
OAUTH2=your_oauth2_credentials
DIGEST=your_digest_credentials
HMAC=your_hmac_secret
LDAP=your_ldap_config
SAML=your_saml_config

# Git Configuration
GIT_TOKEN=your_git_token

# Frontend Server Configuration
FRONTEND_SERVER_IP=your_frontend_server_ip
FRONTEND_SERVER_USERNAME=your_username
FRONTEND_SERVER_PASSWORD=your_password

# Digital Ocean Configuration
DIGITAL_OCEAN_TOKEN=your_digital_ocean_token

# Jenkins Configuration
JENKINS_TOKEN=your_jenkins_token

# Google Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URL=https://baas.mytechpassport.com/v2/api/lambda/google/code
GOOGLE_CALENDAR_CLIENT_ID=your_google_calendar_client_id
GOOGLE_CALENDAR_CLIENT_SECRET=your_google_calendar_client_secret
GOOGLE_CALENDAR_REDIRECT_URI=http://localhost:3000/auth/google/calendar/callback
GOOGLE_DRIVE_CLIENT_ID=your_google_drive_client_id
GOOGLE_DRIVE_CLIENT_SECRET=your_google_drive_client_secret
GOOGLE_DRIVE_REDIRECT_URL=http://localhost:3000/auth/google/drive/callback
GOOGLE_DRIVE_API_KEY=your_google_drive_api_key

# Apple Configuration
APPLE_CLIENT_ID=your_apple_client_id
APPLE_CLIENT_SECRET=your_apple_client_secret
APPLE_REDIRECT_URL=http://localhost:3000/auth/apple/callback
APPLE_TEAM_ID=your_apple_team_id
APPLE_PRIVATE_KEY=your_apple_private_key
APPLE_KEY_ID=your_apple_key_id

# Facebook Configuration
FACEBOOK_CLIENT_ID=your_facebook_client_id
FACEBOOK_CLIENT_SECRET=your_facebook_client_secret
FACEBOOK_CALLBACK_URI=https://baas.mytechpassport.com/v2/api/lambda/facebook/code

# Instagram Configuration
INSTAGRAM_CLIENT_ID=your_instagram_client_id
INSTAGRAM_CLIENT_SECRET=your_instagram_client_secret
INSTAGRAM_CALLBACK_URI=http://localhost:3000/auth/instagram/callback

# Microsoft Configuration
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
MICROSOFT_CLIENT_VALUES=your_microsoft_client_values
MICROSOFT_APPLICATION_ID=your_microsoft_application_id
MICROSOFT_OBJECT_ID=your_microsoft_object_id
MICROSOFT_REDIRECT_URL=http://localhost:3000/auth/microsoft/callback

# LinkedIn Configuration
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
LINKEDIN_REDIRECT_URL=https://baas.mytechpassport.com/v2/api/lambda/linkedin/code


# Hostnames
KANGLINK_HOSTNAME=app.traineriq.ca