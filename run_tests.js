const fs = require("fs");
const path = require("path");
const yargs = require("yargs/yargs");
const { hideBin } = require("yargs/helpers");

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option("name", {
    alias: "n",
    type: "string",
    description: "Name of the project",
    demandOption: true,
  })
  .help().argv;

function parseAPIFile(content) {
  const endpoints = [];

  // Multiple regex patterns to match different API declarations
  const patterns = [
    // Pattern 1: Standard with middleware array
    /app\.(get|post|put|delete|patch)\s*\(\s*['"]([^'"]+)['"]\s*,\s*\[([^\]]*)\]\s*,\s*async\s*\(([^)]+)\)/g,
    // Pattern 2: Without middleware array
    /app\.(get|post|put|delete|patch)\s*\(\s*['"]([^'"]+)['"]\s*,\s*async\s*function\s*\(([^)]+)\)/g,
    // Pattern 3: Simple function declaration
    /app\.(get|post|put|delete|patch)\s*\(\s*['"]([^'"]+)['"]\s*,\s*async\s*\(([^)]+)\)/g,
  ];

  patterns.forEach((pattern) => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const endpoint = {
        method: match[1],
        path: match[2],
        params: (match[3] || match[2]).split(",").map((p) => p.trim()),
        requiresAuth:
          content.includes("jwt") ||
          content.includes("authentication") ||
          content.includes("authorize"),
        validates: {
          body: content.includes("req.body"),
          params: content.includes("req.params"),
          query: content.includes("req.query"),
        },
        services: {
          sdk: content.includes("sdk."),
          auth: content.includes("AuthService"),
          twoFactor: content.includes("TwoFactorService"),
        },
      };
      endpoints.push(endpoint);
    }
  });

  return endpoints;
}

function generateTestCase(endpoint) {
  const testCases = [];

  // Validation tests
  if (endpoint.validates.body) {
    testCases.push(`
    framework.addTestCase('should validate required fields', async () => {
        const response = await framework.makeRequest('${endpoint.path}', {
            method: '${endpoint.method.toUpperCase()}',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})  // Empty body to trigger validation
        });
        framework.assert(response.status === 403, 'Expected validation error status code');
        framework.assert(response.body.error === true, 'Expected error flag in response');
        framework.assert(Array.isArray(response.body.validation), 'Expected validation array in response');
    });`);
  }

  // Authentication test
  if (endpoint.requiresAuth) {
    testCases.push(`
    framework.addTestCase('should require authentication', async () => {
        const response = await framework.makeRequest('${endpoint.path}', {
            method: '${endpoint.method.toUpperCase()}'
        });
        framework.assert(response.status === 403, 'Expected forbidden status code');
    });`);
  }

  // Service-specific tests
  if (endpoint.services.twoFactor) {
    testCases.push(`
    framework.addTestCase('should handle 2FA flow', async () => {
        const response = await framework.makeRequest('${endpoint.path}', {
            method: '${endpoint.method.toUpperCase()}',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test_token'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'password123',
                role: 'company_admin'
            })
        });
        framework.assert(response.status === 200, 'Expected successful 2FA initialization');
        framework.assert(response.body.qr_code !== undefined, 'Expected QR code in response');
        framework.assert(response.body.one_time_token !== undefined, 'Expected one-time token in response');
    });`);
  }

  // Success case
  testCases.push(`
    framework.addTestCase('should return 200 with valid request', async () => {
        const response = await framework.makeRequest('${endpoint.path}', {
            method: '${endpoint.method.toUpperCase()}',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test_token'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'password123',
                role: 'company_admin'
            })
        });
        framework.assert(response.status === 200, 'Expected success status code');
        framework.assert(!response.body.error, 'Expected success response');
    });`);

  // Error handling
  testCases.push(`
    framework.addTestCase('should handle errors gracefully', async () => {
        const response = await framework.makeRequest('${endpoint.path}', {
            method: '${endpoint.method.toUpperCase()}',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test_token'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'wrong_password',
                role: 'company_admin'
            })
        });
        framework.assert(response.status === 403, 'Expected error status code');
        framework.assert(response.body.error === true, 'Expected error flag in response');
        framework.assert(typeof response.body.message === 'string', 'Expected error message in response');
    });`);

  return testCases.join("\n");
}

function generateTestContent(endpoints, sourceFileName) {
  return `const APITestFramework = require('../APITestFramework');

const framework = new APITestFramework();

// Test suite for ${sourceFileName}
describe('${sourceFileName} API Tests', () => {
    ${endpoints
      .map(
        (endpoint) => `
    describe('${endpoint.method.toUpperCase()} ${endpoint.path}', () => {
        ${generateTestCase(endpoint)}
    });`
      )
      .join("\n")}
});

// Run tests
(async () => {
    await framework.runTests();
    const report = framework.generateTestReport();
    process.exit(report.failed > 0 ? 1 : 0);
})();
`;
}
function createTestFile() {
  const name = argv.name;
  const sourcePath = path.join(__dirname, "custom", name);
  const destinationPath = path.join(sourcePath, "tests");
  console.log(sourcePath, destinationPath);

  const isSourceDirectory = fs.lstatSync(sourcePath).isDirectory();

  if (isSourceDirectory) {
    const folderName = path.basename(sourcePath);
    const testFolderPath = destinationPath;

    if (!fs.existsSync(testFolderPath)) {
      console.log(`Creating directory at: ${testFolderPath}`);
      fs.mkdirSync(testFolderPath, { recursive: true });
    }
    // Start recursive processing from the source directory
    processDirectory(sourcePath, destinationPath);
  } else {
    // If it's not a directory, create test file as before
    createTestForFile(sourcePath, destinationPath);
  }
}


function processDirectory(sourceDir, destinationDir) {
  const folderName = path.basename(sourceDir);
  if (folderName === `${folderName}.test`) return;
  if (folderName === "node_modules") return;
  if (folderName === "tests") return;
  const testFolderPath = path.join(destinationDir, `${folderName}.test`);

  if (!fs.existsSync(testFolderPath)) {
    console.log(`Creating directory at: ${testFolderPath}`);
    fs.mkdirSync(testFolderPath, { recursive: true });
  }

  // Read all items in the directory (both files and folders)
  const items = fs.readdirSync(sourceDir);
  items.forEach((item) => {
    const itemPath = path.join(sourceDir, item);
    const itemDestPath = path.join(testFolderPath, item);

    if (fs.lstatSync(itemPath).isDirectory()) {
      // If the item is a directory, recurse into it
      processDirectory(itemPath, testFolderPath);
    } else if (fs.lstatSync(itemPath).isFile()) {
      // If the item is a file, create a test for it
      createTestForFile(itemPath, testFolderPath);
    }
  });
}

function createTestForFile(sourcePath, destinationPath) {
  const sourceContent = fs.readFileSync(sourcePath, 'utf8');
  const sourceFileName = path.parse(sourcePath).name;
  const endpoints = parseAPIFile(sourceContent);

  if (endpoints.length === 0) {
    console.log("No API endpoints found in the source file.");
    return;
  }

  const testFileName = `${sourceFileName}.test.js`;
  const testFilePath = path.join(destinationPath, testFileName);
  const testContent = generateTestContent(endpoints, sourceFileName);

  try {
    fs.writeFileSync(testFilePath, testContent);
    console.log(`Created test file at: ${testFilePath}`);
    console.log(`Generated test cases for ${endpoints.length} endpoint(s):`);
    endpoints.forEach((endpoint) => {
      console.log(`- ${endpoint.method.toUpperCase()} ${endpoint.path}`);
      if (endpoint.services.twoFactor) console.log("  - Includes 2FA tests");
      if (endpoint.validates.body) console.log("  - Includes validation tests");
      if (endpoint.requiresAuth)
        console.log("  - Includes authentication tests");
    });
  } catch (error) {
    console.error("Error creating test file:", error);
    process.exit(1);
  }
}

createTestFile();
